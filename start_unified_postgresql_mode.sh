#!/bin/bash

# pyspiderNX2 統一PostgreSQLモード起動スクリプト
# 全てのコンポーネントがPostgreSQLデータベースを使用

echo "=== pyspiderNX2 統一PostgreSQLモード起動 ==="
echo "データベース: PostgreSQL"
echo "メッセージキュー: Redis"
echo "WebUI: PostgreSQL (統一モード)"
echo ""

# 作業ディレクトリの設定
cd "$(dirname "$0")"
PYSPIDER_ROOT=$(pwd)

# ログディレクトリの作成
mkdir -p logs

# 設定ファイルの確認
CONFIG_FILE="postgresql_redis_config.json"
if [ ! -f "$CONFIG_FILE" ]; then
    echo "エラー: 設定ファイル $CONFIG_FILE が見つかりません"
    exit 1
fi

echo "設定ファイル: $CONFIG_FILE"
echo "usedatabase設定: $(jq -r '.usedatabase // "未設定"' $CONFIG_FILE)"
echo ""

# 既存のpyspiderプロセスを停止
echo "既存のpyspiderプロセスを停止中..."
pkill -f "python -m pyspider" 2>/dev/null || true
sleep 2

# PostgreSQLサーバーの確認
echo "PostgreSQLサーバーの確認中..."
if ! pg_isready -h localhost -p 5432 > /dev/null 2>&1; then
    echo "エラー: PostgreSQLサーバーが起動していません"
    echo "PostgreSQLサーバーを起動してください: sudo systemctl start postgresql"
    exit 1
fi
echo "PostgreSQL接続確認: OK"

# PostgreSQLデータベースとユーザーの確認
echo "PostgreSQLデータベースの確認中..."
if ! psql -h localhost -U pyspider -d pyspider_projectdb -c "SELECT 1;" > /dev/null 2>&1; then
    echo "警告: pyspiderデータベースまたはユーザーが存在しません"
    echo "以下のコマンドでセットアップしてください:"
    echo "  sudo -u postgres createuser pyspider"
    echo "  sudo -u postgres createdb pyspider_projectdb -O pyspider"
    echo "  sudo -u postgres createdb pyspider_taskdb -O pyspider"
    echo "  sudo -u postgres createdb pyspider_resultdb -O pyspider"
    echo "  sudo -u postgres psql -c \"ALTER USER pyspider PASSWORD 'PySpider2024!SecurePass#';\""
    echo ""
fi

# Redisサーバーの確認
echo "Redisサーバーの確認中..."
if ! redis-cli ping > /dev/null 2>&1; then
    echo "警告: Redisサーバーが起動していません"
    echo "Redisを起動してください: sudo systemctl start redis-server"
    echo ""
fi

# 統一PostgreSQLモードでpyspiderコンポーネントを起動
echo "=== pyspiderコンポーネント起動 (統一PostgreSQLモード) ==="

# 1. Scheduler (PostgreSQL)
echo "1. Scheduler起動中..."
python -m pyspider.run -c $CONFIG_FILE --log-level=info --log-dir=logs scheduler &
SCHEDULER_PID=$!
echo "   Scheduler PID: $SCHEDULER_PID"
sleep 3

# 2. Fetcher (PostgreSQL)
echo "2. Fetcher起動中..."
python -m pyspider.run -c $CONFIG_FILE --log-level=info --log-dir=logs fetcher &
FETCHER_PID=$!
echo "   Fetcher PID: $FETCHER_PID"
sleep 2

# 3. Processor (PostgreSQL)
echo "3. Processor起動中..."
python -m pyspider.run -c $CONFIG_FILE --log-level=info --log-dir=logs processor &
PROCESSOR_PID=$!
echo "   Processor PID: $PROCESSOR_PID"
sleep 2

# 4. Result Worker (PostgreSQL)
echo "4. Result Worker起動中..."
python -m pyspider.run -c $CONFIG_FILE --log-level=info --log-dir=logs result-worker &
RESULT_WORKER_PID=$!
echo "   Result Worker PID: $RESULT_WORKER_PID"
sleep 2

# 5. WebUI (PostgreSQL - 統一モード)
echo "5. WebUI起動中..."
python -m pyspider.run -c $CONFIG_FILE --log-level=info --log-dir=logs webui --scheduler-rpc=http://localhost:23333/ &
WEBUI_PID=$!
echo "   WebUI PID: $WEBUI_PID"
sleep 3

echo ""
echo "=== 起動完了 ==="
echo "全てのコンポーネントがPostgreSQLデータベースを使用しています"
echo ""
echo "プロセス一覧:"
echo "  Scheduler:     PID $SCHEDULER_PID"
echo "  Fetcher:       PID $FETCHER_PID"
echo "  Processor:     PID $PROCESSOR_PID"
echo "  Result Worker: PID $RESULT_WORKER_PID"
echo "  WebUI:         PID $WEBUI_PID"
echo ""
echo "アクセスURL:"
echo "  WebUI:         http://localhost:5000"
echo "  WebUI-Next:    http://localhost:3000 (別途起動が必要)"
echo ""
echo "PostgreSQLデータベース:"
echo "  プロジェクト:  pyspider_projectdb.projectdb"
echo "  タスク:        pyspider_taskdb.*"
echo "  結果:          pyspider_resultdb.*"
echo ""
echo "ログファイル:"
echo "  logs/scheduler.log"
echo "  logs/fetcher.log"
echo "  logs/processor.log"
echo "  logs/result-worker.log"
echo "  logs/webui.log"
echo ""
echo "停止方法:"
echo "  pkill -f 'python -m pyspider'"
echo ""
echo "=== 統一PostgreSQLモード起動完了 ==="

# プロセス監視
echo "プロセス監視を開始します (Ctrl+Cで停止)..."
trap 'echo ""; echo "プロセスを停止中..."; pkill -f "python -m pyspider"; exit 0' INT

while true; do
    sleep 10
    # プロセスの生存確認
    if ! kill -0 $SCHEDULER_PID 2>/dev/null; then
        echo "警告: Schedulerプロセスが停止しました"
    fi
    if ! kill -0 $FETCHER_PID 2>/dev/null; then
        echo "警告: Fetcherプロセスが停止しました"
    fi
    if ! kill -0 $PROCESSOR_PID 2>/dev/null; then
        echo "警告: Processorプロセスが停止しました"
    fi
    if ! kill -0 $RESULT_WORKER_PID 2>/dev/null; then
        echo "警告: Result Workerプロセスが停止しました"
    fi
    if ! kill -0 $WEBUI_PID 2>/dev/null; then
        echo "警告: WebUIプロセスが停止しました"
    fi
done
