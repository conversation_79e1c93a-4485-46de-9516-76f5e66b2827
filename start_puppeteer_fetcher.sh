#!/bin/bash
# Start Puppeteer Fetcher

# Set environment variables
export PUPPETEER_FETCHER_PORT=22223
export NODE_ENV=production
# Skip Chromium download if already installed
export PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true
# Disable sandbox for Docker environments
export PUPPETEER_NO_SANDBOX=true

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "Node.js is not installed. Please install Node.js first."
    exit 1
fi

# Check if puppeteer_fetcher.js exists
if [ ! -f "pyspider/fetcher/puppeteer_fetcher.js" ]; then
    echo "puppeteer_fetcher.js not found."
    exit 1
fi

# Check if required Node.js packages are installed
if [ -d "pyspider" ]; then
    cd pyspider
    if [ ! -d "node_modules/puppeteer" ] || [ ! -d "node_modules/express" ] || [ ! -d "node_modules/body-parser" ]; then
        echo "Installing required Node.js packages..."
        npm install
    fi
else
    echo "Error: pyspider directory not found"
    exit 1
fi

# 完全なプロセスクリーンアップ機能
cleanup_puppeteer_processes() {
    echo "Puppeteer Fetcherプロセスの完全クリーンアップを実行中..."

    # 1. ポート使用プロセスの特定と終了
    for PORT in 22223 22224 22225 22226 22227; do
        PIDS=$(lsof -i:$PORT -t 2>/dev/null)
        if [ ! -z "$PIDS" ]; then
            echo "ポート$PORTを使用中のプロセスを終了: $PIDS"
            kill -TERM $PIDS 2>/dev/null
            sleep 1
            # 強制終了が必要な場合
            REMAINING_PIDS=$(lsof -i:$PORT -t 2>/dev/null)
            if [ ! -z "$REMAINING_PIDS" ]; then
                echo "強制終了: $REMAINING_PIDS"
                kill -9 $REMAINING_PIDS 2>/dev/null
            fi
        fi
    done

    # 2. puppeteer_fetcher.jsプロセスの終了
    NODE_PIDS=$(ps aux | grep "node.*puppeteer_fetcher.js" | grep -v grep | awk '{print $2}')
    if [ ! -z "$NODE_PIDS" ]; then
        echo "Puppeteer Fetcherプロセスを終了: $NODE_PIDS"
        kill -TERM $NODE_PIDS 2>/dev/null
        sleep 2
        # 強制終了が必要な場合
        REMAINING_NODE_PIDS=$(ps aux | grep "node.*puppeteer_fetcher.js" | grep -v grep | awk '{print $2}')
        if [ ! -z "$REMAINING_NODE_PIDS" ]; then
            echo "強制終了: $REMAINING_NODE_PIDS"
            kill -9 $REMAINING_NODE_PIDS 2>/dev/null
        fi
    fi

    # 3. Chromiumプロセスの終了（孤立プロセス対策）
    CHROME_PIDS=$(ps aux | grep -E "(chrome|chromium).*--no-sandbox" | grep -v grep | awk '{print $2}')
    if [ ! -z "$CHROME_PIDS" ]; then
        echo "孤立したChromiumプロセスを終了: $CHROME_PIDS"
        kill -TERM $CHROME_PIDS 2>/dev/null
        sleep 1
        # 強制終了が必要な場合
        REMAINING_CHROME_PIDS=$(ps aux | grep -E "(chrome|chromium).*--no-sandbox" | grep -v grep | awk '{print $2}')
        if [ ! -z "$REMAINING_CHROME_PIDS" ]; then
            echo "強制終了: $REMAINING_CHROME_PIDS"
            kill -9 $REMAINING_CHROME_PIDS 2>/dev/null
        fi
    fi

    # 4. ポート解放の確認
    sleep 2
    echo "プロセスクリーンアップ完了"
}

# クリーンアップ実行
cleanup_puppeteer_processes

# ポートプール管理システム
find_available_port() {
    local AVAILABLE_PORTS=(22223 22224 22225 22226 22227)
    local SELECTED_PORT=""

    echo "利用可能なポートを検索中..."

    for PORT in "${AVAILABLE_PORTS[@]}"; do
        if ! lsof -i:$PORT -t &>/dev/null; then
            SELECTED_PORT=$PORT
            echo "利用可能なポートを発見: $PORT"
            break
        else
            echo "ポート$PORTは使用中"
        fi
    done

    if [ -z "$SELECTED_PORT" ]; then
        echo "エラー: 利用可能なポートが見つかりません"
        echo "使用中のポート一覧:"
        for PORT in "${AVAILABLE_PORTS[@]}"; do
            PIDS=$(lsof -i:$PORT -t 2>/dev/null)
            if [ ! -z "$PIDS" ]; then
                echo "  ポート$PORT: PID $PIDS"
            fi
        done
        exit 1
    fi

    export PUPPETEER_FETCHER_PORT=$SELECTED_PORT
    echo "選択されたポート: $PUPPETEER_FETCHER_PORT"
}

# 利用可能ポートの検索と設定
find_available_port

# Puppeteer Fetcherの起動
echo "Starting Puppeteer Fetcher on port $PUPPETEER_FETCHER_PORT..."
node fetcher/puppeteer_fetcher.js $PUPPETEER_FETCHER_PORT &
PUPPETEER_NODE_PID=$!

# 起動確認とヘルスチェック
verify_puppeteer_startup() {
    local MAX_ATTEMPTS=10
    local ATTEMPT=1

    echo "Puppeteer Fetcherの起動を確認中..."

    while [ $ATTEMPT -le $MAX_ATTEMPTS ]; do
        echo "起動確認試行 $ATTEMPT/$MAX_ATTEMPTS..."

        # プロセスが生きているかチェック
        if ! kill -0 $PUPPETEER_NODE_PID 2>/dev/null; then
            echo "エラー: Puppeteer Fetcherプロセスが終了しました"
            return 1
        fi

        # ポートがリッスンしているかチェック
        if lsof -i:$PUPPETEER_FETCHER_PORT -t &>/dev/null; then
            # ヘルスチェックエンドポイントをテスト
            if curl -s -f "http://localhost:$PUPPETEER_FETCHER_PORT/health" >/dev/null 2>&1; then
                echo "✅ Puppeteer Fetcherが正常に起動しました (ポート: $PUPPETEER_FETCHER_PORT)"
                return 0
            fi
        fi

        sleep 2
        ATTEMPT=$((ATTEMPT + 1))
    done

    echo "❌ Puppeteer Fetcherの起動に失敗しました"
    return 1
}

# 起動確認実行
if verify_puppeteer_startup; then
    echo "Puppeteer Fetcher PID: $PUPPETEER_NODE_PID"

    # 設定ファイルのpuppeteer-endpointを更新
    update_config_files() {
        echo "設定ファイルのpuppeteer-endpointを更新中..."
        cd ..

        # config.jsonの更新
        if [ -f "config.json" ]; then
            sed -i "s|\"puppeteer-endpoint\": \"http://localhost:[0-9]*\"|\"puppeteer-endpoint\": \"http://localhost:$PUPPETEER_FETCHER_PORT\"|g" config.json
            echo "config.jsonを更新しました"
        fi

        # redis_config.jsonの更新
        if [ -f "redis_config.json" ]; then
            sed -i "s|\"puppeteer-endpoint\": \"http://localhost:[0-9]*\"|\"puppeteer-endpoint\": \"http://localhost:$PUPPETEER_FETCHER_PORT\"|g" redis_config.json
            echo "redis_config.jsonを更新しました"
        fi

        cd pyspider
    }

    update_config_files

    echo "🎉 Puppeteer Fetcherが正常に起動し、設定ファイルが更新されました"
    echo "エンドポイント: http://localhost:$PUPPETEER_FETCHER_PORT"
else
    echo "❌ Puppeteer Fetcherの起動に失敗しました"
    exit 1
fi
