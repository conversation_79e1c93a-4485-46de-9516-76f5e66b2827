site_name: pyspider
site_description: A Powerful Spider(Web Crawler) System in Python.
site_author: binux
repo_url: https://github.com/binux/pyspider
pages:
- Introduction: index.md
- Quickstart: Quickstart.md
- Command Line: Command-Line.md
- Tutorial:
  - Index: tutorial/index.md
  - 'Level 1: HTML and CSS Selector': tutorial/HTML-and-CSS-Selector.md
  - 'Level 2: AJAX and More HTTP': tutorial/AJAX-and-more-HTTP.md
  - 'Level 3: Render with PhantomJS': tutorial/Render-with-PhantomJS.md
- About pyspider:
  - Architecture: Architecture.md
  - About Tasks: About-Tasks.md
  - About Projects: About-Projects.md
  - Script Environment: Script-Environment.md
  - Working with Results: Working-with-Results.md
- API Reference:
  - Index: apis/index.md
  - self.crawl: apis/self.crawl.md
  - Response: apis/Response.md
  - self.send_message: apis/self.send_message.md
  - '@catch_status_code_error': apis/@catch_status_code_error.md
  - '@every': apis/@every.md
- Deployment: Deployment.md
- Running pyspider with Docker: Running-pyspider-with-Docker.md
- Deployment of demo.pyspider.org: Deployment-demo.pyspider.org.md
- Frequently Asked Questions: Frequently-Asked-Questions.md

theme: readthedocs
markdown_extensions: ['toc(permalink=true)', ]
