# pyspiderNX2 インストールガイド

## 📦 pip install での簡単インストール

### 1. 基本インストール

```bash
pip install pyspiderNX2
```

### 2. webui-nextセットアップ（推奨）

webui-nextを使用する場合は、Node.js 22+が必要です。

#### Node.js のインストール

```bash
# Ubuntu/Debian
curl -fsSL https://deb.nodesource.com/setup_22.x | sudo -E bash -
sudo apt-get install -y nodejs

# macOS (Homebrew)
brew install node@22

# Windows
# https://nodejs.org/ からダウンロード
```

#### webui-nextセットアップ

```bash
# 統合npmセットアップ（推奨）- ルートディレクトリとwebui-nextの両方
pyspider-setup-npm

# webui-nextのみセットアップ
pyspider-setup-webui-next

# または手動セットアップ
python -m pyspider.tools.setup_webui_next
```

### 3. データベースセットアップ

#### MySQL（推奨）

```bash
# MySQL サーバーインストール
sudo apt-get install mysql-server

# データベースとユーザー作成
mysql -u root -p
CREATE DATABASE pyspider_projectdb;
CREATE DATABASE pyspider_taskdb;
CREATE DATABASE pyspider_resultdb;
CREATE DATABASE pyspider_scheduler;
CREATE USER 'pyspider'@'localhost' IDENTIFIED BY 'PySpider2024!SecurePass#';
GRANT ALL PRIVILEGES ON pyspider_*.* TO 'pyspider'@'localhost';
FLUSH PRIVILEGES;
```

#### Redis（メッセージキュー用）

```bash
# Redis インストール
sudo apt-get install redis-server

# Redis 起動
sudo systemctl start redis-server
sudo systemctl enable redis-server
```

## 🚀 起動方法

### 1. 基本起動（SQLite + Redis）

```bash
pyspider
```

### 2. MySQL + Redis モード

```bash
pyspider --config mysql_redis_config.json
```

### 3. ファイル出力機能付き起動

```bash
# ファイル出力機能を有効にして起動
pyspider --config file_output_test_config.json
```

### 4. webui-next起動

```bash
# webui-next開発サーバー起動
pyspider-webui-next

# または
python -m pyspider.tools.webui_next_launcher
```

## 📁 設定ファイル

インストール後、以下の設定ファイルが利用可能です：

- `config.json` - 基本設定（SQLite + ファイル出力）
- `mysql_redis_config.json` - MySQL + Redis + ファイル出力設定
- `redis_config.json` - Redis設定
- `postgresql_config.json` - PostgreSQL設定
- `file_output_test_config.json` - ファイル出力テスト設定

### ファイル出力機能

結果データをデータベースと同時にJSONL形式でファイルに保存：

```json
{
    "result_worker": {
        "type": "file_output",
        "file_output": {
            "enabled": true,
            "output_dir": "results",
            "max_file_size": 104857600,
            "enable_rotation": true,
            "rotation_count": 10
        }
    }
}
```

## 🔧 カスタマイズ

### 設定ファイルの場所

```python
import pyspider
import os
config_dir = os.path.dirname(pyspider.__file__)
print(f"設定ファイル場所: {config_dir}")
```

### webui-nextの場所

```python
import pyspider
from pathlib import Path
webui_next_path = Path(pyspider.__file__).parent / "webui-next"
print(f"webui-next場所: {webui_next_path}")
```

## 🐛 トラブルシューティング

### webui-nextが起動しない

```bash
# Node.js バージョン確認
node --version  # v22.x.x が必要

# npm バージョン確認
npm --version

# 手動セットアップ
cd $(python -c "import pyspider; from pathlib import Path; print(Path(pyspider.__file__).parent / 'webui-next')")
npm install
npm run dev
```

### データベース接続エラー

```bash
# MySQL接続確認
mysql -u pyspider -p -h localhost

# Redis接続確認
redis-cli ping
```

### ポート競合

デフォルトポート：
- pyspider WebUI: 5000
- webui-next: 3000
- Scheduler: 23333
- Fetcher: 24444

## 📚 詳細情報

- [README.md](README.md) - 基本的な使用方法
- [webui-next/README.md](pyspider/webui-next/README.md) - webui-next詳細
- [GitHub Repository](https://github.com/igtmtakan/pyspiderNX2-3.git)

## 🆘 サポート

問題が発生した場合：

1. [GitHub Issues](https://github.com/igtmtakan/pyspiderNX2-3/issues)
2. ログファイルの確認
3. 設定ファイルの確認
