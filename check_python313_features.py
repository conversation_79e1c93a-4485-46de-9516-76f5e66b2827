#!/usr/bin/env python3
# -*- encoding: utf-8 -*-
"""
Python 3.13 features checker for pyspiderNX2
"""

import sys
import os
import json
from pyspider.libs.python313_optimizations import optimizer

def check_python313_features():
    """Check Python 3.13 features availability and usage"""
    
    print("=" * 60)
    print("Python 3.13 Features Check for pyspiderNX2")
    print("=" * 60)
    
    # Basic Python version info
    print(f"Python Version: {sys.version}")
    print(f"Python Version Info: {sys.version_info}")
    
    # Get performance info from optimizer
    perf_info = optimizer.get_performance_info()
    
    print("\n" + "=" * 40)
    print("Python 3.13 Feature Status")
    print("=" * 40)
    
    # Python 3.13+ features
    print(f"✓ Python 3.13+: {'Yes' if perf_info['python_313_plus'] else 'No'}")
    
    # Free-threaded mode (GIL disabled)
    if perf_info['python_313_plus']:
        gil_status = "Disabled (Free-threaded)" if perf_info['free_threaded_mode'] else "Enabled (Traditional)"
        print(f"✓ GIL Status: {gil_status}")
        
        if perf_info['free_threaded_mode']:
            print("  → Enhanced multi-threading performance available")
        else:
            print("  → Traditional GIL-based threading")
    else:
        print("✗ GIL Status: N/A (Python < 3.13)")
    
    # JIT Compiler
    jit_status = "Available" if perf_info['jit_available'] else "Not Available"
    print(f"✓ JIT Compiler: {jit_status}")
    
    if perf_info['jit_available']:
        print("  → Experimental JIT compilation enabled")
    else:
        print("  → JIT compilation not available")
    
    # CPU and threading info
    print(f"✓ CPU Cores: {perf_info['cpu_count']}")
    
    # Optimal worker count
    from pyspider.libs.python313_optimizations import get_optimal_worker_count
    optimal_workers = get_optimal_worker_count()
    print(f"✓ Optimal Worker Count: {optimal_workers}")
    
    print("\n" + "=" * 40)
    print("Environment Variables")
    print("=" * 40)
    
    # Check environment variables
    python_gil = os.environ.get('PYTHON_GIL', 'Not Set')
    python_jit = os.environ.get('PYTHON_JIT', 'Not Set')
    
    print(f"✓ PYTHON_GIL: {python_gil}")
    if python_gil == '0':
        print("  → GIL disabled via environment variable")
    elif python_gil == 'Not Set':
        print("  → Using default GIL behavior")
    
    print(f"✓ PYTHON_JIT: {python_jit}")
    if python_jit == '1':
        print("  → JIT enabled via environment variable")
    elif python_jit == 'Not Set':
        print("  → Using default JIT behavior")
    
    print("\n" + "=" * 40)
    print("Memory Usage")
    print("=" * 40)
    
    # Memory usage
    from pyspider.libs.python313_optimizations import MemoryOptimizer
    memory_info = MemoryOptimizer.get_memory_usage()
    
    print(f"✓ RSS Memory: {memory_info['rss'] / 1024 / 1024:.2f} MB")
    print(f"✓ VMS Memory: {memory_info['vms'] / 1024 / 1024:.2f} MB")
    print(f"✓ Memory Percent: {memory_info['percent']:.2f}%")
    
    print("\n" + "=" * 40)
    print("Recommendations")
    print("=" * 40)
    
    # Recommendations
    if not perf_info['python_313_plus']:
        print("⚠️  Consider upgrading to Python 3.13 for:")
        print("   - Free-threaded mode (GIL-free execution)")
        print("   - Experimental JIT compiler")
        print("   - Enhanced error messages")
        print("   - Improved type system")
    
    if perf_info['python_313_plus'] and not perf_info['free_threaded_mode']:
        print("💡 To enable free-threaded mode:")
        print("   - Set PYTHON_GIL=0 environment variable")
        print("   - Use a free-threaded Python build")
        print("   - Restart pyspiderNX2 with the updated environment")
    
    if perf_info['python_313_plus'] and not perf_info['jit_available']:
        print("💡 To enable JIT compiler:")
        print("   - Set PYTHON_JIT=1 environment variable")
        print("   - Use a Python build with JIT support")
        print("   - Note: JIT is experimental in Python 3.13")
    
    if perf_info['free_threaded_mode']:
        print("🚀 Free-threaded mode is active!")
        print("   - Enhanced multi-threading performance")
        print("   - Better CPU utilization")
        print("   - Improved concurrent processing")
    
    print("\n" + "=" * 40)
    print("Performance Tips")
    print("=" * 40)
    
    print("🔧 For optimal performance:")
    print(f"   - Use {optimal_workers} worker threads")
    print("   - Enable free-threaded mode if available")
    print("   - Monitor memory usage regularly")
    print("   - Use async/await for I/O operations")
    
    if perf_info['cpu_count'] and perf_info['cpu_count'] >= 4:
        print("   - Your system has sufficient CPU cores for parallel processing")
    else:
        print("   - Consider using a system with more CPU cores")
    
    print("\n" + "=" * 60)
    print("Check completed successfully!")
    print("=" * 60)

def main():
    """Main function"""
    try:
        check_python313_features()
    except Exception as e:
        print(f"Error checking Python 3.13 features: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
