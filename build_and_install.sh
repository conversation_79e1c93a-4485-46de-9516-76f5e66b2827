#!/bin/bash

# 色の定義
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${YELLOW}PySpiderNX2 ビルド・インストールスクリプト${NC}"
echo "=================================="

# 必要なパッケージのインストール
echo -e "${YELLOW}必要なパッケージをインストールしています...${NC}"
pip install --upgrade pip build wheel twine

# 古いビルドディレクトリを削除
echo -e "${YELLOW}古いビルドディレクトリを削除しています...${NC}"
rm -rf build/ dist/ *.egg-info/

# パッケージをビルド
echo -e "${YELLOW}パッケージをビルドしています...${NC}"
python -m build

# ビルドが成功したか確認
if [ $? -ne 0 ]; then
    echo -e "${RED}ビルドに失敗しました。${NC}"
    exit 1
fi

echo -e "${GREEN}ビルドが完了しました。${NC}"

# インストールするか確認
read -p "パッケージをインストールしますか？ (y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo -e "${YELLOW}パッケージをインストールしています...${NC}"
    pip install dist/*.whl --force-reinstall
    
    if [ $? -ne 0 ]; then
        echo -e "${RED}インストールに失敗しました。${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}インストールが完了しました。${NC}"
    echo -e "${YELLOW}以下のコマンドでPySpiderNX2を起動できます:${NC}"
    echo "pyspidernx2"
    echo "pyspider"
else
    echo -e "${YELLOW}インストールをスキップしました。${NC}"
    echo -e "${YELLOW}以下のコマンドで手動でインストールできます:${NC}"
    echo "pip install dist/*.whl"
fi

echo "=================================="
