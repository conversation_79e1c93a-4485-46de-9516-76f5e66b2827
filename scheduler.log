{"timestamp": "2025-06-08T09:41:03.795888", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203}
[I 250608 09:41:04 memory_optimizer:57] Memory optimizer initialized with max_memory_percent=80.0%, gc_interval=60s, check_interval=30s, auto_optimize=True
[I 250608 09:41:04 redis_fallback:60] ✅ Redis接続成功: redis://localhost:6379/0
[I 250608 09:41:04 redis_fallback:105] 🔍 Redis接続監視を開始しました
[I 250608 09:41:04 redis_fallback:256] ✅ Redis フォールバック機能を初期化しました
[I 250608 09:41:04 scheduler:188] ✅ Redis フォールバック機能を初期化: redis://localhost:6379/0
[I 250608 09:41:04 memory_optimizer:69] Memory monitoring started
[I 250608 09:41:04 run:420] Memory optimizer started for scheduler
[I 250608 09:41:04 run:31] Created PID file for scheduler: /tmp/pyspider_scheduler.pid
[I 250608 09:41:04 scheduler:698] scheduler starting...
[I 250608 09:41:04 scheduler:704] ✅ Redis接続確立: redis://localhost:6379/0
[I 250608 09:41:04 scheduler:969] scheduler.xmlrpc listening on 0.0.0.0:23333
[I 250608 09:41:04 scheduler:125] project bunbunbun updated, status:RUNNING, paused:False, 0 tasks
[I 250608 09:41:04 scheduler:125] project douyo updated, status:RUNNING, paused:False, 0 tasks
[I 250608 09:41:04 scheduler:125] project complete_fix_test updated, status:TODO, paused:False, 0 tasks
[I 250608 09:41:04 scheduler:125] project fixed_mysql_project updated, status:TODO, paused:False, 0 tasks
[I 250608 09:41:04 scheduler:637] in 5m: new:0,success:0,retry:0,failed:0
[I 250608 09:41:04 scheduler:1155] select douyo:_on_get_info data:,_on_get_info
[I 250608 09:41:04 scheduler:1155] select bunbunbun:_on_get_info data:,_on_get_info
[I 250608 09:41:04 scheduler:125] project mysql_comprehensive_test updated, status:CHECKING, paused:False, 0 tasks
[I 250608 09:41:04 scheduler:125] project iuiu updated, status:RUNNING, paused:False, 0 tasks
[I 250608 09:41:04 scheduler:1155] select iuiu:_on_get_info data:,_on_get_info
[I 250608 09:41:04 scheduler:125] project mysql_detailed_test_2 updated, status:RUNNING, paused:False, 0 tasks
[I 250608 09:41:04 scheduler:125] project kokoko updated, status:RUNNING, paused:False, 0 tasks
[I 250608 09:41:04 scheduler:1155] select mysql_detailed_test_2:_on_get_info data:,_on_get_info
[I 250608 09:41:04 scheduler:1155] select kokoko:_on_get_info data:,_on_get_info
[I 250608 09:41:04 scheduler:125] project mysql_detailed_test updated, status:RUNNING, paused:False, 0 tasks
[I 250608 09:41:04 scheduler:1155] select mysql_detailed_test:_on_get_info data:,_on_get_info
[I 250608 09:41:04 scheduler:125] project jijijiji updated, status:RUNNING, paused:False, 0 tasks
[I 250608 09:41:04 scheduler:1155] select jijijiji:_on_get_info data:,_on_get_info
[I 250608 09:41:04 scheduler:125] project test_commit_fixed updated, status:TODO, paused:False, 0 tasks
[I 250608 09:41:04 scheduler:125] project mysql_test_project updated, status:TODO, paused:False, 0 tasks
[I 250608 09:41:04 scheduler:125] project tinti updated, status:RUNNING, paused:False, 0 tasks
[I 250608 09:41:04 scheduler:1155] select tinti:_on_get_info data:,_on_get_info
[I 250608 09:41:04 scheduler:125] project test_fixed_transaction updated, status:TODO, paused:False, 0 tasks
[I 250608 09:41:08 scheduler:383] douyo on_get_info {'min_tick': 0, 'retry_delay': {}, 'crawl_config': {'headers': {'User-Agent': 'GoogleBot'}, 'timeout': 3000, 'connect_timeout': 1000}}
[I 250608 09:41:08 scheduler:383] bunbunbun on_get_info {'min_tick': 86400, 'retry_delay': {}, 'crawl_config': {}}
[I 250608 09:41:08 scheduler:383] iuiu on_get_info {'min_tick': 0, 'retry_delay': {}, 'crawl_config': {'headers': {'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'}, 'timeout': 30, 'connect_timeout': 10}}
[I 250608 09:41:08 scheduler:383] mysql_detailed_test_2 on_get_info {'min_tick': 0, 'retry_delay': {}, 'crawl_config': {}}
[I 250608 09:41:08 scheduler:383] kokoko on_get_info {'min_tick': 0, 'retry_delay': {}, 'crawl_config': {'headers': {'User-Agent': 'GoogleBot'}, 'timeout': 3000, 'connect_timeout': 1000}}
[I 250608 09:41:08 scheduler:383] mysql_detailed_test on_get_info {'min_tick': 0, 'retry_delay': {}, 'crawl_config': {}}
[I 250608 09:41:08 scheduler:383] jijijiji on_get_info {'min_tick': 0, 'retry_delay': {}, 'crawl_config': {'headers': {'User-Agent': 'GoogleBot'}, 'timeout': 3000, 'connect_timeout': 1000}}
[I 250608 09:41:08 scheduler:383] tinti on_get_info {'min_tick': 0, 'retry_delay': {}, 'crawl_config': {'headers': {'User-Agent': 'GoogleBot'}, 'timeout': 3000, 'connect_timeout': 1000}}
[I 250608 09:42:04 metrics:173] Gauges: {"memory_usage_rss": 88715264, "memory_usage_vms": 636190720, "memory_usage_percent": 0.2642170202653808, "system_memory_total": 33576665088, "system_memory_available": 27589476352, "system_memory_percent": 17.8}
[I 250608 09:42:04 scheduler:637] in 5m: new:0,success:0,retry:0,failed:0
[I 250608 09:43:04 metrics:173] Gauges: {"memory_usage_rss": 88715264, "memory_usage_vms": 636190720, "memory_usage_percent": 0.2642170202653808, "system_memory_total": 33576665088, "system_memory_available": 27513737216, "system_memory_percent": 18.1}
[I 250608 09:43:04 scheduler:637] in 5m: new:0,success:0,retry:0,failed:0
[I 250608 09:44:04 metrics:173] Gauges: {"memory_usage_rss": 88715264, "memory_usage_vms": 636190720, "memory_usage_percent": 0.2642170202653808, "system_memory_total": 33576665088, "system_memory_available": 27186425856, "system_memory_percent": 19.0}
[I 250608 09:44:04 scheduler:637] in 5m: new:0,success:0,retry:0,failed:0
[I 250608 09:45:04 metrics:173] Gauges: {"memory_usage_rss": 88715264, "memory_usage_vms": 636190720, "memory_usage_percent": 0.2642170202653808, "system_memory_total": 33576665088, "system_memory_available": 26927431680, "system_memory_percent": 19.8}
[I 250608 09:45:04 scheduler:637] in 5m: new:0,success:0,retry:0,failed:0
[I 250608 09:46:04 metrics:173] Gauges: {"memory_usage_rss": 88715264, "memory_usage_vms": 636190720, "memory_usage_percent": 0.2642170202653808, "system_memory_total": 33576665088, "system_memory_available": 26998677504, "system_memory_percent": 19.6}
[I 250608 09:46:04 scheduler:637] in 5m: new:0,success:0,retry:0,failed:0
[I 250608 09:47:04 metrics:173] Gauges: {"memory_usage_rss": 88715264, "memory_usage_vms": 636190720, "memory_usage_percent": 0.2642170202653808, "system_memory_total": 33576665088, "system_memory_available": 27149279232, "system_memory_percent": 19.1}
[I 250608 09:47:04 scheduler:637] in 5m: new:0,success:0,retry:0,failed:0
[I 250608 09:48:04 metrics:173] Gauges: {"memory_usage_rss": 88715264, "memory_usage_vms": 636190720, "memory_usage_percent": 0.2642170202653808, "system_memory_total": 33576665088, "system_memory_available": 26836697088, "system_memory_percent": 20.1}
[I 250608 09:48:04 scheduler:637] in 5m: new:0,success:0,retry:0,failed:0
[I 250608 09:49:04 metrics:173] Gauges: {"memory_usage_rss": 88715264, "memory_usage_vms": 636190720, "memory_usage_percent": 0.2642170202653808, "system_memory_total": 33576665088, "system_memory_available": 26923331584, "system_memory_percent": 19.8}
[I 250608 09:49:05 scheduler:637] in 5m: new:0,success:0,retry:0,failed:0
[I 250608 09:50:04 metrics:173] Gauges: {"memory_usage_rss": 88715264, "memory_usage_vms": 636190720, "memory_usage_percent": 0.2642170202653808, "system_memory_total": 33576665088, "system_memory_available": 26876325888, "system_memory_percent": 20.0}
[I 250608 09:50:05 scheduler:637] in 5m: new:0,success:0,retry:0,failed:0
[I 250608 09:51:04 metrics:173] Gauges: {"memory_usage_rss": 88715264, "memory_usage_vms": 636190720, "memory_usage_percent": 0.2642170202653808, "system_memory_total": 33576665088, "system_memory_available": 26778230784, "system_memory_percent": 20.2}
[I 250608 09:51:05 scheduler:637] in 5m: new:0,success:0,retry:0,failed:0
[I 250608 09:52:04 metrics:173] Gauges: {"memory_usage_rss": 88715264, "memory_usage_vms": 636190720, "memory_usage_percent": 0.2642170202653808, "system_memory_total": 33576665088, "system_memory_available": 28067651584, "system_memory_percent": 16.4}
[I 250608 09:52:05 scheduler:637] in 5m: new:0,success:0,retry:0,failed:0
[I 250608 09:53:04 metrics:173] Gauges: {"memory_usage_rss": 88715264, "memory_usage_vms": 636190720, "memory_usage_percent": 0.2642170202653808, "system_memory_total": 33576665088, "system_memory_available": 27264684032, "system_memory_percent": 18.8}
[I 250608 09:53:05 scheduler:637] in 5m: new:0,success:0,retry:0,failed:0
[I 250608 09:54:04 metrics:173] Gauges: {"memory_usage_rss": 88715264, "memory_usage_vms": 636190720, "memory_usage_percent": 0.2642170202653808, "system_memory_total": 33576665088, "system_memory_available": 27388051456, "system_memory_percent": 18.4}
[I 250608 09:54:05 scheduler:637] in 5m: new:0,success:0,retry:0,failed:0
[I 250608 09:55:04 metrics:173] Gauges: {"memory_usage_rss": 88715264, "memory_usage_vms": 636190720, "memory_usage_percent": 0.2642170202653808, "system_memory_total": 33576665088, "system_memory_available": 27138420736, "system_memory_percent": 19.2}
[I 250608 09:55:05 scheduler:637] in 5m: new:0,success:0,retry:0,failed:0
[I 250608 09:56:04 metrics:173] Gauges: {"memory_usage_rss": 88715264, "memory_usage_vms": 636190720, "memory_usage_percent": 0.2642170202653808, "system_memory_total": 33576665088, "system_memory_available": 27012681728, "system_memory_percent": 19.5}
[I 250608 09:56:05 scheduler:637] in 5m: new:0,success:0,retry:0,failed:0
[I 250608 09:57:04 metrics:173] Gauges: {"memory_usage_rss": 88715264, "memory_usage_vms": 636190720, "memory_usage_percent": 0.2642170202653808, "system_memory_total": 33576665088, "system_memory_available": 26955694080, "system_memory_percent": 19.7}
[I 250608 09:57:05 scheduler:637] in 5m: new:0,success:0,retry:0,failed:0
[I 250608 09:58:04 metrics:173] Gauges: {"memory_usage_rss": 88715264, "memory_usage_vms": 636190720, "memory_usage_percent": 0.2642170202653808, "system_memory_total": 33576665088, "system_memory_available": 27125391360, "system_memory_percent": 19.2}
[I 250608 09:58:05 scheduler:637] in 5m: new:0,success:0,retry:0,failed:0
[I 250608 09:59:04 metrics:173] Gauges: {"memory_usage_rss": 88715264, "memory_usage_vms": 636190720, "memory_usage_percent": 0.2642170202653808, "system_memory_total": 33576665088, "system_memory_available": 26490728448, "system_memory_percent": 21.1}
[I 250608 09:59:05 scheduler:637] in 5m: new:0,success:0,retry:0,failed:0
[I 250608 10:00:04 metrics:173] Gauges: {"memory_usage_rss": 88715264, "memory_usage_vms": 636190720, "memory_usage_percent": 0.2642170202653808, "system_memory_total": 33576665088, "system_memory_available": 26991554560, "system_memory_percent": 19.6}
[I 250608 10:00:05 scheduler:637] in 5m: new:0,success:0,retry:0,failed:0
[E 250608 10:00:35 scheduler:330] unknown project: test_project
[I 250608 10:01:04 metrics:173] Gauges: {"memory_usage_rss": 88715264, "memory_usage_vms": 636190720, "memory_usage_percent": 0.2642170202653808, "system_memory_total": 33576665088, "system_memory_available": 27216326656, "system_memory_percent": 18.9}
[I 250608 10:01:05 scheduler:637] in 5m: new:0,success:0,retry:0,failed:0
[I 250608 10:02:04 metrics:173] Gauges: {"memory_usage_rss": 88715264, "memory_usage_vms": 636190720, "memory_usage_percent": 0.2642170202653808, "system_memory_total": 33576665088, "system_memory_available": 27241521152, "system_memory_percent": 18.9}
[I 250608 10:02:05 scheduler:637] in 5m: new:0,success:0,retry:0,failed:0
[I 250608 10:03:04 metrics:173] Gauges: {"memory_usage_rss": 88715264, "memory_usage_vms": 636190720, "memory_usage_percent": 0.2642170202653808, "system_memory_total": 33576665088, "system_memory_available": 26767904768, "system_memory_percent": 20.3}
[I 250608 10:03:05 scheduler:637] in 5m: new:0,success:0,retry:0,failed:0
[I 250608 10:04:04 metrics:173] Gauges: {"memory_usage_rss": 88715264, "memory_usage_vms": 636190720, "memory_usage_percent": 0.2642170202653808, "system_memory_total": 33576665088, "system_memory_available": 26830852096, "system_memory_percent": 20.1}
[I 250608 10:04:05 scheduler:637] in 5m: new:0,success:0,retry:0,failed:0
[I 250608 10:05:04 metrics:173] Gauges: {"memory_usage_rss": 88715264, "memory_usage_vms": 636190720, "memory_usage_percent": 0.2642170202653808, "system_memory_total": 33576665088, "system_memory_available": 26705797120, "system_memory_percent": 20.5}
[I 250608 10:05:06 scheduler:637] in 5m: new:0,success:0,retry:0,failed:0
[I 250608 10:06:04 metrics:173] Gauges: {"memory_usage_rss": 88715264, "memory_usage_vms": 636190720, "memory_usage_percent": 0.2642170202653808, "system_memory_total": 33576665088, "system_memory_available": 26996748288, "system_memory_percent": 19.6}
[I 250608 10:06:06 scheduler:637] in 5m: new:0,success:0,retry:0,failed:0
