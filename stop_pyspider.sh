#!/bin/bash

# OpenSSL設定を環境変数に設定
export OPENSSL_CONF=/dev/null

# 色の定義
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# ログファイル
LOG_FILE="pyspider_stop.log"

# ログ関数
log_message() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

echo -e "${BLUE}PySpider完全停止スクリプト${NC}"
echo "=================================="
log_message "PySpider停止プロセス開始"

# 完全なPySpiderプロセスクリーンアップ機能
cleanup_pyspider_processes() {
    echo -e "${YELLOW}PySpiderプロセスの完全クリーンアップを実行中...${NC}"
    log_message "プロセスクリーンアップ開始"

    # 1. PySpiderメインプロセスの終了
    echo -e "${YELLOW}PySpiderメインプロセスを終了中...${NC}"
    PYSPIDER_PIDS=$(ps aux | grep -E "python.*run.py|python.*pyspider.run" | grep -v grep | awk '{print $2}')
    if [ ! -z "$PYSPIDER_PIDS" ]; then
        log_message "PySpiderプロセスを終了: $PYSPIDER_PIDS"
        kill -TERM $PYSPIDER_PIDS 2>/dev/null
        sleep 3
        # 強制終了が必要な場合
        REMAINING_PIDS=$(ps aux | grep -E "python.*run.py|python.*pyspider.run" | grep -v grep | awk '{print $2}')
        if [ ! -z "$REMAINING_PIDS" ]; then
            log_message "PySpiderプロセス強制終了: $REMAINING_PIDS"
            kill -9 $REMAINING_PIDS 2>/dev/null
        fi
        echo -e "${GREEN}PySpiderメインプロセスを終了しました${NC}"
    else
        echo -e "${GREEN}PySpiderメインプロセスは実行されていません${NC}"
    fi

    # 2. Puppeteer Fetcherプロセスの終了
    echo -e "${YELLOW}Puppeteer Fetcherプロセスを終了中...${NC}"
    PUPPETEER_PIDS=$(ps aux | grep "node.*puppeteer_fetcher.js" | grep -v grep | awk '{print $2}')
    if [ ! -z "$PUPPETEER_PIDS" ]; then
        log_message "Puppeteer Fetcherプロセスを終了: $PUPPETEER_PIDS"
        kill -TERM $PUPPETEER_PIDS 2>/dev/null
        sleep 2
        # 強制終了が必要な場合
        REMAINING_PIDS=$(ps aux | grep "node.*puppeteer_fetcher.js" | grep -v grep | awk '{print $2}')
        if [ ! -z "$REMAINING_PIDS" ]; then
            log_message "Puppeteer Fetcherプロセス強制終了: $REMAINING_PIDS"
            kill -9 $REMAINING_PIDS 2>/dev/null
        fi
        echo -e "${GREEN}Puppeteer Fetcherプロセスを終了しました${NC}"
    else
        echo -e "${GREEN}Puppeteer Fetcherプロセスは実行されていません${NC}"
    fi

    # 3. 孤立したChromiumプロセスの終了
    echo -e "${YELLOW}孤立したChromiumプロセスを終了中...${NC}"
    CHROME_PIDS=$(ps aux | grep -E "(chrome|chromium).*--no-sandbox" | grep -v grep | awk '{print $2}')
    if [ ! -z "$CHROME_PIDS" ]; then
        log_message "孤立したChromiumプロセスを終了: $CHROME_PIDS"
        kill -TERM $CHROME_PIDS 2>/dev/null
        sleep 1
        # 強制終了が必要な場合
        REMAINING_PIDS=$(ps aux | grep -E "(chrome|chromium).*--no-sandbox" | grep -v grep | awk '{print $2}')
        if [ ! -z "$REMAINING_PIDS" ]; then
            log_message "Chromiumプロセス強制終了: $REMAINING_PIDS"
            kill -9 $REMAINING_PIDS 2>/dev/null
        fi
        echo -e "${GREEN}孤立したChromiumプロセスを終了しました${NC}"
    else
        echo -e "${GREEN}孤立したChromiumプロセスはありません${NC}"
    fi
}

# プロセスクリーンアップ実行
cleanup_pyspider_processes

# 改良されたポート管理機能
cleanup_ports() {
    echo -e "${YELLOW}PySpider関連ポートのクリーンアップを実行中...${NC}"
    log_message "ポートクリーンアップ開始"

    # PySpider関連ポートの定義
    local PYSPIDER_PORTS=(
        5000    # WebUI
        3000    # WebUI-Next (Next.js)
        23333   # Scheduler
        24444   # Fetcher
        22223   # Puppeteer Fetcher (primary)
        22224   # Puppeteer Fetcher (fallback)
        22225   # Puppeteer Fetcher (fallback)
        22226   # Puppeteer Fetcher (fallback)
        22227   # Puppeteer Fetcher (fallback)
    )

    for PORT in "${PYSPIDER_PORTS[@]}"; do
        echo -e "${YELLOW}ポート$PORTを確認中...${NC}"
        PIDS=$(lsof -i:$PORT -t 2>/dev/null)
        if [ ! -z "$PIDS" ]; then
            log_message "ポート$PORTを使用中のプロセスを終了: $PIDS"
            echo -e "${YELLOW}ポート$PORTを使用中のプロセスを終了: $PIDS${NC}"

            # 段階的終了（TERM → KILL）
            kill -TERM $PIDS 2>/dev/null
            sleep 1

            # 強制終了が必要な場合
            REMAINING_PIDS=$(lsof -i:$PORT -t 2>/dev/null)
            if [ ! -z "$REMAINING_PIDS" ]; then
                log_message "ポート$PORT強制終了: $REMAINING_PIDS"
                kill -9 $REMAINING_PIDS 2>/dev/null
            fi

            echo -e "${GREEN}ポート$PORTのプロセスを終了しました${NC}"
        else
            echo -e "${GREEN}ポート$PORTは使用されていません${NC}"
        fi
    done

    log_message "ポートクリーンアップ完了"
}

# ポートクリーンアップ実行
cleanup_ports

# 少し待機してポートが解放されるのを確認
echo -e "${YELLOW}ポートが解放されるのを待機中...${NC}"
sleep 2

# 最終確認と残存プロセスのクリーンアップ
final_cleanup_verification() {
    echo -e "${YELLOW}最終確認とクリーンアップを実行中...${NC}"
    log_message "最終確認開始"

    local SCRIPT_PID=$$
    local CLEANUP_ATTEMPTS=0
    local MAX_ATTEMPTS=3

    while [ $CLEANUP_ATTEMPTS -lt $MAX_ATTEMPTS ]; do
        CLEANUP_ATTEMPTS=$((CLEANUP_ATTEMPTS + 1))
        echo -e "${YELLOW}確認試行 $CLEANUP_ATTEMPTS/$MAX_ATTEMPTS...${NC}"

        # PySpider関連プロセスの確認（WebUI-Nextも含む）
        REMAINING_PIDS=$(ps aux | grep -E "pyspider|puppeteer_fetcher|node.*puppeteer|chrome.*--no-sandbox|npm.*dev|next.*dev" | grep -v grep | grep -v $SCRIPT_PID | awk '{print $2}')

        if [ -z "$REMAINING_PIDS" ]; then
            echo -e "${GREEN}✅ すべてのプロセスが正常に終了しました${NC}"
            log_message "すべてのプロセスが正常に終了"
            break
        else
            echo -e "${YELLOW}残存プロセスを発見: $REMAINING_PIDS${NC}"
            log_message "残存プロセス発見 (試行$CLEANUP_ATTEMPTS): $REMAINING_PIDS"

            # 残存プロセスを終了
            for PID in $REMAINING_PIDS; do
                if [ "$PID" != "$SCRIPT_PID" ] && [ "$PID" != "$$" ]; then
                    echo -e "${YELLOW}残存プロセス終了: PID $PID${NC}"
                    kill -9 $PID 2>/dev/null
                fi
            done

            sleep 2
        fi
    done

    # 最終状態の確認（WebUI-Nextも含む）
    FINAL_PIDS=$(ps aux | grep -E "pyspider|puppeteer_fetcher|node.*puppeteer|chrome.*--no-sandbox|npm.*dev|next.*dev" | grep -v grep | grep -v $SCRIPT_PID)
    if [ ! -z "$FINAL_PIDS" ]; then
        echo -e "${RED}⚠️  一部のプロセスが残っています:${NC}"
        echo "$FINAL_PIDS"
        log_message "警告: 一部のプロセスが残存"
        echo -e "${YELLOW}手動で確認してください: ps aux | grep -E 'pyspider|puppeteer'${NC}"
    else
        echo -e "${GREEN}🎉 すべてのプロセスが完全に終了しました${NC}"
        log_message "完全クリーンアップ成功"
    fi
}

# 最終確認実行
final_cleanup_verification

# ポート使用状況の最終確認
echo -e "${YELLOW}ポート使用状況の最終確認...${NC}"
USED_PORTS=""
for PORT in 5000 3000 23333 24444 22223 22224 22225 22226 22227; do
    if lsof -i:$PORT -t &>/dev/null; then
        USED_PORTS="$USED_PORTS $PORT"
    fi
done

if [ -z "$USED_PORTS" ]; then
    echo -e "${GREEN}✅ すべてのPySpider関連ポートが解放されました${NC}"
    log_message "すべてのポートが解放"
else
    echo -e "${YELLOW}⚠️  以下のポートがまだ使用中です:$USED_PORTS${NC}"
    log_message "警告: 使用中ポート:$USED_PORTS"
fi

echo "=================================="
echo -e "${BLUE}PySpider完全停止スクリプト完了${NC}"
echo -e "${GREEN}✅ すべてのPySpiderプロセスを停止しました${NC}"
echo -e "${GREEN}📝 ログファイル: $LOG_FILE${NC}"
echo -e "${GREEN}🚀 PySpiderを再起動するには以下を実行してください:${NC}"
echo -e "${BLUE}   • SQLite + Redisモード: ./start_sqlite_redis_mode.sh${NC}"
echo -e "${BLUE}   • MySQL + Redisモード: ./start_mysql_redis_mode.sh${NC}"
echo -e "${BLUE}   • PostgreSQL + Redisモード: ./start_postgresql_redis_mode.sh${NC}"
log_message "PySpider停止プロセス完了"
