#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sqlite3
import json
import time
import os

# プロジェクト作成用のスクリプト
def create_project_in_db(name, group="default", status="RUNNING"):
    # プロジェクトのスクリプトテンプレート
    script = """
#!/usr/bin/env python
# -*- coding: utf-8 -*-
import re
from pyspider.libs.base_handler import *

class Handler(BaseHandler):
    crawl_config = {
    }

    @every(minutes=24 * 60)
    def on_start(self):
        self.crawl('https://example.com/', callback=self.index_page)

    @config(age=10 * 24 * 60 * 60)
    def index_page(self, response):
        for each in response.doc('a[href^="http"]').items():
            self.crawl(each.attr.href, callback=self.detail_page)

    @config(priority=2)
    def detail_page(self, response):
        return {
            "url": response.url,
            "title": response.doc('title').text(),
        }
"""

    # プロジェクトデータの作成
    project_data = {
        "name": name,
        "group": group,
        "status": status,
        "script": script,
        "rate": 1,
        "burst": 10,
        "updatetime": time.time()
    }

    # データベースファイルのパスを設定
    db_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "pyspider", "data", "project.db")

    # データベースディレクトリが存在するか確認
    db_dir = os.path.dirname(db_path)
    if not os.path.exists(db_dir):
        os.makedirs(db_dir)

    print(f"Database path: {db_path}")

    # データベースに接続
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # テーブルが存在するか確認
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='projectdb'")
        if not cursor.fetchone():
            # テーブルが存在しない場合は作成
            cursor.execute('''
            CREATE TABLE projectdb (
                name TEXT PRIMARY KEY,
                group_name TEXT,
                status TEXT,
                script TEXT,
                comments TEXT,
                rate REAL,
                burst REAL,
                updatetime REAL
            )
            ''')
            print("Created projectdb table")

        # プロジェクトが既に存在するか確認
        cursor.execute("SELECT name FROM projectdb WHERE name=?", (name,))
        if cursor.fetchone():
            # 既存のプロジェクトを更新
            cursor.execute(
                "UPDATE projectdb SET \"group\"=?, status=?, script=?, rate=?, burst=?, updatetime=? WHERE name=?",
                (group, status, script, project_data["rate"], project_data["burst"], project_data["updatetime"], name)
            )
            print(f"Updated project: {name}")
        else:
            # 新しいプロジェクトを作成
            cursor.execute(
                "INSERT INTO projectdb (name, \"group\", status, script, rate, burst, updatetime) VALUES (?, ?, ?, ?, ?, ?, ?)",
                (name, group, status, script, project_data["rate"], project_data["burst"], project_data["updatetime"])
            )
            print(f"Created project: {name}")

        # 変更をコミット
        conn.commit()

        return True
    except Exception as e:
        print(f"Error creating project in database: {e}")
        return False
    finally:
        if conn:
            conn.close()

# サンプルプロジェクトの作成
if __name__ == "__main__":
    projects = [
        {"name": "example_project", "group": "examples", "status": "RUNNING"},
        {"name": "news_crawler", "group": "crawlers", "status": "PAUSED"},
        {"name": "product_scraper", "group": "scrapers", "status": "RUNNING"}
    ]

    for project in projects:
        print(f"Creating project: {project['name']}")
        create_project_in_db(project['name'], project['group'], project['status'])
        print("---")
