/** @type {import('next').NextConfig} */
const path = require('path');

const nextConfig = {
  eslint: {
    // ESLint チェックをスキップする
    ignoreDuringBuilds: true,
  },
  typescript: {
    // TypeScript チェックをスキップする
    ignoreBuildErrors: true,
  },
  webpack: (config) => {
    config.resolve.alias['@'] = path.resolve(__dirname, 'src');
    return config;
  },
  async rewrites() {
    return [
      {
        source: '/api/:path*',
        destination: process.env.NODE_ENV === 'production'
          ? '/api/:path*'
          : 'http://localhost:5000/api/:path*',
      },
      {
        source: '/metrics',
        destination: process.env.NODE_ENV === 'production'
          ? '/metrics'
          : 'http://localhost:5000/metrics',
      },
      {
        source: '/counter',
        destination: process.env.NODE_ENV === 'production'
          ? '/counter'
          : 'http://localhost:5000/counter',
      },
      {
        source: '/dashboard-active-tasks',
        destination: process.env.NODE_ENV === 'production'
          ? '/dashboard-active-tasks'
          : 'http://localhost:5000/dashboard-active-tasks',
      },
      {
        source: '/index-v2/projects',
        destination: process.env.NODE_ENV === 'production'
          ? '/index-v2/projects'
          : 'http://localhost:5000/index-v2/projects',
      },
    ]
  },
};

module.exports = nextConfig;
