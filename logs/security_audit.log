{"timestamp": "2025-05-24T13:55:51.499904", "level": "ERROR", "message": "load project unauthorized_test error", "module": "project_module", "extra": {}}
{"timestamp": "2025-05-24T13:55:51.506297", "level": "ERROR", "message": "process unauthorized_test:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "extra": {}}
{"timestamp": "2025-05-24T18:54:35.029358", "level": "ERROR", "message": "load project unauthorized_test error", "module": "project_module", "extra": {}}
{"timestamp": "2025-05-24T18:54:35.033029", "level": "ERROR", "message": "process unauthorized_test:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need <PERSON><PERSON><PERSON><PERSON> in project module')", "module": "processor", "extra": {}}
{"timestamp": "2025-05-24T19:04:22.895022", "level": "ERROR", "message": "load project unauthorized_test error", "module": "project_module", "extra": {}}
{"timestamp": "2025-05-24T19:04:22.898748", "level": "ERROR", "message": "process unauthorized_test:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "extra": {}}
{"timestamp": "2025-05-24T19:15:12.745680", "level": "ERROR", "message": "load project unauthorized_test error", "module": "project_module", "extra": {}}
{"timestamp": "2025-05-24T19:15:12.749957", "level": "ERROR", "message": "process unauthorized_test:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "extra": {}}
