2025-06-05 06:27:19 [INFO] pyspider.libs.redis_fallback: ✅ Redis接続成功: redis://localhost:6379/0
2025-06-05 06:27:19 [INFO] pyspider.libs.redis_fallback: 🔍 Redis接続監視を開始しました
2025-06-05 06:27:19 [INFO] pyspider.libs.redis_fallback: ✅ Redis フォールバック機能を初期化しました
2025-06-05 06:27:19 [INFO] memory_optimizer: Memory optimizer initialized with max_memory_percent=80.0%, gc_interval=60s, check_interval=30s, auto_optimize=True
2025-06-05 06:27:19 [INFO] memory_optimizer: Memory monitoring started
2025-06-05 06:27:19 [INFO] root: Memory optimizer started for scheduler
2025-06-05 06:27:19 [INFO] root: Created PID file for scheduler: /tmp/pyspider_scheduler.pid
{"timestamp": "2025-06-05T06:27:21.639402", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-05 06:27:21 [INFO] root: Created PID file for fetcher: /tmp/pyspider_fetcher.pid
{"timestamp": "2025-06-05T06:27:23.676330", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-05 06:27:24 [INFO] root: Created PID file for processor: /tmp/pyspider_processor.pid
{"timestamp": "2025-06-05T06:27:25.961121", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-05 06:27:26 [INFO] root: Created PID file for result_worker: /tmp/pyspider_result_worker.pid
2025-06-05 06:27:26 [INFO] result: result_worker starting...
{"timestamp": "2025-06-05T06:27:28.232506", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-05 06:27:29 [INFO] libs.cache: Using Redis cache
2025-06-05 06:27:29 [INFO] root: Created PID file for webui: /tmp/pyspider_webui.pid
2025-06-05 06:27:31 [INFO] result: result amazon:cbba98565acf8a7107f627d33d38102e https://www.amazon.co.jp/EVANE-%E3%82%AA%E3%83%BC%E3%83%90%E3%83%BC%E3%82%B5%E3%82%A4%E3%82%BA-%E3%82%B3%E3%83%83%E3%83%88%E3%83%B3-%E3%82%B9%E3%83%88%E3%83%AC%E3%83%83%E3%83%81-T%E3%82%B7%E3%83%A3%E3%83%84/dp/B0CTMR98CV/ref=zg_bs_g_15218661_d_sccl_17/000-0000000-0000000?psc=1 -> {'url': 'https://www.amazon.co
2025-06-05 06:27:36 [INFO] result: result amazon:6bdda6256a49ccd55d95ae279a072ced https://www.amazon.co.jp/%E3%83%AC%E3%83%87%E3%82%A3%E3%83%BC%E3%82%B9-%E3%83%A8%E3%82%AC%E3%82%A6%E3%82%A7%E3%82%A2-%E3%82%B9%E3%83%88%E3%83%AC%E3%83%83%E3%83%81-%E3%83%95%E3%82%A3%E3%83%83%E3%83%88%E3%83%8D%E3%82%B9-%E8%96%84%E6%89%8B-%E3%83%96%E3%83%A9%E3%83%83%E3%82%AF/dp/B0CB5YL5F8/ref=zg_bs_g_15218661_d_sccl_5/000-0000000-0000000?psc=1 -> {'url': 'https://www.amazon.co
2025-06-05 06:27:39 [INFO] result: result amazon:f36d74642483c740b6027d8e0f8f8f8c https://www.amazon.co.jp/%E3%83%9F%E3%82%BA%E3%83%8E-%E3%83%88%E3%83%AC%E3%83%BC%E3%83%8B%E3%83%B3%E3%82%B0%E3%82%A6%E3%82%A7%E3%82%A2-%E3%83%8A%E3%83%93%E3%83%89%E3%83%A9%E3%82%A4-32MAC390-%E3%82%B9%E3%83%97%E3%83%A9%E3%83%83%E3%82%B7%E3%83%A5%E3%83%96%E3%83%AB%E3%83%BC/dp/B0D6GG6DL5/ref=zg_bs_g_15218661_d_sccl_6/000-0000000-0000000?psc=1 -> {'url': 'https://www.amazon.co
2025-06-05 06:27:42 [INFO] result: result amazon:5ae7425bcc40c3fe4d68e54f384ca893 https://www.amazon.co.jp/%E3%83%9F%E3%82%BA%E3%83%8E-%E3%83%88%E3%83%AC%E3%83%BC%E3%83%8B%E3%83%B3%E3%82%B0%E3%82%A6%E3%82%A7%E3%82%A2-Simple-%E3%82%AF%E3%82%A4%E3%83%83%E3%82%AF%E3%83%89%E3%83%A9%E3%82%A4%E5%8D%8A%E8%A2%96T%E3%82%B7%E3%83%A3%E3%83%84-K2JACM10/dp/B0DGLHN4QV/ref=zg_bs_g_15218661_d_sccl_7/000-0000000-0000000?psc=1 -> {'url': 'https://www.amazon.co
2025-06-05 06:27:44 [INFO] result: result amazon:20b36c3d619029306c0081eefabd3654 https://www.amazon.co.jp/%E3%83%A8%E3%83%8D%E3%83%83%E3%82%AF%E3%82%B9-%E5%8D%8A%E8%A2%96%E3%82%B7%E3%83%A3%E3%83%84-%E3%83%89%E3%83%A9%E3%82%A4T%E3%82%B7%E3%83%A3%E3%83%84-%E3%83%86%E3%82%A3%E3%83%BC%E3%83%AB%E3%83%96%E3%83%AB%E3%83%BC-817/dp/B0CBR9JP2R/ref=zg_bs_g_15218661_d_sccl_11/000-0000000-0000000?psc=1 -> {'url': 'https://www.amazon.co
2025-06-05 06:27:47 [INFO] result: result amazon:444860d3e00c2cb271f50423d9da33f1 https://www.amazon.co.jp/%E3%82%B6%E3%83%8E%E3%83%BC%E3%82%B9%E3%83%95%E3%82%A7%E3%82%A4%E3%82%B9-%E3%82%AB%E3%83%83%E3%83%88%E3%82%BD%E3%83%BC-T%E3%82%B7%E3%83%A3%E3%83%84-Globe-Cotton/dp/B0D6MMR7B3/ref=zg_bs_g_15218661_d_sccl_15/000-0000000-0000000?psc=1 -> {'url': 'https://www.amazon.co
2025-06-05 06:27:56 [INFO] result: result amazon:2e1ab2fbda7e54e7dfe2ee9b4751b05f https://www.amazon.co.jp/%E3%83%9F%E3%82%BA%E3%83%8E-%E3%83%88%E3%83%AC%E3%83%BC%E3%83%8B%E3%83%B3%E3%82%B0%E3%82%A6%E3%82%A7%E3%82%A2-%E3%82%AF%E3%82%A4%E3%83%83%E3%82%AF%E3%83%89%E3%83%A9%E3%82%A4%E9%95%B7%E8%A2%96T%E3%82%B7%E3%83%A3%E3%83%84-K2JACM13-%E3%83%87%E3%82%A3%E3%83%BC%E3%83%97%E3%83%8D%E3%82%A4%E3%83%93%E3%83%BC/dp/B0DGLJGZGN/ref=zg_bs_g_15218661_d_sccl_24/000-0000000-0000000?psc=1 -> {'url': 'https://www.amazon.co
2025-06-05 06:28:19 [INFO] metrics: Gauges: {"memory_usage_rss": 70373376, "memory_usage_vms": 382754816, "memory_usage_percent": 0.20880934634391507, "system_memory_total": ***********, "system_memory_available": ***********, "system_memory_percent": 16.0}
2025-06-05 06:28:30 [INFO] metrics: Counters: {"fetch_count[project=yahoo]": 1, "fetch_count[project=mehoo]": 1, "fetch_count[project=roastmeat]": 1, "fetch_count[project=nohoo]": 1, "fetch_count[project=yahooit]": 1, "fetch_count[project=business]": 1, "fetch_count[project=lplplp]": 1, "fetch_count[project=local]": 1, "fetch_count[project=popopop]": 1, "fetch_count[project=0987]": 1, "fetch_count[project=sport]": 1, "fetch_count[project=woeld]": 1, "fetch_count[project=mkmkmk]": 1, "fetch_count[project=science]": 1, "fetch_count[project=entertainment]": 1, "fetch_count[project=domestic]": 1, "fetch_count[project=science2]": 1, "fetch_count[project=life]": 1, "fetch_count[project=01sono2]": 1, "fetch_count[project=2025]": 1, "fetch_count[project=0202020202]": 1, "fetch_count[project=040404040_1]": 1, "fetch_count[project=aiueo]": 1, "fetch_count[project=topics_1]": 1, "fetch_count[project=yahooo2]": 1, "fetch_count[project=yahoooo3]": 1, "fetch_count[project=uiuiuuiuiuiuiuiuiu]": 1, "fetch_count[project=aiueo6]": 1, "fetch_count[project=hemohemohemo]": 1, "fetch_count[project=bibibi]": 1, "fetch_count[project=uino]": 1, "fetch_count[project=tesr]": 1, "fetch_count[project=testbus]": 1, "fetch_count[project=domestic_1]": 1, "fetch_count[project=popopopo]": 4, "fetch_count[project=sportssssssss]": 1, "fetch_count[project=siense]": 1, "fetch_count[project=oioio]": 11, "fetch_count[project=amazon]": 41}
2025-06-05 06:29:19 [INFO] metrics: Gauges: {"memory_usage_rss": 70373376, "memory_usage_vms": 382754816, "memory_usage_percent": 0.20959007884178335, "system_memory_total": ***********, "system_memory_available": ***********, "system_memory_percent": 15.9}
2025-06-05 06:29:30 [INFO] metrics: Counters: {"fetch_count[project=yahoo]": 1, "fetch_count[project=mehoo]": 1, "fetch_count[project=roastmeat]": 1, "fetch_count[project=nohoo]": 1, "fetch_count[project=yahooit]": 1, "fetch_count[project=business]": 1, "fetch_count[project=lplplp]": 1, "fetch_count[project=local]": 1, "fetch_count[project=popopop]": 1, "fetch_count[project=0987]": 1, "fetch_count[project=sport]": 1, "fetch_count[project=woeld]": 1, "fetch_count[project=mkmkmk]": 1, "fetch_count[project=science]": 1, "fetch_count[project=entertainment]": 1, "fetch_count[project=domestic]": 1, "fetch_count[project=science2]": 1, "fetch_count[project=life]": 1, "fetch_count[project=01sono2]": 1, "fetch_count[project=2025]": 1, "fetch_count[project=0202020202]": 1, "fetch_count[project=040404040_1]": 1, "fetch_count[project=aiueo]": 1, "fetch_count[project=topics_1]": 1, "fetch_count[project=yahooo2]": 1, "fetch_count[project=yahoooo3]": 1, "fetch_count[project=uiuiuuiuiuiuiuiuiu]": 1, "fetch_count[project=aiueo6]": 1, "fetch_count[project=hemohemohemo]": 1, "fetch_count[project=bibibi]": 1, "fetch_count[project=uino]": 1, "fetch_count[project=tesr]": 1, "fetch_count[project=testbus]": 1, "fetch_count[project=domestic_1]": 1, "fetch_count[project=popopopo]": 6, "fetch_count[project=sportssssssss]": 1, "fetch_count[project=siense]": 1, "fetch_count[project=oioio]": 11, "fetch_count[project=amazon]": 41}
2025-06-05 06:30:19 [INFO] metrics: Gauges: {"memory_usage_rss": 70373376, "memory_usage_vms": 382754816, "memory_usage_percent": 0.20959007884178335, "system_memory_total": ***********, "system_memory_available": ***********, "system_memory_percent": 15.8}
2025-06-05 06:30:30 [INFO] metrics: Counters: {"fetch_count[project=yahoo]": 1, "fetch_count[project=mehoo]": 1, "fetch_count[project=roastmeat]": 1, "fetch_count[project=nohoo]": 1, "fetch_count[project=yahooit]": 1, "fetch_count[project=business]": 1, "fetch_count[project=lplplp]": 1, "fetch_count[project=local]": 1, "fetch_count[project=popopop]": 1, "fetch_count[project=0987]": 1, "fetch_count[project=sport]": 1, "fetch_count[project=woeld]": 1, "fetch_count[project=mkmkmk]": 1, "fetch_count[project=science]": 1, "fetch_count[project=entertainment]": 1, "fetch_count[project=domestic]": 1, "fetch_count[project=science2]": 1, "fetch_count[project=life]": 1, "fetch_count[project=01sono2]": 1, "fetch_count[project=2025]": 1, "fetch_count[project=0202020202]": 1, "fetch_count[project=040404040_1]": 1, "fetch_count[project=aiueo]": 1, "fetch_count[project=topics_1]": 1, "fetch_count[project=yahooo2]": 1, "fetch_count[project=yahoooo3]": 1, "fetch_count[project=uiuiuuiuiuiuiuiuiu]": 1, "fetch_count[project=aiueo6]": 1, "fetch_count[project=hemohemohemo]": 1, "fetch_count[project=bibibi]": 1, "fetch_count[project=uino]": 1, "fetch_count[project=tesr]": 1, "fetch_count[project=testbus]": 1, "fetch_count[project=domestic_1]": 1, "fetch_count[project=popopopo]": 8, "fetch_count[project=sportssssssss]": 1, "fetch_count[project=siense]": 1, "fetch_count[project=oioio]": 11, "fetch_count[project=amazon]": 41}
2025-06-05 06:31:19 [INFO] metrics: Gauges: {"memory_usage_rss": 70373376, "memory_usage_vms": 382754816, "memory_usage_percent": 0.20959007884178335, "system_memory_total": ***********, "system_memory_available": ***********, "system_memory_percent": 15.8}
2025-06-05 06:32:00 [INFO] metrics: Counters: {"fetch_count[project=yahoo]": 1, "fetch_count[project=mehoo]": 1, "fetch_count[project=roastmeat]": 1, "fetch_count[project=nohoo]": 1, "fetch_count[project=yahooit]": 1, "fetch_count[project=business]": 1, "fetch_count[project=lplplp]": 1, "fetch_count[project=local]": 1, "fetch_count[project=popopop]": 1, "fetch_count[project=0987]": 1, "fetch_count[project=sport]": 1, "fetch_count[project=woeld]": 1, "fetch_count[project=mkmkmk]": 1, "fetch_count[project=science]": 1, "fetch_count[project=entertainment]": 1, "fetch_count[project=domestic]": 1, "fetch_count[project=science2]": 1, "fetch_count[project=life]": 1, "fetch_count[project=01sono2]": 1, "fetch_count[project=2025]": 1, "fetch_count[project=0202020202]": 1, "fetch_count[project=040404040_1]": 1, "fetch_count[project=aiueo]": 1, "fetch_count[project=topics_1]": 1, "fetch_count[project=yahooo2]": 1, "fetch_count[project=yahoooo3]": 1, "fetch_count[project=uiuiuuiuiuiuiuiuiu]": 1, "fetch_count[project=aiueo6]": 1, "fetch_count[project=hemohemohemo]": 1, "fetch_count[project=bibibi]": 1, "fetch_count[project=uino]": 1, "fetch_count[project=tesr]": 1, "fetch_count[project=testbus]": 1, "fetch_count[project=domestic_1]": 1, "fetch_count[project=popopopo]": 11, "fetch_count[project=sportssssssss]": 1, "fetch_count[project=siense]": 1, "fetch_count[project=oioio]": 11, "fetch_count[project=amazon]": 41}
{"timestamp": "2025-06-05T06:35:46.539402", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-05 06:35:47 [INFO] pyspider.libs.redis_fallback: ✅ Redis接続成功: redis://localhost:6379/0
2025-06-05 06:35:47 [INFO] pyspider.libs.redis_fallback: 🔍 Redis接続監視を開始しました
2025-06-05 06:35:47 [INFO] pyspider.libs.redis_fallback: ✅ Redis フォールバック機能を初期化しました
2025-06-05 06:35:47 [INFO] memory_optimizer: Memory optimizer initialized with max_memory_percent=80.0%, gc_interval=60s, check_interval=30s, auto_optimize=True
2025-06-05 06:35:47 [INFO] memory_optimizer: Memory monitoring started
2025-06-05 06:35:47 [INFO] root: Memory optimizer started for scheduler
2025-06-05 06:35:47 [INFO] root: Created PID file for scheduler: /tmp/pyspider_scheduler.pid
{"timestamp": "2025-06-05T06:35:48.718652", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-05 06:35:49 [INFO] root: Created PID file for fetcher: /tmp/pyspider_fetcher.pid
{"timestamp": "2025-06-05T06:35:50.931806", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-05 06:35:51 [INFO] root: Created PID file for processor: /tmp/pyspider_processor.pid
{"timestamp": "2025-06-05T06:35:53.526599", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-05 06:35:53 [INFO] root: Created PID file for result_worker: /tmp/pyspider_result_worker.pid
2025-06-05 06:35:53 [INFO] result: result_worker starting...
{"timestamp": "2025-06-05T06:35:55.670381", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-05 06:35:57 [INFO] libs.cache: Using Redis cache
2025-06-05 06:35:57 [INFO] root: Created PID file for webui: /tmp/pyspider_webui.pid
2025-06-05 06:36:47 [INFO] metrics: Gauges: {"memory_usage_rss": 70070272, "memory_usage_vms": 382685184, "memory_usage_percent": 0.2086873568911232, "system_memory_total": ***********, "system_memory_available": ***********, "system_memory_percent": 15.7}
2025-06-05 06:37:00 [INFO] metrics: Counters: {"fetch_count[project=yahoo]": 1, "fetch_count[project=mehoo]": 1, "fetch_count[project=roastmeat]": 1, "fetch_count[project=nohoo]": 1, "fetch_count[project=yahooit]": 1, "fetch_count[project=business]": 1, "fetch_count[project=lplplp]": 1, "fetch_count[project=local]": 1, "fetch_count[project=popopop]": 1, "fetch_count[project=0987]": 1, "fetch_count[project=sport]": 1, "fetch_count[project=woeld]": 1, "fetch_count[project=mkmkmk]": 1, "fetch_count[project=science]": 1, "fetch_count[project=entertainment]": 1, "fetch_count[project=domestic]": 1, "fetch_count[project=science2]": 1, "fetch_count[project=life]": 1, "fetch_count[project=01sono2]": 1, "fetch_count[project=2025]": 1, "fetch_count[project=0202020202]": 1, "fetch_count[project=040404040_1]": 1, "fetch_count[project=aiueo]": 1, "fetch_count[project=topics_1]": 1, "fetch_count[project=yahooo2]": 1, "fetch_count[project=yahoooo3]": 1, "fetch_count[project=uiuiuuiuiuiuiuiuiu]": 1, "fetch_count[project=aiueo6]": 1, "fetch_count[project=hemohemohemo]": 1, "fetch_count[project=bibibi]": 1, "fetch_count[project=uino]": 1, "fetch_count[project=tesr]": 1, "fetch_count[project=testbus]": 1, "fetch_count[project=domestic_1]": 1, "fetch_count[project=popopopo]": 4, "fetch_count[project=sportssssssss]": 1, "fetch_count[project=siense]": 1, "fetch_count[project=oioio]": 13, "fetch_count[project=amazon]": 12}
2025-06-05 06:37:47 [INFO] metrics: Gauges: {"memory_usage_rss": 70070272, "memory_usage_vms": 382685184, "memory_usage_percent": 0.2086873568911232, "system_memory_total": ***********, "system_memory_available": ***********, "system_memory_percent": 15.7}
2025-06-05 06:38:30 [INFO] metrics: Counters: {"fetch_count[project=yahoo]": 1, "fetch_count[project=mehoo]": 1, "fetch_count[project=roastmeat]": 1, "fetch_count[project=nohoo]": 1, "fetch_count[project=yahooit]": 1, "fetch_count[project=business]": 1, "fetch_count[project=lplplp]": 1, "fetch_count[project=local]": 1, "fetch_count[project=popopop]": 1, "fetch_count[project=0987]": 1, "fetch_count[project=sport]": 1, "fetch_count[project=woeld]": 1, "fetch_count[project=mkmkmk]": 1, "fetch_count[project=science]": 1, "fetch_count[project=entertainment]": 1, "fetch_count[project=domestic]": 1, "fetch_count[project=science2]": 1, "fetch_count[project=life]": 1, "fetch_count[project=01sono2]": 1, "fetch_count[project=2025]": 1, "fetch_count[project=0202020202]": 1, "fetch_count[project=040404040_1]": 1, "fetch_count[project=aiueo]": 1, "fetch_count[project=topics_1]": 1, "fetch_count[project=yahooo2]": 1, "fetch_count[project=yahoooo3]": 1, "fetch_count[project=uiuiuuiuiuiuiuiuiu]": 1, "fetch_count[project=aiueo6]": 1, "fetch_count[project=hemohemohemo]": 1, "fetch_count[project=bibibi]": 1, "fetch_count[project=uino]": 1, "fetch_count[project=tesr]": 1, "fetch_count[project=testbus]": 1, "fetch_count[project=domestic_1]": 1, "fetch_count[project=popopopo]": 7, "fetch_count[project=sportssssssss]": 1, "fetch_count[project=siense]": 1, "fetch_count[project=oioio]": 13, "fetch_count[project=amazon]": 12}
{"timestamp": "2025-06-05T06:42:14.444822", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-05 06:42:15 [INFO] pyspider.libs.redis_fallback: ✅ Redis接続成功: redis://localhost:6379/0
2025-06-05 06:42:15 [INFO] pyspider.libs.redis_fallback: 🔍 Redis接続監視を開始しました
2025-06-05 06:42:15 [INFO] pyspider.libs.redis_fallback: ✅ Redis フォールバック機能を初期化しました
2025-06-05 06:42:15 [INFO] memory_optimizer: Memory optimizer initialized with max_memory_percent=80.0%, gc_interval=60s, check_interval=30s, auto_optimize=True
2025-06-05 06:42:15 [INFO] memory_optimizer: Memory monitoring started
2025-06-05 06:42:15 [INFO] root: Memory optimizer started for scheduler
2025-06-05 06:42:15 [INFO] root: Created PID file for scheduler: /tmp/pyspider_scheduler.pid
{"timestamp": "2025-06-05T06:42:16.621001", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-05 06:42:16 [INFO] root: Created PID file for fetcher: /tmp/pyspider_fetcher.pid
{"timestamp": "2025-06-05T06:42:18.806624", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-05 06:42:19 [INFO] root: Created PID file for processor: /tmp/pyspider_processor.pid
{"timestamp": "2025-06-05T06:42:21.244730", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-05 06:42:21 [INFO] root: Created PID file for result_worker: /tmp/pyspider_result_worker.pid
2025-06-05 06:42:21 [INFO] result: result_worker starting...
{"timestamp": "2025-06-05T06:42:23.258085", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-05 06:42:25 [INFO] libs.cache: Using Redis cache
2025-06-05 06:42:25 [INFO] root: Created PID file for webui: /tmp/pyspider_webui.pid
2025-06-05 06:42:27 [INFO] result: result amazon:5b35be65f61336908dacae85770b8e52 https://www.amazon.co.jp/%E3%82%A2%E3%83%B3%E3%83%80%E3%83%BC%E3%82%A2%E3%83%BC%E3%83%9E%E3%83%BC-%E3%83%99%E3%83%BC%E3%82%B9%E3%83%AC%E3%82%A4%E3%83%A4%E3%83%BC-UA%E3%83%92%E3%83%BC%E3%83%88%E3%82%AE%E3%82%A2%E3%82%A2%E3%83%BC%E3%83%9E%E3%83%BC-%E3%83%AD%E3%83%B3%E3%82%B0%E3%82%B9%E3%83%AA%E3%83%BC%E3%83%96-%E3%82%B7%E3%83%A3%E3%83%84/dp/B0C5RB9D3N/ref=zg_bs_g_15218661_d_sccl_20/000-0000000-0000000?psc=1 -> {'url': 'https://www.amazon.co
2025-06-05 06:43:15 [INFO] metrics: Gauges: {"memory_usage_rss": 70352896, "memory_usage_vms": 382627840, "memory_usage_percent": 0.20913871786645324, "system_memory_total": ***********, "system_memory_available": ***********, "system_memory_percent": 13.6}
2025-06-05 06:43:30 [INFO] metrics: Counters: {"fetch_count[project=yahoo]": 1, "fetch_count[project=mehoo]": 1, "fetch_count[project=roastmeat]": 1, "fetch_count[project=nohoo]": 1, "fetch_count[project=yahooit]": 1, "fetch_count[project=business]": 1, "fetch_count[project=lplplp]": 1, "fetch_count[project=local]": 1, "fetch_count[project=popopop]": 1, "fetch_count[project=0987]": 1, "fetch_count[project=sport]": 1, "fetch_count[project=woeld]": 1, "fetch_count[project=mkmkmk]": 1, "fetch_count[project=science]": 1, "fetch_count[project=entertainment]": 1, "fetch_count[project=domestic]": 1, "fetch_count[project=science2]": 1, "fetch_count[project=life]": 1, "fetch_count[project=01sono2]": 1, "fetch_count[project=2025]": 1, "fetch_count[project=0202020202]": 1, "fetch_count[project=040404040_1]": 1, "fetch_count[project=aiueo]": 1, "fetch_count[project=topics_1]": 1, "fetch_count[project=yahooo2]": 1, "fetch_count[project=yahoooo3]": 1, "fetch_count[project=uiuiuuiuiuiuiuiuiu]": 1, "fetch_count[project=aiueo6]": 1, "fetch_count[project=hemohemohemo]": 1, "fetch_count[project=bibibi]": 1, "fetch_count[project=uino]": 1, "fetch_count[project=tesr]": 1, "fetch_count[project=testbus]": 1, "fetch_count[project=domestic_1]": 1, "fetch_count[project=popopopo]": 4, "fetch_count[project=sportssssssss]": 1, "fetch_count[project=siense]": 1, "fetch_count[project=oioio]": 12, "fetch_count[project=amazon]": 17}
2025-06-05 06:44:15 [INFO] metrics: Gauges: {"memory_usage_rss": 70352896, "memory_usage_vms": 382767104, "memory_usage_percent": 0.20952908411538737, "system_memory_total": ***********, "system_memory_available": ***********, "system_memory_percent": 13.6}
2025-06-05 06:44:30 [INFO] metrics: Counters: {"fetch_count[project=yahoo]": 1, "fetch_count[project=mehoo]": 1, "fetch_count[project=roastmeat]": 1, "fetch_count[project=nohoo]": 1, "fetch_count[project=yahooit]": 1, "fetch_count[project=business]": 1, "fetch_count[project=lplplp]": 1, "fetch_count[project=local]": 1, "fetch_count[project=popopop]": 1, "fetch_count[project=0987]": 1, "fetch_count[project=sport]": 1, "fetch_count[project=woeld]": 1, "fetch_count[project=mkmkmk]": 1, "fetch_count[project=science]": 1, "fetch_count[project=entertainment]": 1, "fetch_count[project=domestic]": 1, "fetch_count[project=science2]": 1, "fetch_count[project=life]": 1, "fetch_count[project=01sono2]": 1, "fetch_count[project=2025]": 1, "fetch_count[project=0202020202]": 1, "fetch_count[project=040404040_1]": 1, "fetch_count[project=aiueo]": 1, "fetch_count[project=topics_1]": 1, "fetch_count[project=yahooo2]": 1, "fetch_count[project=yahoooo3]": 1, "fetch_count[project=uiuiuuiuiuiuiuiuiu]": 1, "fetch_count[project=aiueo6]": 1, "fetch_count[project=hemohemohemo]": 1, "fetch_count[project=bibibi]": 1, "fetch_count[project=uino]": 1, "fetch_count[project=tesr]": 1, "fetch_count[project=testbus]": 1, "fetch_count[project=domestic_1]": 1, "fetch_count[project=popopopo]": 6, "fetch_count[project=sportssssssss]": 1, "fetch_count[project=siense]": 1, "fetch_count[project=oioio]": 12, "fetch_count[project=amazon]": 17}
2025-06-05 06:45:15 [INFO] metrics: Gauges: {"memory_usage_rss": 70615040, "memory_usage_vms": 382767104, "memory_usage_percent": 0.20952908411538737, "system_memory_total": ***********, "system_memory_available": ***********, "system_memory_percent": 13.9}
2025-06-05 06:45:30 [INFO] metrics: Counters: {"fetch_count[project=yahoo]": 1, "fetch_count[project=mehoo]": 1, "fetch_count[project=roastmeat]": 1, "fetch_count[project=nohoo]": 1, "fetch_count[project=yahooit]": 1, "fetch_count[project=business]": 1, "fetch_count[project=lplplp]": 1, "fetch_count[project=local]": 1, "fetch_count[project=popopop]": 1, "fetch_count[project=0987]": 1, "fetch_count[project=sport]": 1, "fetch_count[project=woeld]": 1, "fetch_count[project=mkmkmk]": 1, "fetch_count[project=science]": 1, "fetch_count[project=entertainment]": 1, "fetch_count[project=domestic]": 1, "fetch_count[project=science2]": 1, "fetch_count[project=life]": 1, "fetch_count[project=01sono2]": 1, "fetch_count[project=2025]": 1, "fetch_count[project=0202020202]": 1, "fetch_count[project=040404040_1]": 1, "fetch_count[project=aiueo]": 1, "fetch_count[project=topics_1]": 1, "fetch_count[project=yahooo2]": 1, "fetch_count[project=yahoooo3]": 1, "fetch_count[project=uiuiuuiuiuiuiuiuiu]": 1, "fetch_count[project=aiueo6]": 1, "fetch_count[project=hemohemohemo]": 1, "fetch_count[project=bibibi]": 1, "fetch_count[project=uino]": 1, "fetch_count[project=tesr]": 1, "fetch_count[project=testbus]": 1, "fetch_count[project=domestic_1]": 1, "fetch_count[project=popopopo]": 8, "fetch_count[project=sportssssssss]": 1, "fetch_count[project=siense]": 1, "fetch_count[project=oioio]": 12, "fetch_count[project=amazon]": 17}
2025-06-05 06:46:15 [INFO] metrics: Gauges: {"memory_usage_rss": 70615040, "memory_usage_vms": 382767104, "memory_usage_percent": 0.21030981661325562, "system_memory_total": ***********, "system_memory_available": ***********, "system_memory_percent": 13.8}
2025-06-05 06:47:00 [INFO] metrics: Counters: {"fetch_count[project=yahoo]": 1, "fetch_count[project=mehoo]": 1, "fetch_count[project=roastmeat]": 1, "fetch_count[project=nohoo]": 1, "fetch_count[project=yahooit]": 1, "fetch_count[project=business]": 1, "fetch_count[project=lplplp]": 1, "fetch_count[project=local]": 1, "fetch_count[project=popopop]": 1, "fetch_count[project=0987]": 1, "fetch_count[project=sport]": 1, "fetch_count[project=woeld]": 1, "fetch_count[project=mkmkmk]": 1, "fetch_count[project=science]": 1, "fetch_count[project=entertainment]": 1, "fetch_count[project=domestic]": 1, "fetch_count[project=science2]": 1, "fetch_count[project=life]": 1, "fetch_count[project=01sono2]": 1, "fetch_count[project=2025]": 1, "fetch_count[project=0202020202]": 1, "fetch_count[project=040404040_1]": 1, "fetch_count[project=aiueo]": 1, "fetch_count[project=topics_1]": 1, "fetch_count[project=yahooo2]": 1, "fetch_count[project=yahoooo3]": 1, "fetch_count[project=uiuiuuiuiuiuiuiuiu]": 1, "fetch_count[project=aiueo6]": 1, "fetch_count[project=hemohemohemo]": 1, "fetch_count[project=bibibi]": 1, "fetch_count[project=uino]": 1, "fetch_count[project=tesr]": 1, "fetch_count[project=testbus]": 1, "fetch_count[project=domestic_1]": 1, "fetch_count[project=popopopo]": 11, "fetch_count[project=sportssssssss]": 1, "fetch_count[project=siense]": 1, "fetch_count[project=oioio]": 12, "fetch_count[project=amazon]": 17}
2025-06-05 06:47:15 [INFO] metrics: Gauges: {"memory_usage_rss": 70615040, "memory_usage_vms": 382767104, "memory_usage_percent": 0.21030981661325562, "system_memory_total": ***********, "system_memory_available": 28907446272, "system_memory_percent": 13.9}
2025-06-05 06:47:57 [INFO] result: result oioio:01dce7368053070e962dcc3475b4c91e https://www.amazon.co.jp/%E3%82%B9%E3%82%AF%E3%82%A6%E3%82%A7%E3%82%A2%E3%83%BB%E3%82%A8%E3%83%8B%E3%83%83%E3%82%AF%E3%82%B9-SQUARE-ENIX-HAC-P-BGX2A-%E3%83%89%E3%83%A9%E3%82%B4%E3%83%B3%E3%82%AF%E3%82%A8%E3%82%B9%E3%83%88III/dp/B0D6Z43VB1/ref=zg_bs_g_videogames_d_sccl_19/000-0000000-0000000?psc=1 -> {'url': 'https://www.amazon.co
2025-06-05 06:48:15 [INFO] metrics: Gauges: {"memory_usage_rss": 70746112, "memory_usage_vms": 382767104, "memory_usage_percent": 0.21030981661325562, "system_memory_total": ***********, "system_memory_available": ***********, "system_memory_percent": 14.7}
2025-06-05 06:48:30 [INFO] metrics: Counters: {"fetch_count[project=yahoo]": 1, "fetch_count[project=mehoo]": 1, "fetch_count[project=roastmeat]": 1, "fetch_count[project=nohoo]": 1, "fetch_count[project=yahooit]": 1, "fetch_count[project=business]": 1, "fetch_count[project=lplplp]": 1, "fetch_count[project=local]": 1, "fetch_count[project=popopop]": 1, "fetch_count[project=0987]": 1, "fetch_count[project=sport]": 1, "fetch_count[project=woeld]": 1, "fetch_count[project=mkmkmk]": 1, "fetch_count[project=science]": 1, "fetch_count[project=entertainment]": 1, "fetch_count[project=domestic]": 1, "fetch_count[project=science2]": 1, "fetch_count[project=life]": 1, "fetch_count[project=01sono2]": 1, "fetch_count[project=2025]": 1, "fetch_count[project=0202020202]": 1, "fetch_count[project=040404040_1]": 1, "fetch_count[project=aiueo]": 1, "fetch_count[project=topics_1]": 1, "fetch_count[project=yahooo2]": 1, "fetch_count[project=yahoooo3]": 1, "fetch_count[project=uiuiuuiuiuiuiuiuiu]": 1, "fetch_count[project=aiueo6]": 1, "fetch_count[project=hemohemohemo]": 1, "fetch_count[project=bibibi]": 1, "fetch_count[project=uino]": 1, "fetch_count[project=tesr]": 1, "fetch_count[project=testbus]": 1, "fetch_count[project=domestic_1]": 1, "fetch_count[project=popopopo]": 14, "fetch_count[project=sportssssssss]": 1, "fetch_count[project=siense]": 1, "fetch_count[project=oioio]": 15, "fetch_count[project=amazon]": 20}
2025-06-05 06:49:15 [INFO] metrics: Gauges: {"memory_usage_rss": 70746112, "memory_usage_vms": 382767104, "memory_usage_percent": 0.21070018286218975, "system_memory_total": ***********, "system_memory_available": ***********, "system_memory_percent": 14.8}
2025-06-05 06:49:30 [INFO] metrics: Counters: {"fetch_count[project=yahoo]": 1, "fetch_count[project=mehoo]": 1, "fetch_count[project=roastmeat]": 1, "fetch_count[project=nohoo]": 1, "fetch_count[project=yahooit]": 1, "fetch_count[project=business]": 1, "fetch_count[project=lplplp]": 1, "fetch_count[project=local]": 1, "fetch_count[project=popopop]": 1, "fetch_count[project=0987]": 1, "fetch_count[project=sport]": 1, "fetch_count[project=woeld]": 1, "fetch_count[project=mkmkmk]": 1, "fetch_count[project=science]": 1, "fetch_count[project=entertainment]": 1, "fetch_count[project=domestic]": 1, "fetch_count[project=science2]": 1, "fetch_count[project=life]": 1, "fetch_count[project=01sono2]": 1, "fetch_count[project=2025]": 1, "fetch_count[project=0202020202]": 1, "fetch_count[project=040404040_1]": 1, "fetch_count[project=aiueo]": 1, "fetch_count[project=topics_1]": 1, "fetch_count[project=yahooo2]": 1, "fetch_count[project=yahoooo3]": 1, "fetch_count[project=uiuiuuiuiuiuiuiuiu]": 1, "fetch_count[project=aiueo6]": 1, "fetch_count[project=hemohemohemo]": 1, "fetch_count[project=bibibi]": 1, "fetch_count[project=uino]": 1, "fetch_count[project=tesr]": 1, "fetch_count[project=testbus]": 1, "fetch_count[project=domestic_1]": 1, "fetch_count[project=popopopo]": 16, "fetch_count[project=sportssssssss]": 1, "fetch_count[project=siense]": 1, "fetch_count[project=oioio]": 15, "fetch_count[project=amazon]": 20}
2025-06-05 06:50:15 [INFO] metrics: Gauges: {"memory_usage_rss": 70746112, "memory_usage_vms": 382767104, "memory_usage_percent": 0.21070018286218975, "system_memory_total": ***********, "system_memory_available": ***********, "system_memory_percent": 14.5}
2025-06-05 06:51:00 [INFO] metrics: Counters: {"fetch_count[project=yahoo]": 1, "fetch_count[project=mehoo]": 1, "fetch_count[project=roastmeat]": 1, "fetch_count[project=nohoo]": 1, "fetch_count[project=yahooit]": 1, "fetch_count[project=business]": 1, "fetch_count[project=lplplp]": 1, "fetch_count[project=local]": 1, "fetch_count[project=popopop]": 1, "fetch_count[project=0987]": 1, "fetch_count[project=sport]": 1, "fetch_count[project=woeld]": 1, "fetch_count[project=mkmkmk]": 1, "fetch_count[project=science]": 1, "fetch_count[project=entertainment]": 1, "fetch_count[project=domestic]": 1, "fetch_count[project=science2]": 1, "fetch_count[project=life]": 1, "fetch_count[project=01sono2]": 1, "fetch_count[project=2025]": 1, "fetch_count[project=0202020202]": 1, "fetch_count[project=040404040_1]": 1, "fetch_count[project=aiueo]": 1, "fetch_count[project=topics_1]": 1, "fetch_count[project=yahooo2]": 1, "fetch_count[project=yahoooo3]": 1, "fetch_count[project=uiuiuuiuiuiuiuiuiu]": 1, "fetch_count[project=aiueo6]": 1, "fetch_count[project=hemohemohemo]": 1, "fetch_count[project=bibibi]": 1, "fetch_count[project=uino]": 1, "fetch_count[project=tesr]": 1, "fetch_count[project=testbus]": 1, "fetch_count[project=domestic_1]": 1, "fetch_count[project=popopopo]": 19, "fetch_count[project=sportssssssss]": 1, "fetch_count[project=siense]": 1, "fetch_count[project=oioio]": 15, "fetch_count[project=amazon]": 20}
2025-06-05 06:51:15 [INFO] metrics: Gauges: {"memory_usage_rss": 70746112, "memory_usage_vms": 382767104, "memory_usage_percent": 0.21070018286218975, "system_memory_total": ***********, "system_memory_available": 28670193664, "system_memory_percent": 14.6}
2025-06-05 06:52:15 [INFO] metrics: Gauges: {"memory_usage_rss": 70746112, "memory_usage_vms": 382767104, "memory_usage_percent": 0.21070018286218975, "system_memory_total": ***********, "system_memory_available": ***********, "system_memory_percent": 14.6}
2025-06-05 06:52:30 [INFO] metrics: Counters: {"fetch_count[project=yahoo]": 1, "fetch_count[project=mehoo]": 1, "fetch_count[project=roastmeat]": 1, "fetch_count[project=nohoo]": 1, "fetch_count[project=yahooit]": 1, "fetch_count[project=business]": 1, "fetch_count[project=lplplp]": 1, "fetch_count[project=local]": 1, "fetch_count[project=popopop]": 1, "fetch_count[project=0987]": 1, "fetch_count[project=sport]": 1, "fetch_count[project=woeld]": 1, "fetch_count[project=mkmkmk]": 1, "fetch_count[project=science]": 1, "fetch_count[project=entertainment]": 1, "fetch_count[project=domestic]": 1, "fetch_count[project=science2]": 1, "fetch_count[project=life]": 1, "fetch_count[project=01sono2]": 1, "fetch_count[project=2025]": 1, "fetch_count[project=0202020202]": 1, "fetch_count[project=040404040_1]": 1, "fetch_count[project=aiueo]": 1, "fetch_count[project=topics_1]": 1, "fetch_count[project=yahooo2]": 1, "fetch_count[project=yahoooo3]": 1, "fetch_count[project=uiuiuuiuiuiuiuiuiu]": 1, "fetch_count[project=aiueo6]": 1, "fetch_count[project=hemohemohemo]": 1, "fetch_count[project=bibibi]": 1, "fetch_count[project=uino]": 1, "fetch_count[project=tesr]": 1, "fetch_count[project=testbus]": 1, "fetch_count[project=domestic_1]": 1, "fetch_count[project=popopopo]": 22, "fetch_count[project=sportssssssss]": 1, "fetch_count[project=siense]": 1, "fetch_count[project=oioio]": 15, "fetch_count[project=amazon]": 20}
2025-06-05 06:53:15 [INFO] metrics: Gauges: {"memory_usage_rss": 70746112, "memory_usage_vms": 382767104, "memory_usage_percent": 0.21070018286218975, "system_memory_total": ***********, "system_memory_available": ***********, "system_memory_percent": 14.5}
2025-06-05 06:54:00 [INFO] metrics: Counters: {"fetch_count[project=yahoo]": 1, "fetch_count[project=mehoo]": 1, "fetch_count[project=roastmeat]": 1, "fetch_count[project=nohoo]": 1, "fetch_count[project=yahooit]": 1, "fetch_count[project=business]": 1, "fetch_count[project=lplplp]": 1, "fetch_count[project=local]": 1, "fetch_count[project=popopop]": 1, "fetch_count[project=0987]": 1, "fetch_count[project=sport]": 1, "fetch_count[project=woeld]": 1, "fetch_count[project=mkmkmk]": 1, "fetch_count[project=science]": 1, "fetch_count[project=entertainment]": 1, "fetch_count[project=domestic]": 1, "fetch_count[project=science2]": 1, "fetch_count[project=life]": 1, "fetch_count[project=01sono2]": 1, "fetch_count[project=2025]": 1, "fetch_count[project=0202020202]": 1, "fetch_count[project=040404040_1]": 1, "fetch_count[project=aiueo]": 1, "fetch_count[project=topics_1]": 1, "fetch_count[project=yahooo2]": 1, "fetch_count[project=yahoooo3]": 1, "fetch_count[project=uiuiuuiuiuiuiuiuiu]": 1, "fetch_count[project=aiueo6]": 1, "fetch_count[project=hemohemohemo]": 1, "fetch_count[project=bibibi]": 1, "fetch_count[project=uino]": 1, "fetch_count[project=tesr]": 1, "fetch_count[project=testbus]": 1, "fetch_count[project=domestic_1]": 1, "fetch_count[project=popopopo]": 25, "fetch_count[project=sportssssssss]": 1, "fetch_count[project=siense]": 1, "fetch_count[project=oioio]": 15, "fetch_count[project=amazon]": 23}
2025-06-05 06:54:15 [INFO] metrics: Gauges: {"memory_usage_rss": 70877184, "memory_usage_vms": 382767104, "memory_usage_percent": 0.21109054911112388, "system_memory_total": ***********, "system_memory_available": 28405760000, "system_memory_percent": 15.4}
2025-06-05 06:55:15 [INFO] metrics: Gauges: {"memory_usage_rss": 70877184, "memory_usage_vms": 382767104, "memory_usage_percent": 0.21109054911112388, "system_memory_total": ***********, "system_memory_available": ***********, "system_memory_percent": 15.2}
2025-06-05 06:55:30 [INFO] metrics: Counters: {"fetch_count[project=yahoo]": 1, "fetch_count[project=mehoo]": 1, "fetch_count[project=roastmeat]": 1, "fetch_count[project=nohoo]": 1, "fetch_count[project=yahooit]": 1, "fetch_count[project=business]": 1, "fetch_count[project=lplplp]": 1, "fetch_count[project=local]": 1, "fetch_count[project=popopop]": 1, "fetch_count[project=0987]": 1, "fetch_count[project=sport]": 1, "fetch_count[project=woeld]": 1, "fetch_count[project=mkmkmk]": 1, "fetch_count[project=science]": 1, "fetch_count[project=entertainment]": 1, "fetch_count[project=domestic]": 1, "fetch_count[project=science2]": 1, "fetch_count[project=life]": 1, "fetch_count[project=01sono2]": 1, "fetch_count[project=2025]": 1, "fetch_count[project=0202020202]": 1, "fetch_count[project=040404040_1]": 1, "fetch_count[project=aiueo]": 1, "fetch_count[project=topics_1]": 1, "fetch_count[project=yahooo2]": 1, "fetch_count[project=yahoooo3]": 1, "fetch_count[project=uiuiuuiuiuiuiuiuiu]": 1, "fetch_count[project=aiueo6]": 1, "fetch_count[project=hemohemohemo]": 1, "fetch_count[project=bibibi]": 1, "fetch_count[project=uino]": 1, "fetch_count[project=tesr]": 1, "fetch_count[project=testbus]": 1, "fetch_count[project=domestic_1]": 1, "fetch_count[project=popopopo]": 28, "fetch_count[project=sportssssssss]": 1, "fetch_count[project=siense]": 1, "fetch_count[project=oioio]": 15, "fetch_count[project=amazon]": 23}
2025-06-05 06:56:15 [INFO] metrics: Gauges: {"memory_usage_rss": 70877184, "memory_usage_vms": 382767104, "memory_usage_percent": 0.21109054911112388, "system_memory_total": ***********, "system_memory_available": ***********, "system_memory_percent": 15.1}
2025-06-05 06:57:00 [INFO] metrics: Counters: {"fetch_count[project=yahoo]": 1, "fetch_count[project=mehoo]": 1, "fetch_count[project=roastmeat]": 1, "fetch_count[project=nohoo]": 1, "fetch_count[project=yahooit]": 1, "fetch_count[project=business]": 1, "fetch_count[project=lplplp]": 1, "fetch_count[project=local]": 1, "fetch_count[project=popopop]": 1, "fetch_count[project=0987]": 1, "fetch_count[project=sport]": 1, "fetch_count[project=woeld]": 1, "fetch_count[project=mkmkmk]": 1, "fetch_count[project=science]": 1, "fetch_count[project=entertainment]": 1, "fetch_count[project=domestic]": 1, "fetch_count[project=science2]": 1, "fetch_count[project=life]": 1, "fetch_count[project=01sono2]": 1, "fetch_count[project=2025]": 1, "fetch_count[project=0202020202]": 1, "fetch_count[project=040404040_1]": 1, "fetch_count[project=aiueo]": 1, "fetch_count[project=topics_1]": 1, "fetch_count[project=yahooo2]": 1, "fetch_count[project=yahoooo3]": 1, "fetch_count[project=uiuiuuiuiuiuiuiuiu]": 1, "fetch_count[project=aiueo6]": 1, "fetch_count[project=hemohemohemo]": 1, "fetch_count[project=bibibi]": 1, "fetch_count[project=uino]": 1, "fetch_count[project=tesr]": 1, "fetch_count[project=testbus]": 1, "fetch_count[project=domestic_1]": 1, "fetch_count[project=popopopo]": 31, "fetch_count[project=sportssssssss]": 1, "fetch_count[project=siense]": 1, "fetch_count[project=oioio]": 15, "fetch_count[project=amazon]": 23}
2025-06-05 06:57:15 [INFO] metrics: Gauges: {"memory_usage_rss": 70877184, "memory_usage_vms": 382767104, "memory_usage_percent": 0.21109054911112388, "system_memory_total": ***********, "system_memory_available": 28471205888, "system_memory_percent": 15.2}
2025-06-05 06:57:40 [INFO] result: result amazon:5611145ffc197869cd879173fb3f87d3 https://www.amazon.co.jp/%E3%82%AB%E3%83%B3%E3%82%BF%E3%83%99%E3%83%AA%E3%83%BC-%E3%83%A9%E3%82%AC%E3%83%BC%E3%82%B7%E3%83%A3%E3%83%84-SOLID-COLOR-JERSEY/dp/B0D6G7JFSB/ref=zg_bs_g_15218661_d_sccl_10/000-0000000-0000000?psc=1 -> {'url': 'https://www.amazon.co
2025-06-05 06:58:00 [INFO] metrics: Counters: {"fetch_count[project=yahoo]": 1, "fetch_count[project=mehoo]": 1, "fetch_count[project=roastmeat]": 1, "fetch_count[project=nohoo]": 1, "fetch_count[project=yahooit]": 1, "fetch_count[project=business]": 1, "fetch_count[project=lplplp]": 1, "fetch_count[project=local]": 1, "fetch_count[project=popopop]": 1, "fetch_count[project=0987]": 1, "fetch_count[project=sport]": 1, "fetch_count[project=woeld]": 1, "fetch_count[project=mkmkmk]": 1, "fetch_count[project=science]": 1, "fetch_count[project=entertainment]": 1, "fetch_count[project=domestic]": 1, "fetch_count[project=science2]": 1, "fetch_count[project=life]": 1, "fetch_count[project=01sono2]": 1, "fetch_count[project=2025]": 1, "fetch_count[project=0202020202]": 1, "fetch_count[project=040404040_1]": 1, "fetch_count[project=aiueo]": 1, "fetch_count[project=topics_1]": 1, "fetch_count[project=yahooo2]": 1, "fetch_count[project=yahoooo3]": 1, "fetch_count[project=uiuiuuiuiuiuiuiuiu]": 1, "fetch_count[project=aiueo6]": 1, "fetch_count[project=hemohemohemo]": 1, "fetch_count[project=bibibi]": 1, "fetch_count[project=uino]": 1, "fetch_count[project=tesr]": 1, "fetch_count[project=testbus]": 1, "fetch_count[project=domestic_1]": 1, "fetch_count[project=popopopo]": 33, "fetch_count[project=sportssssssss]": 1, "fetch_count[project=siense]": 1, "fetch_count[project=oioio]": 15, "fetch_count[project=amazon]": 27}
2025-06-05 06:58:15 [INFO] metrics: Gauges: {"memory_usage_rss": 70877184, "memory_usage_vms": 382902272, "memory_usage_percent": 0.21109054911112388, "system_memory_total": ***********, "system_memory_available": 28488429568, "system_memory_percent": 15.2}
{"timestamp": "2025-06-05T06:59:00.827741", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-05 06:59:01 [INFO] pyspider.libs.redis_fallback: ✅ Redis接続成功: redis://localhost:6379/0
2025-06-05 06:59:01 [INFO] pyspider.libs.redis_fallback: 🔍 Redis接続監視を開始しました
2025-06-05 06:59:01 [INFO] pyspider.libs.redis_fallback: ✅ Redis フォールバック機能を初期化しました
2025-06-05 06:59:01 [INFO] memory_optimizer: Memory optimizer initialized with max_memory_percent=80.0%, gc_interval=60s, check_interval=30s, auto_optimize=True
2025-06-05 06:59:01 [INFO] memory_optimizer: Memory monitoring started
2025-06-05 06:59:01 [INFO] root: Memory optimizer started for scheduler
2025-06-05 06:59:01 [INFO] root: Created PID file for scheduler: /tmp/pyspider_scheduler.pid
{"timestamp": "2025-06-05T06:59:02.976686", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-05 06:59:03 [INFO] root: Created PID file for fetcher: /tmp/pyspider_fetcher.pid
{"timestamp": "2025-06-05T06:59:05.166769", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-05 06:59:05 [INFO] root: Created PID file for processor: /tmp/pyspider_processor.pid
{"timestamp": "2025-06-05T06:59:07.241631", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-05 06:59:07 [INFO] root: Created PID file for result_worker: /tmp/pyspider_result_worker.pid
2025-06-05 06:59:07 [INFO] result: result_worker starting...
{"timestamp": "2025-06-05T06:59:09.245294", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-05 06:59:10 [INFO] libs.cache: Using Redis cache
2025-06-05 06:59:10 [INFO] root: Created PID file for webui: /tmp/pyspider_webui.pid
2025-06-05 07:00:01 [INFO] metrics: Gauges: {"memory_usage_rss": 70139904, "memory_usage_vms": 382676992, "memory_usage_percent": 0.20889473896086946, "system_memory_total": ***********, "system_memory_available": ***********, "system_memory_percent": 15.0}
2025-06-05 07:00:30 [INFO] metrics: Counters: {"fetch_count[project=yahoo]": 1, "fetch_count[project=mehoo]": 1, "fetch_count[project=roastmeat]": 1, "fetch_count[project=nohoo]": 1, "fetch_count[project=yahooit]": 1, "fetch_count[project=business]": 1, "fetch_count[project=lplplp]": 1, "fetch_count[project=local]": 1, "fetch_count[project=popopop]": 1, "fetch_count[project=0987]": 1, "fetch_count[project=sport]": 1, "fetch_count[project=woeld]": 1, "fetch_count[project=mkmkmk]": 1, "fetch_count[project=science]": 1, "fetch_count[project=entertainment]": 1, "fetch_count[project=domestic]": 1, "fetch_count[project=science2]": 1, "fetch_count[project=life]": 1, "fetch_count[project=01sono2]": 1, "fetch_count[project=2025]": 1, "fetch_count[project=0202020202]": 1, "fetch_count[project=040404040_1]": 2, "fetch_count[project=aiueo]": 1, "fetch_count[project=topics_1]": 1, "fetch_count[project=yahooo2]": 1, "fetch_count[project=yahoooo3]": 1, "fetch_count[project=uiuiuuiuiuiuiuiuiu]": 1, "fetch_count[project=aiueo6]": 1, "fetch_count[project=hemohemohemo]": 1, "fetch_count[project=bibibi]": 1, "fetch_count[project=uino]": 1, "fetch_count[project=tesr]": 2, "fetch_count[project=testbus]": 2, "fetch_count[project=domestic_1]": 1, "fetch_count[project=popopopo]": 4, "fetch_count[project=sportssssssss]": 1, "fetch_count[project=siense]": 1, "fetch_count[project=oioio]": 1, "fetch_count[project=amazon]": 11}
2025-06-05 07:01:01 [INFO] metrics: Gauges: {"memory_usage_rss": 70139904, "memory_usage_vms": 382676992, "memory_usage_percent": 0.20889473896086946, "system_memory_total": ***********, "system_memory_available": ***********, "system_memory_percent": 15.0}
2025-06-05 07:02:00 [INFO] metrics: Counters: {"fetch_count[project=yahoo]": 1, "fetch_count[project=mehoo]": 1, "fetch_count[project=roastmeat]": 1, "fetch_count[project=nohoo]": 1, "fetch_count[project=yahooit]": 1, "fetch_count[project=business]": 1, "fetch_count[project=lplplp]": 1, "fetch_count[project=local]": 1, "fetch_count[project=popopop]": 1, "fetch_count[project=0987]": 1, "fetch_count[project=sport]": 1, "fetch_count[project=woeld]": 1, "fetch_count[project=mkmkmk]": 1, "fetch_count[project=science]": 1, "fetch_count[project=entertainment]": 1, "fetch_count[project=domestic]": 1, "fetch_count[project=science2]": 1, "fetch_count[project=life]": 1, "fetch_count[project=01sono2]": 1, "fetch_count[project=2025]": 1, "fetch_count[project=0202020202]": 1, "fetch_count[project=040404040_1]": 2, "fetch_count[project=aiueo]": 1, "fetch_count[project=topics_1]": 1, "fetch_count[project=yahooo2]": 1, "fetch_count[project=yahoooo3]": 1, "fetch_count[project=uiuiuuiuiuiuiuiuiu]": 1, "fetch_count[project=aiueo6]": 1, "fetch_count[project=hemohemohemo]": 1, "fetch_count[project=bibibi]": 1, "fetch_count[project=uino]": 1, "fetch_count[project=tesr]": 2, "fetch_count[project=testbus]": 2, "fetch_count[project=domestic_1]": 1, "fetch_count[project=popopopo]": 7, "fetch_count[project=sportssssssss]": 1, "fetch_count[project=siense]": 1, "fetch_count[project=oioio]": 1, "fetch_count[project=amazon]": 11}
2025-06-05 07:02:01 [INFO] metrics: Gauges: {"memory_usage_rss": 70270976, "memory_usage_vms": 382676992, "memory_usage_percent": 0.20928510520980356, "system_memory_total": ***********, "system_memory_available": ***********, "system_memory_percent": 14.9}
2025-06-05 07:03:00 [INFO] metrics: Counters: {"fetch_count[project=yahoo]": 1, "fetch_count[project=mehoo]": 1, "fetch_count[project=roastmeat]": 1, "fetch_count[project=nohoo]": 1, "fetch_count[project=yahooit]": 1, "fetch_count[project=business]": 1, "fetch_count[project=lplplp]": 1, "fetch_count[project=local]": 1, "fetch_count[project=popopop]": 1, "fetch_count[project=0987]": 1, "fetch_count[project=sport]": 1, "fetch_count[project=woeld]": 1, "fetch_count[project=mkmkmk]": 1, "fetch_count[project=science]": 1, "fetch_count[project=entertainment]": 1, "fetch_count[project=domestic]": 1, "fetch_count[project=science2]": 1, "fetch_count[project=life]": 1, "fetch_count[project=01sono2]": 1, "fetch_count[project=2025]": 1, "fetch_count[project=0202020202]": 1, "fetch_count[project=040404040_1]": 2, "fetch_count[project=aiueo]": 1, "fetch_count[project=topics_1]": 1, "fetch_count[project=yahooo2]": 1, "fetch_count[project=yahoooo3]": 1, "fetch_count[project=uiuiuuiuiuiuiuiuiu]": 1, "fetch_count[project=aiueo6]": 1, "fetch_count[project=hemohemohemo]": 1, "fetch_count[project=bibibi]": 1, "fetch_count[project=uino]": 1, "fetch_count[project=tesr]": 2, "fetch_count[project=testbus]": 2, "fetch_count[project=domestic_1]": 1, "fetch_count[project=popopopo]": 9, "fetch_count[project=sportssssssss]": 1, "fetch_count[project=siense]": 1, "fetch_count[project=oioio]": 1, "fetch_count[project=amazon]": 11}
2025-06-05 07:03:01 [INFO] metrics: Gauges: {"memory_usage_rss": 70270976, "memory_usage_vms": 382824448, "memory_usage_percent": 0.20928510520980356, "system_memory_total": ***********, "system_memory_available": 28570914816, "system_memory_percent": 14.9}
2025-06-05 07:04:01 [INFO] metrics: Gauges: {"memory_usage_rss": 70270976, "memory_usage_vms": 382824448, "memory_usage_percent": 0.20928510520980356, "system_memory_total": ***********, "system_memory_available": ***********, "system_memory_percent": 15.0}
2025-06-05 07:04:14 [INFO] metrics: Counters: {"fetch_count[project=yahoo]": 1, "fetch_count[project=mehoo]": 1, "fetch_count[project=roastmeat]": 1, "fetch_count[project=nohoo]": 1, "fetch_count[project=yahooit]": 1, "fetch_count[project=business]": 1, "fetch_count[project=lplplp]": 1, "fetch_count[project=local]": 1, "fetch_count[project=popopop]": 1, "fetch_count[project=0987]": 1, "fetch_count[project=sport]": 1, "fetch_count[project=woeld]": 1, "fetch_count[project=mkmkmk]": 1, "fetch_count[project=science]": 1, "fetch_count[project=entertainment]": 1, "fetch_count[project=domestic]": 1, "fetch_count[project=science2]": 1, "fetch_count[project=life]": 1, "fetch_count[project=01sono2]": 1, "fetch_count[project=2025]": 1, "fetch_count[project=0202020202]": 1, "fetch_count[project=040404040_1]": 2, "fetch_count[project=aiueo]": 1, "fetch_count[project=topics_1]": 1, "fetch_count[project=yahooo2]": 1, "fetch_count[project=yahoooo3]": 1, "fetch_count[project=uiuiuuiuiuiuiuiuiu]": 1, "fetch_count[project=aiueo6]": 1, "fetch_count[project=hemohemohemo]": 1, "fetch_count[project=bibibi]": 1, "fetch_count[project=uino]": 1, "fetch_count[project=tesr]": 2, "fetch_count[project=testbus]": 2, "fetch_count[project=domestic_1]": 1, "fetch_count[project=popopopo]": 11, "fetch_count[project=sportssssssss]": 1, "fetch_count[project=siense]": 1, "fetch_count[project=oioio]": 1, "fetch_count[project=amazon]": 12}
2025-06-05 07:04:20 [INFO] result: result amazon:06bd16788dab757d9a7dc8cd580e44a4 https://www.amazon.co.jp/%E3%83%A8%E3%83%8D%E3%83%83%E3%82%AF%E3%82%B9-%E5%8D%8A%E8%A2%96%E3%82%B7%E3%83%A3%E3%83%84-%E3%82%B2%E3%83%BC%E3%83%A0%E3%82%B7%E3%83%A3%E3%83%84-%E3%83%AC%E3%83%87%E3%82%A3%E3%83%BC%E3%82%B9-%E3%83%9F%E3%83%83%E3%83%89%E3%83%8A%E3%82%A4%E3%83%88%E3%83%8D%E3%82%A4%E3%83%93%E3%83%BC/dp/B0CBR8T5GY/ref=zg_bs_g_15218661_d_sccl_18/000-0000000-0000000?psc=1 -> {'url': 'https://www.amazon.co
2025-06-05 07:05:01 [INFO] metrics: Gauges: {"memory_usage_rss": 70533120, "memory_usage_vms": 382824448, "memory_usage_percent": 0.2100658377076718, "system_memory_total": ***********, "system_memory_available": ***********, "system_memory_percent": 15.1}
2025-06-05 07:05:30 [INFO] metrics: Counters: {"fetch_count[project=yahoo]": 1, "fetch_count[project=mehoo]": 1, "fetch_count[project=roastmeat]": 1, "fetch_count[project=nohoo]": 1, "fetch_count[project=yahooit]": 1, "fetch_count[project=business]": 1, "fetch_count[project=lplplp]": 1, "fetch_count[project=local]": 1, "fetch_count[project=popopop]": 1, "fetch_count[project=0987]": 1, "fetch_count[project=sport]": 1, "fetch_count[project=woeld]": 1, "fetch_count[project=mkmkmk]": 1, "fetch_count[project=science]": 1, "fetch_count[project=entertainment]": 1, "fetch_count[project=domestic]": 1, "fetch_count[project=science2]": 1, "fetch_count[project=life]": 1, "fetch_count[project=01sono2]": 1, "fetch_count[project=2025]": 1, "fetch_count[project=0202020202]": 1, "fetch_count[project=040404040_1]": 2, "fetch_count[project=aiueo]": 1, "fetch_count[project=topics_1]": 1, "fetch_count[project=yahooo2]": 1, "fetch_count[project=yahoooo3]": 1, "fetch_count[project=uiuiuuiuiuiuiuiuiu]": 1, "fetch_count[project=aiueo6]": 1, "fetch_count[project=hemohemohemo]": 1, "fetch_count[project=bibibi]": 1, "fetch_count[project=uino]": 1, "fetch_count[project=tesr]": 2, "fetch_count[project=testbus]": 2, "fetch_count[project=domestic_1]": 1, "fetch_count[project=popopopo]": 14, "fetch_count[project=sportssssssss]": 1, "fetch_count[project=siense]": 1, "fetch_count[project=oioio]": 1, "fetch_count[project=amazon]": 16}
2025-06-05 07:06:01 [INFO] metrics: Gauges: {"memory_usage_rss": 70533120, "memory_usage_vms": 382824448, "memory_usage_percent": 0.2100658377076718, "system_memory_total": ***********, "system_memory_available": 28316897280, "system_memory_percent": 15.7}
2025-06-05 07:06:15 [INFO] result: result oioio:587673006459360f89abe3461e6b0f22 https://www.amazon.co.jp/%E4%BB%BB%E5%A4%A9%E5%A0%82-HAC-P-A7HLA-%E3%82%B9%E3%83%BC%E3%83%91%E3%83%BC-%E3%83%9E%E3%83%AA%E3%82%AA%E3%83%91%E3%83%BC%E3%83%86%E3%82%A3-%E3%82%B8%E3%83%A3%E3%83%B3%E3%83%9C%E3%83%AA%E3%83%BC/dp/B0D7GQRT8R/ref=zg_bs_g_videogames_d_sccl_5/000-0000000-0000000?psc=1 -> {'url': 'https://www.amazon.co
2025-06-05 07:07:00 [INFO] metrics: Counters: {"fetch_count[project=yahoo]": 1, "fetch_count[project=mehoo]": 1, "fetch_count[project=roastmeat]": 1, "fetch_count[project=nohoo]": 1, "fetch_count[project=yahooit]": 1, "fetch_count[project=business]": 1, "fetch_count[project=lplplp]": 1, "fetch_count[project=local]": 1, "fetch_count[project=popopop]": 1, "fetch_count[project=0987]": 1, "fetch_count[project=sport]": 1, "fetch_count[project=woeld]": 1, "fetch_count[project=mkmkmk]": 1, "fetch_count[project=science]": 1, "fetch_count[project=entertainment]": 1, "fetch_count[project=domestic]": 1, "fetch_count[project=science2]": 1, "fetch_count[project=life]": 1, "fetch_count[project=01sono2]": 1, "fetch_count[project=2025]": 1, "fetch_count[project=0202020202]": 1, "fetch_count[project=040404040_1]": 2, "fetch_count[project=aiueo]": 1, "fetch_count[project=topics_1]": 1, "fetch_count[project=yahooo2]": 1, "fetch_count[project=yahoooo3]": 1, "fetch_count[project=uiuiuuiuiuiuiuiuiu]": 1, "fetch_count[project=aiueo6]": 1, "fetch_count[project=hemohemohemo]": 1, "fetch_count[project=bibibi]": 1, "fetch_count[project=uino]": 1, "fetch_count[project=tesr]": 2, "fetch_count[project=testbus]": 2, "fetch_count[project=domestic_1]": 1, "fetch_count[project=popopopo]": 17, "fetch_count[project=sportssssssss]": 1, "fetch_count[project=siense]": 1, "fetch_count[project=oioio]": 11, "fetch_count[project=amazon]": 16}
2025-06-05 07:07:01 [INFO] metrics: Gauges: {"memory_usage_rss": 70795264, "memory_usage_vms": 382824448, "memory_usage_percent": 0.21084657020554004, "system_memory_total": ***********, "system_memory_available": 28457840640, "system_memory_percent": 15.2}
{"timestamp": "2025-06-05T07:07:52.662923", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-05 07:08:00 [INFO] metrics: Counters: {"fetch_count[project=yahoo]": 1, "fetch_count[project=mehoo]": 1, "fetch_count[project=roastmeat]": 1, "fetch_count[project=nohoo]": 1, "fetch_count[project=yahooit]": 1, "fetch_count[project=business]": 1, "fetch_count[project=lplplp]": 1, "fetch_count[project=local]": 1, "fetch_count[project=popopop]": 1, "fetch_count[project=0987]": 1, "fetch_count[project=sport]": 1, "fetch_count[project=woeld]": 1, "fetch_count[project=mkmkmk]": 1, "fetch_count[project=science]": 1, "fetch_count[project=entertainment]": 1, "fetch_count[project=domestic]": 1, "fetch_count[project=science2]": 1, "fetch_count[project=life]": 1, "fetch_count[project=01sono2]": 1, "fetch_count[project=2025]": 1, "fetch_count[project=0202020202]": 1, "fetch_count[project=040404040_1]": 2, "fetch_count[project=aiueo]": 1, "fetch_count[project=topics_1]": 1, "fetch_count[project=yahooo2]": 1, "fetch_count[project=yahoooo3]": 1, "fetch_count[project=uiuiuuiuiuiuiuiuiu]": 1, "fetch_count[project=aiueo6]": 1, "fetch_count[project=hemohemohemo]": 1, "fetch_count[project=bibibi]": 1, "fetch_count[project=uino]": 1, "fetch_count[project=tesr]": 2, "fetch_count[project=testbus]": 2, "fetch_count[project=domestic_1]": 1, "fetch_count[project=popopopo]": 19, "fetch_count[project=sportssssssss]": 1, "fetch_count[project=siense]": 1, "fetch_count[project=oioio]": 11, "fetch_count[project=amazon]": 16}
2025-06-05 07:08:01 [INFO] metrics: Gauges: {"memory_usage_rss": 70795264, "memory_usage_vms": 382824448, "memory_usage_percent": 0.21084657020554004, "system_memory_total": ***********, "system_memory_available": 28474671104, "system_memory_percent": 15.2}
{"timestamp": "2025-06-05T07:08:17.079248", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-05 07:08:18 [INFO] libs.cache: Using Redis cache
2025-06-05 07:08:18 [INFO] root: Created PID file for webui: /tmp/pyspider_webui.pid
2025-06-05 07:08:18 [ERROR] pyspider.errors: Uncaught exception occurred:
2025-06-05 07:08:18 [ERROR] pyspider.errors: Exception: OSError: [Errno 98] Address already in use

  File "/home/<USER>/.pyenv/versions/3.13.2/bin/pyspider", line 8, in <module>
    sys.exit(main())
  Local variables:
    re = <module 're' from '/home/<USER>/.pyenv/versions/3.13.2/lib/python3.13/re/__init__.py'>
    sys = <module 'sys' (built-in)>
    main = <function main at 0x729785506f20>

  File "/home/<USER>/workplace/python/pyspiderNX2/pyspider/run.py", line 1080, in main
    cli()

  File "/home/<USER>/.pyenv/versions/3.13.2/lib/python3.13/site-packages/click/core.py", line 1442, in __call__
    return self.main(*args, **kwargs)
  Local variables:
    self = <Group cli>
    args = ()
    kwargs = {}

  File "/home/<USER>/.pyenv/versions/3.13.2/lib/python3.13/site-packages/click/core.py", line 1363, in main
    rv = self.invoke(ctx)
  Local variables:
    self = <Group cli>
    args = ['webui', '--host', '0.0.0.0', '--port', '5000']
    prog_name = 'pyspider'
    complete_var = None
    standalone_mode = True
    windows_expand_args = True
    extra = {}
    ctx = <click.core.Context object at 0x72978549dbe0>

  File "/home/<USER>/.pyenv/versions/3.13.2/lib/python3.13/site-packages/click/core.py", line 1830, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
  Local variables:
    self = <Group cli>
    ctx = <click.core.Context object at 0x72978549dbe0>
    _process_result = <function Group.invoke.<locals>._process_result at 0x729785528180>
    args = []
    cmd_name = 'webui'
    cmd = <Command webui>
    sub_ctx = <click.core.Context object at 0x729785522490>

  File "/home/<USER>/.pyenv/versions/3.13.2/lib/python3.13/site-packages/click/core.py", line 1226, in invoke
    return ctx.invoke(self.callback, **ctx.params)
  Local variables:
    self = <Command webui>
    ctx = <click.core.Context object at 0x729785522490>

  File "/home/<USER>/.pyenv/versions/3.13.2/lib/python3.13/site-packages/click/core.py", line 794, in invoke
    return callback(*args, **kwargs)
  Local variables:
    self = <click.core.Context object at 0x729785522490>
    callback = <function webui at 0x729785507ec0>
    args = ()
    kwargs = {'host': '0.0.0.0', 'port': 5000, 'cdn': '//cdnjs.cloudflare.com/ajax/libs/', 'scheduler_rpc': 'h...
    ctx = <click.core.Context object at 0x729785522490>

  File "/home/<USER>/.pyenv/versions/3.13.2/lib/python3.13/site-packages/click/decorators.py", line 34, in new_func
    return f(get_current_context(), *args, **kwargs)
  Local variables:
    args = ()
    kwargs = {'host': '0.0.0.0', 'port': 5000, 'cdn': '//cdnjs.cloudflare.com/ajax/libs/', 'scheduler_rpc': 'h...
    f = <function webui at 0x729785507e20>

  File "/home/<USER>/workplace/python/pyspiderNX2/pyspider/run.py", line 696, in webui
    app.run(host=host, port=port)
  Local variables:
    ctx = <click.core.Context object at 0x729785522490>
    host = '0.0.0.0'
    port = 5000
    cdn = '//cdnjs.cloudflare.com/ajax/libs/'
    scheduler_rpc = <ServerProxy for localhost:23333/>
    fetcher_rpc = None
    max_rate = None
    max_burst = None
    username = 'admin'
    password = 'PySpider2024!SecurePass#'
    need_auth = True
    webui_instance = <QuitableFlask 'webui'>
    process_time_limit = 30
    get_object = False
    app = <QuitableFlask 'webui'>
    g = {'instances': [<pyspider.fetcher.tornado_fetcher.Fetcher object at 0x729781aa02f0>, <QuitableFlas...
    name = 'processor2result'
    fetcher_config = {'xmlrpc_port': 24444, 'puppeteer_endpoint': 'http://localhost:22223', 'timeout': 30, 'poolsize':...
    safe_fetch = <function webui.<locals>.safe_fetch at 0x729781ac56c0>
    webui_fetcher = <pyspider.fetcher.tornado_fetcher.Fetcher object at 0x729781aa02f0>

  File "/home/<USER>/workplace/python/pyspiderNX2/pyspider/webui/app.py", line 65, in run
    self.http_server.listen(port, hostname)
  Local variables:
    self = <QuitableFlask 'webui'>
    host = '0.0.0.0'
    port = 5000
    debug = None
    options = {}
    tornado = <module 'tornado' from '/home/<USER>/.pyenv/versions/3.13.2/lib/python3.13/site-packages/torna...
    hostname = '0.0.0.0'
    application = <QuitableFlask 'webui'>
    use_reloader = False
    use_debugger = False
    container = <tornado.wsgi.WSGIContainer object at 0x7297817d8980>

  File "/home/<USER>/.pyenv/versions/3.13.2/lib/python3.13/site-packages/tornado/tcpserver.py", line 183, in listen
    sockets = bind_sockets(
  Local variables:
    self = <tornado.httpserver.HTTPServer object at 0x7297817d8ad0>
    port = 5000
    address = '0.0.0.0'
    family = <AddressFamily.AF_UNSPEC: 0>
    backlog = 128
    flags = None
    reuse_port = False

  File "/home/<USER>/.pyenv/versions/3.13.2/lib/python3.13/site-packages/tornado/netutil.py", line 162, in bind_sockets
    sock.bind(sockaddr)
  Local variables:
    port = 5000
    address = '0.0.0.0'
    family = <AddressFamily.AF_UNSPEC: 0>
    backlog = 128
    flags = <AddressInfo.AI_PASSIVE: 1>
    reuse_port = False
    sockets = []
    bound_port = None
    unique_addresses = {(<AddressFamily.AF_INET: 2>, <SocketKind.SOCK_STREAM: 1>, 6, '', ('0.0.0.0', 5000))}
    res = (<AddressFamily.AF_INET: 2>, <SocketKind.SOCK_STREAM: 1>, 6, '', ('0.0.0.0', 5000))
    af = <AddressFamily.AF_INET: 2>
    socktype = <SocketKind.SOCK_STREAM: 1>
    proto = 6
    canonname = ''
    sockaddr = ('0.0.0.0', 5000)
    sock = <socket.socket fd=18, family=2, type=1, proto=6, laddr=('0.0.0.0', 0)>
    host = '0.0.0.0'
    requested_port = 5000

2025-06-05 07:09:01 [INFO] metrics: Gauges: {"memory_usage_rss": 70926336, "memory_usage_vms": 382824448, "memory_usage_percent": 0.21084657020554004, "system_memory_total": ***********, "system_memory_available": ***********, "system_memory_percent": 15.4}
2025-06-05 07:09:16 [INFO] metrics: Counters: {"fetch_count[project=yahoo]": 1, "fetch_count[project=mehoo]": 1, "fetch_count[project=roastmeat]": 1, "fetch_count[project=nohoo]": 1, "fetch_count[project=yahooit]": 1, "fetch_count[project=business]": 1, "fetch_count[project=lplplp]": 1, "fetch_count[project=local]": 1, "fetch_count[project=popopop]": 1, "fetch_count[project=0987]": 1, "fetch_count[project=sport]": 1, "fetch_count[project=woeld]": 1, "fetch_count[project=mkmkmk]": 1, "fetch_count[project=science]": 1, "fetch_count[project=entertainment]": 1, "fetch_count[project=domestic]": 1, "fetch_count[project=science2]": 1, "fetch_count[project=life]": 1, "fetch_count[project=01sono2]": 1, "fetch_count[project=2025]": 1, "fetch_count[project=0202020202]": 1, "fetch_count[project=040404040_1]": 2, "fetch_count[project=aiueo]": 1, "fetch_count[project=topics_1]": 1, "fetch_count[project=yahooo2]": 1, "fetch_count[project=yahoooo3]": 1, "fetch_count[project=uiuiuuiuiuiuiuiuiu]": 1, "fetch_count[project=aiueo6]": 1, "fetch_count[project=hemohemohemo]": 1, "fetch_count[project=bibibi]": 1, "fetch_count[project=uino]": 1, "fetch_count[project=tesr]": 2, "fetch_count[project=testbus]": 2, "fetch_count[project=domestic_1]": 1, "fetch_count[project=popopopo]": 21, "fetch_count[project=sportssssssss]": 1, "fetch_count[project=siense]": 1, "fetch_count[project=oioio]": 20, "fetch_count[project=amazon]": 17}
2025-06-05 07:10:01 [INFO] metrics: Gauges: {"memory_usage_rss": 70926336, "memory_usage_vms": 382824448, "memory_usage_percent": 0.21123693645447414, "system_memory_total": ***********, "system_memory_available": ***********, "system_memory_percent": 15.3}
2025-06-05 07:10:30 [INFO] metrics: Counters: {"fetch_count[project=yahoo]": 1, "fetch_count[project=mehoo]": 1, "fetch_count[project=roastmeat]": 1, "fetch_count[project=nohoo]": 1, "fetch_count[project=yahooit]": 1, "fetch_count[project=business]": 1, "fetch_count[project=lplplp]": 1, "fetch_count[project=local]": 1, "fetch_count[project=popopop]": 1, "fetch_count[project=0987]": 1, "fetch_count[project=sport]": 1, "fetch_count[project=woeld]": 1, "fetch_count[project=mkmkmk]": 1, "fetch_count[project=science]": 1, "fetch_count[project=entertainment]": 1, "fetch_count[project=domestic]": 1, "fetch_count[project=science2]": 1, "fetch_count[project=life]": 1, "fetch_count[project=01sono2]": 1, "fetch_count[project=2025]": 1, "fetch_count[project=0202020202]": 1, "fetch_count[project=040404040_1]": 2, "fetch_count[project=aiueo]": 1, "fetch_count[project=topics_1]": 1, "fetch_count[project=yahooo2]": 1, "fetch_count[project=yahoooo3]": 1, "fetch_count[project=uiuiuuiuiuiuiuiuiu]": 1, "fetch_count[project=aiueo6]": 1, "fetch_count[project=hemohemohemo]": 1, "fetch_count[project=bibibi]": 1, "fetch_count[project=uino]": 1, "fetch_count[project=tesr]": 2, "fetch_count[project=testbus]": 2, "fetch_count[project=domestic_1]": 1, "fetch_count[project=popopopo]": 24, "fetch_count[project=sportssssssss]": 1, "fetch_count[project=siense]": 1, "fetch_count[project=oioio]": 20, "fetch_count[project=amazon]": 19}
2025-06-05 07:11:01 [INFO] metrics: Gauges: {"memory_usage_rss": 70926336, "memory_usage_vms": 382824448, "memory_usage_percent": 0.21123693645447414, "system_memory_total": ***********, "system_memory_available": ***********, "system_memory_percent": 15.2}
2025-06-05 07:12:00 [INFO] metrics: Counters: {"fetch_count[project=yahoo]": 1, "fetch_count[project=mehoo]": 1, "fetch_count[project=roastmeat]": 1, "fetch_count[project=nohoo]": 1, "fetch_count[project=yahooit]": 1, "fetch_count[project=business]": 1, "fetch_count[project=lplplp]": 1, "fetch_count[project=local]": 1, "fetch_count[project=popopop]": 1, "fetch_count[project=0987]": 1, "fetch_count[project=sport]": 1, "fetch_count[project=woeld]": 1, "fetch_count[project=mkmkmk]": 1, "fetch_count[project=science]": 1, "fetch_count[project=entertainment]": 1, "fetch_count[project=domestic]": 1, "fetch_count[project=science2]": 1, "fetch_count[project=life]": 1, "fetch_count[project=01sono2]": 1, "fetch_count[project=2025]": 1, "fetch_count[project=0202020202]": 1, "fetch_count[project=040404040_1]": 2, "fetch_count[project=aiueo]": 1, "fetch_count[project=topics_1]": 1, "fetch_count[project=yahooo2]": 1, "fetch_count[project=yahoooo3]": 1, "fetch_count[project=uiuiuuiuiuiuiuiuiu]": 1, "fetch_count[project=aiueo6]": 1, "fetch_count[project=hemohemohemo]": 1, "fetch_count[project=bibibi]": 1, "fetch_count[project=uino]": 1, "fetch_count[project=tesr]": 2, "fetch_count[project=testbus]": 2, "fetch_count[project=domestic_1]": 1, "fetch_count[project=popopopo]": 27, "fetch_count[project=sportssssssss]": 1, "fetch_count[project=siense]": 1, "fetch_count[project=oioio]": 20, "fetch_count[project=amazon]": 19}
2025-06-05 07:12:01 [INFO] metrics: Gauges: {"memory_usage_rss": 70926336, "memory_usage_vms": 382824448, "memory_usage_percent": 0.21123693645447414, "system_memory_total": ***********, "system_memory_available": 28463292416, "system_memory_percent": 15.2}
{"timestamp": "2025-06-05T07:14:40.013223", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-05 07:14:40 [INFO] pyspider.libs.redis_fallback: ✅ Redis接続成功: redis://localhost:6379/0
2025-06-05 07:14:40 [INFO] pyspider.libs.redis_fallback: 🔍 Redis接続監視を開始しました
2025-06-05 07:14:40 [INFO] pyspider.libs.redis_fallback: ✅ Redis フォールバック機能を初期化しました
2025-06-05 07:14:40 [INFO] memory_optimizer: Memory optimizer initialized with max_memory_percent=80.0%, gc_interval=60s, check_interval=30s, auto_optimize=True
2025-06-05 07:14:40 [INFO] memory_optimizer: Memory monitoring started
2025-06-05 07:14:40 [INFO] root: Memory optimizer started for scheduler
2025-06-05 07:14:40 [INFO] root: Created PID file for scheduler: /tmp/pyspider_scheduler.pid
{"timestamp": "2025-06-05T07:14:42.162748", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-05 07:14:42 [INFO] root: Created PID file for fetcher: /tmp/pyspider_fetcher.pid
{"timestamp": "2025-06-05T07:14:44.281500", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-05 07:14:44 [INFO] root: Created PID file for processor: /tmp/pyspider_processor.pid
{"timestamp": "2025-06-05T07:14:46.588724", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-05 07:14:46 [INFO] root: Created PID file for result_worker: /tmp/pyspider_result_worker.pid
2025-06-05 07:14:46 [INFO] result: result_worker starting...
{"timestamp": "2025-06-05T07:14:48.468831", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-05 07:14:49 [INFO] libs.cache: Using Redis cache
2025-06-05 07:14:49 [INFO] root: Created PID file for webui: /tmp/pyspider_webui.pid
2025-06-05 07:14:51 [INFO] result: result amazon:51e27cb05ccf3889bf4392f37f26bfc3 https://www.amazon.co.jp/Remarks-Japan-%E3%83%9E%E3%83%BC%E3%82%AB%E3%83%BC%E3%82%B3%E3%83%BC%E3%83%B3-%E3%82%AB%E3%83%A9%E3%83%BC%E3%83%9E%E3%83%BC%E3%82%AB%E3%83%BC-%E3%83%88%E3%83%AC%E3%83%BC%E3%83%8B%E3%83%B3%E3%82%B0%E3%82%B3%E3%83%BC%E3%83%B3/dp/B0CNT3K95S/ref=zg_bs_g_15218661_d_sccl_19/000-0000000-0000000?psc=1 -> {'url': 'https://www.amazon.co
2025-06-05 07:14:58 [INFO] result: result oioio:a8da308eef574b8a45325adbcbca80cd https://www.amazon.co.jp/%E4%BB%BB%E5%A4%A9%E5%A0%82-%E3%83%9E%E3%83%AA%E3%82%AA%E3%82%AB%E3%83%BC%E3%83%888-%E3%83%87%E3%83%A9%E3%83%83%E3%82%AF%E3%82%B9-Switch/dp/B01N12G06K/ref=zg_bs_g_videogames_d_sccl_5/000-0000000-0000000?psc=1 -> {'url': 'https://www.amazon.co
2025-06-05 07:15:40 [INFO] metrics: Gauges: {"memory_usage_rss": 70340608, "memory_usage_vms": 382672896, "memory_usage_percent": 0.20871175478168158, "system_memory_total": ***********, "system_memory_available": ***********, "system_memory_percent": 15.2}
2025-06-05 07:16:00 [INFO] metrics: Counters: {"fetch_count[project=yahoo]": 1, "fetch_count[project=mehoo]": 1, "fetch_count[project=roastmeat]": 1, "fetch_count[project=nohoo]": 1, "fetch_count[project=yahooit]": 1, "fetch_count[project=business]": 1, "fetch_count[project=lplplp]": 1, "fetch_count[project=local]": 1, "fetch_count[project=popopop]": 1, "fetch_count[project=0987]": 1, "fetch_count[project=sport]": 1, "fetch_count[project=woeld]": 1, "fetch_count[project=mkmkmk]": 1, "fetch_count[project=science]": 1, "fetch_count[project=entertainment]": 1, "fetch_count[project=domestic]": 1, "fetch_count[project=science2]": 1, "fetch_count[project=life]": 1, "fetch_count[project=01sono2]": 1, "fetch_count[project=2025]": 1, "fetch_count[project=0202020202]": 1, "fetch_count[project=040404040_1]": 1, "fetch_count[project=aiueo]": 1, "fetch_count[project=topics_1]": 1, "fetch_count[project=yahooo2]": 1, "fetch_count[project=yahoooo3]": 1, "fetch_count[project=uiuiuuiuiuiuiuiuiu]": 1, "fetch_count[project=aiueo6]": 1, "fetch_count[project=hemohemohemo]": 1, "fetch_count[project=bibibi]": 1, "fetch_count[project=uino]": 1, "fetch_count[project=tesr]": 1, "fetch_count[project=testbus]": 1, "fetch_count[project=domestic_1]": 1, "fetch_count[project=popopopo]": 4, "fetch_count[project=sportssssssss]": 1, "fetch_count[project=siense]": 1, "fetch_count[project=oioio]": 12, "fetch_count[project=amazon]": 3}
2025-06-05 07:16:40 [INFO] metrics: Gauges: {"memory_usage_rss": 70471680, "memory_usage_vms": 382672896, "memory_usage_percent": 0.2094924872795498, "system_memory_total": ***********, "system_memory_available": ***********, "system_memory_percent": 15.0}
2025-06-05 07:17:30 [INFO] metrics: Counters: {"fetch_count[project=yahoo]": 1, "fetch_count[project=mehoo]": 1, "fetch_count[project=roastmeat]": 1, "fetch_count[project=nohoo]": 1, "fetch_count[project=yahooit]": 1, "fetch_count[project=business]": 1, "fetch_count[project=lplplp]": 1, "fetch_count[project=local]": 1, "fetch_count[project=popopop]": 1, "fetch_count[project=0987]": 1, "fetch_count[project=sport]": 1, "fetch_count[project=woeld]": 1, "fetch_count[project=mkmkmk]": 1, "fetch_count[project=science]": 1, "fetch_count[project=entertainment]": 1, "fetch_count[project=domestic]": 1, "fetch_count[project=science2]": 1, "fetch_count[project=life]": 1, "fetch_count[project=01sono2]": 1, "fetch_count[project=2025]": 1, "fetch_count[project=0202020202]": 1, "fetch_count[project=040404040_1]": 1, "fetch_count[project=aiueo]": 1, "fetch_count[project=topics_1]": 1, "fetch_count[project=yahooo2]": 1, "fetch_count[project=yahoooo3]": 1, "fetch_count[project=uiuiuuiuiuiuiuiuiu]": 1, "fetch_count[project=aiueo6]": 1, "fetch_count[project=hemohemohemo]": 1, "fetch_count[project=bibibi]": 1, "fetch_count[project=uino]": 1, "fetch_count[project=tesr]": 1, "fetch_count[project=testbus]": 1, "fetch_count[project=domestic_1]": 1, "fetch_count[project=popopopo]": 7, "fetch_count[project=sportssssssss]": 1, "fetch_count[project=siense]": 1, "fetch_count[project=oioio]": 12, "fetch_count[project=amazon]": 3}
2025-06-05 07:17:40 [INFO] metrics: Gauges: {"memory_usage_rss": 70602752, "memory_usage_vms": 382808064, "memory_usage_percent": 0.21027321977741803, "system_memory_total": ***********, "system_memory_available": ***********, "system_memory_percent": 15.0}
2025-06-05 07:18:30 [INFO] metrics: Counters: {"fetch_count[project=yahoo]": 1, "fetch_count[project=mehoo]": 1, "fetch_count[project=roastmeat]": 1, "fetch_count[project=nohoo]": 1, "fetch_count[project=yahooit]": 1, "fetch_count[project=business]": 1, "fetch_count[project=lplplp]": 1, "fetch_count[project=local]": 1, "fetch_count[project=popopop]": 1, "fetch_count[project=0987]": 1, "fetch_count[project=sport]": 1, "fetch_count[project=woeld]": 1, "fetch_count[project=mkmkmk]": 1, "fetch_count[project=science]": 1, "fetch_count[project=entertainment]": 1, "fetch_count[project=domestic]": 1, "fetch_count[project=science2]": 1, "fetch_count[project=life]": 1, "fetch_count[project=01sono2]": 1, "fetch_count[project=2025]": 1, "fetch_count[project=0202020202]": 1, "fetch_count[project=040404040_1]": 1, "fetch_count[project=aiueo]": 1, "fetch_count[project=topics_1]": 1, "fetch_count[project=yahooo2]": 1, "fetch_count[project=yahoooo3]": 1, "fetch_count[project=uiuiuuiuiuiuiuiuiu]": 1, "fetch_count[project=aiueo6]": 1, "fetch_count[project=hemohemohemo]": 1, "fetch_count[project=bibibi]": 1, "fetch_count[project=uino]": 1, "fetch_count[project=tesr]": 1, "fetch_count[project=testbus]": 1, "fetch_count[project=domestic_1]": 1, "fetch_count[project=popopopo]": 9, "fetch_count[project=sportssssssss]": 1, "fetch_count[project=siense]": 1, "fetch_count[project=oioio]": 12, "fetch_count[project=amazon]": 3}
2025-06-05 07:18:40 [INFO] metrics: Gauges: {"memory_usage_rss": 70733824, "memory_usage_vms": 382808064, "memory_usage_percent": 0.21066358602635218, "system_memory_total": ***********, "system_memory_available": 28537044992, "system_memory_percent": 15.0}
2025-06-05 07:19:25 [INFO] result: result amazon:cf7b3a35721a45f47c66eac4bef9f1f8 https://www.amazon.co.jp/%E3%82%A2%E3%83%B3%E3%83%80%E3%83%BC%E3%82%A2%E3%83%BC%E3%83%9E%E3%83%BC-TECH-LOGO-Mens-%E6%97%A5%E6%9C%AC%E3%82%B5%E3%82%A4%E3%82%BAL%E7%9B%B8%E5%BD%93/dp/B07SYGTP6X/ref=zg_bs_g_15218661_d_sccl_13/000-0000000-0000000?psc=1 -> {'url': 'https://www.amazon.co
2025-06-05 07:19:30 [INFO] metrics: Counters: {"fetch_count[project=yahoo]": 1, "fetch_count[project=mehoo]": 1, "fetch_count[project=roastmeat]": 1, "fetch_count[project=nohoo]": 1, "fetch_count[project=yahooit]": 1, "fetch_count[project=business]": 1, "fetch_count[project=lplplp]": 1, "fetch_count[project=local]": 1, "fetch_count[project=popopop]": 1, "fetch_count[project=0987]": 1, "fetch_count[project=sport]": 1, "fetch_count[project=woeld]": 1, "fetch_count[project=mkmkmk]": 1, "fetch_count[project=science]": 1, "fetch_count[project=entertainment]": 1, "fetch_count[project=domestic]": 1, "fetch_count[project=science2]": 1, "fetch_count[project=life]": 1, "fetch_count[project=01sono2]": 1, "fetch_count[project=2025]": 1, "fetch_count[project=0202020202]": 1, "fetch_count[project=040404040_1]": 1, "fetch_count[project=aiueo]": 1, "fetch_count[project=topics_1]": 1, "fetch_count[project=yahooo2]": 1, "fetch_count[project=yahoooo3]": 1, "fetch_count[project=uiuiuuiuiuiuiuiuiu]": 1, "fetch_count[project=aiueo6]": 1, "fetch_count[project=hemohemohemo]": 1, "fetch_count[project=bibibi]": 1, "fetch_count[project=uino]": 1, "fetch_count[project=tesr]": 1, "fetch_count[project=testbus]": 1, "fetch_count[project=domestic_1]": 1, "fetch_count[project=popopopo]": 11, "fetch_count[project=sportssssssss]": 1, "fetch_count[project=siense]": 1, "fetch_count[project=oioio]": 12, "fetch_count[project=amazon]": 19}
2025-06-05 07:19:32 [INFO] result: result amazon:340f1dd511880a75afc97e1c315704a5 https://www.amazon.co.jp/%E3%83%AB%E3%82%B3%E3%83%83%E3%82%AF%E3%82%B9%E3%83%9D%E3%83%AB%E3%83%86%E3%82%A3%E3%83%95-%E3%83%8F%E3%83%BC%E3%83%95%E3%82%B8%E3%83%83%E3%83%97%E5%8D%8A%E8%A2%96%E3%82%B7%E3%83%A3%E3%83%84-%E5%8D%8A%E8%A2%96%E3%82%B7%E3%83%A3%E3%83%84-%E3%83%AF%E3%83%B3%E3%83%9D%E3%82%A4%E3%83%B3%E3%83%88-%E3%83%88%E3%83%AC%E3%83%BC%E3%83%8B%E3%83%B3%E3%82%B0/dp/B096VSMFRH/ref=zg_bs_g_15218661_d_sccl_25/000-0000000-0000000?psc=1 -> {'url': 'https://www.amazon.co
2025-06-05 07:19:40 [INFO] metrics: Gauges: {"memory_usage_rss": 70995968, "memory_usage_vms": 382808064, "memory_usage_percent": 0.21066358602635218, "system_memory_total": ***********, "system_memory_available": 28563689472, "system_memory_percent": 14.9}
{"timestamp": "2025-06-05T07:20:26.054095", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-05 07:20:26 [INFO] pyspider.libs.redis_fallback: ✅ Redis接続成功: redis://localhost:6379/0
2025-06-05 07:20:26 [INFO] pyspider.libs.redis_fallback: 🔍 Redis接続監視を開始しました
2025-06-05 07:20:26 [INFO] pyspider.libs.redis_fallback: ✅ Redis フォールバック機能を初期化しました
2025-06-05 07:20:26 [INFO] memory_optimizer: Memory optimizer initialized with max_memory_percent=80.0%, gc_interval=60s, check_interval=30s, auto_optimize=True
2025-06-05 07:20:26 [INFO] memory_optimizer: Memory monitoring started
2025-06-05 07:20:26 [INFO] root: Memory optimizer started for scheduler
2025-06-05 07:20:26 [INFO] root: Created PID file for scheduler: /tmp/pyspider_scheduler.pid
{"timestamp": "2025-06-05T07:20:28.256262", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-05 07:20:28 [INFO] root: Created PID file for fetcher: /tmp/pyspider_fetcher.pid
{"timestamp": "2025-06-05T07:20:30.420933", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-05 07:20:30 [INFO] root: Created PID file for processor: /tmp/pyspider_processor.pid
{"timestamp": "2025-06-05T07:20:32.588153", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-05 07:20:32 [INFO] root: Created PID file for result_worker: /tmp/pyspider_result_worker.pid
2025-06-05 07:20:32 [INFO] result: result_worker starting...
{"timestamp": "2025-06-05T07:20:34.470487", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-05 07:20:35 [INFO] libs.cache: Using Redis cache
2025-06-05 07:20:35 [INFO] root: Created PID file for webui: /tmp/pyspider_webui.pid
2025-06-05 07:21:26 [INFO] metrics: Gauges: {"memory_usage_rss": 69959680, "memory_usage_vms": 382664704, "memory_usage_percent": 0.2075772528707168, "system_memory_total": ***********, "system_memory_available": ***********, "system_memory_percent": 14.9}
2025-06-05 07:21:30 [INFO] metrics: Counters: {"fetch_count[project=yahoo]": 1, "fetch_count[project=mehoo]": 1, "fetch_count[project=roastmeat]": 1, "fetch_count[project=nohoo]": 1, "fetch_count[project=yahooit]": 1, "fetch_count[project=business]": 1, "fetch_count[project=lplplp]": 1, "fetch_count[project=local]": 1, "fetch_count[project=popopop]": 1, "fetch_count[project=0987]": 1, "fetch_count[project=sport]": 1, "fetch_count[project=woeld]": 1, "fetch_count[project=mkmkmk]": 1, "fetch_count[project=science]": 1, "fetch_count[project=entertainment]": 1, "fetch_count[project=domestic]": 1, "fetch_count[project=science2]": 1, "fetch_count[project=life]": 1, "fetch_count[project=01sono2]": 1, "fetch_count[project=2025]": 1, "fetch_count[project=0202020202]": 1, "fetch_count[project=040404040_1]": 1, "fetch_count[project=aiueo]": 1, "fetch_count[project=topics_1]": 1, "fetch_count[project=yahooo2]": 1, "fetch_count[project=yahoooo3]": 1, "fetch_count[project=uiuiuuiuiuiuiuiuiu]": 1, "fetch_count[project=aiueo6]": 1, "fetch_count[project=hemohemohemo]": 1, "fetch_count[project=bibibi]": 1, "fetch_count[project=uino]": 1, "fetch_count[project=tesr]": 1, "fetch_count[project=testbus]": 1, "fetch_count[project=domestic_1]": 1, "fetch_count[project=popopopo]": 3, "fetch_count[project=sportssssssss]": 1, "fetch_count[project=siense]": 1, "fetch_count[project=oioio]": 11, "fetch_count[project=amazon]": 1}
{"timestamp": "2025-06-05T07:22:38.017999", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-05 07:22:38 [INFO] pyspider.libs.redis_fallback: ✅ Redis接続成功: redis://localhost:6379/0
2025-06-05 07:22:38 [INFO] pyspider.libs.redis_fallback: 🔍 Redis接続監視を開始しました
2025-06-05 07:22:38 [INFO] pyspider.libs.redis_fallback: ✅ Redis フォールバック機能を初期化しました
2025-06-05 07:22:38 [INFO] memory_optimizer: Memory optimizer initialized with max_memory_percent=80.0%, gc_interval=60s, check_interval=30s, auto_optimize=True
2025-06-05 07:22:38 [INFO] memory_optimizer: Memory monitoring started
2025-06-05 07:22:38 [INFO] root: Memory optimizer started for scheduler
2025-06-05 07:22:38 [INFO] root: Created PID file for scheduler: /tmp/pyspider_scheduler.pid
{"timestamp": "2025-06-05T07:22:40.174002", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-05 07:22:40 [INFO] root: Created PID file for fetcher: /tmp/pyspider_fetcher.pid
{"timestamp": "2025-06-05T07:22:42.244784", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-05 07:22:42 [INFO] root: Created PID file for processor: /tmp/pyspider_processor.pid
{"timestamp": "2025-06-05T07:22:44.257935", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-05 07:22:44 [INFO] root: Created PID file for result_worker: /tmp/pyspider_result_worker.pid
2025-06-05 07:22:44 [INFO] result: result_worker starting...
{"timestamp": "2025-06-05T07:22:46.301706", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-05 07:22:47 [INFO] libs.cache: Using Redis cache
2025-06-05 07:22:47 [INFO] root: Created PID file for webui: /tmp/pyspider_webui.pid
{"timestamp": "2025-06-05T07:23:55.131361", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-05 07:23:55 [INFO] pyspider.libs.redis_fallback: ✅ Redis接続成功: redis://localhost:6379/0
2025-06-05 07:23:55 [INFO] pyspider.libs.redis_fallback: 🔍 Redis接続監視を開始しました
2025-06-05 07:23:55 [INFO] pyspider.libs.redis_fallback: ✅ Redis フォールバック機能を初期化しました
2025-06-05 07:23:55 [INFO] memory_optimizer: Memory optimizer initialized with max_memory_percent=80.0%, gc_interval=60s, check_interval=30s, auto_optimize=True
2025-06-05 07:23:55 [INFO] memory_optimizer: Memory monitoring started
2025-06-05 07:23:55 [INFO] root: Memory optimizer started for scheduler
2025-06-05 07:23:55 [INFO] root: Created PID file for scheduler: /tmp/pyspider_scheduler.pid
{"timestamp": "2025-06-05T07:23:57.312216", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-05 07:23:57 [INFO] root: Created PID file for fetcher: /tmp/pyspider_fetcher.pid
{"timestamp": "2025-06-05T07:23:59.531749", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-05 07:24:00 [INFO] root: Created PID file for processor: /tmp/pyspider_processor.pid
{"timestamp": "2025-06-05T07:24:01.510467", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-05 07:24:01 [INFO] root: Created PID file for result_worker: /tmp/pyspider_result_worker.pid
2025-06-05 07:24:01 [INFO] result: result_worker starting...
{"timestamp": "2025-06-05T07:24:03.524733", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-05 07:24:04 [INFO] libs.cache: Using Redis cache
2025-06-05 07:24:04 [INFO] root: Created PID file for webui: /tmp/pyspider_webui.pid
2025-06-05 07:24:55 [INFO] metrics: Gauges: {"memory_usage_rss": 67964928, "memory_usage_vms": 381485056, "memory_usage_percent": 0.2016363665197507, "system_memory_total": ***********, "system_memory_available": 28576985088, "system_memory_percent": 14.9}
{"timestamp": "2025-06-05T07:25:46.741915", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-05 07:25:47 [INFO] pyspider.libs.redis_fallback: ✅ Redis接続成功: redis://localhost:6379/0
2025-06-05 07:25:47 [INFO] pyspider.libs.redis_fallback: 🔍 Redis接続監視を開始しました
2025-06-05 07:25:47 [INFO] pyspider.libs.redis_fallback: ✅ Redis フォールバック機能を初期化しました
2025-06-05 07:25:47 [INFO] memory_optimizer: Memory optimizer initialized with max_memory_percent=80.0%, gc_interval=60s, check_interval=30s, auto_optimize=True
2025-06-05 07:25:47 [INFO] memory_optimizer: Memory monitoring started
2025-06-05 07:25:47 [INFO] root: Memory optimizer started for scheduler
2025-06-05 07:25:47 [INFO] root: Created PID file for scheduler: /tmp/pyspider_scheduler.pid
{"timestamp": "2025-06-05T07:25:48.844144", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-05 07:25:49 [INFO] root: Created PID file for fetcher: /tmp/pyspider_fetcher.pid
{"timestamp": "2025-06-05T07:25:50.961025", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-05 07:25:51 [INFO] root: Created PID file for processor: /tmp/pyspider_processor.pid
{"timestamp": "2025-06-05T07:25:52.983011", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-05 07:25:53 [INFO] root: Created PID file for result_worker: /tmp/pyspider_result_worker.pid
2025-06-05 07:25:53 [INFO] result: result_worker starting...
{"timestamp": "2025-06-05T07:25:54.981239", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-05 07:25:55 [INFO] libs.cache: Using Redis cache
2025-06-05 07:25:55 [INFO] root: Created PID file for webui: /tmp/pyspider_webui.pid
2025-06-05 07:26:47 [INFO] metrics: Gauges: {"memory_usage_rss": 67833856, "memory_usage_vms": 381444096, "memory_usage_percent": 0.2016363665197507, "system_memory_total": ***********, "system_memory_available": 28535476224, "system_memory_percent": 15.0}
2025-06-05 07:27:47 [INFO] metrics: Gauges: {"memory_usage_rss": 67833856, "memory_usage_vms": 381444096, "memory_usage_percent": 0.20202673276868482, "system_memory_total": ***********, "system_memory_available": 28544512000, "system_memory_percent": 15.0}
2025-06-05 07:28:29 [INFO] metrics: Counters: {"fetch_count[project=douyo]": 1}
2025-06-05 07:28:47 [INFO] metrics: Gauges: {"memory_usage_rss": 67833856, "memory_usage_vms": 382492672, "memory_usage_percent": 0.20202673276868482, "system_memory_total": ***********, "system_memory_available": 28536147968, "system_memory_percent": 15.0}
2025-06-05 07:29:47 [INFO] metrics: Gauges: {"memory_usage_rss": 67833856, "memory_usage_vms": 382492672, "memory_usage_percent": 0.20202673276868482, "system_memory_total": ***********, "system_memory_available": 28533264384, "system_memory_percent": 15.0}
2025-06-05 07:30:47 [INFO] metrics: Gauges: {"memory_usage_rss": 67833856, "memory_usage_vms": 382492672, "memory_usage_percent": 0.20202673276868482, "system_memory_total": ***********, "system_memory_available": 28509609984, "system_memory_percent": 15.1}
{"timestamp": "2025-06-05T07:31:28.161276", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-05 07:31:28 [INFO] pyspider.libs.redis_fallback: ✅ Redis接続成功: redis://localhost:6379/0
2025-06-05 07:31:28 [INFO] pyspider.libs.redis_fallback: 🔍 Redis接続監視を開始しました
2025-06-05 07:31:28 [INFO] pyspider.libs.redis_fallback: ✅ Redis フォールバック機能を初期化しました
2025-06-05 07:31:28 [INFO] memory_optimizer: Memory optimizer initialized with max_memory_percent=80.0%, gc_interval=60s, check_interval=30s, auto_optimize=True
2025-06-05 07:31:28 [INFO] memory_optimizer: Memory monitoring started
2025-06-05 07:31:28 [INFO] root: Memory optimizer started for scheduler
2025-06-05 07:31:28 [INFO] root: Created PID file for scheduler: /tmp/pyspider_scheduler.pid
{"timestamp": "2025-06-05T07:31:30.411906", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-05 07:31:30 [INFO] root: Created PID file for fetcher: /tmp/pyspider_fetcher.pid
{"timestamp": "2025-06-05T07:31:32.644414", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-05 07:31:33 [INFO] root: Created PID file for processor: /tmp/pyspider_processor.pid
{"timestamp": "2025-06-05T07:31:34.709421", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-05 07:31:34 [INFO] root: Created PID file for result_worker: /tmp/pyspider_result_worker.pid
2025-06-05 07:31:34 [INFO] result: result_worker starting...
{"timestamp": "2025-06-05T07:31:36.719158", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-05 07:31:37 [INFO] libs.cache: Using Redis cache
2025-06-05 07:31:37 [INFO] root: Created PID file for webui: /tmp/pyspider_webui.pid
2025-06-05 07:32:28 [INFO] metrics: Gauges: {"memory_usage_rss": 67895296, "memory_usage_vms": 381485056, "memory_usage_percent": 0.20220971694787265, "system_memory_total": ***********, "system_memory_available": 28406304768, "system_memory_percent": 15.4}
2025-06-05 07:33:28 [INFO] metrics: Gauges: {"memory_usage_rss": 67895296, "memory_usage_vms": 381485056, "memory_usage_percent": 0.20220971694787265, "system_memory_total": ***********, "system_memory_available": 28400893952, "system_memory_percent": 15.4}
{"timestamp": "2025-06-05T07:34:27.966273", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-05 07:34:28 [INFO] pyspider.libs.redis_fallback: ✅ Redis接続成功: redis://localhost:6379/0
2025-06-05 07:34:28 [INFO] pyspider.libs.redis_fallback: 🔍 Redis接続監視を開始しました
2025-06-05 07:34:28 [INFO] pyspider.libs.redis_fallback: ✅ Redis フォールバック機能を初期化しました
2025-06-05 07:34:28 [INFO] memory_optimizer: Memory optimizer initialized with max_memory_percent=80.0%, gc_interval=60s, check_interval=30s, auto_optimize=True
2025-06-05 07:34:28 [INFO] memory_optimizer: Memory monitoring started
2025-06-05 07:34:28 [INFO] root: Memory optimizer started for scheduler
2025-06-05 07:34:28 [INFO] root: Created PID file for scheduler: /tmp/pyspider_scheduler.pid
{"timestamp": "2025-06-05T07:34:30.139770", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-05 07:34:30 [INFO] root: Created PID file for fetcher: /tmp/pyspider_fetcher.pid
{"timestamp": "2025-06-05T07:34:32.303954", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-05 07:34:32 [INFO] root: Created PID file for processor: /tmp/pyspider_processor.pid
{"timestamp": "2025-06-05T07:34:34.279448", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-05 07:34:34 [INFO] root: Created PID file for result_worker: /tmp/pyspider_result_worker.pid
2025-06-05 07:34:34 [INFO] result: result_worker starting...
{"timestamp": "2025-06-05T07:34:36.416480", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-05 07:34:37 [INFO] libs.cache: Using Redis cache
2025-06-05 07:34:37 [INFO] root: Created PID file for webui: /tmp/pyspider_webui.pid
{"timestamp": "2025-06-05T15:42:06.758463", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-05 15:42:07 [INFO] pyspider.libs.redis_fallback: ✅ Redis接続成功: redis://localhost:6379/0
2025-06-05 15:42:07 [INFO] pyspider.libs.redis_fallback: 🔍 Redis接続監視を開始しました
2025-06-05 15:42:07 [INFO] pyspider.libs.redis_fallback: ✅ Redis フォールバック機能を初期化しました
2025-06-05 15:42:07 [INFO] memory_optimizer: Memory optimizer initialized with max_memory_percent=80.0%, gc_interval=60s, check_interval=30s, auto_optimize=True
2025-06-05 15:42:07 [INFO] memory_optimizer: Memory monitoring started
2025-06-05 15:42:07 [INFO] root: Memory optimizer started for scheduler
2025-06-05 15:42:07 [INFO] root: Created PID file for scheduler: /tmp/pyspider_scheduler.pid
{"timestamp": "2025-06-05T15:42:08.828318", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-05 15:42:09 [INFO] root: Created PID file for fetcher: /tmp/pyspider_fetcher.pid
{"timestamp": "2025-06-05T15:42:11.096625", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-05 15:42:11 [INFO] root: Created PID file for processor: /tmp/pyspider_processor.pid
{"timestamp": "2025-06-05T15:42:13.053514", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-05 15:42:13 [INFO] root: Created PID file for result_worker: /tmp/pyspider_result_worker.pid
2025-06-05 15:42:13 [INFO] result: result_worker starting...
{"timestamp": "2025-06-05T15:42:15.016583", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-05 15:42:15 [INFO] libs.cache: Using Redis cache
2025-06-05 15:42:15 [INFO] root: Created PID file for webui: /tmp/pyspider_webui.pid
2025-06-05 15:43:07 [INFO] metrics: Gauges: {"memory_usage_rss": 67981312, "memory_usage_vms": 381517824, "memory_usage_percent": 0.2024658947987357, "system_memory_total": ***********, "system_memory_available": 21541883904, "system_memory_percent": 35.8}
2025-06-05 15:44:07 [INFO] metrics: Gauges: {"memory_usage_rss": 67981312, "memory_usage_vms": 381517824, "memory_usage_percent": 0.2024658947987357, "system_memory_total": ***********, "system_memory_available": 21515890688, "system_memory_percent": 35.9}
2025-06-05 15:45:07 [INFO] metrics: Gauges: {"memory_usage_rss": 67981312, "memory_usage_vms": 381517824, "memory_usage_percent": 0.2024658947987357, "system_memory_total": ***********, "system_memory_available": 21513818112, "system_memory_percent": 35.9}
2025-06-05 15:46:07 [INFO] metrics: Gauges: {"memory_usage_rss": 67981312, "memory_usage_vms": 381517824, "memory_usage_percent": 0.2024658947987357, "system_memory_total": ***********, "system_memory_available": 21486252032, "system_memory_percent": 36.0}
2025-06-05 15:47:07 [INFO] metrics: Gauges: {"memory_usage_rss": 67981312, "memory_usage_vms": 381517824, "memory_usage_percent": 0.2024658947987357, "system_memory_total": ***********, "system_memory_available": 21983412224, "system_memory_percent": 34.5}
2025-06-05 15:48:07 [INFO] metrics: Gauges: {"memory_usage_rss": 67981312, "memory_usage_vms": 381517824, "memory_usage_percent": 0.2024658947987357, "system_memory_total": ***********, "system_memory_available": 21967155200, "system_memory_percent": 34.6}
2025-06-05 15:49:07 [INFO] metrics: Gauges: {"memory_usage_rss": 67981312, "memory_usage_vms": 381517824, "memory_usage_percent": 0.2024658947987357, "system_memory_total": ***********, "system_memory_available": 21959876608, "system_memory_percent": 34.6}
2025-06-05 15:50:07 [INFO] metrics: Gauges: {"memory_usage_rss": 67981312, "memory_usage_vms": 381517824, "memory_usage_percent": 0.2024658947987357, "system_memory_total": ***********, "system_memory_available": 21891186688, "system_memory_percent": 34.8}
2025-06-05 15:51:07 [INFO] metrics: Gauges: {"memory_usage_rss": 67981312, "memory_usage_vms": 381517824, "memory_usage_percent": 0.2024658947987357, "system_memory_total": ***********, "system_memory_available": 21879189504, "system_memory_percent": 34.8}
2025-06-05 15:52:07 [INFO] metrics: Gauges: {"memory_usage_rss": 67981312, "memory_usage_vms": 381517824, "memory_usage_percent": 0.2024658947987357, "system_memory_total": ***********, "system_memory_available": 21885300736, "system_memory_percent": 34.8}
2025-06-05 15:53:07 [INFO] metrics: Gauges: {"memory_usage_rss": 67981312, "memory_usage_vms": 381517824, "memory_usage_percent": 0.2024658947987357, "system_memory_total": ***********, "system_memory_available": 21865775104, "system_memory_percent": 34.9}
2025-06-05 15:54:07 [INFO] metrics: Gauges: {"memory_usage_rss": 67981312, "memory_usage_vms": 381517824, "memory_usage_percent": 0.2024658947987357, "system_memory_total": ***********, "system_memory_available": 21786513408, "system_memory_percent": 35.1}
2025-06-05 15:54:15 [INFO] metrics: Counters: {"fetch_count[project=final_test_project]": 1}
2025-06-05 15:55:07 [INFO] metrics: Gauges: {"memory_usage_rss": 67981312, "memory_usage_vms": 381517824, "memory_usage_percent": 0.2024658947987357, "system_memory_total": ***********, "system_memory_available": 21896634368, "system_memory_percent": 34.8}
2025-06-05 15:56:07 [INFO] metrics: Gauges: {"memory_usage_rss": 67981312, "memory_usage_vms": 381517824, "memory_usage_percent": 0.2024658947987357, "system_memory_total": ***********, "system_memory_available": 21603373056, "system_memory_percent": 35.7}
2025-06-05 15:57:07 [INFO] metrics: Gauges: {"memory_usage_rss": 67981312, "memory_usage_vms": 381517824, "memory_usage_percent": 0.2024658947987357, "system_memory_total": ***********, "system_memory_available": 21594304512, "system_memory_percent": 35.7}
2025-06-05 15:58:07 [INFO] metrics: Gauges: {"memory_usage_rss": 67981312, "memory_usage_vms": 381517824, "memory_usage_percent": 0.2024658947987357, "system_memory_total": ***********, "system_memory_available": 21593550848, "system_memory_percent": 35.7}
2025-06-05 15:59:07 [INFO] metrics: Gauges: {"memory_usage_rss": 67981312, "memory_usage_vms": 381517824, "memory_usage_percent": 0.2024658947987357, "system_memory_total": ***********, "system_memory_available": 21555281920, "system_memory_percent": 35.8}
2025-06-05 16:00:07 [INFO] metrics: Gauges: {"memory_usage_rss": 67981312, "memory_usage_vms": 381517824, "memory_usage_percent": 0.2024658947987357, "system_memory_total": ***********, "system_memory_available": 21516914688, "system_memory_percent": 35.9}
2025-06-05 16:01:07 [INFO] metrics: Gauges: {"memory_usage_rss": 67981312, "memory_usage_vms": 381517824, "memory_usage_percent": 0.2024658947987357, "system_memory_total": ***********, "system_memory_available": 21509427200, "system_memory_percent": 35.9}
2025-06-05 16:02:07 [INFO] metrics: Gauges: {"memory_usage_rss": 67981312, "memory_usage_vms": 381517824, "memory_usage_percent": 0.2024658947987357, "system_memory_total": ***********, "system_memory_available": 21538443264, "system_memory_percent": 35.9}
2025-06-05 16:03:07 [INFO] metrics: Gauges: {"memory_usage_rss": 67981312, "memory_usage_vms": 381517824, "memory_usage_percent": 0.2024658947987357, "system_memory_total": ***********, "system_memory_available": 21543006208, "system_memory_percent": 35.8}
{"timestamp": "2025-06-05T16:58:49.894212", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-05 16:58:51 [INFO] libs.cache: Using Redis cache
2025-06-05 16:58:51 [INFO] root: Created PID file for webui: /tmp/pyspider_webui.pid
{"timestamp": "2025-06-05T17:00:08.483030", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-05 17:00:09 [INFO] pyspider.libs.redis_fallback: ✅ Redis接続成功: redis://localhost:6379/0
2025-06-05 17:00:09 [INFO] pyspider.libs.redis_fallback: 🔍 Redis接続監視を開始しました
2025-06-05 17:00:09 [INFO] pyspider.libs.redis_fallback: ✅ Redis フォールバック機能を初期化しました
2025-06-05 17:00:09 [INFO] memory_optimizer: Memory optimizer initialized with max_memory_percent=80.0%, gc_interval=60s, check_interval=30s, auto_optimize=True
2025-06-05 17:00:09 [INFO] memory_optimizer: Memory monitoring started
2025-06-05 17:00:09 [INFO] root: Memory optimizer started for scheduler
2025-06-05 17:00:09 [INFO] root: Created PID file for scheduler: /tmp/pyspider_scheduler.pid
2025-06-05 17:01:09 [INFO] metrics: Gauges: {"memory_usage_rss": 68333568, "memory_usage_vms": 458194944, "memory_usage_percent": 0.20351500409274614, "system_memory_total": ***********, "system_memory_available": 21911257088, "system_memory_percent": 34.7}
2025-06-05 17:02:09 [INFO] metrics: Gauges: {"memory_usage_rss": 68333568, "memory_usage_vms": 458194944, "memory_usage_percent": 0.20351500409274614, "system_memory_total": ***********, "system_memory_available": 21659451392, "system_memory_percent": 35.5}
2025-06-05 17:03:09 [INFO] metrics: Gauges: {"memory_usage_rss": 68333568, "memory_usage_vms": 458194944, "memory_usage_percent": 0.20351500409274614, "system_memory_total": ***********, "system_memory_available": 21892304896, "system_memory_percent": 34.8}
2025-06-05 17:04:09 [INFO] metrics: Gauges: {"memory_usage_rss": 68333568, "memory_usage_vms": 458194944, "memory_usage_percent": 0.20351500409274614, "system_memory_total": ***********, "system_memory_available": 22202187776, "system_memory_percent": 33.9}
2025-06-05 17:05:09 [INFO] metrics: Gauges: {"memory_usage_rss": 68333568, "memory_usage_vms": 458194944, "memory_usage_percent": 0.20351500409274614, "system_memory_total": ***********, "system_memory_available": 22190436352, "system_memory_percent": 33.9}
2025-06-05 17:06:09 [INFO] metrics: Gauges: {"memory_usage_rss": 68333568, "memory_usage_vms": 458194944, "memory_usage_percent": 0.20351500409274614, "system_memory_total": ***********, "system_memory_available": 22188986368, "system_memory_percent": 33.9}
2025-06-05 17:07:09 [INFO] metrics: Gauges: {"memory_usage_rss": 68333568, "memory_usage_vms": 458194944, "memory_usage_percent": 0.20351500409274614, "system_memory_total": ***********, "system_memory_available": 21950611456, "system_memory_percent": 34.6}
2025-06-05 17:08:09 [INFO] metrics: Gauges: {"memory_usage_rss": 68333568, "memory_usage_vms": 458194944, "memory_usage_percent": 0.20351500409274614, "system_memory_total": ***********, "system_memory_available": 21937631232, "system_memory_percent": 34.7}
2025-06-05 17:09:09 [INFO] metrics: Gauges: {"memory_usage_rss": 68333568, "memory_usage_vms": 458194944, "memory_usage_percent": 0.20351500409274614, "system_memory_total": ***********, "system_memory_available": 21978554368, "system_memory_percent": 34.5}
2025-06-05 17:10:09 [INFO] metrics: Gauges: {"memory_usage_rss": 68333568, "memory_usage_vms": 458194944, "memory_usage_percent": 0.20351500409274614, "system_memory_total": ***********, "system_memory_available": 21927460864, "system_memory_percent": 34.7}
2025-06-05 17:11:09 [INFO] metrics: Gauges: {"memory_usage_rss": 68333568, "memory_usage_vms": 458194944, "memory_usage_percent": 0.20351500409274614, "system_memory_total": ***********, "system_memory_available": 21822017536, "system_memory_percent": 35.0}
2025-06-05 17:12:09 [INFO] metrics: Gauges: {"memory_usage_rss": 68333568, "memory_usage_vms": 458194944, "memory_usage_percent": 0.20351500409274614, "system_memory_total": ***********, "system_memory_available": 21613379584, "system_memory_percent": 35.6}
{"timestamp": "2025-06-05T17:12:34.470155", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-05 17:12:35 [INFO] pyspider.libs.redis_fallback: ✅ Redis接続成功: redis://localhost:6379/0
2025-06-05 17:12:35 [INFO] pyspider.libs.redis_fallback: 🔍 Redis接続監視を開始しました
2025-06-05 17:12:35 [INFO] pyspider.libs.redis_fallback: ✅ Redis フォールバック機能を初期化しました
2025-06-05 17:12:35 [INFO] memory_optimizer: Memory optimizer initialized with max_memory_percent=80.0%, gc_interval=60s, check_interval=30s, auto_optimize=True
2025-06-05 17:12:35 [INFO] memory_optimizer: Memory monitoring started
2025-06-05 17:12:35 [INFO] root: Memory optimizer started for scheduler
2025-06-05 17:12:35 [INFO] root: Created PID file for scheduler: /tmp/pyspider_scheduler.pid
{"timestamp": "2025-06-05T17:12:36.677806", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-05 17:12:37 [INFO] root: Created PID file for fetcher: /tmp/pyspider_fetcher.pid
{"timestamp": "2025-06-05T17:12:38.894020", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-05 17:12:39 [INFO] root: Created PID file for processor: /tmp/pyspider_processor.pid
{"timestamp": "2025-06-05T17:12:40.833444", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-05 17:12:40 [INFO] root: Created PID file for result_worker: /tmp/pyspider_result_worker.pid
2025-06-05 17:12:40 [INFO] result: result_worker starting...
{"timestamp": "2025-06-05T17:12:42.978052", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-05 17:12:44 [INFO] libs.cache: Using Redis cache
2025-06-05 17:12:44 [INFO] root: Created PID file for webui: /tmp/pyspider_webui.pid
2025-06-05 17:13:35 [INFO] metrics: Gauges: {"memory_usage_rss": 68001792, "memory_usage_vms": 381452288, "memory_usage_percent": 0.20213652327619752, "system_memory_total": ***********, "system_memory_available": 21402058752, "system_memory_percent": 36.3}
{"timestamp": "2025-06-05T17:17:50.571071", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-05 17:17:51 [INFO] pyspider.libs.redis_fallback: ✅ Redis接続成功: redis://localhost:6379/0
2025-06-05 17:17:51 [INFO] pyspider.libs.redis_fallback: 🔍 Redis接続監視を開始しました
2025-06-05 17:17:51 [INFO] pyspider.libs.redis_fallback: ✅ Redis フォールバック機能を初期化しました
2025-06-05 17:17:51 [INFO] memory_optimizer: Memory optimizer initialized with max_memory_percent=80.0%, gc_interval=60s, check_interval=30s, auto_optimize=True
2025-06-05 17:17:51 [INFO] memory_optimizer: Memory monitoring started
2025-06-05 17:17:51 [INFO] root: Memory optimizer started for scheduler
2025-06-05 17:17:51 [INFO] root: Created PID file for scheduler: /tmp/pyspider_scheduler.pid
{"timestamp": "2025-06-05T17:17:52.789183", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-05 17:17:53 [INFO] root: Created PID file for fetcher: /tmp/pyspider_fetcher.pid
{"timestamp": "2025-06-05T17:17:54.926024", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-05 17:17:55 [INFO] root: Created PID file for processor: /tmp/pyspider_processor.pid
{"timestamp": "2025-06-05T17:17:56.955463", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-05 17:17:57 [INFO] root: Created PID file for result_worker: /tmp/pyspider_result_worker.pid
2025-06-05 17:17:57 [INFO] result: result_worker starting...
{"timestamp": "2025-06-05T17:17:58.953008", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-05 17:17:59 [INFO] libs.cache: Using Redis cache
2025-06-05 17:17:59 [INFO] root: Created PID file for webui: /tmp/pyspider_webui.pid
2025-06-05 17:18:51 [INFO] metrics: Gauges: {"memory_usage_rss": 67936256, "memory_usage_vms": 381452288, "memory_usage_percent": 0.2023317064006646, "system_memory_total": ***********, "system_memory_available": 21687554048, "system_memory_percent": 35.4}
2025-06-05 17:19:51 [INFO] metrics: Gauges: {"memory_usage_rss": 67936256, "memory_usage_vms": 381452288, "memory_usage_percent": 0.2023317064006646, "system_memory_total": ***********, "system_memory_available": 21830283264, "system_memory_percent": 35.0}
2025-06-05 17:20:51 [INFO] metrics: Gauges: {"memory_usage_rss": 67936256, "memory_usage_vms": 381452288, "memory_usage_percent": 0.2023317064006646, "system_memory_total": ***********, "system_memory_available": 21584207872, "system_memory_percent": 35.7}
{"timestamp": "2025-06-05T17:22:29.333459", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-05 17:22:29 [INFO] pyspider.libs.redis_fallback: ✅ Redis接続成功: redis://localhost:6379/0
2025-06-05 17:22:29 [INFO] pyspider.libs.redis_fallback: 🔍 Redis接続監視を開始しました
2025-06-05 17:22:29 [INFO] pyspider.libs.redis_fallback: ✅ Redis フォールバック機能を初期化しました
2025-06-05 17:22:29 [INFO] memory_optimizer: Memory optimizer initialized with max_memory_percent=80.0%, gc_interval=60s, check_interval=30s, auto_optimize=True
2025-06-05 17:22:29 [INFO] memory_optimizer: Memory monitoring started
2025-06-05 17:22:29 [INFO] root: Memory optimizer started for scheduler
2025-06-05 17:22:29 [INFO] root: Created PID file for scheduler: /tmp/pyspider_scheduler.pid
{"timestamp": "2025-06-05T17:22:31.356062", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-05 17:22:31 [INFO] root: Created PID file for fetcher: /tmp/pyspider_fetcher.pid
{"timestamp": "2025-06-05T17:22:33.516850", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-05 17:22:34 [INFO] root: Created PID file for processor: /tmp/pyspider_processor.pid
{"timestamp": "2025-06-05T17:22:35.458827", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-05 17:22:35 [INFO] root: Created PID file for result_worker: /tmp/pyspider_result_worker.pid
2025-06-05 17:22:35 [INFO] result: result_worker starting...
{"timestamp": "2025-06-05T17:22:37.527435", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-05 17:22:38 [INFO] libs.cache: Using Redis cache
2025-06-05 17:22:38 [INFO] root: Created PID file for webui: /tmp/pyspider_webui.pid
2025-06-05 17:23:29 [INFO] metrics: Gauges: {"memory_usage_rss": 67846144, "memory_usage_vms": 382500864, "memory_usage_percent": 0.2020633296045224, "system_memory_total": ***********, "system_memory_available": 21951836160, "system_memory_percent": 34.6}
2025-06-05 17:24:29 [INFO] metrics: Gauges: {"memory_usage_rss": 67846144, "memory_usage_vms": 382500864, "memory_usage_percent": 0.2020633296045224, "system_memory_total": ***********, "system_memory_available": 21792075776, "system_memory_percent": 35.1}
2025-06-05 17:25:30 [INFO] metrics: Gauges: {"memory_usage_rss": 67846144, "memory_usage_vms": 382500864, "memory_usage_percent": 0.2020633296045224, "system_memory_total": ***********, "system_memory_available": 21412265984, "system_memory_percent": 36.2}
2025-06-05 17:26:30 [INFO] metrics: Gauges: {"memory_usage_rss": 67846144, "memory_usage_vms": 382500864, "memory_usage_percent": 0.2020633296045224, "system_memory_total": ***********, "system_memory_available": 21254672384, "system_memory_percent": 36.7}
2025-06-05 17:27:30 [INFO] metrics: Gauges: {"memory_usage_rss": 67846144, "memory_usage_vms": 382500864, "memory_usage_percent": 0.2020633296045224, "system_memory_total": ***********, "system_memory_available": 21860671488, "system_memory_percent": 34.9}
2025-06-05 17:28:30 [INFO] metrics: Gauges: {"memory_usage_rss": 67846144, "memory_usage_vms": 382500864, "memory_usage_percent": 0.2020633296045224, "system_memory_total": ***********, "system_memory_available": 21974544384, "system_memory_percent": 34.6}
2025-06-05 17:29:30 [INFO] metrics: Gauges: {"memory_usage_rss": 67846144, "memory_usage_vms": 382500864, "memory_usage_percent": 0.2020633296045224, "system_memory_total": ***********, "system_memory_available": 21741371392, "system_memory_percent": 35.2}
2025-06-05 17:30:30 [INFO] metrics: Gauges: {"memory_usage_rss": 67846144, "memory_usage_vms": 382500864, "memory_usage_percent": 0.2020633296045224, "system_memory_total": ***********, "system_memory_available": 21824942080, "system_memory_percent": 35.0}
2025-06-05 17:31:30 [INFO] metrics: Gauges: {"memory_usage_rss": 67846144, "memory_usage_vms": 382500864, "memory_usage_percent": 0.2020633296045224, "system_memory_total": ***********, "system_memory_available": 21911293952, "system_memory_percent": 34.7}
2025-06-05 17:32:30 [INFO] metrics: Gauges: {"memory_usage_rss": 67846144, "memory_usage_vms": 382500864, "memory_usage_percent": 0.2020633296045224, "system_memory_total": ***********, "system_memory_available": 21680111616, "system_memory_percent": 35.4}
2025-06-05 17:33:30 [INFO] metrics: Gauges: {"memory_usage_rss": 67846144, "memory_usage_vms": 382500864, "memory_usage_percent": 0.2020633296045224, "system_memory_total": ***********, "system_memory_available": 21687767040, "system_memory_percent": 35.4}
2025-06-05 17:34:30 [INFO] metrics: Gauges: {"memory_usage_rss": 67846144, "memory_usage_vms": 382500864, "memory_usage_percent": 0.2020633296045224, "system_memory_total": ***********, "system_memory_available": 21718892544, "system_memory_percent": 35.3}
2025-06-05 17:35:30 [INFO] metrics: Gauges: {"memory_usage_rss": 67846144, "memory_usage_vms": 382500864, "memory_usage_percent": 0.2020633296045224, "system_memory_total": ***********, "system_memory_available": 21751824384, "system_memory_percent": 35.2}
2025-06-05 17:36:30 [INFO] metrics: Gauges: {"memory_usage_rss": 67846144, "memory_usage_vms": 382500864, "memory_usage_percent": 0.2020633296045224, "system_memory_total": ***********, "system_memory_available": 21562810368, "system_memory_percent": 35.8}
2025-06-05 17:37:30 [INFO] metrics: Gauges: {"memory_usage_rss": 67846144, "memory_usage_vms": 382500864, "memory_usage_percent": 0.2020633296045224, "system_memory_total": ***********, "system_memory_available": 21585612800, "system_memory_percent": 35.7}
2025-06-05 17:38:30 [INFO] metrics: Gauges: {"memory_usage_rss": 67977216, "memory_usage_vms": 382500864, "memory_usage_percent": 0.2020633296045224, "system_memory_total": ***********, "system_memory_available": 21674102784, "system_memory_percent": 35.4}
2025-06-05 17:39:30 [INFO] metrics: Gauges: {"memory_usage_rss": 67977216, "memory_usage_vms": 382500864, "memory_usage_percent": 0.20245369585345652, "system_memory_total": ***********, "system_memory_available": 21442527232, "system_memory_percent": 36.1}
{"timestamp": "2025-06-05T17:41:00.009488", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-05 17:41:00 [INFO] pyspider.libs.redis_fallback: ✅ Redis接続成功: redis://localhost:6379/0
2025-06-05 17:41:00 [INFO] pyspider.libs.redis_fallback: 🔍 Redis接続監視を開始しました
2025-06-05 17:41:00 [INFO] pyspider.libs.redis_fallback: ✅ Redis フォールバック機能を初期化しました
2025-06-05 17:41:00 [INFO] memory_optimizer: Memory optimizer initialized with max_memory_percent=80.0%, gc_interval=60s, check_interval=30s, auto_optimize=True
2025-06-05 17:41:00 [INFO] memory_optimizer: Memory monitoring started
2025-06-05 17:41:00 [INFO] root: Memory optimizer started for scheduler
2025-06-05 17:41:00 [INFO] root: Created PID file for scheduler: /tmp/pyspider_scheduler.pid
{"timestamp": "2025-06-05T17:41:02.162317", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-05 17:41:02 [INFO] root: Created PID file for fetcher: /tmp/pyspider_fetcher.pid
{"timestamp": "2025-06-05T17:41:04.329325", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-05 17:41:04 [INFO] root: Created PID file for processor: /tmp/pyspider_processor.pid
{"timestamp": "2025-06-05T17:41:06.366901", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-05 17:41:06 [INFO] root: Created PID file for result_worker: /tmp/pyspider_result_worker.pid
2025-06-05 17:41:06 [INFO] result: result_worker starting...
{"timestamp": "2025-06-05T17:41:08.362561", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-05 17:41:09 [INFO] libs.cache: Using Redis cache
2025-06-05 17:41:09 [INFO] root: Created PID file for webui: /tmp/pyspider_webui.pid
2025-06-05 17:42:00 [INFO] metrics: Gauges: {"memory_usage_rss": 68132864, "memory_usage_vms": 381448192, "memory_usage_percent": 0.20291725577406575, "system_memory_total": ***********, "system_memory_available": 21502316544, "system_memory_percent": 36.0}
2025-06-05 17:43:00 [INFO] metrics: Gauges: {"memory_usage_rss": 68132864, "memory_usage_vms": 381448192, "memory_usage_percent": 0.20291725577406575, "system_memory_total": ***********, "system_memory_available": 21336215552, "system_memory_percent": 36.5}
2025-06-05 17:44:00 [INFO] metrics: Gauges: {"memory_usage_rss": 68132864, "memory_usage_vms": 381448192, "memory_usage_percent": 0.20291725577406575, "system_memory_total": ***********, "system_memory_available": 21405806592, "system_memory_percent": 36.2}
2025-06-05 17:45:00 [INFO] metrics: Gauges: {"memory_usage_rss": 68132864, "memory_usage_vms": 381448192, "memory_usage_percent": 0.20291725577406575, "system_memory_total": ***********, "system_memory_available": 21449560064, "system_memory_percent": 36.1}
2025-06-05 17:46:00 [INFO] metrics: Gauges: {"memory_usage_rss": 68132864, "memory_usage_vms": 381448192, "memory_usage_percent": 0.20291725577406575, "system_memory_total": ***********, "system_memory_available": 21310283776, "system_memory_percent": 36.5}
2025-06-05 17:47:00 [INFO] metrics: Gauges: {"memory_usage_rss": 68132864, "memory_usage_vms": 381448192, "memory_usage_percent": 0.20291725577406575, "system_memory_total": ***********, "system_memory_available": 21415006208, "system_memory_percent": 36.2}
2025-06-05 17:48:00 [INFO] metrics: Gauges: {"memory_usage_rss": 68132864, "memory_usage_vms": 381448192, "memory_usage_percent": 0.20291725577406575, "system_memory_total": ***********, "system_memory_available": 21475688448, "system_memory_percent": 36.0}
2025-06-05 17:49:00 [INFO] metrics: Gauges: {"memory_usage_rss": 68132864, "memory_usage_vms": 381448192, "memory_usage_percent": 0.20291725577406575, "system_memory_total": ***********, "system_memory_available": 21331963904, "system_memory_percent": 36.5}
{"timestamp": "2025-06-05T17:50:35.165218", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-05 17:50:35 [INFO] pyspider.libs.redis_fallback: ✅ Redis接続成功: redis://localhost:6379/0
2025-06-05 17:50:35 [INFO] pyspider.libs.redis_fallback: 🔍 Redis接続監視を開始しました
2025-06-05 17:50:35 [INFO] pyspider.libs.redis_fallback: ✅ Redis フォールバック機能を初期化しました
2025-06-05 17:50:35 [INFO] memory_optimizer: Memory optimizer initialized with max_memory_percent=80.0%, gc_interval=60s, check_interval=30s, auto_optimize=True
2025-06-05 17:50:35 [INFO] memory_optimizer: Memory monitoring started
2025-06-05 17:50:35 [INFO] root: Memory optimizer started for scheduler
2025-06-05 17:50:35 [INFO] root: Created PID file for scheduler: /tmp/pyspider_scheduler.pid
{"timestamp": "2025-06-05T17:50:37.319202", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-05 17:50:37 [INFO] root: Created PID file for fetcher: /tmp/pyspider_fetcher.pid
{"timestamp": "2025-06-05T17:50:39.478963", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-05 17:50:40 [INFO] root: Created PID file for processor: /tmp/pyspider_processor.pid
{"timestamp": "2025-06-05T17:50:41.483947", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-05 17:50:41 [INFO] root: Created PID file for result_worker: /tmp/pyspider_result_worker.pid
2025-06-05 17:50:41 [INFO] result: result_worker starting...
{"timestamp": "2025-06-05T17:50:43.541923", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-05 17:50:44 [INFO] libs.cache: Using Redis cache
2025-06-05 17:50:44 [INFO] root: Created PID file for webui: /tmp/pyspider_webui.pid
2025-06-05 17:51:35 [INFO] metrics: Gauges: {"memory_usage_rss": 67747840, "memory_usage_vms": 381460480, "memory_usage_percent": 0.20098982241995356, "system_memory_total": ***********, "system_memory_available": 21788073984, "system_memory_percent": 35.1}
2025-06-05 17:52:35 [INFO] metrics: Gauges: {"memory_usage_rss": 67747840, "memory_usage_vms": 381460480, "memory_usage_percent": 0.20177055491782178, "system_memory_total": ***********, "system_memory_available": 21898686464, "system_memory_percent": 34.8}
2025-06-05 17:53:35 [INFO] metrics: Gauges: {"memory_usage_rss": 67747840, "memory_usage_vms": 381460480, "memory_usage_percent": 0.20177055491782178, "system_memory_total": ***********, "system_memory_available": 21684248576, "system_memory_percent": 35.4}
2025-06-05 17:54:35 [INFO] metrics: Gauges: {"memory_usage_rss": 67747840, "memory_usage_vms": 381460480, "memory_usage_percent": 0.20177055491782178, "system_memory_total": ***********, "system_memory_available": 21727760384, "system_memory_percent": 35.3}
2025-06-05 17:55:35 [INFO] metrics: Gauges: {"memory_usage_rss": 67747840, "memory_usage_vms": 381460480, "memory_usage_percent": 0.20177055491782178, "system_memory_total": ***********, "system_memory_available": 21864955904, "system_memory_percent": 34.9}
2025-06-05 17:56:35 [INFO] metrics: Gauges: {"memory_usage_rss": 67747840, "memory_usage_vms": 381460480, "memory_usage_percent": 0.20177055491782178, "system_memory_total": ***********, "system_memory_available": 21683884032, "system_memory_percent": 35.4}
{"timestamp": "2025-06-05T17:57:14.426454", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-05 17:57:15 [INFO] pyspider.libs.redis_fallback: ✅ Redis接続成功: redis://localhost:6379/0
2025-06-05 17:57:15 [INFO] pyspider.libs.redis_fallback: 🔍 Redis接続監視を開始しました
2025-06-05 17:57:15 [INFO] pyspider.libs.redis_fallback: ✅ Redis フォールバック機能を初期化しました
2025-06-05 17:57:15 [INFO] memory_optimizer: Memory optimizer initialized with max_memory_percent=80.0%, gc_interval=60s, check_interval=30s, auto_optimize=True
2025-06-05 17:57:15 [INFO] memory_optimizer: Memory monitoring started
2025-06-05 17:57:15 [INFO] root: Memory optimizer started for scheduler
2025-06-05 17:57:15 [INFO] root: Created PID file for scheduler: /tmp/pyspider_scheduler.pid
{"timestamp": "2025-06-05T17:57:16.549217", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-05 17:57:16 [INFO] root: Created PID file for fetcher: /tmp/pyspider_fetcher.pid
{"timestamp": "2025-06-05T17:57:18.732994", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-05 17:57:19 [INFO] root: Created PID file for processor: /tmp/pyspider_processor.pid
{"timestamp": "2025-06-05T17:57:20.734188", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-05 17:57:20 [INFO] root: Created PID file for result_worker: /tmp/pyspider_result_worker.pid
2025-06-05 17:57:20 [INFO] result: result_worker starting...
{"timestamp": "2025-06-05T17:57:22.778772", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-05 17:57:23 [INFO] libs.cache: Using Redis cache
2025-06-05 17:57:24 [INFO] root: Created PID file for webui: /tmp/pyspider_webui.pid
2025-06-05 17:58:15 [INFO] metrics: Gauges: {"memory_usage_rss": 67633152, "memory_usage_vms": 381452288, "memory_usage_percent": 0.20142898445000443, "system_memory_total": ***********, "system_memory_available": 21576757248, "system_memory_percent": 35.7}
2025-06-05 17:59:15 [INFO] metrics: Gauges: {"memory_usage_rss": 67764224, "memory_usage_vms": 381452288, "memory_usage_percent": 0.20181935069893855, "system_memory_total": ***********, "system_memory_available": 21348179968, "system_memory_percent": 36.4}
2025-06-05 18:00:15 [INFO] metrics: Gauges: {"memory_usage_rss": 67764224, "memory_usage_vms": 381452288, "memory_usage_percent": 0.20181935069893855, "system_memory_total": ***********, "system_memory_available": 21398016000, "system_memory_percent": 36.3}
2025-06-05 18:01:15 [INFO] metrics: Gauges: {"memory_usage_rss": 67764224, "memory_usage_vms": 381452288, "memory_usage_percent": 0.20181935069893855, "system_memory_total": ***********, "system_memory_available": 21625483264, "system_memory_percent": 35.6}
2025-06-05 18:02:15 [INFO] metrics: Gauges: {"memory_usage_rss": 67764224, "memory_usage_vms": 381452288, "memory_usage_percent": 0.20181935069893855, "system_memory_total": ***********, "system_memory_available": 21711712256, "system_memory_percent": 35.3}
2025-06-05 18:03:15 [INFO] metrics: Gauges: {"memory_usage_rss": 67764224, "memory_usage_vms": 381452288, "memory_usage_percent": 0.20181935069893855, "system_memory_total": ***********, "system_memory_available": 21822623744, "system_memory_percent": 35.0}
2025-06-05 18:04:15 [INFO] metrics: Gauges: {"memory_usage_rss": 67764224, "memory_usage_vms": 381452288, "memory_usage_percent": 0.20181935069893855, "system_memory_total": ***********, "system_memory_available": 21747986432, "system_memory_percent": 35.2}
2025-06-05 18:05:15 [INFO] metrics: Gauges: {"memory_usage_rss": 67764224, "memory_usage_vms": 381452288, "memory_usage_percent": 0.20181935069893855, "system_memory_total": ***********, "system_memory_available": 21331496960, "system_memory_percent": 36.5}
2025-06-05 18:06:15 [INFO] metrics: Gauges: {"memory_usage_rss": 67764224, "memory_usage_vms": 381452288, "memory_usage_percent": 0.20181935069893855, "system_memory_total": ***********, "system_memory_available": 21265178624, "system_memory_percent": 36.7}
2025-06-05 18:07:15 [INFO] metrics: Gauges: {"memory_usage_rss": 67764224, "memory_usage_vms": 381452288, "memory_usage_percent": 0.20181935069893855, "system_memory_total": ***********, "system_memory_available": 21448220672, "system_memory_percent": 36.1}
2025-06-05 18:08:15 [INFO] metrics: Gauges: {"memory_usage_rss": 67764224, "memory_usage_vms": 381452288, "memory_usage_percent": 0.20181935069893855, "system_memory_total": ***********, "system_memory_available": 21378887680, "system_memory_percent": 36.3}
2025-06-05 18:09:15 [INFO] metrics: Gauges: {"memory_usage_rss": 67764224, "memory_usage_vms": 381452288, "memory_usage_percent": 0.20181935069893855, "system_memory_total": ***********, "system_memory_available": 21358596096, "system_memory_percent": 36.4}
{"timestamp": "2025-06-05T18:10:24.965076", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-05 18:10:25 [INFO] pyspider.libs.redis_fallback: ✅ Redis接続成功: redis://localhost:6379/0
2025-06-05 18:10:25 [INFO] pyspider.libs.redis_fallback: 🔍 Redis接続監視を開始しました
2025-06-05 18:10:25 [INFO] pyspider.libs.redis_fallback: ✅ Redis フォールバック機能を初期化しました
2025-06-05 18:10:25 [INFO] memory_optimizer: Memory optimizer initialized with max_memory_percent=80.0%, gc_interval=60s, check_interval=30s, auto_optimize=True
2025-06-05 18:10:25 [INFO] memory_optimizer: Memory monitoring started
2025-06-05 18:10:25 [INFO] root: Memory optimizer started for scheduler
2025-06-05 18:10:25 [INFO] root: Created PID file for scheduler: /tmp/pyspider_scheduler.pid
{"timestamp": "2025-06-05T18:10:27.107680", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-05 18:10:27 [INFO] root: Created PID file for fetcher: /tmp/pyspider_fetcher.pid
{"timestamp": "2025-06-05T18:10:29.335328", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-05 18:10:30 [INFO] root: Created PID file for processor: /tmp/pyspider_processor.pid
{"timestamp": "2025-06-05T18:10:31.341177", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-05 18:10:32 [INFO] root: Created PID file for result_worker: /tmp/pyspider_result_worker.pid
2025-06-05 18:10:32 [INFO] result: result_worker starting...
{"timestamp": "2025-06-05T18:10:33.663371", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-05 18:10:33 [ERROR] root: not processing pack: douyo:71eaff9f1a08adf5ccc164081c1298dc https://www.amazon.co.jp/%E3%80%90Amazon-co-jp%E9%99%90%E5%AE%9A%E3%80%91Logicool-%E3%83%AD%E3%82%B8%E3%82%AF%E3%83%BC%E3%83%AB-G-PHS-003d-%E3%80%90Amazon-co-jp-%E9%99%90%E5%AE%9A%E5%A3%81%E7%B4%99%E3%83%80%E3%82%A6%E3%83%B3%E3%83%AD%E3%83%BC%E3%83%89%E4%BB%98%E3%81%8D%E3%80%91/dp/B0BC142H1L/ref=zg_bs_g_videogames_d_sccl_15/000-0000000-0000000?psc=1
2025-06-05 18:10:34 [ERROR] root: not processing pack: douyo:610c2f6a9f62f4bc13f04b638682d3a9 https://www.amazon.co.jp/Nintendo-Switch-%E6%9C%89%E6%A9%9FEL%E3%83%A2%E3%83%87%E3%83%AB-%E3%83%8D%E3%82%AA%E3%83%B3%E3%83%96%E3%83%AB%E3%83%BC-%E3%83%8D%E3%82%AA%E3%83%B3%E3%83%AC%E3%83%83%E3%83%89/dp/B098B79SJL/ref=zg_bs_g_videogames_d_sccl_22/000-0000000-0000000?psc=1
2025-06-05 18:10:34 [ERROR] root: not processing pack: douyo:610c2f6a9f62f4bc13f04b638682d3a9 https://www.amazon.co.jp/Nintendo-Switch-%E6%9C%89%E6%A9%9FEL%E3%83%A2%E3%83%87%E3%83%AB-%E3%83%8D%E3%82%AA%E3%83%B3%E3%83%96%E3%83%AB%E3%83%BC-%E3%83%8D%E3%82%AA%E3%83%B3%E3%83%AC%E3%83%83%E3%83%89/dp/B098B79SJL/ref=zg_bs_g_videogames_d_sccl_22/000-0000000-0000000?psc=1
2025-06-05 18:10:34 [ERROR] root: not processing pack: douyo:8f4988620b353fdc3ad0c5bd9308fceb https://www.amazon.co.jp/%E4%BB%BB%E5%A4%A9%E5%A0%82-Nintendo-Switch-%E3%82%BF%E3%83%BC%E3%82%B3%E3%82%A4%E3%82%BA/dp/B07X779ZK5/ref=zg_bs_g_videogames_d_sccl_20/000-0000000-0000000?psc=1
2025-06-05 18:10:34 [ERROR] root: not processing pack: douyo:a1418923683dd1aaa093e1be1209d397 https://www.amazon.co.jp/%E3%82%BD%E3%83%8B%E3%83%BC%E3%83%BB%E3%82%A4%E3%83%B3%E3%82%BF%E3%83%A9%E3%82%AF%E3%83%86%E3%82%A3%E3%83%96%E3%82%A8%E3%83%B3%E3%82%BF%E3%83%86%E3%82%A4%E3%83%B3%E3%83%A1%E3%83%B3%E3%83%88-PlayStation-5-CFI-2000A01/dp/B0CKYM15RJ/ref=zg_bs_g_videogames_d_sccl_3/000-0000000-0000000?psc=1
2025-06-05 18:10:34 [ERROR] root: not processing pack: douyo:558fcd3a023ec03dcf2797819589d46a https://www.amazon.co.jp/%E4%BB%BB%E5%A4%A9%E5%A0%82-Nintendo-Switch-Lite-%E3%82%B0%E3%83%AC%E3%83%BC/dp/B07X47QTN3/ref=zg_bs_g_videogames_d_sccl_3/000-0000000-0000000?psc=1
2025-06-05 18:10:34 [ERROR] root: not processing pack: douyo:e0d28106efbcdbcf6c2cf0db2ec7c2fd https://www.amazon.co.jp/%E3%82%BD%E3%83%8B%E3%83%BC-%E3%82%B2%E3%83%BC%E3%83%9F%E3%83%B3%E3%82%B0%E3%82%A4%E3%83%A4%E3%83%9B%E3%83%B3-INZONE-Buds-USBType-C%E3%83%88%E3%83%A9%E3%83%B3%E3%82%B7%E3%83%BC%E3%83%90%E3%83%BC%E5%90%8C%E6%A2%B1/dp/B0CKFHB9P3/ref=zg_bs_g_videogames_d_sccl_13/000-0000000-0000000?psc=1
2025-06-05 18:10:34 [ERROR] root: not processing pack: douyo:2a97f7968ac12c7ab39dd20af81c75b8 https://www.amazon.co.jp/%E3%83%9D%E3%82%B1%E3%83%83%E3%83%88%E3%83%A2%E3%83%B3%E3%82%B9%E3%82%BF%E3%83%BC-%E3%83%90%E3%82%A4%E3%82%AA%E3%83%AC%E3%83%83%E3%83%88-Switch-%E3%80%90%E6%97%A9%E6%9C%9F%E8%B3%BC%E5%85%A5%E7%89%B9%E5%85%B8%E3%80%91%E3%83%97%E3%83%AD%E3%83%A2%E3%82%AB%E3%83%BC%E3%83%89%E3%80%8C%E3%83%94%E3%82%AB%E3%83%81%E3%83%A5%E3%82%A6%E3%80%8D-%C3%971/dp/B09X17GBLT/ref=zg_bs_g_videogames_d_sccl_1/000-0000000-0000000?psc=1
2025-06-05 18:10:34 [ERROR] root: not processing pack: douyo:558fcd3a023ec03dcf2797819589d46a https://www.amazon.co.jp/%E4%BB%BB%E5%A4%A9%E5%A0%82-Nintendo-Switch-Lite-%E3%82%B0%E3%83%AC%E3%83%BC/dp/B07X47QTN3/ref=zg_bs_g_videogames_d_sccl_3/000-0000000-0000000?psc=1
2025-06-05 18:10:34 [ERROR] root: not processing pack: douyo:558fcd3a023ec03dcf2797819589d46a https://www.amazon.co.jp/%E4%BB%BB%E5%A4%A9%E5%A0%82-Nintendo-Switch-Lite-%E3%82%B0%E3%83%AC%E3%83%BC/dp/B07X47QTN3/ref=zg_bs_g_videogames_d_sccl_3/000-0000000-0000000?psc=1
2025-06-05 18:10:34 [ERROR] root: not processing pack: douyo:71eaff9f1a08adf5ccc164081c1298dc https://www.amazon.co.jp/%E3%80%90Amazon-co-jp%E9%99%90%E5%AE%9A%E3%80%91Logicool-%E3%83%AD%E3%82%B8%E3%82%AF%E3%83%BC%E3%83%AB-G-PHS-003d-%E3%80%90Amazon-co-jp-%E9%99%90%E5%AE%9A%E5%A3%81%E7%B4%99%E3%83%80%E3%82%A6%E3%83%B3%E3%83%AD%E3%83%BC%E3%83%89%E4%BB%98%E3%81%8D%E3%80%91/dp/B0BC142H1L/ref=zg_bs_g_videogames_d_sccl_15/000-0000000-0000000?psc=1
2025-06-05 18:10:34 [ERROR] root: not processing pack: douyo:a857f277d91ba540ba01fd50400bb4c6 https://www.amazon.co.jp/%E4%BB%BB%E5%A4%A9%E5%A0%82-%E5%A4%A7%E4%B9%B1%E9%97%98%E3%82%B9%E3%83%9E%E3%83%83%E3%82%B7%E3%83%A5%E3%83%96%E3%83%A9%E3%82%B6%E3%83%BC%E3%82%BA-SPECIAL-Switch/dp/B07FDW61HX/ref=zg_bs_g_videogames_d_sccl_20/000-0000000-0000000?psc=1
2025-06-05 18:10:35 [ERROR] root: not processing pack: douyo:a1418923683dd1aaa093e1be1209d397 https://www.amazon.co.jp/%E3%82%BD%E3%83%8B%E3%83%BC%E3%83%BB%E3%82%A4%E3%83%B3%E3%82%BF%E3%83%A9%E3%82%AF%E3%83%86%E3%82%A3%E3%83%96%E3%82%A8%E3%83%B3%E3%82%BF%E3%83%86%E3%82%A4%E3%83%B3%E3%83%A1%E3%83%B3%E3%83%88-PlayStation-5-CFI-2000A01/dp/B0CKYM15RJ/ref=zg_bs_g_videogames_d_sccl_3/000-0000000-0000000?psc=1
2025-06-05 18:10:35 [ERROR] root: not processing pack: douyo:971c616bfaac5a5793cc99c122fbbcb7 https://www.amazon.co.jp/Nintendo-Switch-Joy-%E3%83%8D%E3%82%AA%E3%83%B3%E3%83%96%E3%83%AB%E3%83%BC-%E3%83%8D%E3%82%AA%E3%83%B3%E3%83%AC%E3%83%83%E3%83%89/dp/B0BM46DFH1/ref=zg_bs_g_videogames_d_sccl_28/000-0000000-0000000?psc=1
2025-06-05 18:10:35 [INFO] libs.cache: Using Redis cache
2025-06-05 18:10:35 [ERROR] root: not processing pack: douyo:a857f277d91ba540ba01fd50400bb4c6 https://www.amazon.co.jp/%E4%BB%BB%E5%A4%A9%E5%A0%82-%E5%A4%A7%E4%B9%B1%E9%97%98%E3%82%B9%E3%83%9E%E3%83%83%E3%82%B7%E3%83%A5%E3%83%96%E3%83%A9%E3%82%B6%E3%83%BC%E3%82%BA-SPECIAL-Switch/dp/B07FDW61HX/ref=zg_bs_g_videogames_d_sccl_20/000-0000000-0000000?psc=1
2025-06-05 18:10:35 [ERROR] root: not processing pack: douyo:c81792f942c90564ca29b8fdcc76b35e https://www.amazon.co.jp/%E3%82%BD%E3%83%8B%E3%83%BC%E3%83%BB%E3%82%A4%E3%83%B3%E3%82%BF%E3%83%A9%E3%82%AF%E3%83%86%E3%82%A3%E3%83%96%E3%82%A8%E3%83%B3%E3%82%BF%E3%83%86%E3%82%A4%E3%83%B3%E3%83%A1%E3%83%B3%E3%83%88-%E3%80%90PS5%E3%80%91%E3%82%B0%E3%83%A9%E3%83%B3%E3%83%84%E3%83%BC%E3%83%AA%E3%82%B9%E3%83%A27/dp/B09H2X6R6C/ref=zg_bs_g_videogames_d_sccl_1/000-0000000-0000000?psc=1
2025-06-05 18:10:36 [ERROR] root: not processing pack: douyo:ccc726e6af8d56fdeccfe00ceeb92e83 https://www.amazon.co.jp/%E3%82%AB%E3%83%97%E3%82%B3%E3%83%B3-CAPCOM-ELJM-30582-%E3%80%90PS5%E3%80%91%E3%83%A2%E3%83%B3%E3%82%B9%E3%82%BF%E3%83%BC%E3%83%8F%E3%83%B3%E3%82%BF%E3%83%BC%E3%83%AF%E3%82%A4%E3%83%AB%E3%82%BA/dp/B0DHCTRH16/ref=zg_bs_g_videogames_d_sccl_15/000-0000000-0000000?psc=1
2025-06-05 18:10:36 [ERROR] root: not processing pack: douyo:ccc726e6af8d56fdeccfe00ceeb92e83 https://www.amazon.co.jp/%E3%82%AB%E3%83%97%E3%82%B3%E3%83%B3-CAPCOM-ELJM-30582-%E3%80%90PS5%E3%80%91%E3%83%A2%E3%83%B3%E3%82%B9%E3%82%BF%E3%83%BC%E3%83%8F%E3%83%B3%E3%82%BF%E3%83%BC%E3%83%AF%E3%82%A4%E3%83%AB%E3%82%BA/dp/B0DHCTRH16/ref=zg_bs_g_videogames_d_sccl_15/000-0000000-0000000?psc=1
2025-06-05 18:10:36 [INFO] root: Created PID file for webui: /tmp/pyspider_webui.pid
2025-06-05 18:10:37 [ERROR] root: not processing pack: douyo:ccc726e6af8d56fdeccfe00ceeb92e83 https://www.amazon.co.jp/%E3%82%AB%E3%83%97%E3%82%B3%E3%83%B3-CAPCOM-ELJM-30582-%E3%80%90PS5%E3%80%91%E3%83%A2%E3%83%B3%E3%82%B9%E3%82%BF%E3%83%BC%E3%83%8F%E3%83%B3%E3%82%BF%E3%83%BC%E3%83%AF%E3%82%A4%E3%83%AB%E3%82%BA/dp/B0DHCTRH16/ref=zg_bs_g_videogames_d_sccl_15/000-0000000-0000000?psc=1
2025-06-05 18:10:37 [ERROR] root: not processing pack: douyo:e0d28106efbcdbcf6c2cf0db2ec7c2fd https://www.amazon.co.jp/%E3%82%BD%E3%83%8B%E3%83%BC-%E3%82%B2%E3%83%BC%E3%83%9F%E3%83%B3%E3%82%B0%E3%82%A4%E3%83%A4%E3%83%9B%E3%83%B3-INZONE-Buds-USBType-C%E3%83%88%E3%83%A9%E3%83%B3%E3%82%B7%E3%83%BC%E3%83%90%E3%83%BC%E5%90%8C%E6%A2%B1/dp/B0CKFHB9P3/ref=zg_bs_g_videogames_d_sccl_13/000-0000000-0000000?psc=1
2025-06-05 18:10:37 [ERROR] root: not processing pack: douyo:f30a722677881a072de1dfd4e233c506 https://www.amazon.co.jp/%E3%80%90%E7%B4%94%E6%AD%A3%E5%93%81%E3%80%91%E3%82%B9%E3%83%86%E3%82%A3%E3%83%83%E3%82%AF%E3%83%A2%E3%82%B8%E3%83%A5%E3%83%BC%E3%83%AB-DualSense-Edge-%E3%83%AF%E3%82%A4%E3%83%A4%E3%83%AC%E3%82%B9%E3%82%B3%E3%83%B3%E3%83%88%E3%83%AD%E3%83%BC%E3%83%A9%E3%83%BC%E7%94%A8-CFI-ZSM1G/dp/B0BJTSHHK8/ref=zg_bs_g_videogames_d_sccl_14/000-0000000-0000000?psc=1
2025-06-05 18:11:25 [INFO] metrics: Gauges: {"memory_usage_rss": 88829952, "memory_usage_vms": 636264448, "memory_usage_percent": 0.26455852626981874, "system_memory_total": ***********, "system_memory_available": 21625290752, "system_memory_percent": 35.6}
2025-06-05 18:12:25 [INFO] metrics: Gauges: {"memory_usage_rss": 89223168, "memory_usage_vms": 636264448, "memory_usage_percent": 0.26572962501662106, "system_memory_total": ***********, "system_memory_available": 21564874752, "system_memory_percent": 35.8}
2025-06-05 18:13:25 [INFO] metrics: Gauges: {"memory_usage_rss": 89223168, "memory_usage_vms": 636264448, "memory_usage_percent": 0.26572962501662106, "system_memory_total": ***********, "system_memory_available": 21741887488, "system_memory_percent": 35.2}
2025-06-05 18:14:25 [INFO] metrics: Gauges: {"memory_usage_rss": 89223168, "memory_usage_vms": 636264448, "memory_usage_percent": 0.26572962501662106, "system_memory_total": ***********, "system_memory_available": 21248765952, "system_memory_percent": 36.7}
2025-06-05 18:15:25 [INFO] metrics: Gauges: {"memory_usage_rss": 89223168, "memory_usage_vms": 636264448, "memory_usage_percent": 0.26572962501662106, "system_memory_total": ***********, "system_memory_available": 21684404224, "system_memory_percent": 35.4}
2025-06-05 18:16:25 [INFO] metrics: Gauges: {"memory_usage_rss": 89223168, "memory_usage_vms": 636264448, "memory_usage_percent": 0.26572962501662106, "system_memory_total": ***********, "system_memory_available": 21166714880, "system_memory_percent": 37.0}
2025-06-05 18:17:25 [INFO] metrics: Gauges: {"memory_usage_rss": 89354240, "memory_usage_vms": 636264448, "memory_usage_percent": 0.2661199912655552, "system_memory_total": ***********, "system_memory_available": 21345333248, "system_memory_percent": 36.4}
2025-06-05 18:18:25 [INFO] metrics: Gauges: {"memory_usage_rss": 89354240, "memory_usage_vms": 636264448, "memory_usage_percent": 0.2661199912655552, "system_memory_total": ***********, "system_memory_available": 21463265280, "system_memory_percent": 36.1}
2025-06-05 18:19:25 [INFO] metrics: Gauges: {"memory_usage_rss": 89354240, "memory_usage_vms": 636264448, "memory_usage_percent": 0.2661199912655552, "system_memory_total": ***********, "system_memory_available": 21289918464, "system_memory_percent": 36.6}
2025-06-05 18:20:25 [INFO] metrics: Gauges: {"memory_usage_rss": 89354240, "memory_usage_vms": 636264448, "memory_usage_percent": 0.2661199912655552, "system_memory_total": ***********, "system_memory_available": 21803540480, "system_memory_percent": 35.1}
2025-06-05 18:20:30 [INFO] metrics: Counters: {"fetch_count[project=douyo]": 30, "fetch_count[project=iuiu]": 1, "fetch_count[project=mysql_detailed_test]": 1, "fetch_count[project=jijijiji]": 1, "fetch_count[project=kokoko]": 1, "fetch_count[project=mysql_detailed_test_2]": 1}
2025-06-05 18:20:33 [ERROR] root: not processing pack: douyo:558fcd3a023ec03dcf2797819589d46a https://www.amazon.co.jp/%E4%BB%BB%E5%A4%A9%E5%A0%82-Nintendo-Switch-Lite-%E3%82%B0%E3%83%AC%E3%83%BC/dp/B07X47QTN3/ref=zg_bs_g_videogames_d_sccl_3/000-0000000-0000000?psc=1
2025-06-05 18:20:33 [ERROR] root: not processing pack: douyo:71eaff9f1a08adf5ccc164081c1298dc https://www.amazon.co.jp/%E3%80%90Amazon-co-jp%E9%99%90%E5%AE%9A%E3%80%91Logicool-%E3%83%AD%E3%82%B8%E3%82%AF%E3%83%BC%E3%83%AB-G-PHS-003d-%E3%80%90Amazon-co-jp-%E9%99%90%E5%AE%9A%E5%A3%81%E7%B4%99%E3%83%80%E3%82%A6%E3%83%B3%E3%83%AD%E3%83%BC%E3%83%89%E4%BB%98%E3%81%8D%E3%80%91/dp/B0BC142H1L/ref=zg_bs_g_videogames_d_sccl_15/000-0000000-0000000?psc=1
2025-06-05 18:20:33 [ERROR] root: not processing pack: douyo:610c2f6a9f62f4bc13f04b638682d3a9 https://www.amazon.co.jp/Nintendo-Switch-%E6%9C%89%E6%A9%9FEL%E3%83%A2%E3%83%87%E3%83%AB-%E3%83%8D%E3%82%AA%E3%83%B3%E3%83%96%E3%83%AB%E3%83%BC-%E3%83%8D%E3%82%AA%E3%83%B3%E3%83%AC%E3%83%83%E3%83%89/dp/B098B79SJL/ref=zg_bs_g_videogames_d_sccl_22/000-0000000-0000000?psc=1
2025-06-05 18:20:34 [ERROR] root: not processing pack: douyo:8f4988620b353fdc3ad0c5bd9308fceb https://www.amazon.co.jp/%E4%BB%BB%E5%A4%A9%E5%A0%82-Nintendo-Switch-%E3%82%BF%E3%83%BC%E3%82%B3%E3%82%A4%E3%82%BA/dp/B07X779ZK5/ref=zg_bs_g_videogames_d_sccl_20/000-0000000-0000000?psc=1
2025-06-05 18:20:34 [ERROR] root: not processing pack: douyo:558fcd3a023ec03dcf2797819589d46a https://www.amazon.co.jp/%E4%BB%BB%E5%A4%A9%E5%A0%82-Nintendo-Switch-Lite-%E3%82%B0%E3%83%AC%E3%83%BC/dp/B07X47QTN3/ref=zg_bs_g_videogames_d_sccl_3/000-0000000-0000000?psc=1
2025-06-05 18:20:34 [ERROR] root: not processing pack: douyo:610c2f6a9f62f4bc13f04b638682d3a9 https://www.amazon.co.jp/Nintendo-Switch-%E6%9C%89%E6%A9%9FEL%E3%83%A2%E3%83%87%E3%83%AB-%E3%83%8D%E3%82%AA%E3%83%B3%E3%83%96%E3%83%AB%E3%83%BC-%E3%83%8D%E3%82%AA%E3%83%B3%E3%83%AC%E3%83%83%E3%83%89/dp/B098B79SJL/ref=zg_bs_g_videogames_d_sccl_22/000-0000000-0000000?psc=1
2025-06-05 18:20:34 [ERROR] root: not processing pack: douyo:2a97f7968ac12c7ab39dd20af81c75b8 https://www.amazon.co.jp/%E3%83%9D%E3%82%B1%E3%83%83%E3%83%88%E3%83%A2%E3%83%B3%E3%82%B9%E3%82%BF%E3%83%BC-%E3%83%90%E3%82%A4%E3%82%AA%E3%83%AC%E3%83%83%E3%83%88-Switch-%E3%80%90%E6%97%A9%E6%9C%9F%E8%B3%BC%E5%85%A5%E7%89%B9%E5%85%B8%E3%80%91%E3%83%97%E3%83%AD%E3%83%A2%E3%82%AB%E3%83%BC%E3%83%89%E3%80%8C%E3%83%94%E3%82%AB%E3%83%81%E3%83%A5%E3%82%A6%E3%80%8D-%C3%971/dp/B09X17GBLT/ref=zg_bs_g_videogames_d_sccl_1/000-0000000-0000000?psc=1
2025-06-05 18:20:34 [ERROR] root: not processing pack: douyo:a1418923683dd1aaa093e1be1209d397 https://www.amazon.co.jp/%E3%82%BD%E3%83%8B%E3%83%BC%E3%83%BB%E3%82%A4%E3%83%B3%E3%82%BF%E3%83%A9%E3%82%AF%E3%83%86%E3%82%A3%E3%83%96%E3%82%A8%E3%83%B3%E3%82%BF%E3%83%86%E3%82%A4%E3%83%B3%E3%83%A1%E3%83%B3%E3%83%88-PlayStation-5-CFI-2000A01/dp/B0CKYM15RJ/ref=zg_bs_g_videogames_d_sccl_3/000-0000000-0000000?psc=1
2025-06-05 18:20:34 [ERROR] root: not processing pack: douyo:558fcd3a023ec03dcf2797819589d46a https://www.amazon.co.jp/%E4%BB%BB%E5%A4%A9%E5%A0%82-Nintendo-Switch-Lite-%E3%82%B0%E3%83%AC%E3%83%BC/dp/B07X47QTN3/ref=zg_bs_g_videogames_d_sccl_3/000-0000000-0000000?psc=1
2025-06-05 18:20:34 [ERROR] root: not processing pack: douyo:e0d28106efbcdbcf6c2cf0db2ec7c2fd https://www.amazon.co.jp/%E3%82%BD%E3%83%8B%E3%83%BC-%E3%82%B2%E3%83%BC%E3%83%9F%E3%83%B3%E3%82%B0%E3%82%A4%E3%83%A4%E3%83%9B%E3%83%B3-INZONE-Buds-USBType-C%E3%83%88%E3%83%A9%E3%83%B3%E3%82%B7%E3%83%BC%E3%83%90%E3%83%BC%E5%90%8C%E6%A2%B1/dp/B0CKFHB9P3/ref=zg_bs_g_videogames_d_sccl_13/000-0000000-0000000?psc=1
2025-06-05 18:20:34 [ERROR] root: not processing pack: douyo:71eaff9f1a08adf5ccc164081c1298dc https://www.amazon.co.jp/%E3%80%90Amazon-co-jp%E9%99%90%E5%AE%9A%E3%80%91Logicool-%E3%83%AD%E3%82%B8%E3%82%AF%E3%83%BC%E3%83%AB-G-PHS-003d-%E3%80%90Amazon-co-jp-%E9%99%90%E5%AE%9A%E5%A3%81%E7%B4%99%E3%83%80%E3%82%A6%E3%83%B3%E3%83%AD%E3%83%BC%E3%83%89%E4%BB%98%E3%81%8D%E3%80%91/dp/B0BC142H1L/ref=zg_bs_g_videogames_d_sccl_15/000-0000000-0000000?psc=1
2025-06-05 18:20:34 [ERROR] root: not processing pack: douyo:a857f277d91ba540ba01fd50400bb4c6 https://www.amazon.co.jp/%E4%BB%BB%E5%A4%A9%E5%A0%82-%E5%A4%A7%E4%B9%B1%E9%97%98%E3%82%B9%E3%83%9E%E3%83%83%E3%82%B7%E3%83%A5%E3%83%96%E3%83%A9%E3%82%B6%E3%83%BC%E3%82%BA-SPECIAL-Switch/dp/B07FDW61HX/ref=zg_bs_g_videogames_d_sccl_20/000-0000000-0000000?psc=1
2025-06-05 18:20:34 [ERROR] root: not processing pack: douyo:ccc726e6af8d56fdeccfe00ceeb92e83 https://www.amazon.co.jp/%E3%82%AB%E3%83%97%E3%82%B3%E3%83%B3-CAPCOM-ELJM-30582-%E3%80%90PS5%E3%80%91%E3%83%A2%E3%83%B3%E3%82%B9%E3%82%BF%E3%83%BC%E3%83%8F%E3%83%B3%E3%82%BF%E3%83%BC%E3%83%AF%E3%82%A4%E3%83%AB%E3%82%BA/dp/B0DHCTRH16/ref=zg_bs_g_videogames_d_sccl_15/000-0000000-0000000?psc=1
2025-06-05 18:20:35 [ERROR] root: not processing pack: douyo:a1418923683dd1aaa093e1be1209d397 https://www.amazon.co.jp/%E3%82%BD%E3%83%8B%E3%83%BC%E3%83%BB%E3%82%A4%E3%83%B3%E3%82%BF%E3%83%A9%E3%82%AF%E3%83%86%E3%82%A3%E3%83%96%E3%82%A8%E3%83%B3%E3%82%BF%E3%83%86%E3%82%A4%E3%83%B3%E3%83%A1%E3%83%B3%E3%83%88-PlayStation-5-CFI-2000A01/dp/B0CKYM15RJ/ref=zg_bs_g_videogames_d_sccl_3/000-0000000-0000000?psc=1
2025-06-05 18:20:35 [ERROR] root: not processing pack: douyo:971c616bfaac5a5793cc99c122fbbcb7 https://www.amazon.co.jp/Nintendo-Switch-Joy-%E3%83%8D%E3%82%AA%E3%83%B3%E3%83%96%E3%83%AB%E3%83%BC-%E3%83%8D%E3%82%AA%E3%83%B3%E3%83%AC%E3%83%83%E3%83%89/dp/B0BM46DFH1/ref=zg_bs_g_videogames_d_sccl_28/000-0000000-0000000?psc=1
2025-06-05 18:20:36 [ERROR] root: not processing pack: douyo:a857f277d91ba540ba01fd50400bb4c6 https://www.amazon.co.jp/%E4%BB%BB%E5%A4%A9%E5%A0%82-%E5%A4%A7%E4%B9%B1%E9%97%98%E3%82%B9%E3%83%9E%E3%83%83%E3%82%B7%E3%83%A5%E3%83%96%E3%83%A9%E3%82%B6%E3%83%BC%E3%82%BA-SPECIAL-Switch/dp/B07FDW61HX/ref=zg_bs_g_videogames_d_sccl_20/000-0000000-0000000?psc=1
2025-06-05 18:20:36 [ERROR] root: not processing pack: douyo:c81792f942c90564ca29b8fdcc76b35e https://www.amazon.co.jp/%E3%82%BD%E3%83%8B%E3%83%BC%E3%83%BB%E3%82%A4%E3%83%B3%E3%82%BF%E3%83%A9%E3%82%AF%E3%83%86%E3%82%A3%E3%83%96%E3%82%A8%E3%83%B3%E3%82%BF%E3%83%86%E3%82%A4%E3%83%B3%E3%83%A1%E3%83%B3%E3%83%88-%E3%80%90PS5%E3%80%91%E3%82%B0%E3%83%A9%E3%83%B3%E3%83%84%E3%83%BC%E3%83%AA%E3%82%B9%E3%83%A27/dp/B09H2X6R6C/ref=zg_bs_g_videogames_d_sccl_1/000-0000000-0000000?psc=1
2025-06-05 18:20:36 [ERROR] root: not processing pack: douyo:ccc726e6af8d56fdeccfe00ceeb92e83 https://www.amazon.co.jp/%E3%82%AB%E3%83%97%E3%82%B3%E3%83%B3-CAPCOM-ELJM-30582-%E3%80%90PS5%E3%80%91%E3%83%A2%E3%83%B3%E3%82%B9%E3%82%BF%E3%83%BC%E3%83%8F%E3%83%B3%E3%82%BF%E3%83%BC%E3%83%AF%E3%82%A4%E3%83%AB%E3%82%BA/dp/B0DHCTRH16/ref=zg_bs_g_videogames_d_sccl_15/000-0000000-0000000?psc=1
2025-06-05 18:20:37 [ERROR] root: not processing pack: douyo:ccc726e6af8d56fdeccfe00ceeb92e83 https://www.amazon.co.jp/%E3%82%AB%E3%83%97%E3%82%B3%E3%83%B3-CAPCOM-ELJM-30582-%E3%80%90PS5%E3%80%91%E3%83%A2%E3%83%B3%E3%82%B9%E3%82%BF%E3%83%BC%E3%83%8F%E3%83%B3%E3%82%BF%E3%83%BC%E3%83%AF%E3%82%A4%E3%83%AB%E3%82%BA/dp/B0DHCTRH16/ref=zg_bs_g_videogames_d_sccl_15/000-0000000-0000000?psc=1
2025-06-05 18:20:38 [ERROR] root: not processing pack: douyo:e0d28106efbcdbcf6c2cf0db2ec7c2fd https://www.amazon.co.jp/%E3%82%BD%E3%83%8B%E3%83%BC-%E3%82%B2%E3%83%BC%E3%83%9F%E3%83%B3%E3%82%B0%E3%82%A4%E3%83%A4%E3%83%9B%E3%83%B3-INZONE-Buds-USBType-C%E3%83%88%E3%83%A9%E3%83%B3%E3%82%B7%E3%83%BC%E3%83%90%E3%83%BC%E5%90%8C%E6%A2%B1/dp/B0CKFHB9P3/ref=zg_bs_g_videogames_d_sccl_13/000-0000000-0000000?psc=1
2025-06-05 18:20:38 [ERROR] root: not processing pack: douyo:f30a722677881a072de1dfd4e233c506 https://www.amazon.co.jp/%E3%80%90%E7%B4%94%E6%AD%A3%E5%93%81%E3%80%91%E3%82%B9%E3%83%86%E3%82%A3%E3%83%83%E3%82%AF%E3%83%A2%E3%82%B8%E3%83%A5%E3%83%BC%E3%83%AB-DualSense-Edge-%E3%83%AF%E3%82%A4%E3%83%A4%E3%83%AC%E3%82%B9%E3%82%B3%E3%83%B3%E3%83%88%E3%83%AD%E3%83%BC%E3%83%A9%E3%83%BC%E7%94%A8-CFI-ZSM1G/dp/B0BJTSHHK8/ref=zg_bs_g_videogames_d_sccl_14/000-0000000-0000000?psc=1
2025-06-05 18:21:25 [INFO] metrics: Gauges: {"memory_usage_rss": 89616384, "memory_usage_vms": 637313024, "memory_usage_percent": 0.2669007237634234, "system_memory_total": ***********, "system_memory_available": 21151817728, "system_memory_percent": 37.0}
2025-06-05 18:22:25 [INFO] metrics: Gauges: {"memory_usage_rss": 89616384, "memory_usage_vms": 637313024, "memory_usage_percent": 0.2669007237634234, "system_memory_total": ***********, "system_memory_available": 21361401856, "system_memory_percent": 36.4}
2025-06-05 18:23:25 [INFO] metrics: Gauges: {"memory_usage_rss": 89616384, "memory_usage_vms": 637313024, "memory_usage_percent": 0.2669007237634234, "system_memory_total": ***********, "system_memory_available": 21207367680, "system_memory_percent": 36.8}
2025-06-05 18:24:25 [INFO] metrics: Gauges: {"memory_usage_rss": 89616384, "memory_usage_vms": 637313024, "memory_usage_percent": 0.2669007237634234, "system_memory_total": ***********, "system_memory_available": 21315334144, "system_memory_percent": 36.5}
2025-06-05 18:25:25 [INFO] metrics: Gauges: {"memory_usage_rss": 89616384, "memory_usage_vms": 637313024, "memory_usage_percent": 0.2669007237634234, "system_memory_total": ***********, "system_memory_available": 21261991936, "system_memory_percent": 36.7}
{"timestamp": "2025-06-05T18:26:21.417315", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-05 18:26:22 [INFO] pyspider.libs.redis_fallback: ✅ Redis接続成功: redis://localhost:6379/0
2025-06-05 18:26:22 [INFO] pyspider.libs.redis_fallback: 🔍 Redis接続監視を開始しました
2025-06-05 18:26:22 [INFO] pyspider.libs.redis_fallback: ✅ Redis フォールバック機能を初期化しました
2025-06-05 18:26:22 [INFO] memory_optimizer: Memory optimizer initialized with max_memory_percent=80.0%, gc_interval=60s, check_interval=30s, auto_optimize=True
2025-06-05 18:26:22 [INFO] memory_optimizer: Memory monitoring started
2025-06-05 18:26:22 [INFO] root: Memory optimizer started for scheduler
2025-06-05 18:26:22 [INFO] root: Created PID file for scheduler: /tmp/pyspider_scheduler.pid
{"timestamp": "2025-06-05T18:26:23.576860", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-05 18:26:23 [INFO] root: Created PID file for fetcher: /tmp/pyspider_fetcher.pid
{"timestamp": "2025-06-05T18:26:25.766722", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-05 18:26:26 [INFO] root: Created PID file for processor: /tmp/pyspider_processor.pid
{"timestamp": "2025-06-05T18:26:28.024561", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-05 18:26:29 [INFO] root: Created PID file for result_worker: /tmp/pyspider_result_worker.pid
2025-06-05 18:26:29 [INFO] result: result_worker starting...
{"timestamp": "2025-06-05T18:26:30.465653", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-05 18:26:31 [ERROR] root: not processing pack: douyo:71eaff9f1a08adf5ccc164081c1298dc https://www.amazon.co.jp/%E3%80%90Amazon-co-jp%E9%99%90%E5%AE%9A%E3%80%91Logicool-%E3%83%AD%E3%82%B8%E3%82%AF%E3%83%BC%E3%83%AB-G-PHS-003d-%E3%80%90Amazon-co-jp-%E9%99%90%E5%AE%9A%E5%A3%81%E7%B4%99%E3%83%80%E3%82%A6%E3%83%B3%E3%83%AD%E3%83%BC%E3%83%89%E4%BB%98%E3%81%8D%E3%80%91/dp/B0BC142H1L/ref=zg_bs_g_videogames_d_sccl_15/000-0000000-0000000?psc=1
2025-06-05 18:26:31 [ERROR] root: not processing pack: jijijiji:610c2f6a9f62f4bc13f04b638682d3a9 https://www.amazon.co.jp/Nintendo-Switch-%E6%9C%89%E6%A9%9FEL%E3%83%A2%E3%83%87%E3%83%AB-%E3%83%8D%E3%82%AA%E3%83%B3%E3%83%96%E3%83%AB%E3%83%BC-%E3%83%8D%E3%82%AA%E3%83%B3%E3%83%AC%E3%83%83%E3%83%89/dp/B098B79SJL/ref=zg_bs_g_videogames_d_sccl_22/000-0000000-0000000?psc=1
2025-06-05 18:26:31 [ERROR] root: not processing pack: douyo:71eaff9f1a08adf5ccc164081c1298dc https://www.amazon.co.jp/%E3%80%90Amazon-co-jp%E9%99%90%E5%AE%9A%E3%80%91Logicool-%E3%83%AD%E3%82%B8%E3%82%AF%E3%83%BC%E3%83%AB-G-PHS-003d-%E3%80%90Amazon-co-jp-%E9%99%90%E5%AE%9A%E5%A3%81%E7%B4%99%E3%83%80%E3%82%A6%E3%83%B3%E3%83%AD%E3%83%BC%E3%83%89%E4%BB%98%E3%81%8D%E3%80%91/dp/B0BC142H1L/ref=zg_bs_g_videogames_d_sccl_15/000-0000000-0000000?psc=1
2025-06-05 18:26:31 [ERROR] root: not processing pack: douyo:558fcd3a023ec03dcf2797819589d46a https://www.amazon.co.jp/%E4%BB%BB%E5%A4%A9%E5%A0%82-Nintendo-Switch-Lite-%E3%82%B0%E3%83%AC%E3%83%BC/dp/B07X47QTN3/ref=zg_bs_g_videogames_d_sccl_3/000-0000000-0000000?psc=1
2025-06-05 18:26:31 [ERROR] root: not processing pack: douyo:558fcd3a023ec03dcf2797819589d46a https://www.amazon.co.jp/%E4%BB%BB%E5%A4%A9%E5%A0%82-Nintendo-Switch-Lite-%E3%82%B0%E3%83%AC%E3%83%BC/dp/B07X47QTN3/ref=zg_bs_g_videogames_d_sccl_3/000-0000000-0000000?psc=1
2025-06-05 18:26:32 [ERROR] root: not processing pack: jijijiji:a1418923683dd1aaa093e1be1209d397 https://www.amazon.co.jp/%E3%82%BD%E3%83%8B%E3%83%BC%E3%83%BB%E3%82%A4%E3%83%B3%E3%82%BF%E3%83%A9%E3%82%AF%E3%83%86%E3%82%A3%E3%83%96%E3%82%A8%E3%83%B3%E3%82%BF%E3%83%86%E3%82%A4%E3%83%B3%E3%83%A1%E3%83%B3%E3%83%88-PlayStation-5-CFI-2000A01/dp/B0CKYM15RJ/ref=zg_bs_g_videogames_d_sccl_3/000-0000000-0000000?psc=1
2025-06-05 18:26:32 [INFO] libs.cache: Using Redis cache
2025-06-05 18:26:33 [INFO] root: Created PID file for webui: /tmp/pyspider_webui.pid
2025-06-05 18:26:34 [ERROR] root: not processing pack: jijijiji:ccc726e6af8d56fdeccfe00ceeb92e83 https://www.amazon.co.jp/%E3%82%AB%E3%83%97%E3%82%B3%E3%83%B3-CAPCOM-ELJM-30582-%E3%80%90PS5%E3%80%91%E3%83%A2%E3%83%B3%E3%82%B9%E3%82%BF%E3%83%BC%E3%83%8F%E3%83%B3%E3%82%BF%E3%83%BC%E3%83%AF%E3%82%A4%E3%83%AB%E3%82%BA/dp/B0DHCTRH16/ref=zg_bs_g_videogames_d_sccl_15/000-0000000-0000000?psc=1
2025-06-05 18:26:34 [ERROR] root: not processing pack: jijijiji:ccc726e6af8d56fdeccfe00ceeb92e83 https://www.amazon.co.jp/%E3%82%AB%E3%83%97%E3%82%B3%E3%83%B3-CAPCOM-ELJM-30582-%E3%80%90PS5%E3%80%91%E3%83%A2%E3%83%B3%E3%82%B9%E3%82%BF%E3%83%BC%E3%83%8F%E3%83%B3%E3%82%BF%E3%83%BC%E3%83%AF%E3%82%A4%E3%83%AB%E3%82%BA/dp/B0DHCTRH16/ref=zg_bs_g_videogames_d_sccl_15/000-0000000-0000000?psc=1
2025-06-05 18:27:22 [INFO] metrics: Gauges: {"memory_usage_rss": 89128960, "memory_usage_vms": 636166144, "memory_usage_percent": 0.26544904927519963, "system_memory_total": ***********, "system_memory_available": 21235662848, "system_memory_percent": 36.8}
2025-06-05 18:28:22 [INFO] metrics: Gauges: {"memory_usage_rss": 89128960, "memory_usage_vms": 636166144, "memory_usage_percent": 0.26544904927519963, "system_memory_total": ***********, "system_memory_available": 21115785216, "system_memory_percent": 37.1}
2025-06-05 18:29:22 [INFO] metrics: Gauges: {"memory_usage_rss": 89128960, "memory_usage_vms": 636166144, "memory_usage_percent": 0.26544904927519963, "system_memory_total": ***********, "system_memory_available": 21182058496, "system_memory_percent": 36.9}
2025-06-05 18:30:22 [INFO] metrics: Gauges: {"memory_usage_rss": 89128960, "memory_usage_vms": 636166144, "memory_usage_percent": 0.26544904927519963, "system_memory_total": ***********, "system_memory_available": 21288714240, "system_memory_percent": 36.6}
2025-06-05 18:31:22 [INFO] metrics: Gauges: {"memory_usage_rss": 89128960, "memory_usage_vms": 636166144, "memory_usage_percent": 0.26544904927519963, "system_memory_total": ***********, "system_memory_available": 21186830336, "system_memory_percent": 36.9}
2025-06-05 18:32:22 [INFO] metrics: Gauges: {"memory_usage_rss": 89128960, "memory_usage_vms": 636166144, "memory_usage_percent": 0.26544904927519963, "system_memory_total": ***********, "system_memory_available": 21324505088, "system_memory_percent": 36.5}
2025-06-05 18:33:22 [INFO] metrics: Gauges: {"memory_usage_rss": 89128960, "memory_usage_vms": 636166144, "memory_usage_percent": 0.26544904927519963, "system_memory_total": ***********, "system_memory_available": 21259264000, "system_memory_percent": 36.7}
2025-06-05 18:34:22 [INFO] metrics: Gauges: {"memory_usage_rss": 89128960, "memory_usage_vms": 636166144, "memory_usage_percent": 0.26544904927519963, "system_memory_total": ***********, "system_memory_available": 21138825216, "system_memory_percent": 37.0}
2025-06-05 18:35:22 [INFO] metrics: Gauges: {"memory_usage_rss": 89128960, "memory_usage_vms": 636166144, "memory_usage_percent": 0.26544904927519963, "system_memory_total": ***********, "system_memory_available": 21263425536, "system_memory_percent": 36.7}
{"timestamp": "2025-06-05T18:36:34.280600", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-05 18:36:35 [INFO] pyspider.libs.redis_fallback: ✅ Redis接続成功: redis://localhost:6379/0
2025-06-05 18:36:35 [INFO] pyspider.libs.redis_fallback: 🔍 Redis接続監視を開始しました
2025-06-05 18:36:35 [INFO] pyspider.libs.redis_fallback: ✅ Redis フォールバック機能を初期化しました
2025-06-05 18:36:35 [INFO] memory_optimizer: Memory optimizer initialized with max_memory_percent=80.0%, gc_interval=60s, check_interval=30s, auto_optimize=True
2025-06-05 18:36:35 [INFO] memory_optimizer: Memory monitoring started
2025-06-05 18:36:35 [INFO] root: Memory optimizer started for scheduler
2025-06-05 18:36:35 [INFO] root: Created PID file for scheduler: /tmp/pyspider_scheduler.pid
{"timestamp": "2025-06-05T18:36:36.555959", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-05 18:36:36 [INFO] root: Created PID file for fetcher: /tmp/pyspider_fetcher.pid
{"timestamp": "2025-06-05T18:36:38.704567", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-05 18:36:39 [INFO] root: Created PID file for processor: /tmp/pyspider_processor.pid
{"timestamp": "2025-06-05T18:36:40.951449", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-05 18:36:42 [INFO] root: Created PID file for result_worker: /tmp/pyspider_result_worker.pid
2025-06-05 18:36:42 [INFO] result: result_worker starting...
{"timestamp": "2025-06-05T18:36:43.265211", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-05 18:36:43 [ERROR] root: not processing pack: douyo:1ab126e2a022911c500d93bad423b484 https://www.amazon.co.jp/Switch2-%E3%82%AC%E3%83%A9%E3%82%B9%E3%83%95%E3%82%A4%E3%83%AB%E3%83%A0-%E3%82%AC%E3%82%A4%E3%83%89%E6%9E%A0%E4%BB%98%E3%81%8D%E3%80%90Seninhi-%E6%97%A5%E6%9C%AC%E6%97%AD%E7%A1%9D%E5%AD%90%E8%A3%BD-%E9%AB%98-SENLX-TMSH2/dp/B0DZ2BFLPH/ref=zg_bs_g_videogames_d_sccl_10/000-0000000-0000000?psc=1
2025-06-05 18:36:43 [ERROR] root: not processing pack: douyo:1ea87cf2808630f3594ef373a156dcc1 https://www.amazon.co.jp/%E3%80%90%E7%B4%94%E6%AD%A3%E5%93%81%E3%80%91DualSense-Edge-%E3%83%AF%E3%82%A4%E3%83%A4%E3%83%AC%E3%82%B9%E3%82%B3%E3%83%B3%E3%83%88%E3%83%AD%E3%83%BC%E3%83%A9%E3%83%BC-%E3%83%9F%E3%83%83%E3%83%89%E3%83%8A%E3%82%A4%E3%83%88-CFI-ZCP1J01/dp/B0DT2PGBYZ/ref=zg_bs_g_videogames_d_sccl_16/000-0000000-0000000?psc=1
2025-06-05 18:36:43 [ERROR] root: not processing pack: douyo:81e809593e9f4525587f863f6149eeed https://www.amazon.co.jp/%E3%82%BD%E3%83%8B%E3%83%BC%E3%83%BB%E3%82%A4%E3%83%B3%E3%82%BF%E3%83%A9%E3%82%AF%E3%83%86%E3%82%A3%E3%83%96%E3%82%A8%E3%83%B3%E3%82%BF%E3%83%86%E3%82%A4%E3%83%B3%E3%83%A1%E3%83%B3%E3%83%88-%E3%80%90PS5%E3%80%91DEATH-STRANDING-DIRECTORS-CUT/dp/B09923SLTK/ref=zg_bs_g_videogames_d_sccl_26/000-0000000-0000000?psc=1
2025-06-05 18:36:44 [ERROR] root: not processing pack: douyo:88ac8e1f5681b844442bcb25c3a746e5 https://www.amazon.co.jp/Nintendo-Switch-Online-%E5%80%8B%E4%BA%BA%E3%83%97%E3%83%A9%E3%83%B33%E3%81%8B%E6%9C%88-%E3%82%AA%E3%83%B3%E3%83%A9%E3%82%A4%E3%83%B3%E3%82%B3%E3%83%BC%E3%83%89%E7%89%88/dp/B07HG6HC3H/ref=zg_bs_g_videogames_d_sccl_16/000-0000000-0000000?psc=1
2025-06-05 18:36:45 [ERROR] root: not processing pack: douyo:ac6198c3e505101d67532a48a5c2988c https://www.amazon.co.jp/Joy-Con-L-%E3%83%8D%E3%82%AA%E3%83%B3%E3%83%96%E3%83%AB%E3%83%BC/dp/B071KPQD5X/ref=zg_bs_g_videogames_d_sccl_18/000-0000000-0000000?psc=1
2025-06-05 18:36:45 [INFO] libs.cache: Using Redis cache
2025-06-05 18:36:46 [ERROR] root: not processing pack: douyo:ae3bd2c959af13beb5c3697c7aafabda https://www.amazon.co.jp/%E3%80%90%E7%B4%94%E6%AD%A3%E5%93%81%E3%80%91DualSense-%E3%83%AF%E3%82%A4%E3%83%A4%E3%83%AC%E3%82%B9%E3%82%B3%E3%83%B3%E3%83%88%E3%83%AD%E3%83%BC%E3%83%A9%E3%83%BC-%E3%82%B3%E3%83%90%E3%83%AB%E3%83%88-%E3%83%96%E3%83%AB%E3%83%BC%E2%80%8B-CFI-ZCT1J09/dp/B0CK86FQY2/ref=zg_bs_g_videogames_d_sccl_17/000-0000000-0000000?psc=1
2025-06-05 18:36:46 [INFO] root: Created PID file for webui: /tmp/pyspider_webui.pid
2025-06-05 18:36:47 [ERROR] root: not processing pack: douyo:bbdbd3d611ef340d7a32cfcf7bb86567 https://www.amazon.co.jp/%E9%BE%8D%E3%81%AE%E5%9B%BD-%E3%83%AB%E3%83%BC%E3%83%B3%E3%83%95%E3%82%A1%E3%82%AF%E3%83%88%E3%83%AA%E3%83%BC-Nintendo-Switch-Switch2/dp/B0F5WKRKXD/ref=zg_bs_g_videogames_d_sccl_30/000-0000000-0000000?psc=1
2025-06-05 18:36:48 [ERROR] root: not processing pack: douyo:d73c3dc2a2c549c30948773f7c4096b3 https://www.amazon.co.jp/%E3%82%BC%E3%83%AB%E3%83%80%E3%81%AE%E4%BC%9D%E8%AA%AC-%E3%83%96%E3%83%AC%E3%82%B9-%E3%82%AA%E3%83%96-%E3%83%AF%E3%82%A4%E3%83%AB%E3%83%89-Switch/dp/B01N12HJHQ/ref=zg_bs_g_videogames_d_sccl_6/000-0000000-0000000?psc=1
2025-06-05 18:37:35 [INFO] metrics: Gauges: {"memory_usage_rss": 89378816, "memory_usage_vms": 636243968, "memory_usage_percent": 0.265022086190428, "system_memory_total": ***********, "system_memory_available": 21045264384, "system_memory_percent": 37.3}
2025-06-05 18:38:35 [INFO] metrics: Gauges: {"memory_usage_rss": 89378816, "memory_usage_vms": 636243968, "memory_usage_percent": 0.2661931849372303, "system_memory_total": ***********, "system_memory_available": 21165957120, "system_memory_percent": 37.0}
2025-06-05 18:39:35 [INFO] metrics: Gauges: {"memory_usage_rss": 89378816, "memory_usage_vms": 636243968, "memory_usage_percent": 0.2661931849372303, "system_memory_total": ***********, "system_memory_available": 21248843776, "system_memory_percent": 36.7}
2025-06-05 18:40:35 [INFO] metrics: Gauges: {"memory_usage_rss": 89378816, "memory_usage_vms": 636243968, "memory_usage_percent": 0.2661931849372303, "system_memory_total": ***********, "system_memory_available": 21593362432, "system_memory_percent": 35.7}
2025-06-05 18:41:35 [INFO] metrics: Gauges: {"memory_usage_rss": 89378816, "memory_usage_vms": 636243968, "memory_usage_percent": 0.2661931849372303, "system_memory_total": ***********, "system_memory_available": 21466275840, "system_memory_percent": 36.1}
2025-06-05 18:42:35 [INFO] metrics: Gauges: {"memory_usage_rss": 89378816, "memory_usage_vms": 636243968, "memory_usage_percent": 0.2661931849372303, "system_memory_total": ***********, "system_memory_available": 21665345536, "system_memory_percent": 35.5}
2025-06-05 18:43:35 [INFO] metrics: Gauges: {"memory_usage_rss": 89378816, "memory_usage_vms": 636243968, "memory_usage_percent": 0.2661931849372303, "system_memory_total": ***********, "system_memory_available": 21635809280, "system_memory_percent": 35.6}
2025-06-05 18:44:35 [INFO] metrics: Gauges: {"memory_usage_rss": 89378816, "memory_usage_vms": 636243968, "memory_usage_percent": 0.2661931849372303, "system_memory_total": ***********, "system_memory_available": 21061271552, "system_memory_percent": 37.3}
2025-06-05 18:45:35 [INFO] metrics: Gauges: {"memory_usage_rss": 89378816, "memory_usage_vms": 636243968, "memory_usage_percent": 0.2661931849372303, "system_memory_total": ***********, "system_memory_available": 21167185920, "system_memory_percent": 37.0}
2025-06-05 18:46:35 [INFO] metrics: Gauges: {"memory_usage_rss": 89378816, "memory_usage_vms": 636243968, "memory_usage_percent": 0.2661931849372303, "system_memory_total": ***********, "system_memory_available": 21555871744, "system_memory_percent": 35.8}
2025-06-05 18:46:40 [INFO] metrics: Counters: {"fetch_count[project=iuiu]": 1, "fetch_count[project=kokoko]": 1, "fetch_count[project=jijijiji]": 1, "fetch_count[project=mysql_detailed_test_2]": 1, "fetch_count[project=douyo]": 18, "fetch_count[project=mysql_detailed_test]": 1}
2025-06-05 18:46:40 [ERROR] root: not processing pack: douyo:1ab126e2a022911c500d93bad423b484 https://www.amazon.co.jp/Switch2-%E3%82%AC%E3%83%A9%E3%82%B9%E3%83%95%E3%82%A4%E3%83%AB%E3%83%A0-%E3%82%AC%E3%82%A4%E3%83%89%E6%9E%A0%E4%BB%98%E3%81%8D%E3%80%90Seninhi-%E6%97%A5%E6%9C%AC%E6%97%AD%E7%A1%9D%E5%AD%90%E8%A3%BD-%E9%AB%98-SENLX-TMSH2/dp/B0DZ2BFLPH/ref=zg_bs_g_videogames_d_sccl_10/000-0000000-0000000?psc=1
2025-06-05 18:46:40 [ERROR] root: not processing pack: douyo:81e809593e9f4525587f863f6149eeed https://www.amazon.co.jp/%E3%82%BD%E3%83%8B%E3%83%BC%E3%83%BB%E3%82%A4%E3%83%B3%E3%82%BF%E3%83%A9%E3%82%AF%E3%83%86%E3%82%A3%E3%83%96%E3%82%A8%E3%83%B3%E3%82%BF%E3%83%86%E3%82%A4%E3%83%B3%E3%83%A1%E3%83%B3%E3%83%88-%E3%80%90PS5%E3%80%91DEATH-STRANDING-DIRECTORS-CUT/dp/B09923SLTK/ref=zg_bs_g_videogames_d_sccl_26/000-0000000-0000000?psc=1
2025-06-05 18:46:41 [ERROR] root: not processing pack: douyo:88ac8e1f5681b844442bcb25c3a746e5 https://www.amazon.co.jp/Nintendo-Switch-Online-%E5%80%8B%E4%BA%BA%E3%83%97%E3%83%A9%E3%83%B33%E3%81%8B%E6%9C%88-%E3%82%AA%E3%83%B3%E3%83%A9%E3%82%A4%E3%83%B3%E3%82%B3%E3%83%BC%E3%83%89%E7%89%88/dp/B07HG6HC3H/ref=zg_bs_g_videogames_d_sccl_16/000-0000000-0000000?psc=1
2025-06-05 18:46:42 [ERROR] root: not processing pack: douyo:ac6198c3e505101d67532a48a5c2988c https://www.amazon.co.jp/Joy-Con-L-%E3%83%8D%E3%82%AA%E3%83%B3%E3%83%96%E3%83%AB%E3%83%BC/dp/B071KPQD5X/ref=zg_bs_g_videogames_d_sccl_18/000-0000000-0000000?psc=1
2025-06-05 18:46:43 [ERROR] root: not processing pack: douyo:1ea87cf2808630f3594ef373a156dcc1 https://www.amazon.co.jp/%E3%80%90%E7%B4%94%E6%AD%A3%E5%93%81%E3%80%91DualSense-Edge-%E3%83%AF%E3%82%A4%E3%83%A4%E3%83%AC%E3%82%B9%E3%82%B3%E3%83%B3%E3%83%88%E3%83%AD%E3%83%BC%E3%83%A9%E3%83%BC-%E3%83%9F%E3%83%83%E3%83%89%E3%83%8A%E3%82%A4%E3%83%88-CFI-ZCP1J01/dp/B0DT2PGBYZ/ref=zg_bs_g_videogames_d_sccl_16/000-0000000-0000000?psc=1
2025-06-05 18:46:43 [ERROR] root: not processing pack: douyo:ae3bd2c959af13beb5c3697c7aafabda https://www.amazon.co.jp/%E3%80%90%E7%B4%94%E6%AD%A3%E5%93%81%E3%80%91DualSense-%E3%83%AF%E3%82%A4%E3%83%A4%E3%83%AC%E3%82%B9%E3%82%B3%E3%83%B3%E3%83%88%E3%83%AD%E3%83%BC%E3%83%A9%E3%83%BC-%E3%82%B3%E3%83%90%E3%83%AB%E3%83%88-%E3%83%96%E3%83%AB%E3%83%BC%E2%80%8B-CFI-ZCT1J09/dp/B0CK86FQY2/ref=zg_bs_g_videogames_d_sccl_17/000-0000000-0000000?psc=1
{"timestamp": "2025-06-05T18:47:37.892256", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-05 18:47:38 [INFO] pyspider.libs.redis_fallback: ✅ Redis接続成功: redis://localhost:6379/0
2025-06-05 18:47:38 [INFO] pyspider.libs.redis_fallback: 🔍 Redis接続監視を開始しました
2025-06-05 18:47:38 [INFO] pyspider.libs.redis_fallback: ✅ Redis フォールバック機能を初期化しました
2025-06-05 18:47:38 [INFO] memory_optimizer: Memory optimizer initialized with max_memory_percent=80.0%, gc_interval=60s, check_interval=30s, auto_optimize=True
2025-06-05 18:47:38 [INFO] memory_optimizer: Memory monitoring started
2025-06-05 18:47:38 [INFO] root: Memory optimizer started for scheduler
2025-06-05 18:47:38 [INFO] root: Created PID file for scheduler: /tmp/pyspider_scheduler.pid
{"timestamp": "2025-06-05T18:47:40.027261", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-05 18:47:40 [INFO] root: Created PID file for fetcher: /tmp/pyspider_fetcher.pid
{"timestamp": "2025-06-05T18:47:42.240009", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-05 18:47:43 [INFO] root: Created PID file for processor: /tmp/pyspider_processor.pid
2025-06-05 18:47:44 [ERROR] root: not processing pack: jijijiji:558fcd3a023ec03dcf2797819589d46a https://www.amazon.co.jp/%E4%BB%BB%E5%A4%A9%E5%A0%82-Nintendo-Switch-Lite-%E3%82%B0%E3%83%AC%E3%83%BC/dp/B07X47QTN3/ref=zg_bs_g_videogames_d_sccl_3/000-0000000-0000000?psc=1
2025-06-05 18:47:44 [ERROR] root: not processing pack: jijijiji:610c2f6a9f62f4bc13f04b638682d3a9 https://www.amazon.co.jp/Nintendo-Switch-%E6%9C%89%E6%A9%9FEL%E3%83%A2%E3%83%87%E3%83%AB-%E3%83%8D%E3%82%AA%E3%83%B3%E3%83%96%E3%83%AB%E3%83%BC-%E3%83%8D%E3%82%AA%E3%83%B3%E3%83%AC%E3%83%83%E3%83%89/dp/B098B79SJL/ref=zg_bs_g_videogames_d_sccl_22/000-0000000-0000000?psc=1
{"timestamp": "2025-06-05T18:47:44.536608", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-05 18:47:45 [INFO] root: Created PID file for result_worker: /tmp/pyspider_result_worker.pid
2025-06-05 18:47:45 [INFO] result: result_worker starting...
2025-06-05 18:47:46 [ERROR] root: not processing pack: jijijiji:a857f277d91ba540ba01fd50400bb4c6 https://www.amazon.co.jp/%E4%BB%BB%E5%A4%A9%E5%A0%82-%E5%A4%A7%E4%B9%B1%E9%97%98%E3%82%B9%E3%83%9E%E3%83%83%E3%82%B7%E3%83%A5%E3%83%96%E3%83%A9%E3%82%B6%E3%83%BC%E3%82%BA-SPECIAL-Switch/dp/B07FDW61HX/ref=zg_bs_g_videogames_d_sccl_20/000-0000000-0000000?psc=1
{"timestamp": "2025-06-05T18:47:46.692949", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-05 18:47:46 [ERROR] root: not processing pack: jijijiji:71eaff9f1a08adf5ccc164081c1298dc https://www.amazon.co.jp/%E3%80%90Amazon-co-jp%E9%99%90%E5%AE%9A%E3%80%91Logicool-%E3%83%AD%E3%82%B8%E3%82%AF%E3%83%BC%E3%83%AB-G-PHS-003d-%E3%80%90Amazon-co-jp-%E9%99%90%E5%AE%9A%E5%A3%81%E7%B4%99%E3%83%80%E3%82%A6%E3%83%B3%E3%83%AD%E3%83%BC%E3%83%89%E4%BB%98%E3%81%8D%E3%80%91/dp/B0BC142H1L/ref=zg_bs_g_videogames_d_sccl_15/000-0000000-0000000?psc=1
2025-06-05 18:47:47 [ERROR] root: not processing pack: jijijiji:ccc726e6af8d56fdeccfe00ceeb92e83 https://www.amazon.co.jp/%E3%82%AB%E3%83%97%E3%82%B3%E3%83%B3-CAPCOM-ELJM-30582-%E3%80%90PS5%E3%80%91%E3%83%A2%E3%83%B3%E3%82%B9%E3%82%BF%E3%83%BC%E3%83%8F%E3%83%B3%E3%82%BF%E3%83%BC%E3%83%AF%E3%82%A4%E3%83%AB%E3%82%BA/dp/B0DHCTRH16/ref=zg_bs_g_videogames_d_sccl_15/000-0000000-0000000?psc=1
2025-06-05 18:47:47 [ERROR] root: not processing pack: jijijiji:a1418923683dd1aaa093e1be1209d397 https://www.amazon.co.jp/%E3%82%BD%E3%83%8B%E3%83%BC%E3%83%BB%E3%82%A4%E3%83%B3%E3%82%BF%E3%83%A9%E3%82%AF%E3%83%86%E3%82%A3%E3%83%96%E3%82%A8%E3%83%B3%E3%82%BF%E3%83%86%E3%82%A4%E3%83%B3%E3%83%A1%E3%83%B3%E3%83%88-PlayStation-5-CFI-2000A01/dp/B0CKYM15RJ/ref=zg_bs_g_videogames_d_sccl_3/000-0000000-0000000?psc=1
2025-06-05 18:47:48 [INFO] libs.cache: Using Redis cache
2025-06-05 18:47:49 [INFO] root: Created PID file for webui: /tmp/pyspider_webui.pid
2025-06-05 18:47:51 [ERROR] root: not processing pack: jijijiji:e0d28106efbcdbcf6c2cf0db2ec7c2fd https://www.amazon.co.jp/%E3%82%BD%E3%83%8B%E3%83%BC-%E3%82%B2%E3%83%BC%E3%83%9F%E3%83%B3%E3%82%B0%E3%82%A4%E3%83%A4%E3%83%9B%E3%83%B3-INZONE-Buds-USBType-C%E3%83%88%E3%83%A9%E3%83%B3%E3%82%B7%E3%83%BC%E3%83%90%E3%83%BC%E5%90%8C%E6%A2%B1/dp/B0CKFHB9P3/ref=zg_bs_g_videogames_d_sccl_13/000-0000000-0000000?psc=1
2025-06-05 18:48:38 [INFO] metrics: Gauges: {"memory_usage_rss": 89137152, "memory_usage_vms": 636231680, "memory_usage_percent": 0.2650830809168239, "system_memory_total": ***********, "system_memory_available": 21682884608, "system_memory_percent": 35.4}
2025-06-05 18:49:38 [INFO] metrics: Gauges: {"memory_usage_rss": 89137152, "memory_usage_vms": 636231680, "memory_usage_percent": 0.26547344716575805, "system_memory_total": ***********, "system_memory_available": 21432799232, "system_memory_percent": 36.2}
2025-06-05 18:50:39 [INFO] metrics: Gauges: {"memory_usage_rss": 89137152, "memory_usage_vms": 636231680, "memory_usage_percent": 0.26547344716575805, "system_memory_total": ***********, "system_memory_available": 21739872256, "system_memory_percent": 35.3}
2025-06-05 18:51:39 [INFO] metrics: Gauges: {"memory_usage_rss": 89137152, "memory_usage_vms": 636231680, "memory_usage_percent": 0.26547344716575805, "system_memory_total": ***********, "system_memory_available": 21442727936, "system_memory_percent": 36.1}
2025-06-05 18:52:02 [INFO] metrics: Counters: {"fetch_count[project=iuiu]": 1, "fetch_count[project=douyo]": 1, "fetch_count[project=jijijiji]": 16, "fetch_count[project=mysql_detailed_test_2]": 1, "fetch_count[project=mysql_detailed_test]": 1, "fetch_count[project=kokoko]": 1, "fetch_count[project=bunbunbun]": 1}
2025-06-05 18:52:06 [ERROR] root: not processing pack: jijijiji:on_start data:,on_start
2025-06-05 18:52:39 [INFO] metrics: Gauges: {"memory_usage_rss": 89268224, "memory_usage_vms": 636231680, "memory_usage_percent": 0.26547344716575805, "system_memory_total": ***********, "system_memory_available": 21662527488, "system_memory_percent": 35.5}
2025-06-05 18:53:39 [INFO] metrics: Gauges: {"memory_usage_rss": 89399296, "memory_usage_vms": 637280256, "memory_usage_percent": 0.26625417966362624, "system_memory_total": ***********, "system_memory_available": 21193523200, "system_memory_percent": 36.9}
2025-06-05 18:54:39 [INFO] metrics: Gauges: {"memory_usage_rss": 89399296, "memory_usage_vms": 637280256, "memory_usage_percent": 0.26625417966362624, "system_memory_total": ***********, "system_memory_available": 21506830336, "system_memory_percent": 35.9}
2025-06-05 18:55:39 [INFO] metrics: Gauges: {"memory_usage_rss": 89399296, "memory_usage_vms": 637280256, "memory_usage_percent": 0.26625417966362624, "system_memory_total": ***********, "system_memory_available": 21505351680, "system_memory_percent": 36.0}
2025-06-05 18:56:39 [INFO] metrics: Gauges: {"memory_usage_rss": 89399296, "memory_usage_vms": 637280256, "memory_usage_percent": 0.26625417966362624, "system_memory_total": ***********, "system_memory_available": 21639557120, "system_memory_percent": 35.6}
2025-06-05 18:57:39 [INFO] metrics: Gauges: {"memory_usage_rss": 89399296, "memory_usage_vms": 637280256, "memory_usage_percent": 0.26625417966362624, "system_memory_total": ***********, "system_memory_available": 21531906048, "system_memory_percent": 35.9}
2025-06-05 18:57:43 [INFO] metrics: Counters: {"fetch_count[project=iuiu]": 1, "fetch_count[project=douyo]": 1, "fetch_count[project=jijijiji]": 18, "fetch_count[project=mysql_detailed_test_2]": 1, "fetch_count[project=mysql_detailed_test]": 1, "fetch_count[project=kokoko]": 1, "fetch_count[project=bunbunbun]": 2}
2025-06-05 18:57:44 [ERROR] root: not processing pack: jijijiji:558fcd3a023ec03dcf2797819589d46a https://www.amazon.co.jp/%E4%BB%BB%E5%A4%A9%E5%A0%82-Nintendo-Switch-Lite-%E3%82%B0%E3%83%AC%E3%83%BC/dp/B07X47QTN3/ref=zg_bs_g_videogames_d_sccl_3/000-0000000-0000000?psc=1
2025-06-05 18:57:44 [ERROR] root: not processing pack: jijijiji:610c2f6a9f62f4bc13f04b638682d3a9 https://www.amazon.co.jp/Nintendo-Switch-%E6%9C%89%E6%A9%9FEL%E3%83%A2%E3%83%87%E3%83%AB-%E3%83%8D%E3%82%AA%E3%83%B3%E3%83%96%E3%83%AB%E3%83%BC-%E3%83%8D%E3%82%AA%E3%83%B3%E3%83%AC%E3%83%83%E3%83%89/dp/B098B79SJL/ref=zg_bs_g_videogames_d_sccl_22/000-0000000-0000000?psc=1
2025-06-05 18:57:46 [ERROR] root: not processing pack: jijijiji:a857f277d91ba540ba01fd50400bb4c6 https://www.amazon.co.jp/%E4%BB%BB%E5%A4%A9%E5%A0%82-%E5%A4%A7%E4%B9%B1%E9%97%98%E3%82%B9%E3%83%9E%E3%83%83%E3%82%B7%E3%83%A5%E3%83%96%E3%83%A9%E3%82%B6%E3%83%BC%E3%82%BA-SPECIAL-Switch/dp/B07FDW61HX/ref=zg_bs_g_videogames_d_sccl_20/000-0000000-0000000?psc=1
2025-06-05 18:57:46 [ERROR] root: not processing pack: jijijiji:71eaff9f1a08adf5ccc164081c1298dc https://www.amazon.co.jp/%E3%80%90Amazon-co-jp%E9%99%90%E5%AE%9A%E3%80%91Logicool-%E3%83%AD%E3%82%B8%E3%82%AF%E3%83%BC%E3%83%AB-G-PHS-003d-%E3%80%90Amazon-co-jp-%E9%99%90%E5%AE%9A%E5%A3%81%E7%B4%99%E3%83%80%E3%82%A6%E3%83%B3%E3%83%AD%E3%83%BC%E3%83%89%E4%BB%98%E3%81%8D%E3%80%91/dp/B0BC142H1L/ref=zg_bs_g_videogames_d_sccl_15/000-0000000-0000000?psc=1
2025-06-05 18:57:47 [ERROR] root: not processing pack: jijijiji:ccc726e6af8d56fdeccfe00ceeb92e83 https://www.amazon.co.jp/%E3%82%AB%E3%83%97%E3%82%B3%E3%83%B3-CAPCOM-ELJM-30582-%E3%80%90PS5%E3%80%91%E3%83%A2%E3%83%B3%E3%82%B9%E3%82%BF%E3%83%BC%E3%83%8F%E3%83%B3%E3%82%BF%E3%83%BC%E3%83%AF%E3%82%A4%E3%83%AB%E3%82%BA/dp/B0DHCTRH16/ref=zg_bs_g_videogames_d_sccl_15/000-0000000-0000000?psc=1
2025-06-05 18:57:47 [ERROR] root: not processing pack: jijijiji:a1418923683dd1aaa093e1be1209d397 https://www.amazon.co.jp/%E3%82%BD%E3%83%8B%E3%83%BC%E3%83%BB%E3%82%A4%E3%83%B3%E3%82%BF%E3%83%A9%E3%82%AF%E3%83%86%E3%82%A3%E3%83%96%E3%82%A8%E3%83%B3%E3%82%BF%E3%83%86%E3%82%A4%E3%83%B3%E3%83%A1%E3%83%B3%E3%83%88-PlayStation-5-CFI-2000A01/dp/B0CKYM15RJ/ref=zg_bs_g_videogames_d_sccl_3/000-0000000-0000000?psc=1
2025-06-05 18:57:55 [ERROR] root: not processing pack: jijijiji:e0d28106efbcdbcf6c2cf0db2ec7c2fd https://www.amazon.co.jp/%E3%82%BD%E3%83%8B%E3%83%BC-%E3%82%B2%E3%83%BC%E3%83%9F%E3%83%B3%E3%82%B0%E3%82%A4%E3%83%A4%E3%83%9B%E3%83%B3-INZONE-Buds-USBType-C%E3%83%88%E3%83%A9%E3%83%B3%E3%82%B7%E3%83%BC%E3%83%90%E3%83%BC%E5%90%8C%E6%A2%B1/dp/B0CKFHB9P3/ref=zg_bs_g_videogames_d_sccl_13/000-0000000-0000000?psc=1
2025-06-05 18:57:55 [INFO] result: result jijijiji:e0d28106efbcdbcf6c2cf0db2ec7c2fd https://www.amazon.co.jp/%E3%82%BD%E3%83%8B%E3%83%BC-%E3%82%B2%E3%83%BC%E3%83%9F%E3%83%B3%E3%82%B0%E3%82%A4%E3%83%A4%E3%83%9B%E3%83%B3-INZONE-Buds-USBType-C%E3%83%88%E3%83%A9%E3%83%B3%E3%82%B7%E3%83%BC%E3%83%90%E3%83%BC%E5%90%8C%E6%A2%B1/dp/B0CKFHB9P3/ref=zg_bs_g_videogames_d_sccl_13/000-0000000-0000000?psc=1 -> {'url': 'https://www.amazon.co
2025-06-05 18:58:39 [INFO] metrics: Gauges: {"memory_usage_rss": 89399296, "memory_usage_vms": 637280256, "memory_usage_percent": 0.26625417966362624, "system_memory_total": ***********, "system_memory_available": 21455958016, "system_memory_percent": 36.1}
2025-06-05 18:59:39 [INFO] metrics: Gauges: {"memory_usage_rss": 89399296, "memory_usage_vms": 637280256, "memory_usage_percent": 0.26625417966362624, "system_memory_total": ***********, "system_memory_available": 21635801088, "system_memory_percent": 35.6}
2025-06-05 19:00:39 [INFO] metrics: Gauges: {"memory_usage_rss": 89399296, "memory_usage_vms": 637280256, "memory_usage_percent": 0.26625417966362624, "system_memory_total": ***********, "system_memory_available": 21566541824, "system_memory_percent": 35.8}
2025-06-05 19:01:39 [INFO] metrics: Gauges: {"memory_usage_rss": 89399296, "memory_usage_vms": 637280256, "memory_usage_percent": 0.26625417966362624, "system_memory_total": ***********, "system_memory_available": 21471522816, "system_memory_percent": 36.1}
2025-06-05 19:02:02 [INFO] metrics: Counters: {"fetch_count[project=iuiu]": 1, "fetch_count[project=douyo]": 1, "fetch_count[project=jijijiji]": 24, "fetch_count[project=mysql_detailed_test_2]": 1, "fetch_count[project=mysql_detailed_test]": 1, "fetch_count[project=kokoko]": 1, "fetch_count[project=bunbunbun]": 3}
2025-06-05 19:02:06 [ERROR] root: not processing pack: jijijiji:on_start data:,on_start
2025-06-05 19:02:39 [INFO] metrics: Gauges: {"memory_usage_rss": 89399296, "memory_usage_vms": 637280256, "memory_usage_percent": 0.26625417966362624, "system_memory_total": ***********, "system_memory_available": 21653655552, "system_memory_percent": 35.5}
2025-06-05 19:03:39 [INFO] metrics: Gauges: {"memory_usage_rss": 89530368, "memory_usage_vms": 637280256, "memory_usage_percent": 0.26625417966362624, "system_memory_total": ***********, "system_memory_available": 21611769856, "system_memory_percent": 35.6}
2025-06-05 19:04:39 [INFO] metrics: Gauges: {"memory_usage_rss": 89530368, "memory_usage_vms": 637280256, "memory_usage_percent": 0.2666445459125604, "system_memory_total": ***********, "system_memory_available": 20994457600, "system_memory_percent": 37.5}
2025-06-05 19:05:39 [INFO] metrics: Gauges: {"memory_usage_rss": 89530368, "memory_usage_vms": 637280256, "memory_usage_percent": 0.2666445459125604, "system_memory_total": ***********, "system_memory_available": 21661663232, "system_memory_percent": 35.5}
{"timestamp": "2025-06-05T19:06:07.279239", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-05 19:06:08 [INFO] pyspider.libs.redis_fallback: ✅ Redis接続成功: redis://localhost:6379/0
2025-06-05 19:06:08 [INFO] pyspider.libs.redis_fallback: 🔍 Redis接続監視を開始しました
2025-06-05 19:06:08 [INFO] pyspider.libs.redis_fallback: ✅ Redis フォールバック機能を初期化しました
2025-06-05 19:06:08 [INFO] memory_optimizer: Memory optimizer initialized with max_memory_percent=80.0%, gc_interval=60s, check_interval=30s, auto_optimize=True
2025-06-05 19:06:08 [INFO] memory_optimizer: Memory monitoring started
2025-06-05 19:06:08 [INFO] root: Memory optimizer started for scheduler
2025-06-05 19:06:08 [INFO] root: Created PID file for scheduler: /tmp/pyspider_scheduler.pid
{"timestamp": "2025-06-05T19:06:09.540994", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-05 19:06:09 [INFO] root: Created PID file for fetcher: /tmp/pyspider_fetcher.pid
{"timestamp": "2025-06-05T19:06:11.726877", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-05 19:06:12 [INFO] root: Created PID file for processor: /tmp/pyspider_processor.pid
{"timestamp": "2025-06-05T19:06:13.662311", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-05 19:06:14 [INFO] root: Created PID file for result_worker: /tmp/pyspider_result_worker.pid
2025-06-05 19:06:14 [INFO] result: result_worker starting...
2025-06-05 19:06:14 [INFO] result: result bunbunbun:8e67e5e5edf32d68195b61e8b17f1d32 http://httpbin.org/ -> {'url': 'http://httpbin.org/',
{"timestamp": "2025-06-05T19:06:15.718164", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-05 19:06:16 [INFO] libs.cache: Using Redis cache
2025-06-05 19:06:17 [INFO] root: Created PID file for webui: /tmp/pyspider_webui.pid
2025-06-05 19:07:08 [INFO] metrics: Gauges: {"memory_usage_rss": 89153536, "memory_usage_vms": 636178432, "memory_usage_percent": 0.2655222429468748, "system_memory_total": ***********, "system_memory_available": 21092311040, "system_memory_percent": 37.2}
2025-06-05 19:08:08 [INFO] metrics: Gauges: {"memory_usage_rss": 89153536, "memory_usage_vms": 636178432, "memory_usage_percent": 0.2655222429468748, "system_memory_total": ***********, "system_memory_available": 21164453888, "system_memory_percent": 37.0}
2025-06-05 19:09:08 [INFO] metrics: Gauges: {"memory_usage_rss": 89153536, "memory_usage_vms": 636178432, "memory_usage_percent": 0.2655222429468748, "system_memory_total": ***********, "system_memory_available": 21404266496, "system_memory_percent": 36.3}
2025-06-05 19:10:08 [INFO] metrics: Gauges: {"memory_usage_rss": 89153536, "memory_usage_vms": 636178432, "memory_usage_percent": 0.2655222429468748, "system_memory_total": ***********, "system_memory_available": 20922519552, "system_memory_percent": 37.7}
2025-06-05 19:11:08 [INFO] metrics: Gauges: {"memory_usage_rss": 89153536, "memory_usage_vms": 636178432, "memory_usage_percent": 0.2655222429468748, "system_memory_total": ***********, "system_memory_available": 20670513152, "system_memory_percent": 38.4}
2025-06-05 19:12:08 [INFO] metrics: Gauges: {"memory_usage_rss": 89153536, "memory_usage_vms": 636178432, "memory_usage_percent": 0.2655222429468748, "system_memory_total": ***********, "system_memory_available": 20840169472, "system_memory_percent": 37.9}
{"timestamp": "2025-06-06T15:08:21.789331", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-06 15:08:22 [INFO] pyspider.libs.redis_fallback: ✅ Redis接続成功: redis://localhost:6379/0
2025-06-06 15:08:22 [INFO] pyspider.libs.redis_fallback: 🔍 Redis接続監視を開始しました
2025-06-06 15:08:22 [INFO] pyspider.libs.redis_fallback: ✅ Redis フォールバック機能を初期化しました
2025-06-06 15:08:22 [INFO] memory_optimizer: Memory optimizer initialized with max_memory_percent=80.0%, gc_interval=60s, check_interval=30s, auto_optimize=True
2025-06-06 15:08:22 [INFO] memory_optimizer: Memory monitoring started
2025-06-06 15:08:22 [INFO] root: Memory optimizer started for scheduler
2025-06-06 15:08:22 [INFO] root: Created PID file for scheduler: /tmp/pyspider_scheduler.pid
{"timestamp": "2025-06-06T15:08:23.830283", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-06 15:08:24 [INFO] root: Created PID file for fetcher: /tmp/pyspider_fetcher.pid
{"timestamp": "2025-06-06T15:08:26.038047", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-06 15:08:26 [INFO] root: Created PID file for processor: /tmp/pyspider_processor.pid
{"timestamp": "2025-06-06T15:08:27.972656", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-06 15:08:28 [INFO] root: Created PID file for result_worker: /tmp/pyspider_result_worker.pid
2025-06-06 15:08:28 [INFO] result: result_worker starting...
{"timestamp": "2025-06-06T15:08:30.019793", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-06 15:08:30 [INFO] libs.cache: Using Redis cache
2025-06-06 15:08:31 [INFO] root: Created PID file for webui: /tmp/pyspider_webui.pid
{"timestamp": "2025-06-06T15:09:05.783655", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-06 15:09:06 [INFO] pyspider.libs.redis_fallback: ✅ Redis接続成功: redis://localhost:6379/0
2025-06-06 15:09:06 [INFO] pyspider.libs.redis_fallback: 🔍 Redis接続監視を開始しました
2025-06-06 15:09:06 [INFO] pyspider.libs.redis_fallback: ✅ Redis フォールバック機能を初期化しました
2025-06-06 15:09:06 [INFO] memory_optimizer: Memory optimizer initialized with max_memory_percent=80.0%, gc_interval=60s, check_interval=30s, auto_optimize=True
2025-06-06 15:09:06 [INFO] memory_optimizer: Memory monitoring started
2025-06-06 15:09:06 [INFO] root: Memory optimizer started for scheduler
2025-06-06 15:09:06 [INFO] root: Created PID file for scheduler: /tmp/pyspider_scheduler.pid
{"timestamp": "2025-06-06T15:09:07.896295", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-06 15:09:08 [INFO] root: Created PID file for fetcher: /tmp/pyspider_fetcher.pid
{"timestamp": "2025-06-06T15:09:10.895506", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-06 15:09:11 [INFO] root: Created PID file for processor: /tmp/pyspider_processor.pid
{"timestamp": "2025-06-06T15:09:12.895876", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-06 15:09:13 [INFO] root: Created PID file for result_worker: /tmp/pyspider_result_worker.pid
2025-06-06 15:09:13 [INFO] result: result_worker starting...
{"timestamp": "2025-06-06T15:09:14.945196", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-06 15:09:15 [INFO] libs.cache: Using Redis cache
2025-06-06 15:09:15 [INFO] root: Created PID file for webui: /tmp/pyspider_webui.pid
2025-06-06 15:10:08 [INFO] metrics: Gauges: {"memory_usage_rss": 88231936, "memory_usage_vms": 636157952, "memory_usage_percent": 0.2627774482029797, "system_memory_total": 33576677376, "system_memory_available": 29755703296, "system_memory_percent": 11.4}
2025-06-06 15:10:58 [INFO] metrics: Counters: {"fetch_count[project=bunbunbun]": 1, "fetch_count[project=douyo]": 1, "fetch_count[project=kokoko]": 1, "fetch_count[project=iuiu]": 1, "fetch_count[project=mysql_detailed_test]": 1, "fetch_count[project=jijijiji]": 1, "fetch_count[project=mysql_detailed_test_2]": 1, "fetch_count[project=tinti]": 1}
2025-06-06 15:11:09 [INFO] metrics: Gauges: {"memory_usage_rss": 89018368, "memory_usage_vms": 636293120, "memory_usage_percent": 0.2627774482029797, "system_memory_total": 33576677376, "system_memory_available": 29711802368, "system_memory_percent": 11.5}
2025-06-06 15:11:10 [INFO] result: result tinti:35d7cfb8774603fa9b668108064099aa https://www.amazon.co.jp/Nintendo-%E6%97%A5%E6%9C%AC%E8%AA%9E%E3%83%BB%E5%9B%BD%E5%86%85%E5%B0%82%E7%94%A8-%E3%80%90%E4%BB%BB%E5%A4%A9%E5%A0%82%E3%83%A9%E3%82%A4%E3%82%BB%E3%83%B3%E3%82%B9%E5%95%86%E5%93%81%E3%80%91Samsung-microSD-Express/dp/B0DX1588W5/ref=zg_bs_g_videogames_d_sccl_1/000-0000000-0000000?psc=1 -> {'url': 'https://www.amazon.co
2025-06-06 15:11:13 [INFO] result: result tinti:3b0a3ff39559d928685d63fd0edf9f00 https://www.amazon.co.jp/JSAUX-GP0123-%E3%80%903%E6%9E%9A%E3%82%BB%E3%83%83%E3%83%88%E3%80%91Switch-2%E5%AF%BE%E5%BF%9C-%E5%BC%B7%E5%8C%96%E3%82%AC%E3%83%A9%E3%82%B9%E3%83%95%E3%82%A3%E3%83%AB%E3%83%A0%EF%BD%9CJSAUX%E5%8F%96%E4%BB%98%E3%82%AC%E3%82%A4%E3%83%89%E4%BB%98%E3%81%8D%E3%83%BB%E3%81%8B%E3%82%93%E3%81%9F%E3%82%93%E8%B2%BC%E4%BB%98%E3%83%BB9H%E7%A1%AC%E5%BA%A6%E3%83%BB%E6%B0%97%E6%B3%A1%E3%82%BC%E3%83%AD%E3%83%BB%E6%8C%87%E7%B4%8B%E9%98%B2%E6%AD%A2%E3%83%BB%E9%AB%98%E9%80%8F%E9%81%8E%E7%8E%87%E3%83%BB%E3%83%89%E3%83%83%E3%82%AF%E5%AF%BE%E5%BF%9C/dp/B0DW8WQ8L2/ref=zg_bs_g_videogames_d_sccl_3/000-0000000-0000000?psc=1 -> {'url': 'https://www.amazon.co
2025-06-06 15:11:15 [INFO] result: result tinti:a405c377a4807633548416a3c524a7da https://www.amazon.co.jp/%E4%BB%BB%E5%A4%A9%E5%A0%82-HAC-P-A7HLA-%E3%82%B9%E3%83%BC%E3%83%91%E3%83%BC-%E3%83%9E%E3%83%AA%E3%82%AA%E3%83%91%E3%83%BC%E3%83%86%E3%82%A3-%E3%82%B8%E3%83%A3%E3%83%B3%E3%83%9C%E3%83%AA%E3%83%BC/dp/B0D7GQRT8R/ref=zg_bs_g_videogames_d_sccl_8/000-0000000-0000000?psc=1 -> {'url': 'https://www.amazon.co
2025-06-06 15:11:17 [INFO] result: result tinti:4b2d55a55b04de9aea3fcee4d3ac41d0 https://www.amazon.co.jp/%E3%83%AC%E3%83%99%E3%83%AB%E3%83%95%E3%82%A1%E3%82%A4%E3%83%96-HAC-P-A8C8A-%E3%83%95%E3%82%A1%E3%83%B3%E3%82%BF%E3%82%B8%E3%83%BC%E3%83%A9%E3%82%A4%E3%83%95%EF%BD%89-%E3%82%B0%E3%83%AB%E3%82%B0%E3%83%AB%E3%81%AE%E7%AB%9C%E3%81%A8%E6%99%82%E3%82%92%E3%81%AC%E3%81%99%E3%82%80%E5%B0%91%E5%A5%B3-Switch/dp/B0DX747PD2/ref=zg_bs_g_videogames_d_sccl_4/000-0000000-0000000?psc=1 -> {'url': 'https://www.amazon.co
2025-06-06 15:11:19 [INFO] result: result tinti:8645c6dc860ea2f2e5dcf4d83cf7ff9a https://www.amazon.co.jp/Nintendo-Switch-%E6%97%A5%E6%9C%AC%E8%AA%9E%E3%83%BB%E5%9B%BD%E5%86%85%E5%B0%82%E7%94%A8-%E3%83%9E%E3%83%AA%E3%82%AA%E3%82%AB%E3%83%BC%E3%83%88-%E3%83%AF%E3%83%BC%E3%83%AB%E3%83%89/dp/B0DWZJBXNZ/ref=zg_bs_g_videogames_d_sccl_2/000-0000000-0000000?psc=1 -> {'url': 'https://www.amazon.co
2025-06-06 15:11:22 [INFO] result: result tinti:4d289c35cacc77d527be687d110757f3 https://www.amazon.co.jp/Agrado-%E3%82%AC%E3%83%A9%E3%82%B9%E3%83%95%E3%82%A3%E3%83%AB%E3%83%A0-Switch-%E3%83%96%E3%83%AB%E3%83%BC%E3%83%A9%E3%82%A4%E3%83%88%E3%82%AB%E3%83%83%E3%83%88-%E4%BF%9D%E8%AD%B7%E3%83%95%E3%82%A3%E3%83%AB%E3%83%A0/dp/B0DY14JF3L/ref=zg_bs_g_videogames_d_sccl_9/000-0000000-0000000?psc=1 -> {'url': 'https://www.amazon.co
2025-06-06 15:11:24 [INFO] result: result tinti:5128941b77cd636d1f42187edb8b0bdb https://www.amazon.co.jp/%E3%83%9E%E3%83%BC%E3%83%99%E3%83%A9%E3%82%B9-HAC-P-BEWEA-%E9%BE%8D%E3%81%AE%E5%9B%BD-%E3%83%AB%E3%83%BC%E3%83%B3%E3%83%95%E3%82%A1%E3%82%AF%E3%83%88%E3%83%AA%E3%83%BC-Switch/dp/B0DT3J2176/ref=zg_bs_g_videogames_d_sccl_5/000-0000000-0000000?psc=1 -> {'url': 'https://www.amazon.co
2025-06-06 15:11:26 [INFO] result: result tinti:df592ff102d4d0c522b2c44f12fbd516 https://www.amazon.co.jp/%E3%83%90%E3%83%B3%E3%83%80%E3%82%A4%E3%83%8A%E3%83%A0%E3%82%B3%E3%82%A8%E3%83%B3%E3%82%BF%E3%83%BC%E3%83%86%E3%82%A4%E3%83%B3%E3%83%A1%E3%83%B3%E3%83%88-HAC-P-BB2BA-%E3%81%9F%E3%81%BE%E3%81%94%E3%81%A3%E3%81%A1%E3%81%AE%E3%83%97%E3%83%81%E3%83%97%E3%83%81%E3%81%8A%E3%81%BF%E3%81%9B%E3%81%A3%E3%81%A1-%E3%81%8A%E3%81%BE%E3%81%A1%E3%81%A9%EF%BD%9E%E3%81%95%E3%81%BE%EF%BC%81-Switch/dp/B0F5QJ6CCL/ref=zg_bs_g_videogames_d_sccl_10/000-0000000-0000000?psc=1 -> {'url': 'https://www.amazon.co
2025-06-06 15:11:28 [INFO] result: result tinti:71378a6c3486293c50b905bf4352db17 https://www.amazon.co.jp/Nintendo-Switch-2-%E3%81%AE%E3%81%B2%E3%81%BF%E3%81%A4%E5%B1%95-%E3%82%AA%E3%83%B3%E3%83%A9%E3%82%A4%E3%83%B3%E3%82%B3%E3%83%BC%E3%83%89%E7%89%88/dp/B0F5GGQWNZ/ref=zg_bs_g_videogames_d_sccl_20/000-0000000-0000000?psc=1 -> {'url': 'https://www.amazon.co
2025-06-06 15:11:30 [INFO] result: result tinti:bce0985f970c2044b148a25802a2c359 https://www.amazon.co.jp/%E4%BB%BB%E5%A4%A9%E5%A0%82-BEE-P-AAAAA-%E3%83%9E%E3%83%AA%E3%82%AA%E3%82%AB%E3%83%BC%E3%83%88-%E3%83%AF%E3%83%BC%E3%83%AB%E3%83%89-Switch2/dp/B0F54D861F/ref=zg_bs_g_videogames_d_sccl_11/000-0000000-0000000?psc=1 -> {'url': 'https://www.amazon.co
2025-06-06 15:11:32 [INFO] result: result tinti:fe659f8c91207fd10c90ac8d6f7622bc https://www.amazon.co.jp/%E3%82%B9%E3%82%AF%E3%82%A6%E3%82%A7%E3%82%A2%E3%83%BB%E3%82%A8%E3%83%8B%E3%83%83%E3%82%AF%E3%82%B9-%E3%83%89%E3%83%A9%E3%82%B4%E3%83%B3%E3%82%AF%E3%82%A8%E3%82%B9%E3%83%88I%EF%BC%86II-Switch/dp/B0F9PQ43Y5/ref=zg_bs_g_videogames_d_sccl_12/000-0000000-0000000?psc=1 -> {'url': 'https://www.amazon.co
2025-06-06 15:11:33 [INFO] result: result tinti:6d0b50c4e0ff7c03be31d8526041f3ea https://www.amazon.co.jp/%E3%82%B9%E3%82%AF%E3%82%A6%E3%82%A7%E3%82%A2%E3%83%BB%E3%82%A8%E3%83%8B%E3%83%83%E3%82%AF%E3%82%B9-%E3%83%89%E3%83%A9%E3%82%B4%E3%83%B3%E3%82%AF%E3%82%A8%E3%82%B9%E3%83%88I%EF%BC%86II-%E3%80%90Amazon-co-jp%E9%99%90%E5%AE%9A%E7%89%B9%E5%85%B8%E3%80%91%E6%97%85%E4%BA%BA%E3%81%AE%E3%81%9F%E3%81%97%E3%81%AA%E3%81%BF%E3%82%BB%E3%83%83%E3%83%88-%E3%82%B3%E3%83%BC%E3%83%89%E9%85%8D%E4%BF%A1-Switch/dp/B0F9PPHDQL/ref=zg_bs_g_videogames_d_sccl_18/000-0000000-0000000?psc=1 -> {'url': 'https://www.amazon.co
2025-06-06 15:11:35 [INFO] result: result tinti:745225fea6e70cdf394780d86fa50a29 https://www.amazon.co.jp/Roblox%E3%82%AE%E3%83%95%E3%83%88%E3%82%AB%E3%83%BC%E3%83%89-%E3%80%90%E9%99%90%E5%AE%9A%E3%83%90%E3%83%BC%E3%83%81%E3%83%A3%E3%83%AB%E3%82%A2%E3%82%A4%E3%83%86%E3%83%A0%E3%82%92%E5%90%AB%E3%82%80%E3%80%91-%E3%80%90%E3%82%AA%E3%83%B3%E3%83%A9%E3%82%A4%E3%83%B3%E3%82%B2%E3%83%BC%E3%83%A0%E3%82%B3%E3%83%BC%E3%83%89%E3%80%91-%E3%83%AD%E3%83%96%E3%83%AD%E3%83%83%E3%82%AF%E3%82%B9-%E3%82%AA%E3%83%B3%E3%83%A9%E3%82%A4%E3%83%B3%E3%82%B3%E3%83%BC%E3%83%89%E7%89%88/dp/B09NQC37C8/ref=zg_bs_g_videogames_d_sccl_24/000-0000000-0000000?psc=1 -> {'url': 'https://www.amazon.co
2025-06-06 15:11:37 [INFO] result: result tinti:baa1fbc75b3a0e947bfcb0b3601d3f02 https://www.amazon.co.jp/%E3%80%90%E7%B4%94%E6%AD%A3%E5%93%81%E3%80%91DualSense-%E3%83%AF%E3%82%A4%E3%83%A4%E3%83%AC%E3%82%B9%E3%82%B3%E3%83%B3%E3%83%88%E3%83%AD%E3%83%BC%E3%83%A9%E3%83%BC-%E3%83%9F%E3%83%83%E3%83%89%E3%83%8A%E3%82%A4%E3%83%88-%E3%83%96%E3%83%A9%E3%83%83%E3%82%AF-CFI-ZCT1J01/dp/B094VJXGGL/ref=zg_bs_g_videogames_d_sccl_15/000-0000000-0000000?psc=1 -> {'url': 'https://www.amazon.co
2025-06-06 15:11:39 [INFO] result: result tinti:487bd9a0f45bc93e91babe967eb718a4 https://www.amazon.co.jp/%E3%83%9E%E3%82%A4%E3%82%AF%E3%83%AD%E3%82%BD%E3%83%95%E3%83%88-Minecraft-%E3%83%9E%E3%82%A4%E3%83%B3%E3%82%AF%E3%83%A9%E3%83%95%E3%83%88-Switch/dp/B07D131MS4/ref=zg_bs_g_videogames_d_sccl_13/000-0000000-0000000?psc=1 -> {'url': 'https://www.amazon.co
2025-06-06 15:11:41 [INFO] result: result tinti:93cbf87c35f8ddc98c5a361f20681273 https://www.amazon.co.jp/%E3%82%B9%E3%82%AF%E3%82%A6%E3%82%A7%E3%82%A2%E3%83%BB%E3%82%A8%E3%83%8B%E3%83%83%E3%82%AF%E3%82%B9-%E3%83%89%E3%83%A9%E3%82%B4%E3%83%B3%E3%82%AF%E3%82%A8%E3%82%B9%E3%83%88I%EF%BC%86II-PS5/dp/B0F9PSSMYW/ref=zg_bs_g_videogames_d_sccl_30/000-0000000-0000000?psc=1 -> {'url': 'https://www.amazon.co
2025-06-06 15:11:43 [INFO] result: result tinti:b591955047c81a75555f33b12a7f36b1 https://www.amazon.co.jp/Switch-Oldstar-%E3%82%AC%E3%83%A9%E3%82%B9%E3%83%95%E3%82%A3%E3%83%AB%E3%83%A0-2025%E5%BC%B7%E5%8C%96%E3%82%AC%E3%83%A9%E3%82%B9-2-5D%E3%83%A9%E3%82%A6%E3%83%B3%E3%83%89%E3%82%A8%E3%83%83%E3%82%B8%E5%8A%A0%E5%B7%A5/dp/B0F54PHY14/ref=zg_bs_g_videogames_d_sccl_17/000-0000000-0000000?psc=1 -> {'url': 'https://www.amazon.co
2025-06-06 15:11:46 [INFO] result: result tinti:854e2e3ad40586bf5d5f76f40ea28f58 https://www.amazon.co.jp/%E3%82%BD%E3%83%8B%E3%83%BC%E3%83%BB%E3%82%A4%E3%83%B3%E3%82%BF%E3%83%A9%E3%82%AF%E3%83%86%E3%82%A3%E3%83%96%E3%82%A8%E3%83%B3%E3%82%BF%E3%83%86%E3%82%A4%E3%83%B3%E3%83%A1%E3%83%B3%E3%83%88-%E3%80%90%E7%B4%94%E6%AD%A3%E5%93%81%E3%80%91DualSense-%E3%83%AF%E3%82%A4%E3%83%A4%E3%83%AC%E3%82%B9%E3%82%B3%E3%83%B3%E3%83%88%E3%83%AD%E3%83%BC%E3%83%A9%E3%83%BC-CFI-ZCT1J/dp/B08GG1QSRR/ref=zg_bs_g_videogames_d_sccl_14/000-0000000-0000000?psc=1 -> {'url': 'https://www.amazon.co
2025-06-06 15:11:48 [INFO] result: result tinti:5edc9a5b48ce0fe606282cdfa7dafb17 https://www.amazon.co.jp/%E3%80%901%E6%9E%9A%E3%82%BB%E3%83%83%E3%83%88%E3%80%91-Switch-%E8%B2%BC%E3%82%8A%E4%BB%98%E3%81%91%E3%82%AD%E3%83%83%E3%83%88%E4%BB%98%E3%81%8D-%E3%82%AC%E3%83%A9%E3%82%B9%E3%83%95%E3%82%A3%E3%83%AB%E3%83%A0-%E9%AB%98%E6%84%9F%E5%BA%A6%E3%82%BF%E3%83%83%E3%83%81/dp/B0F1NFBW9P/ref=zg_bs_g_videogames_d_sccl_16/000-0000000-0000000?psc=1 -> {'url': 'https://www.amazon.co
2025-06-06 15:11:50 [INFO] result: result tinti:7a9153198518d7f719e2197c3fb3ce26 https://www.amazon.co.jp/%E4%BB%BB%E5%A4%A9%E5%A0%82-%E4%B8%96%E7%95%8C%E3%81%AE%E3%82%A2%E3%82%BD%E3%83%93%E5%A4%A7%E5%85%A851-Switch/dp/B086GRKKR9/ref=zg_bs_g_videogames_d_sccl_10/000-0000000-0000000?psc=1 -> {'url': 'https://www.amazon.co
2025-06-06 15:11:52 [INFO] result: result tinti:d1d3d632b82b8a6b32cd0bca12ef8628 https://www.amazon.co.jp/%E3%80%90PS5%E3%80%91DEATH-STRANDING-%E3%83%87%E3%82%B9%E3%83%BB%E3%82%B9%E3%83%88%E3%83%A9%E3%83%B3%E3%83%87%E3%82%A3%E3%83%B3%E3%82%B0-%E3%80%90%E6%97%A9%E6%9C%9F%E8%B3%BC%E5%85%A5%E7%89%B9%E5%85%B8%E3%80%91%E3%82%B2%E3%83%BC%E3%83%A0%E5%86%85%E3%82%A2%E3%82%A4%E3%83%86%E3%83%A0-%E3%82%AF%E3%82%AA%E3%83%83%E3%82%AB%E3%83%AF%E3%83%A9%E3%83%93%E3%83%BC-%E6%97%A9%E6%9C%9F%E3%82%A2%E3%83%B3%E3%83%AD%E3%83%83%E3%82%AF/dp/B0F13HRGK2/ref=zg_bs_g_videogames_d_sccl_5/000-0000000-0000000?psc=1 -> {'url': 'https://www.amazon.co
2025-06-06 15:11:55 [INFO] result: result tinti:7e725c971be29625c7fcedbe257592b7 https://www.amazon.co.jp/%E4%BB%BB%E5%A4%A9%E5%A0%82-%E3%83%8B%E3%83%B3%E3%83%86%E3%83%B3%E3%83%89%E3%83%BC%E3%83%97%E3%83%AA%E3%83%9A%E3%82%A4%E3%83%89%E7%95%AA%E5%8F%B7-9000%E5%86%86-%E3%82%AA%E3%83%B3%E3%83%A9%E3%82%A4%E3%83%B3%E3%82%B3%E3%83%BC%E3%83%89%E7%89%88/dp/B09998HHSY/ref=zg_bs_g_videogames_d_sccl_22/000-0000000-0000000?psc=1 -> {'url': 'https://www.amazon.co
2025-06-06 15:11:57 [INFO] result: result tinti:64b1a4add476738b2bbb85279b67c6c9 https://www.amazon.co.jp/%E3%80%90%E4%BB%BB%E5%A4%A9%E5%A0%82%E7%B4%94%E6%AD%A3%E5%93%81%E3%80%91Joy-2%E5%80%8B%E3%82%BB%E3%83%83%E3%83%88-%E3%80%90Amazon-co-jp%E9%99%90%E5%AE%9A%E7%89%B9%E5%85%B8%E3%80%91Nintendo-Switch-%E3%83%AD%E3%82%B4%E3%83%87%E3%82%B6%E3%82%A4%E3%83%B3%E3%82%B9%E3%83%86%E3%83%83%E3%82%AB%E3%83%BC/dp/B0F54LNT4M/ref=zg_bs_g_videogames_d_sccl_21/000-0000000-0000000?psc=1 -> {'url': 'https://www.amazon.co
2025-06-06 15:11:59 [INFO] result: result tinti:d1293cdccf0b82915471908511d1b4ce https://www.amazon.co.jp/Spigen-%E8%B2%BC%E3%82%8A%E4%BB%98%E3%81%91%E3%82%AD%E3%83%83%E3%83%88%E4%BB%98%E3%81%8D-9H%E7%A1%AC%E5%BA%A6%E5%BC%B7%E5%8C%96%E3%82%AC%E3%83%A9%E3%82%B9-%E3%83%8A%E3%83%8E%E3%82%B3%E3%83%BC%E3%83%86%E3%82%A3%E3%83%B3%E3%82%B0-AGL09111/dp/B0DNH9C9FD/ref=zg_bs_g_videogames_d_sccl_13/000-0000000-0000000?psc=1 -> {'url': 'https://www.amazon.co
2025-06-06 15:12:01 [INFO] result: result tinti:8cfb4c68aefbda14c64659426718029b https://www.amazon.co.jp/%E4%BB%BB%E5%A4%A9%E5%A0%82-%E3%83%8B%E3%83%B3%E3%83%86%E3%83%B3%E3%83%89%E3%83%BC%E3%83%97%E3%83%AA%E3%83%9A%E3%82%A4%E3%83%89%E7%95%AA%E5%8F%B7-500%E5%86%86-%E3%82%AA%E3%83%B3%E3%83%A9%E3%82%A4%E3%83%B3%E3%82%B3%E3%83%BC%E3%83%89%E7%89%88/dp/B09997WD23/ref=zg_bs_g_videogames_d_sccl_2/000-0000000-0000000?psc=1 -> {'url': 'https://www.amazon.co
2025-06-06 15:12:03 [INFO] result: result tinti:89baba3c98d09bcff304445b89b1bc86 https://www.amazon.co.jp/%E4%BB%BB%E5%A4%A9%E5%A0%82-%E3%83%8B%E3%83%B3%E3%83%86%E3%83%B3%E3%83%89%E3%83%BC%E3%83%97%E3%83%AA%E3%83%9A%E3%82%A4%E3%83%89%E7%95%AA%E5%8F%B7-1000%E5%86%86-%E3%82%AA%E3%83%B3%E3%83%A9%E3%82%A4%E3%83%B3%E3%82%B3%E3%83%BC%E3%83%89%E7%89%88/dp/B09999M8HV/ref=zg_bs_g_videogames_d_sccl_6/000-0000000-0000000?psc=1 -> {'url': 'https://www.amazon.co
2025-06-06 15:12:04 [INFO] result: result tinti:e62f92d64e82a00b3a3e902d2ae08425 https://www.amazon.co.jp/%E3%83%95%E3%83%AD%E3%83%A0%E3%82%BD%E3%83%95%E3%83%88%E3%82%A6%E3%82%A7%E3%82%A2-%E3%80%90PS5%E3%80%91ELDEN-RING-NIGHTREIGN/dp/B0DWZVYNM3/ref=zg_bs_g_videogames_d_sccl_23/000-0000000-0000000?psc=1 -> {'url': 'https://www.amazon.co
2025-06-06 15:12:06 [INFO] result: result tinti:58a74c9166e719bc87362f8227dbe2f8 https://www.amazon.co.jp/%E3%80%90%E4%BB%BB%E5%A4%A9%E5%A0%82%E7%B4%94%E6%AD%A3%E5%93%81%E3%80%91Joy-%E3%83%A9%E3%82%A4%E3%83%88%E3%83%96%E3%83%AB%E3%83%BC-%E3%83%A9%E3%82%A4%E3%83%88%E3%83%AC%E3%83%83%E3%83%89-%E3%80%90Amazon-co-jp%E9%99%90%E5%AE%9A%E7%89%B9%E5%85%B8%E3%80%91Nintendo-%E3%83%AD%E3%82%B4%E3%83%87%E3%82%B6%E3%82%A4%E3%83%B3%E3%82%B9%E3%83%86%E3%83%83%E3%82%AB%E3%83%BC/dp/B0F54H3C6T/ref=zg_bs_g_videogames_d_sccl_4/000-0000000-0000000?psc=1 -> {'url': 'https://www.amazon.co
2025-06-06 15:12:08 [INFO] result: result tinti:bd7f0d21513cb6c78af208fcc6ab7bc7 https://www.amazon.co.jp/Nysera-NY04-NSW2CS04-%E3%82%AC%E3%83%A9%E3%82%B9%E3%83%95%E3%82%A3%E3%83%AB%E3%83%A0/dp/B0F2GMH78L/ref=zg_bs_g_videogames_d_sccl_8/000-0000000-0000000?psc=1 -> {'url': 'https://www.amazon.co
2025-06-06 15:12:11 [INFO] result: result tinti:354256b4aa0b826ff01f60f542036d74 https://www.amazon.co.jp/%E3%80%902025%E9%9D%A9%E6%96%B0%E3%83%A2%E3%83%87%E3%83%AB%E3%80%91-1000mAh%E5%A4%A7%E5%AE%B9%E9%87%8F-%E3%82%B8%E3%83%A3%E3%82%A4%E3%83%AD%E3%82%BB%E3%83%B3%E3%82%B5%E3%83%BC%E6%90%AD%E8%BC%89-Switch%E5%85%A8%E4%B8%96%E4%BB%A3%E3%81%AB%E5%AF%BE%E5%BF%9C-%E6%97%A5%E6%9C%AC%E8%AA%9E%E5%8F%96%E6%89%B1%E8%AA%AC%E6%98%8E%E6%9B%B8%E4%BB%98%E3%81%8D/dp/B0DZCWJGBH/ref=zg_bs_g_videogames_d_sccl_18/000-0000000-0000000?psc=1 -> {'url': 'https://www.amazon.co
2025-06-06 15:12:11 [INFO] metrics: Counters: {"fetch_count[project=bunbunbun]": 1, "fetch_count[project=douyo]": 1, "fetch_count[project=kokoko]": 1, "fetch_count[project=iuiu]": 1, "fetch_count[project=mysql_detailed_test]": 1, "fetch_count[project=jijijiji]": 1, "fetch_count[project=mysql_detailed_test_2]": 1, "fetch_count[project=tinti]": 58}
2025-06-06 15:12:11 [INFO] metrics: Gauges: {"memory_usage_rss": 89935872, "memory_usage_vms": 637341696, "memory_usage_percent": 0.2662907440148017, "system_memory_total": 33576677376, "system_memory_available": 29357395968, "system_memory_percent": 12.6}
2025-06-06 15:12:12 [INFO] result: result tinti:0721d810d0d3ebd32120a95409106cde https://www.amazon.co.jp/%E3%80%90%E4%BB%BB%E5%A4%A9%E5%A0%82%E3%83%A9%E3%82%A4%E3%82%BB%E3%83%B3%E3%82%B9%E5%95%86%E5%93%81%E3%80%91%E8%B2%BC%E3%82%8A%E3%82%84%E3%81%99%E3%81%84-%E9%AB%98%E7%A1%AC%E5%BA%A6%E3%83%96%E3%83%AB%E3%83%BC%E3%83%A9%E3%82%A4%E3%83%88%E3%82%AB%E3%83%83%E3%83%88%E3%83%95%E3%82%A3%E3%83%AB%E3%83%A0%E2%80%9C%E3%83%94%E3%82%BF%E8%B2%BC%E3%82%8A-Nintendo-Switch%E2%84%A2-2%E3%80%90Switch2%E5%B0%82%E7%94%A8%E3%80%91/dp/B0F5X11KCM/ref=zg_bs_g_videogames_d_sccl_25/000-0000000-0000000?psc=1 -> {'url': 'https://www.amazon.co
2025-06-06 15:12:15 [INFO] result: result tinti:f19209b620d971aa622763aa4209d398 https://www.amazon.co.jp/%E9%BE%8D%E3%81%AE%E5%9B%BD-%E3%83%AB%E3%83%BC%E3%83%B3%E3%83%95%E3%82%A1%E3%82%AF%E3%83%88%E3%83%AA%E3%83%BC-Nintendo-Switch-Switch2/dp/B0F5WKRKXD/ref=zg_bs_g_videogames_d_sccl_11/000-0000000-0000000?psc=1 -> {'url': 'https://www.amazon.co
2025-06-06 15:12:16 [INFO] result: result tinti:8240406a65f83a00a1fc65103d43ef58 https://www.amazon.co.jp/%E3%83%97%E3%83%AC%E3%82%A4%E3%82%B9%E3%83%86%E3%83%BC%E3%82%B7%E3%83%A7%E3%83%B3-%E3%82%B9%E3%83%88%E3%82%A2%E3%83%81%E3%82%B1%E3%83%83%E3%83%88-1-100%E5%86%86-%E3%82%AA%E3%83%B3%E3%83%A9%E3%82%A4%E3%83%B3%E3%82%B3%E3%83%BC%E3%83%89%E7%89%88/dp/B08M6B8JL1/ref=zg_bs_g_videogames_d_sccl_12/000-0000000-0000000?psc=1 -> {'url': 'https://www.amazon.co
2025-06-06 15:12:19 [INFO] result: result tinti:c5f09039a9011c84dafb42f4b4e1f1ea https://www.amazon.co.jp/%E3%83%A2%E3%83%86%E3%83%AB%E3%81%AB%E5%AF%BE%E5%BF%9C%E3%80%91%E3%82%B1%E3%83%BC%E3%82%B9-iVoler-Switch-%E3%81%AB%E5%AF%BE%E5%BF%9C%E5%8F%8E%E7%B4%8D%E3%82%B1%E3%83%BC%E3%82%B9-%E3%82%A4%E3%83%A4%E3%83%9B%E3%83%B3%E3%81%AA%E3%81%A9%E5%85%A8%E9%83%A8%E5%8F%8E%E7%B4%8D%E5%8F%AF%E8%83%BD/dp/B0DRJQKFRX/ref=zg_bs_g_videogames_d_sccl_20/000-0000000-0000000?psc=1 -> {'url': 'https://www.amazon.co
2025-06-06 15:12:21 [INFO] result: result tinti:4c0894ed32199503fa02bf7782f84a49 https://www.amazon.co.jp/Clair-Obscur-Expedition-33%EF%BC%88%E3%82%AF%E3%83%AC%E3%83%BC%E3%83%AB%E3%82%AA%E3%83%96%E3%82%B9%E3%82%AD%E3%83%A5%E3%83%BC%E3%83%AB%EF%BC%9A%E3%82%A8%E3%82%AF%E3%82%B9%E3%83%9A%E3%83%87%E3%82%A3%E3%82%B7%E3%83%A7%E3%83%B333%EF%BC%89-PS5/dp/B0DTK98QBQ/ref=zg_bs_g_videogames_d_sccl_26/000-0000000-0000000?psc=1 -> {'url': 'https://www.amazon.co
2025-06-06 15:12:23 [INFO] result: result tinti:0d7149f840edefeb68edb28dbd94cdc1 https://www.amazon.co.jp/%E4%BB%BB%E5%A4%A9%E5%A0%82-%E3%83%8B%E3%83%B3%E3%83%86%E3%83%B3%E3%83%89%E3%83%BC%E3%83%97%E3%83%AA%E3%83%9A%E3%82%A4%E3%83%89%E7%95%AA%E5%8F%B7-3000%E5%86%86-%E3%82%AA%E3%83%B3%E3%83%A9%E3%82%A4%E3%83%B3%E3%82%B3%E3%83%BC%E3%83%89%E7%89%88/dp/B09996CH6H/ref=zg_bs_g_videogames_d_sccl_15/000-0000000-0000000?psc=1 -> {'url': 'https://www.amazon.co
2025-06-06 15:12:26 [INFO] result: result tinti:f958bcb625fe158c071f5d5831015703 https://www.amazon.co.jp/%E3%83%AC%E3%83%99%E3%83%AB%E3%83%95%E3%82%A1%E3%82%A4%E3%83%96-ELJM-30648-%E3%83%95%E3%82%A1%E3%83%B3%E3%82%BF%E3%82%B8%E3%83%BC%E3%83%A9%E3%82%A4%E3%83%95%EF%BD%89-%E3%82%B0%E3%83%AB%E3%82%B0%E3%83%AB%E3%81%AE%E7%AB%9C%E3%81%A8%E6%99%82%E3%82%92%E3%81%AC%E3%81%99%E3%82%80%E5%B0%91%E5%A5%B3-PS5/dp/B0DX1T47TD/ref=zg_bs_g_videogames_d_sccl_27/000-0000000-0000000?psc=1 -> {'url': 'https://www.amazon.co
2025-06-06 15:12:29 [INFO] result: result tinti:9b3c2173fb070619869b6b5a83f25ba6 https://www.amazon.co.jp/%E3%82%AC%E3%83%A9%E3%82%B9%E3%83%95%E3%82%A3%E3%83%AB%E3%83%A0-ohyes-Switch2-%E3%82%B9%E3%82%A4%E3%83%83%E3%83%812%E5%AF%BE%E5%BF%9C-SWITCH2/dp/B0F7R89SFV/ref=zg_bs_g_videogames_d_sccl_28/000-0000000-0000000?psc=1 -> {'url': 'https://www.amazon.co
2025-06-06 15:12:31 [INFO] result: result tinti:834c5baa58e0d7eba35665eb38cf9e42 https://www.amazon.co.jp/%E4%BB%BB%E5%A4%A9%E5%A0%82-%E3%83%8B%E3%83%B3%E3%83%86%E3%83%B3%E3%83%89%E3%83%BC%E3%83%97%E3%83%AA%E3%83%9A%E3%82%A4%E3%83%89%E7%95%AA%E5%8F%B7-5000%E5%86%86-%E3%82%AA%E3%83%B3%E3%83%A9%E3%82%A4%E3%83%B3%E3%82%B3%E3%83%BC%E3%83%89%E7%89%88/dp/B09998HHSG/ref=zg_bs_g_videogames_d_sccl_7/000-0000000-0000000?psc=1 -> {'url': 'https://www.amazon.co
2025-06-06 15:12:33 [INFO] result: result tinti:a48c04e2edae0047dc957501015c170b https://www.amazon.co.jp/%E3%80%90%E4%BB%BB%E5%A4%A9%E5%A0%82%E3%83%A9%E3%82%A4%E3%82%BB%E3%83%B3%E3%82%B9%E5%95%86%E5%93%81%E3%80%91Nintendo-Switch-2-%E5%B0%82%E7%94%A8%E6%B6%B2%E6%99%B6%E4%BF%9D%E8%AD%B7%E3%83%95%E3%82%A3%E3%83%AB%E3%83%A0-%E5%8F%8D%E5%B0%84%E4%BD%8E%E6%B8%9B/dp/B0F4XRWMC1/ref=zg_bs_g_videogames_d_sccl_14/000-0000000-0000000?psc=1 -> {'url': 'https://www.amazon.co
2025-06-06 15:12:35 [INFO] result: result tinti:76fdf4b4710651d24935276c8950b721 https://www.amazon.co.jp/Switch2-%E3%82%AC%E3%83%A9%E3%82%B9%E3%83%95%E3%82%A4%E3%83%AB%E3%83%A0-%E3%82%AC%E3%82%A4%E3%83%89%E6%9E%A0%E4%BB%98%E3%81%8D%E3%80%90Seninhi-%E6%97%A5%E6%9C%AC%E6%97%AD%E7%A1%9D%E5%AD%90%E8%A3%BD-%E9%AB%98-SENLX-TMSH2/dp/B0DZ2BFLPH/ref=zg_bs_g_videogames_d_sccl_6/000-0000000-0000000?psc=1 -> {'url': 'https://www.amazon.co
2025-06-06 15:12:38 [INFO] result: result tinti:bf91771ee18ccbd6d56a94e08a2f084c https://www.amazon.co.jp/%E3%82%BD%E3%83%8B%E3%83%BC%E3%83%BB%E3%82%A4%E3%83%B3%E3%82%BF%E3%83%A9%E3%82%AF%E3%83%86%E3%82%A3%E3%83%96%E3%82%A8%E3%83%B3%E3%82%BF%E3%83%86%E3%82%A4%E3%83%B3%E3%83%A1%E3%83%B3%E3%83%88-PlayStation-5-CFI-2000A01/dp/B0CKYM15RJ/ref=zg_bs_g_videogames_d_sccl_7/000-0000000-0000000?psc=1 -> {'url': 'https://www.amazon.co
2025-06-06 15:12:40 [INFO] result: result tinti:a203645552f8af568e7255c4aa196122 https://www.amazon.co.jp/Nintendo-Switch-%E6%9C%89%E6%A9%9FEL%E3%83%A2%E3%83%87%E3%83%AB-Joy-%E3%83%9B%E3%83%AF%E3%82%A4%E3%83%88/dp/B098B8PFXY/ref=zg_bs_g_videogames_d_sccl_29/000-0000000-0000000?psc=1 -> {'url': 'https://www.amazon.co
2025-06-06 15:12:42 [INFO] result: result tinti:6144949cf1dd840951944fa08293e926 https://www.amazon.co.jp/%E3%82%BD%E3%83%8B%E3%83%BC%E3%83%BB%E3%82%A4%E3%83%B3%E3%82%BF%E3%83%A9%E3%82%AF%E3%83%86%E3%82%A3%E3%83%96%E3%82%A8%E3%83%B3%E3%82%BF%E3%83%86%E3%82%A4%E3%83%B3%E3%83%A1%E3%83%B3%E3%83%88-%E3%80%90PS5%E3%80%91DEATH-STRANDING-DIRECTORS-CUT/dp/B09923SLTK/ref=zg_bs_g_videogames_d_sccl_1/000-0000000-0000000?psc=1 -> {'url': 'https://www.amazon.co
2025-06-06 15:12:45 [INFO] result: result tinti:2b283e21ff158a2257b482327bf4134f https://www.amazon.co.jp/Pikmin-4-%E3%83%94%E3%82%AF%E3%83%9F%E3%83%B3-Switch/dp/B0BV983D5M/ref=zg_bs_g_videogames_d_sccl_9/000-0000000-0000000?psc=1 -> {'url': 'https://www.amazon.co
2025-06-06 15:12:47 [INFO] result: result tinti:018d397b208993377bc8693fbd396fa7 https://www.amazon.co.jp/%E3%82%BD%E3%83%8B%E3%83%BC%E3%83%BB%E3%82%A4%E3%83%B3%E3%82%BF%E3%83%A9%E3%82%AF%E3%83%86%E3%82%A3%E3%83%96%E3%82%A8%E3%83%B3%E3%82%BF%E3%83%86%E3%82%A4%E3%83%B3%E3%83%A1%E3%83%B3%E3%83%88-CFI-ZCT1J12-%E3%80%90%E7%B4%94%E6%AD%A3%E5%93%81%E3%80%91DualSense-%E3%83%AF%E3%82%A4%E3%83%A4%E3%83%AC%E3%82%B9%E3%82%B3%E3%83%B3%E3%83%88%E3%83%AD%E3%83%BC%E3%83%A9%E3%83%BC-%E3%83%91%E3%83%BC%E3%83%AB%EF%BC%88CFI-ZCT1J12%EF%BC%89/dp/B0DJBYD71H/ref=zg_bs_g_videogames_d_sccl_3/000-0000000-0000000?psc=1 -> {'url': 'https://www.amazon.co
2025-06-06 15:12:50 [INFO] result: result tinti:fc6fc26d7783ef4ad705a567f64fae17 https://www.amazon.co.jp/%E3%82%B9%E3%82%AF%E3%82%A6%E3%82%A7%E3%82%A2%E3%83%BB%E3%82%A8%E3%83%8B%E3%83%83%E3%82%AF%E3%82%B9-%E3%83%89%E3%83%A9%E3%82%B4%E3%83%B3%E3%82%AF%E3%82%A8%E3%82%B9%E3%83%88I%EF%BC%86II-%E3%80%90Amazon-co-jp%E9%99%90%E5%AE%9A%E7%89%B9%E5%85%B8%E3%80%91%E6%97%85%E4%BA%BA%E3%81%AE%E3%81%9F%E3%81%97%E3%81%AA%E3%81%BF%E3%82%BB%E3%83%83%E3%83%88-%E3%82%B3%E3%83%BC%E3%83%89%E9%85%8D%E4%BF%A1-PS5/dp/B0F9PPH5L8/ref=zg_bs_g_videogames_d_sccl_1/000-0000000-0000000?psc=1 -> {'url': 'https://www.amazon.co
2025-06-06 15:12:52 [INFO] result: result tinti:883e5d3f764d4b5aca4eba07082426b9 https://www.amazon.co.jp/%E3%83%9B%E3%83%AA%E3%83%91%E3%83%83%E3%83%89-TURBO-Nintendo-Switch%E2%84%A2-Windows/dp/B0F53RY6ZP/ref=zg_bs_g_videogames_d_sccl_11/000-0000000-0000000?psc=1 -> {'url': 'https://www.amazon.co
2025-06-06 15:12:54 [INFO] result: result tinti:bcc53a0a46b32aedfee75a82712958a1 https://www.amazon.co.jp/ps4%E3%82%B3%E3%83%B3%E3%83%88%E3%83%AD%E3%83%BC%E3%83%A9%E3%83%BC%E3%80%902025%E5%B9%B4%E9%9D%A9%E6%96%B0%E7%89%88%E3%83%BB%E3%82%A2%E3%83%83%E3%83%97%E3%83%87%E3%83%BC%E3%83%88%E3%80%91ps4-6%E8%BB%B8%E3%82%B8%E3%83%A3%E3%82%A4%E3%83%AD%E3%82%BB%E3%83%B3%E3%82%B5%E3%83%BC-Bluetoooth5-4-%E3%82%A4%E3%83%A4%E3%83%9B%E3%83%B3%E3%82%B8%E3%83%A3%E3%83%83%E3%82%AF%E4%BB%98%E3%81%8D-%E3%83%AF%E3%82%A4%E3%83%A4%E3%83%AC%E3%82%B9%E3%82%B3%E3%83%B3%E3%83%88%E3%83%AD%E3%83%BC%E3%83%A9%E3%83%BC/dp/B0DYNMJW75/ref=zg_bs_g_videogames_d_sccl_2/000-0000000-0000000?psc=1 -> {'url': 'https://www.amazon.co
2025-06-06 15:12:56 [INFO] result: result tinti:33ae67c74e93a332fe9f8e9df60c9629 https://www.amazon.co.jp/%E3%82%AC%E3%83%A9%E3%82%B9%E3%83%95%E3%82%A3%E3%83%AB%E3%83%A0-Seefox%E3%80%902%E6%9E%9A%E5%85%A5%E3%82%8A%E3%80%91Switch-%E3%83%A9%E3%82%A6%E3%83%B3%E3%83%89%E3%82%A8%E3%83%83%E3%82%B8%E5%8A%A0%E5%B7%A5-Switch2-%E5%B0%82%E7%94%A8%E3%82%AC%E3%82%A4%E3%83%89%E6%9E%A0%E4%BB%98%E3%81%8D/dp/B0F66YVZLZ/ref=zg_bs_g_videogames_d_sccl_7/000-0000000-0000000?psc=1 -> {'url': 'https://www.amazon.co
2025-06-06 15:12:59 [INFO] result: result tinti:5b202f07c95ed5cad52d3efb1e3f6aee https://www.amazon.co.jp/%E9%BE%8D%E3%81%8C%E5%A6%82%E3%81%8F0-%E8%AA%93%E3%81%84%E3%81%AE%E5%A0%B4%E6%89%80-Directors-Cut-Switch2/dp/B0F5WKC9PW/ref=zg_bs_g_videogames_d_sccl_24/000-0000000-0000000?psc=1 -> {'url': 'https://www.amazon.co
2025-06-06 15:13:02 [INFO] result: result tinti:32a838577f7cdf5a6647fbfc8adea67a https://www.amazon.co.jp/%E3%82%BD%E3%83%8B%E3%83%BC%E3%83%BB%E3%82%A4%E3%83%B3%E3%82%BF%E3%83%A9%E3%82%AF%E3%83%86%E3%82%A3%E3%83%96%E3%82%A8%E3%83%B3%E3%82%BF%E3%83%86%E3%82%A4%E3%83%B3%E3%83%A1%E3%83%B3%E3%83%88-CFI-ZCP1J-%E3%80%90%E7%B4%94%E6%AD%A3%E5%93%81%E3%80%91DualSense-Edge-%E3%83%AF%E3%82%A4%E3%83%A4%E3%83%AC%E3%82%B9%E3%82%B3%E3%83%B3%E3%83%88%E3%83%AD%E3%83%BC%E3%83%A9%E3%83%BC/dp/B0BJTJNQFD/ref=zg_bs_g_videogames_d_sccl_5/000-0000000-0000000?psc=1 -> {'url': 'https://www.amazon.co
2025-06-06 15:13:05 [INFO] result: result tinti:06be59d7d1d2e417ed9dbfe315d242bf https://www.amazon.co.jp/%E4%BB%BB%E5%A4%A9%E5%A0%82-%E3%83%8B%E3%83%B3%E3%83%86%E3%83%B3%E3%83%89%E3%83%BC%E3%83%97%E3%83%AA%E3%83%9A%E3%82%A4%E3%83%89%E7%95%AA%E5%8F%B7-2000%E5%86%86-%E3%82%AA%E3%83%B3%E3%83%A9%E3%82%A4%E3%83%B3%E3%82%B3%E3%83%BC%E3%83%89%E7%89%88/dp/B09998GN7Z/ref=zg_bs_g_videogames_d_sccl_6/000-0000000-0000000?psc=1 -> {'url': 'https://www.amazon.co
2025-06-06 15:13:08 [INFO] result: result tinti:fdd62d7c7a6ec679d1bc314aeb98d455 https://www.amazon.co.jp/%E3%82%BD%E3%83%8B%E3%83%BC%E3%83%BB%E3%82%A4%E3%83%B3%E3%82%BF%E3%83%A9%E3%82%AF%E3%83%86%E3%82%A3%E3%83%96%E3%82%A8%E3%83%B3%E3%82%BF%E3%83%86%E3%82%A4%E3%83%B3%E3%83%A1%E3%83%B3%E3%83%88-CFI-ZCT1J11-%E3%80%90%E7%B4%94%E6%AD%A3%E5%93%81%E3%80%91DualSense-%E3%83%AF%E3%82%A4%E3%83%A4%E3%83%AC%E3%82%B9%E3%82%B3%E3%83%B3%E3%83%88%E3%83%AD%E3%83%BC%E3%83%A9%E3%83%BC-%E3%82%A4%E3%83%B3%E3%83%87%E3%82%A3%E3%82%B4%EF%BC%88CFI-ZCT1J11%EF%BC%89/dp/B0DJBWTJKZ/ref=zg_bs_g_videogames_d_sccl_9/000-0000000-0000000?psc=1 -> {'url': 'https://www.amazon.co
2025-06-06 15:13:10 [INFO] result: result tinti:3adf229aa989ee1e4688fb2948a8df65 https://www.amazon.co.jp/TURBO%E9%80%A3%E5%B0%84%E6%A9%9F%E8%83%BD-1000mAh%E5%A4%A7%E5%AE%B9%E9%87%8F-Bluetooth-%E3%82%B8%E3%83%A3%E3%82%A4%E3%83%AD%E3%82%BB%E3%83%B3%E3%82%B5%E3%83%BC%E6%90%AD%E8%BC%89-%E6%97%A5%E6%9C%AC%E8%AA%9E%E5%8F%96%E6%89%B1%E8%AA%AC%E6%98%8E%E6%9B%B8%E4%BB%98%E3%81%8D/dp/B0D8KWTL2C/ref=zg_bs_g_videogames_d_sccl_8/000-0000000-0000000?psc=1 -> {'url': 'https://www.amazon.co
2025-06-06 15:13:12 [INFO] metrics: Counters: {"fetch_count[project=bunbunbun]": 1, "fetch_count[project=douyo]": 1, "fetch_count[project=kokoko]": 1, "fetch_count[project=iuiu]": 1, "fetch_count[project=mysql_detailed_test]": 1, "fetch_count[project=jijijiji]": 1, "fetch_count[project=mysql_detailed_test_2]": 1, "fetch_count[project=tinti]": 102}
2025-06-06 15:13:12 [INFO] result: result tinti:3bb770fe50fdeccd8b8516cbae3cef34 https://www.amazon.co.jp/Roblox%E3%82%AE%E3%83%95%E3%83%88%E3%82%AB%E3%83%BC%E3%83%89-%E3%80%90%E9%99%90%E5%AE%9A%E3%83%90%E3%83%BC%E3%83%81%E3%83%A3%E3%83%AB%E3%82%A2%E3%82%A4%E3%83%86%E3%83%A0%E3%82%92%E5%90%AB%E3%82%80%E3%80%91-%E3%80%90%E3%82%AA%E3%83%B3%E3%83%A9%E3%82%A4%E3%83%B3%E3%82%B2%E3%83%BC%E3%83%A0%E3%82%B3%E3%83%BC%E3%83%89%E3%80%91-%E3%83%AD%E3%83%96%E3%83%AD%E3%83%83%E3%82%AF%E3%82%B9-%E3%82%AA%E3%83%B3%E3%83%A9%E3%82%A4%E3%83%B3%E3%82%B3%E3%83%BC%E3%83%89%E7%89%88/dp/B09NQ9SWQ9/ref=zg_bs_g_videogames_d_sccl_16/000-0000000-0000000?psc=1 -> {'url': 'https://www.amazon.co
2025-06-06 15:13:13 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637341696, "memory_usage_percent": 0.26863294122268305, "system_memory_total": 33576677376, "system_memory_available": 29321707520, "system_memory_percent": 12.7}
2025-06-06 15:13:14 [INFO] result: result tinti:00496e208859e8c8ce872a58117646cc https://www.amazon.co.jp/%E3%83%99%E3%83%AB%E3%83%A2%E3%83%B3%E3%83%89-%E3%80%902%E6%9E%9A%E3%82%BB%E3%83%83%E3%83%88%E3%80%91-%E7%94%BB%E9%9D%A2%E4%BF%9D%E8%AD%B7%E3%83%95%E3%82%A3%E3%83%AB%E3%83%A0-%E3%83%A2%E3%83%B3%E3%82%B9%E3%82%BF%E3%83%BC%E3%83%95%E3%82%A3%E3%83%AB%E3%83%A0-%E3%83%96%E3%83%AB%E3%83%BC%E3%83%A9%E3%82%A4%E3%83%88%E3%82%AB%E3%83%83%E3%83%88/dp/B0F1CJRSG5/ref=zg_bs_g_videogames_d_sccl_18/000-0000000-0000000?psc=1 -> {'url': 'https://www.amazon.co
2025-06-06 15:13:17 [INFO] result: result tinti:c8950f3a0f819c2aca910895c27e91e9 https://www.amazon.co.jp/%E3%81%9F%E3%81%BE%E3%81%94%E3%81%A3%E3%81%A1%E3%81%AE%E3%83%97%E3%83%81%E3%83%97%E3%83%81%E3%81%8A%E3%81%BF%E3%81%9B%E3%81%A3%E3%81%A1-%E3%81%8A%E3%81%BE%E3%81%A1%E3%81%A9%EF%BD%9E%E3%81%95%E3%81%BE%EF%BC%81-Nintendo-Switch-Switch2/dp/B0F5QHJ7D6/ref=zg_bs_g_videogames_d_sccl_13/000-0000000-0000000?psc=1 -> {'url': 'https://www.amazon.co
2025-06-06 15:13:20 [INFO] result: result tinti:de371034798b4a795e3cd88366bab444 https://www.amazon.co.jp/%E4%BB%BB%E5%A4%A9%E5%A0%82-%E3%83%AA%E3%83%B3%E3%82%B0%E3%83%95%E3%82%A3%E3%83%83%E3%83%88-%E3%82%A2%E3%83%89%E3%83%99%E3%83%B3%E3%83%81%E3%83%A3%E3%83%BC-Switch/dp/B07XV8VSZT/ref=zg_bs_g_videogames_d_sccl_20/000-0000000-0000000?psc=1 -> {'url': 'https://www.amazon.co
2025-06-06 15:13:22 [INFO] result: result tinti:b137e5bcb07dbc1a7656f4c7a2577b23 https://www.amazon.co.jp/%E4%BB%BB%E5%A4%A9%E5%A0%82-%E5%A4%A7%E4%B9%B1%E9%97%98%E3%82%B9%E3%83%9E%E3%83%83%E3%82%B7%E3%83%A5%E3%83%96%E3%83%A9%E3%82%B6%E3%83%BC%E3%82%BA-SPECIAL-Switch/dp/B07FDW61HX/ref=zg_bs_g_videogames_d_sccl_3/000-0000000-0000000?psc=1 -> {'url': 'https://www.amazon.co
2025-06-06 15:13:24 [INFO] result: result tinti:127c01ce0efe7e4c5acf026366627154 https://www.amazon.co.jp/%E3%82%A2%E3%82%A4%E3%83%AC%E3%83%83%E3%82%AF%E3%82%B9-ILX2S386-%E3%80%90%E4%BB%BB%E5%A4%A9%E5%A0%82%E5%85%AC%E5%BC%8F%E3%83%A9%E3%82%A4%E3%82%BB%E3%83%B3%E3%82%B9%E5%95%86%E5%93%81%E3%80%91%E3%83%8B%E3%83%B3%E3%83%86%E3%83%B3%E3%83%89%E3%83%BC%E3%82%B9%E3%82%A4%E3%83%83%E3%83%812%E5%B0%82%E7%94%A8%E7%94%BB%E9%9D%A2%E4%BF%9D%E8%AD%B7%E3%83%95%E3%82%A3%E3%83%AB%E3%83%A0%E3%80%8E%E3%83%97%E3%83%AC%E3%83%9F%E3%82%A2%E3%83%A0%E3%83%95%E3%82%A3%E3%83%AB%E3%83%A0%E3%80%8C%E8%B6%85%E7%B5%B6%E8%89%B6%E3%80%8D-for-%E3%83%8B%E3%83%B3%E3%83%86%E3%83%B3%E3%83%89%E3%83%BCSWITCH2%E3%80%8F/dp/B0F93K1QZG/ref=zg_bs_g_videogames_d_sccl_23/000-0000000-0000000?psc=1 -> {'url': 'https://www.amazon.co
2025-06-06 15:13:27 [INFO] result: result tinti:bbe78401dac7eaf4f7721651e9aea72d https://www.amazon.co.jp/%E4%BB%BB%E5%A4%A9%E5%A0%82-%E3%83%9E%E3%83%AA%E3%82%AA%E3%82%AB%E3%83%BC%E3%83%888-%E3%83%87%E3%83%A9%E3%83%83%E3%82%AF%E3%82%B9-Switch/dp/B01N12G06K/ref=zg_bs_g_videogames_d_sccl_17/000-0000000-0000000?psc=1 -> {'url': 'https://www.amazon.co
2025-06-06 15:13:28 [INFO] result: result tinti:a4ea7a45590f740f549ca518ee0b1a76 https://www.amazon.co.jp/%E3%82%BD%E3%83%8B%E3%83%BC%E3%83%BB%E3%82%A4%E3%83%B3%E3%82%BF%E3%83%A9%E3%82%AF%E3%83%86%E3%82%A3%E3%83%96%E3%82%A8%E3%83%B3%E3%82%BF%E3%83%86%E3%82%A4%E3%83%B3%E3%83%A1%E3%83%B3%E3%83%88-%E3%80%90PS4%E3%80%91%E3%82%B0%E3%83%A9%E3%83%B3%E3%83%84%E3%83%BC%E3%83%AA%E3%82%B9%E3%83%A27/dp/B09TWQ6H74/ref=zg_bs_g_videogames_d_sccl_16/000-0000000-0000000?psc=1 -> {'url': 'https://www.amazon.co
2025-06-06 15:13:31 [INFO] result: result tinti:4341d0750796cb17d63d7e6d50ea0211 https://www.amazon.co.jp/%E4%BB%BB%E5%A4%A9%E5%A0%82-HAC-P-BAMEA-Pikmin-%E3%83%94%E3%82%AF%E3%83%9F%E3%83%B3-Switch/dp/B0C8YCPC9L/ref=zg_bs_g_videogames_d_sccl_30/000-0000000-0000000?psc=1 -> {'url': 'https://www.amazon.co
2025-06-06 15:13:33 [INFO] result: result tinti:a98c5d149cd59adba8c536a91a151811 https://www.amazon.co.jp/%E3%80%90%E4%BB%BB%E5%A4%A9%E5%A0%82%E3%83%A9%E3%82%A4%E3%82%BB%E3%83%B3%E3%82%B9%E5%95%86%E5%93%81%E3%80%91USB%E3%82%AB%E3%83%A1%E3%83%A9-%E3%83%91%E3%83%83%E3%82%AF%E3%83%B3%E3%83%95%E3%83%A9%E3%83%AF%E3%83%BC-Nintendo-Switch%E2%84%A2-2%E3%80%90Switch2%E5%B0%82%E7%94%A8%E3%80%91/dp/B0F5WZDQD9/ref=zg_bs_g_videogames_d_sccl_2/000-0000000-0000000?psc=1 -> {'url': 'https://www.amazon.co
2025-06-06 15:13:36 [INFO] result: result tinti:502c0ec99e754719c60e532091e8f647 https://www.amazon.co.jp/%E3%80%90%E7%B4%94%E6%AD%A3%E5%93%81%E3%80%91DualSense-%E3%83%AF%E3%82%A4%E3%83%A4%E3%83%AC%E3%82%B9%E3%82%B3%E3%83%B3%E3%83%88%E3%83%AD%E3%83%BC%E3%83%A9%E3%83%BC-%E3%83%B4%E3%82%A9%E3%83%AB%E3%82%AB%E3%83%8B%E3%83%83%E3%82%AF-%E3%83%AC%E3%83%83%E3%83%89-CFI-ZCT1J07/dp/B0CK86ZXHJ/ref=zg_bs_g_videogames_d_sccl_6/000-0000000-0000000?psc=1 -> {'url': 'https://www.amazon.co
2025-06-06 15:13:38 [INFO] result: result tinti:9e1fa4604f4443dc65fa5edc0f5d5fc8 https://www.amazon.co.jp/%E3%83%AF%E3%82%A4%E3%83%A4%E3%83%AC%E3%82%B9%E3%82%B3%E3%83%B3%E3%83%88%E3%83%AD%E3%83%BC%E3%83%A9%E3%83%BC-Turbo%E9%80%A3%E5%B0%84%E6%A9%9F%E8%83%BD-Bluetooth%E6%8E%A5%E7%B6%9A-%E3%82%B8%E3%83%A3%E3%82%A4%E3%83%AD%E3%82%BB%E3%83%B3%E3%82%B5%E3%83%BC%E6%A9%9F%E8%83%BD-%E3%83%98%E3%83%83%E3%83%89%E3%83%95%E3%82%A9%E3%83%B3%E3%82%B8%E3%83%A3%E3%83%83%E3%82%AF%E4%BB%98%E3%81%8D/dp/B0DHCRV5FG/ref=zg_bs_g_videogames_d_sccl_4/000-0000000-0000000?psc=1 -> {'url': 'https://www.amazon.co
2025-06-06 15:13:40 [INFO] result: result tinti:127d99a24edd9d11c7856acf2daa3ee9 https://www.amazon.co.jp/%E3%82%BD%E3%83%8B%E3%83%BC-%E3%82%B2%E3%83%BC%E3%83%9F%E3%83%B3%E3%82%B0%E3%82%A4%E3%83%A4%E3%83%9B%E3%83%B3-INZONE-Buds-USBType-C%E3%83%88%E3%83%A9%E3%83%B3%E3%82%B7%E3%83%BC%E3%83%90%E3%83%BC%E5%90%8C%E6%A2%B1/dp/B0CKFHB9P3/ref=zg_bs_g_videogames_d_sccl_9/000-0000000-0000000?psc=1 -> {'url': 'https://www.amazon.co
2025-06-06 15:13:42 [INFO] result: result tinti:42c2b08b81491ea2f28eab00fb11e270 https://www.amazon.co.jp/%E3%80%90%E7%B4%94%E6%AD%A3%E5%93%81%E3%80%91DualSense-%E3%83%AF%E3%82%A4%E3%83%A4%E3%83%AC%E3%82%B9%E3%82%B3%E3%83%B3%E3%83%88%E3%83%AD%E3%83%BC%E3%83%A9%E3%83%BC-%E3%82%AF%E3%83%AD%E3%83%9E-%E3%82%B0%E3%83%AA%E3%83%BC%E3%83%B3-CFI-ZCT1J10/dp/B0DJBXSTXF/ref=zg_bs_g_videogames_d_sccl_17/000-0000000-0000000?psc=1 -> {'url': 'https://www.amazon.co
2025-06-06 15:13:45 [INFO] result: result tinti:18c1f2b6d66fd1498ed7b7d10b1d73a4 https://www.amazon.co.jp/Lunriwis-%E3%82%BC%E3%83%AB%E3%83%80%E7%84%A1%E5%8F%8C-%E5%8E%84%E7%81%BD%E3%81%AE%E9%BB%99%E7%A4%BA%E9%8C%B2-Switch/dp/B08HSPBMTY/ref=zg_bs_g_videogames_d_sccl_19/000-0000000-0000000?psc=1 -> {'url': 'https://www.amazon.co
2025-06-06 15:14:15 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 29328941056, "system_memory_percent": 12.7}
2025-06-06 15:15:17 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 29375299584, "system_memory_percent": 12.5}
2025-06-06 15:16:18 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 29376299008, "system_memory_percent": 12.5}
2025-06-06 15:17:20 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 29348261888, "system_memory_percent": 12.6}
2025-06-06 15:18:22 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 29308985344, "system_memory_percent": 12.7}
2025-06-06 15:18:45 [INFO] metrics: Counters: {"fetch_count[project=bunbunbun]": 1, "fetch_count[project=douyo]": 1, "fetch_count[project=kokoko]": 1, "fetch_count[project=iuiu]": 1, "fetch_count[project=mysql_detailed_test]": 1, "fetch_count[project=jijijiji]": 1, "fetch_count[project=mysql_detailed_test_2]": 1, "fetch_count[project=tinti]": 128}
2025-06-06 15:19:24 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 29315862528, "system_memory_percent": 12.7}
2025-06-06 15:20:26 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 29279096832, "system_memory_percent": 12.8}
2025-06-06 15:21:28 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 29286178816, "system_memory_percent": 12.8}
2025-06-06 15:22:30 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 29305012224, "system_memory_percent": 12.7}
2025-06-06 15:23:31 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 29289447424, "system_memory_percent": 12.8}
2025-06-06 15:23:46 [INFO] metrics: Counters: {"fetch_count[project=bunbunbun]": 1, "fetch_count[project=douyo]": 1, "fetch_count[project=kokoko]": 1, "fetch_count[project=iuiu]": 1, "fetch_count[project=mysql_detailed_test]": 1, "fetch_count[project=jijijiji]": 1, "fetch_count[project=mysql_detailed_test_2]": 1, "fetch_count[project=tinti]": 131}
2025-06-06 15:24:33 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 29258948608, "system_memory_percent": 12.9}
2025-06-06 15:25:36 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 29253529600, "system_memory_percent": 12.9}
2025-06-06 15:26:38 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 29234925568, "system_memory_percent": 12.9}
2025-06-06 15:27:41 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 29264769024, "system_memory_percent": 12.8}
2025-06-06 15:28:44 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 29240033280, "system_memory_percent": 12.9}
2025-06-06 15:28:47 [INFO] metrics: Counters: {"fetch_count[project=bunbunbun]": 1, "fetch_count[project=douyo]": 1, "fetch_count[project=kokoko]": 1, "fetch_count[project=iuiu]": 1, "fetch_count[project=mysql_detailed_test]": 1, "fetch_count[project=jijijiji]": 1, "fetch_count[project=mysql_detailed_test_2]": 1, "fetch_count[project=tinti]": 134}
2025-06-06 15:29:46 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 29219155968, "system_memory_percent": 13.0}
2025-06-06 15:30:49 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 29220425728, "system_memory_percent": 13.0}
2025-06-06 15:31:50 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 29259296768, "system_memory_percent": 12.9}
2025-06-06 15:32:53 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 29241135104, "system_memory_percent": 12.9}
2025-06-06 15:33:47 [INFO] metrics: Counters: {"fetch_count[project=bunbunbun]": 1, "fetch_count[project=douyo]": 1, "fetch_count[project=kokoko]": 1, "fetch_count[project=iuiu]": 1, "fetch_count[project=mysql_detailed_test]": 1, "fetch_count[project=jijijiji]": 1, "fetch_count[project=mysql_detailed_test_2]": 1, "fetch_count[project=tinti]": 137}
2025-06-06 15:33:55 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 29226684416, "system_memory_percent": 13.0}
2025-06-06 15:34:58 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 29223555072, "system_memory_percent": 13.0}
2025-06-06 15:36:00 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 29219696640, "system_memory_percent": 13.0}
2025-06-06 15:37:03 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 29241544704, "system_memory_percent": 12.9}
2025-06-06 15:38:05 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 29248782336, "system_memory_percent": 12.9}
2025-06-06 15:38:48 [INFO] metrics: Counters: {"fetch_count[project=bunbunbun]": 1, "fetch_count[project=douyo]": 1, "fetch_count[project=kokoko]": 1, "fetch_count[project=iuiu]": 1, "fetch_count[project=mysql_detailed_test]": 1, "fetch_count[project=jijijiji]": 1, "fetch_count[project=mysql_detailed_test_2]": 1, "fetch_count[project=tinti]": 140}
2025-06-06 15:39:07 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 29189595136, "system_memory_percent": 13.1}
2025-06-06 15:40:09 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 29306937344, "system_memory_percent": 12.7}
2025-06-06 15:41:12 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 29296119808, "system_memory_percent": 12.7}
2025-06-06 15:42:14 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 29293182976, "system_memory_percent": 12.8}
2025-06-06 15:43:17 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 29283180544, "system_memory_percent": 12.8}
2025-06-06 15:44:20 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 29276069888, "system_memory_percent": 12.8}
2025-06-06 15:45:22 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 29259853824, "system_memory_percent": 12.9}
2025-06-06 15:46:24 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 29235085312, "system_memory_percent": 12.9}
2025-06-06 15:47:26 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 29260668928, "system_memory_percent": 12.9}
2025-06-06 15:48:29 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 29249101824, "system_memory_percent": 12.9}
2025-06-06 15:49:32 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 29267292160, "system_memory_percent": 12.8}
2025-06-06 15:50:35 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 29229260800, "system_memory_percent": 12.9}
2025-06-06 15:51:38 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 29256843264, "system_memory_percent": 12.9}
2025-06-06 15:52:41 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 29260058624, "system_memory_percent": 12.9}
2025-06-06 15:53:42 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 29244669952, "system_memory_percent": 12.9}
2025-06-06 15:54:45 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 29245648896, "system_memory_percent": 12.9}
2025-06-06 15:55:48 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 29222019072, "system_memory_percent": 13.0}
2025-06-06 15:56:51 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 29205876736, "system_memory_percent": 13.0}
2025-06-06 15:57:54 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 29194559488, "system_memory_percent": 13.1}
2025-06-06 15:58:57 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 29221138432, "system_memory_percent": 13.0}
2025-06-06 16:00:00 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 29195345920, "system_memory_percent": 13.0}
2025-06-06 16:01:01 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 29184229376, "system_memory_percent": 13.1}
2025-06-06 16:02:04 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 29180506112, "system_memory_percent": 13.1}
2025-06-06 16:03:07 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 29169897472, "system_memory_percent": 13.1}
2025-06-06 16:04:10 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 29157810176, "system_memory_percent": 13.2}
2025-06-06 16:05:12 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 29136474112, "system_memory_percent": 13.2}
2025-06-06 16:06:15 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 29141831680, "system_memory_percent": 13.2}
2025-06-06 16:07:18 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 29038952448, "system_memory_percent": 13.5}
2025-06-06 16:08:19 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 28858253312, "system_memory_percent": 14.1}
2025-06-06 16:09:22 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 28753096704, "system_memory_percent": 14.4}
2025-06-06 16:10:25 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 28819128320, "system_memory_percent": 14.2}
2025-06-06 16:11:28 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 28908298240, "system_memory_percent": 13.9}
2025-06-06 16:12:31 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 28772102144, "system_memory_percent": 14.3}
2025-06-06 16:12:40 [INFO] metrics: Counters: {"fetch_count[project=bunbunbun]": 1, "fetch_count[project=douyo]": 1, "fetch_count[project=kokoko]": 1, "fetch_count[project=iuiu]": 1, "fetch_count[project=mysql_detailed_test]": 1, "fetch_count[project=jijijiji]": 1, "fetch_count[project=mysql_detailed_test_2]": 1, "fetch_count[project=tinti]": 143}
2025-06-06 16:13:34 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 28733104128, "system_memory_percent": 14.4}
2025-06-06 16:14:37 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 28503367680, "system_memory_percent": 15.1}
2025-06-06 16:15:39 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 28915154944, "system_memory_percent": 13.9}
2025-06-06 16:16:41 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 28952281088, "system_memory_percent": 13.8}
2025-06-06 16:17:44 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 28949929984, "system_memory_percent": 13.8}
2025-06-06 16:18:25 [INFO] metrics: Counters: {"fetch_count[project=bunbunbun]": 1, "fetch_count[project=douyo]": 1, "fetch_count[project=kokoko]": 1, "fetch_count[project=iuiu]": 1, "fetch_count[project=mysql_detailed_test]": 1, "fetch_count[project=jijijiji]": 1, "fetch_count[project=mysql_detailed_test_2]": 1, "fetch_count[project=tinti]": 146}
2025-06-06 16:18:47 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 28955529216, "system_memory_percent": 13.8}
2025-06-06 16:19:49 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 28936048640, "system_memory_percent": 13.8}
2025-06-06 16:20:52 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 28924698624, "system_memory_percent": 13.9}
2025-06-06 16:21:55 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 28944449536, "system_memory_percent": 13.8}
2025-06-06 16:22:58 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 28939718656, "system_memory_percent": 13.8}
2025-06-06 16:23:26 [INFO] metrics: Counters: {"fetch_count[project=bunbunbun]": 1, "fetch_count[project=douyo]": 1, "fetch_count[project=kokoko]": 1, "fetch_count[project=iuiu]": 1, "fetch_count[project=mysql_detailed_test]": 1, "fetch_count[project=jijijiji]": 1, "fetch_count[project=mysql_detailed_test_2]": 1, "fetch_count[project=tinti]": 149}
2025-06-06 16:23:34 [INFO] result: result tinti:696ea11ebb5a318578d367cebffb0085 https://www.amazon.co.jp/TURBO%E9%80%A3%E5%B0%84%E6%A9%9F%E8%83%BD-1000mAh%E5%A4%A7%E5%AE%B9%E9%87%8F-Bluetooth-%E3%82%B8%E3%83%A3%E3%82%A4%E3%83%AD%E3%82%BB%E3%83%B3%E3%82%B5%E3%83%BC%E6%90%AD%E8%BC%89-%E6%97%A5%E6%9C%AC%E8%AA%9E%E5%8F%96%E6%89%B1%E8%AA%AC%E6%98%8E%E6%9B%B8%E4%BB%98%E3%81%8D/dp/B0DDK6R2KZ/ref=zg_bs_g_videogames_d_sccl_8/000-0000000-0000000?psc=1 -> {'url': 'https://www.amazon.co
2025-06-06 16:23:37 [INFO] result: result tinti:e3dfa48dec123014d8ba62aea06007a5 https://www.amazon.co.jp/%E3%80%902%E6%9E%9A%E3%82%BB%E3%83%83%E3%83%88%E3%80%91-Switch-%E3%82%AC%E3%83%A9%E3%82%B9%E3%83%95%E3%82%A3%E3%83%AB%E3%83%A0-%E9%AB%98%E6%84%9F%E5%BA%A6%E3%82%BF%E3%83%83%E3%83%81-Greerass/dp/B0DP4PSFQV/ref=zg_bs_g_videogames_d_sccl_10/000-0000000-0000000?psc=1 -> {'url': 'https://www.amazon.co
2025-06-06 16:23:39 [INFO] result: result tinti:1c72fc66d4eb273535f50be6a2b3a90c https://www.amazon.co.jp/%E3%82%BD%E3%83%8B%E3%83%BC%E3%83%BB%E3%82%A4%E3%83%B3%E3%82%BF%E3%83%A9%E3%82%AF%E3%83%86%E3%82%A3%E3%83%96%E3%82%A8%E3%83%B3%E3%82%BF%E3%83%86%E3%82%A4%E3%83%B3%E3%83%A1%E3%83%B3%E3%83%88-CFI-7000B01-PlayStation-5-Pro/dp/B0DGT79B1T/ref=zg_bs_g_videogames_d_sccl_15/000-0000000-0000000?psc=1 -> {'url': 'https://www.amazon.co
2025-06-06 16:23:41 [INFO] result: result tinti:4933a6db95df1fa916257977d83310ba https://www.amazon.co.jp/%E3%83%9D%E3%82%B1%E3%83%83%E3%83%88%E3%83%A2%E3%83%B3%E3%82%B9%E3%82%BF%E3%83%BC-%E3%83%90%E3%82%A4%E3%82%AA%E3%83%AC%E3%83%83%E3%83%88-Switch-%E3%80%90%E6%97%A9%E6%9C%9F%E8%B3%BC%E5%85%A5%E7%89%B9%E5%85%B8%E3%80%91%E3%83%97%E3%83%AD%E3%83%A2%E3%82%AB%E3%83%BC%E3%83%89%E3%80%8C%E3%83%94%E3%82%AB%E3%83%81%E3%83%A5%E3%82%A6%E3%80%8D-%C3%971/dp/B09X17GBLT/ref=zg_bs_g_videogames_d_sccl_10/000-0000000-0000000?psc=1 -> {'url': 'https://www.amazon.co
2025-06-06 16:23:43 [INFO] result: result tinti:ebd7b08862a90b7064d018372857cb91 https://www.amazon.co.jp/%E3%83%97%E3%83%AC%E3%82%A4%E3%82%B9%E3%83%86%E3%83%BC%E3%82%B7%E3%83%A7%E3%83%B3-%E3%82%B9%E3%83%88%E3%82%A2%E3%83%81%E3%82%B1%E3%83%83%E3%83%88-3-000%E5%86%86-%E3%82%AA%E3%83%B3%E3%83%A9%E3%82%A4%E3%83%B3%E3%82%B3%E3%83%BC%E3%83%89%E7%89%88/dp/B08M6DTZ38/ref=zg_bs_g_videogames_d_sccl_21/000-0000000-0000000?psc=1 -> {'url': 'https://www.amazon.co
2025-06-06 16:23:46 [INFO] result: result tinti:99bb729d692b3ca0623786649ea475b4 https://www.amazon.co.jp/%E4%BB%BB%E5%A4%A9%E5%A0%82-%E6%98%9F%E3%81%AE%E3%82%AB%E3%83%BC%E3%83%93%E3%82%A3-%E3%83%87%E3%82%A3%E3%82%B9%E3%82%AB%E3%83%90%E3%83%AA%E3%83%BC-Switch/dp/B09Q5GM5PQ/ref=zg_bs_g_videogames_d_sccl_19/000-0000000-0000000?psc=1 -> {'url': 'https://www.amazon.co
2025-06-06 16:23:48 [INFO] result: result tinti:34407c835295e4cdf8abdd013f10cfc3 https://www.amazon.co.jp/%E4%BB%BB%E5%A4%A9%E5%A0%82-%E3%80%90Switch%E7%94%A8%E8%BF%BD%E5%8A%A0%E3%82%B3%E3%83%B3%E3%83%86%E3%83%B3%E3%83%84%E3%80%91%E3%83%9D%E3%82%B1%E3%83%83%E3%83%88%E3%83%A2%E3%83%B3%E3%82%B9%E3%82%BF%E3%83%BC-%E3%82%B9%E3%82%AB%E3%83%BC%E3%83%AC%E3%83%83%E3%83%88%E3%83%BB%E3%83%90%E3%82%A4%E3%82%AA%E3%83%AC%E3%83%83%E3%83%88-%E3%82%BC%E3%83%AD%E3%81%AE%E7%A7%98%E5%AE%9D-%E3%82%AA%E3%83%B3%E3%83%A9%E3%82%A4%E3%83%B3%E3%82%B3%E3%83%BC%E3%83%89%E7%89%88/dp/B0BWQQ1GW3/ref=zg_bs_g_videogames_d_sccl_15/000-0000000-0000000?psc=1 -> {'url': 'https://www.amazon.co
2025-06-06 16:23:50 [INFO] result: result tinti:55e8850ca61909e4d48d06c8ac14fd81 https://www.amazon.co.jp/%E3%80%90PS5%E3%80%91Ghost-of-Tsushima-Directors-Cut/dp/B098HVGHFK/ref=zg_bs_g_videogames_d_sccl_25/000-0000000-0000000?psc=1 -> {'url': 'https://www.amazon.co
2025-06-06 16:23:52 [INFO] result: result tinti:86b12fb9eff14dc0d42f5809cac64ade https://www.amazon.co.jp/%E3%83%97%E3%83%AC%E3%82%A4%E3%82%B9%E3%83%86%E3%83%BC%E3%82%B7%E3%83%A7%E3%83%B3-%E3%82%B9%E3%83%88%E3%82%A2%E3%83%81%E3%82%B1%E3%83%83%E3%83%88-5-000%E5%86%86-%E3%82%AA%E3%83%B3%E3%83%A9%E3%82%A4%E3%83%B3%E3%82%B3%E3%83%BC%E3%83%89%E7%89%88/dp/B08M69WPHZ/ref=zg_bs_g_videogames_d_sccl_22/000-0000000-0000000?psc=1 -> {'url': 'https://www.amazon.co
2025-06-06 16:23:54 [INFO] result: result tinti:375240ad52f696432c8268ae028c2e2c https://www.amazon.co.jp/%E3%82%BC%E3%83%AB%E3%83%80%E3%81%AE%E4%BC%9D%E8%AA%AC-%E3%83%86%E3%82%A3%E3%82%A2%E3%83%BC%E3%82%BA-Nintendo-Switch-%E3%82%A2%E3%83%83%E3%83%97%E3%82%B0%E3%83%AC%E3%83%BC%E3%83%89%E3%83%91%E3%82%B9-%E3%82%AA%E3%83%B3%E3%83%A9%E3%82%A4%E3%83%B3%E3%82%B3%E3%83%BC%E3%83%89%E7%89%88/dp/B0F5G329BR/ref=zg_bs_g_videogames_d_sccl_26/000-0000000-0000000?psc=1 -> {'url': 'https://www.amazon.co
2025-06-06 16:23:56 [INFO] result: result tinti:910f31fd032370d093c3b69867f8210a https://www.amazon.co.jp/Nintendo-Switch2-%E3%83%95%E3%82%A3%E3%83%AB%E3%83%A0-%E5%8F%96%E3%82%8A%E9%99%A4%E3%81%8D%E7%99%BA%E6%B3%A1%E6%9D%BF%E3%80%91%E5%9B%BD%E7%94%A3%E6%97%AD%E7%A1%9D%E5%AD%90%E6%9D%90%E8%B3%AA-SENXLLSwitch2-G21/dp/B0DXT1TB5Y/ref=zg_bs_g_videogames_d_sccl_27/000-0000000-0000000?psc=1 -> {'url': 'https://www.amazon.co
2025-06-06 16:23:59 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 28770783232, "system_memory_percent": 14.3}
2025-06-06 16:25:02 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 28978237440, "system_memory_percent": 13.7}
2025-06-06 16:26:05 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 28959924224, "system_memory_percent": 13.7}
2025-06-06 16:27:08 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 29005922304, "system_memory_percent": 13.6}
2025-06-06 16:28:11 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 28975501312, "system_memory_percent": 13.7}
2025-06-06 16:28:47 [INFO] metrics: Counters: {"fetch_count[project=bunbunbun]": 1, "fetch_count[project=douyo]": 1, "fetch_count[project=kokoko]": 1, "fetch_count[project=iuiu]": 1, "fetch_count[project=mysql_detailed_test]": 1, "fetch_count[project=jijijiji]": 1, "fetch_count[project=mysql_detailed_test_2]": 1, "fetch_count[project=tinti]": 164}
2025-06-06 16:28:53 [INFO] result: result tinti:3a2b0745b8ce72609e9d7fadae031419 https://www.amazon.co.jp/Nintendo-Switch-Online-%E5%80%8B%E4%BA%BA%E3%83%97%E3%83%A9%E3%83%B33%E3%81%8B%E6%9C%88-%E3%82%AA%E3%83%B3%E3%83%A9%E3%82%A4%E3%83%B3%E3%82%B3%E3%83%BC%E3%83%89%E7%89%88/dp/B07HG6HC3H/ref=zg_bs_g_videogames_d_sccl_3/000-0000000-0000000?psc=1 -> {'url': 'https://www.amazon.co
2025-06-06 16:28:54 [INFO] result: result tinti:17590c9974848da8ebfe8b23ec6ea79d https://www.amazon.co.jp/%E3%82%BC%E3%83%AB%E3%83%80%E3%81%AE%E4%BC%9D%E8%AA%AC-%E3%83%96%E3%83%AC%E3%82%B9-%E3%82%AA%E3%83%96-%E3%83%AF%E3%82%A4%E3%83%AB%E3%83%89-Switch/dp/B01N12HJHQ/ref=zg_bs_g_videogames_d_sccl_20/000-0000000-0000000?psc=1 -> {'url': 'https://www.amazon.co
2025-06-06 16:29:14 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 28998561792, "system_memory_percent": 13.6}
2025-06-06 16:30:16 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 28963545088, "system_memory_percent": 13.7}
2025-06-06 16:31:18 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 28950040576, "system_memory_percent": 13.8}
2025-06-06 16:32:21 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 29001216000, "system_memory_percent": 13.6}
2025-06-06 16:33:24 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 28965408768, "system_memory_percent": 13.7}
2025-06-06 16:33:48 [INFO] metrics: Counters: {"fetch_count[project=bunbunbun]": 1, "fetch_count[project=douyo]": 1, "fetch_count[project=kokoko]": 1, "fetch_count[project=iuiu]": 1, "fetch_count[project=mysql_detailed_test]": 1, "fetch_count[project=jijijiji]": 1, "fetch_count[project=mysql_detailed_test_2]": 1, "fetch_count[project=tinti]": 167}
2025-06-06 16:33:54 [INFO] result: result tinti:9f70ab2f9102b9be4b172802bb03190d https://www.amazon.co.jp/%E4%BB%BB%E5%A4%A9%E5%A0%82-4902370552621-%E3%80%90%E4%BB%BB%E5%A4%A9%E5%A0%82%E7%B4%94%E6%AD%A3%E5%93%81%E3%80%91Joy-Con-%E3%83%8F%E3%83%B3%E3%83%89%E3%83%AB-2%E5%80%8B%E3%82%BB%E3%83%83%E3%83%88/dp/B0DX1RRL8N/ref=zg_bs_g_videogames_d_sccl_1/000-0000000-0000000?psc=1 -> {'url': 'https://www.amazon.co
2025-06-06 16:33:57 [INFO] result: result tinti:d238d2fe988504bf18d9ec6d9d855406 https://www.amazon.co.jp/Pok%C3%A9mon-%EF%BC%88%E3%83%9D%E3%82%B1%E3%83%A2%E3%83%B3-%E3%83%97%E3%83%A9%E3%82%B9%E3%83%97%E3%83%A9%E3%82%B9%EF%BC%89-%E3%80%90Amazon-co-jp%E9%99%90%E5%AE%9A%E3%80%91%E3%82%AA%E3%83%AA%E3%82%B8%E3%83%8A%E3%83%AB%E3%83%87%E3%82%B6%E3%82%A4%E3%83%B3-%E3%83%9E%E3%82%A4%E3%82%AF%E3%83%AD%E3%83%95%E3%82%A1%E3%82%A4%E3%83%90%E3%83%BC%E3%82%AF%E3%83%AD%E3%82%B9/dp/B0CLYCCG1N/ref=zg_bs_g_videogames_d_sccl_7/000-0000000-0000000?psc=1 -> {'url': 'https://www.amazon.co
2025-06-06 16:33:59 [INFO] result: result tinti:6271353f0d7a5b0f2641bb35211440ec https://www.amazon.co.jp/%E3%82%B9%E3%83%91%E3%82%A4%E3%82%AF%E3%83%BB%E3%83%81%E3%83%A5%E3%83%B3%E3%82%BD%E3%83%95%E3%83%88-BEE-P-AAE7B-%E3%82%B5%E3%82%A4%E3%83%90%E3%83%BC%E3%83%91%E3%83%B3%E3%82%AF2077-%E3%82%A2%E3%83%AB%E3%83%86%E3%82%A3%E3%83%A1%E3%83%83%E3%83%88%E3%82%A8%E3%83%87%E3%82%A3%E3%82%B7%E3%83%A7%E3%83%B3-Switch2/dp/B0F5HVW3LC/ref=zg_bs_g_videogames_d_sccl_12/000-0000000-0000000?psc=1 -> {'url': 'https://www.amazon.co
2025-06-06 16:34:27 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 28943069184, "system_memory_percent": 13.8}
2025-06-06 16:35:30 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 28954865664, "system_memory_percent": 13.8}
2025-06-06 16:36:33 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 28956905472, "system_memory_percent": 13.8}
2025-06-06 16:37:36 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 28969918464, "system_memory_percent": 13.7}
2025-06-06 16:38:38 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 28971298816, "system_memory_percent": 13.7}
2025-06-06 16:38:49 [INFO] metrics: Counters: {"fetch_count[project=bunbunbun]": 1, "fetch_count[project=douyo]": 1, "fetch_count[project=kokoko]": 1, "fetch_count[project=iuiu]": 1, "fetch_count[project=mysql_detailed_test]": 1, "fetch_count[project=jijijiji]": 1, "fetch_count[project=mysql_detailed_test_2]": 1, "fetch_count[project=tinti]": 170}
2025-06-06 16:38:57 [INFO] result: result tinti:834d53746d3c8fc09cdb1b2b3b5cca70 https://www.amazon.co.jp/%E3%83%97%E3%83%AC%E3%82%A4%E3%82%B9%E3%83%86%E3%83%BC%E3%82%B7%E3%83%A7%E3%83%B3-%E3%82%B9%E3%83%88%E3%82%A2%E3%83%81%E3%82%B1%E3%83%83%E3%83%88-10-000%E5%86%86-%E3%82%AA%E3%83%B3%E3%83%A9%E3%82%A4%E3%83%B3%E3%82%B3%E3%83%BC%E3%83%89%E7%89%88/dp/B08M69W27Q/ref=zg_bs_g_videogames_d_sccl_18/000-0000000-0000000?psc=1 -> {'url': 'https://www.amazon.co
2025-06-06 16:38:59 [INFO] result: result tinti:21d7fbcf7c6f0f2a43748c317f1c2d27 https://www.amazon.co.jp/%E6%A9%9F%E5%8B%95%E6%88%A6%E5%A3%AB%E3%82%AC%E3%83%B3%E3%83%80%E3%83%A0SEED-BATTLE-DESTINY-REMASTERED-Switch/dp/B0DX65ZFDZ/ref=zg_bs_g_videogames_d_sccl_14/000-0000000-0000000?psc=1 -> {'url': 'https://www.amazon.co
2025-06-06 16:39:41 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 28925665280, "system_memory_percent": 13.9}
2025-06-06 16:40:44 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 28980797440, "system_memory_percent": 13.7}
2025-06-06 16:41:47 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 28964990976, "system_memory_percent": 13.7}
2025-06-06 16:42:50 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 28965195776, "system_memory_percent": 13.7}
2025-06-06 16:43:53 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 28955009024, "system_memory_percent": 13.8}
2025-06-06 16:44:56 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 28909563904, "system_memory_percent": 13.9}
2025-06-06 16:45:58 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 28960935936, "system_memory_percent": 13.7}
2025-06-06 16:47:01 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 28938137600, "system_memory_percent": 13.8}
2025-06-06 16:48:04 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 28958756864, "system_memory_percent": 13.8}
2025-06-06 16:49:07 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 28967477248, "system_memory_percent": 13.7}
2025-06-06 16:50:10 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 28952993792, "system_memory_percent": 13.8}
2025-06-06 16:51:13 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 28966424576, "system_memory_percent": 13.7}
2025-06-06 16:52:16 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 28960051200, "system_memory_percent": 13.7}
2025-06-06 16:53:17 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 28957519872, "system_memory_percent": 13.8}
2025-06-06 16:54:20 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 28973629440, "system_memory_percent": 13.7}
2025-06-06 16:55:23 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 28975730688, "system_memory_percent": 13.7}
2025-06-06 16:56:26 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 28976316416, "system_memory_percent": 13.7}
2025-06-06 16:57:30 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 28963639296, "system_memory_percent": 13.7}
2025-06-06 16:58:33 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 28922155008, "system_memory_percent": 13.9}
2025-06-06 16:59:36 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 28962426880, "system_memory_percent": 13.7}
2025-06-06 17:00:40 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 28963655680, "system_memory_percent": 13.7}
2025-06-06 17:01:41 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 28958228480, "system_memory_percent": 13.8}
2025-06-06 17:02:44 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 28949938176, "system_memory_percent": 13.8}
2025-06-06 17:03:47 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 28936863744, "system_memory_percent": 13.8}
2025-06-06 17:04:51 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 28942372864, "system_memory_percent": 13.8}
2025-06-06 17:05:54 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 28957622272, "system_memory_percent": 13.8}
2025-06-06 17:06:57 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 28758323200, "system_memory_percent": 14.4}
2025-06-06 17:08:00 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 28862296064, "system_memory_percent": 14.0}
2025-06-06 17:09:02 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 28736385024, "system_memory_percent": 14.4}
2025-06-06 17:10:05 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 28574543872, "system_memory_percent": 14.9}
2025-06-06 17:11:08 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 28782817280, "system_memory_percent": 14.3}
2025-06-06 17:12:11 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 28532035584, "system_memory_percent": 15.0}
2025-06-06 17:13:15 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 28725997568, "system_memory_percent": 14.4}
2025-06-06 17:14:18 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 28750573568, "system_memory_percent": 14.4}
2025-06-06 17:15:21 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 28766101504, "system_memory_percent": 14.3}
2025-06-06 17:16:22 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 28754534400, "system_memory_percent": 14.4}
2025-06-06 17:17:25 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 28557504512, "system_memory_percent": 14.9}
2025-06-06 17:18:29 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 28735590400, "system_memory_percent": 14.4}
2025-06-06 17:19:32 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 28724469760, "system_memory_percent": 14.5}
2025-06-06 17:20:35 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 28719316992, "system_memory_percent": 14.5}
2025-06-06 17:21:38 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 28736745472, "system_memory_percent": 14.4}
2025-06-06 17:22:41 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 28773097472, "system_memory_percent": 14.3}
2025-06-06 17:23:43 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 28758482944, "system_memory_percent": 14.3}
2025-06-06 17:24:44 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 28733968384, "system_memory_percent": 14.4}
2025-06-06 17:25:45 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 28733071360, "system_memory_percent": 14.4}
2025-06-06 17:26:48 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 28783308800, "system_memory_percent": 14.3}
2025-06-06 17:27:51 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 28760420352, "system_memory_percent": 14.3}
2025-06-06 17:28:54 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 28664414208, "system_memory_percent": 14.6}
2025-06-06 17:29:57 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 28556836864, "system_memory_percent": 15.0}
2025-06-06 17:30:58 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 28180963328, "system_memory_percent": 16.1}
2025-06-06 17:32:02 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 28142178304, "system_memory_percent": 16.2}
2025-06-06 17:33:05 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 28475514880, "system_memory_percent": 15.2}
2025-06-06 17:34:08 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 28007337984, "system_memory_percent": 16.6}
2025-06-06 17:35:11 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 27761967104, "system_memory_percent": 17.3}
2025-06-06 17:36:14 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 27974615040, "system_memory_percent": 16.7}
2025-06-06 17:37:17 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 27974774784, "system_memory_percent": 16.7}
2025-06-06 17:38:19 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 27967606784, "system_memory_percent": 16.7}
2025-06-06 17:39:22 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 27973017600, "system_memory_percent": 16.7}
2025-06-06 17:40:25 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 27963723776, "system_memory_percent": 16.7}
2025-06-06 17:41:28 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 27971751936, "system_memory_percent": 16.7}
2025-06-06 17:42:32 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 27955863552, "system_memory_percent": 16.7}
2025-06-06 17:43:35 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 27969593344, "system_memory_percent": 16.7}
2025-06-06 17:44:38 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 27985895424, "system_memory_percent": 16.7}
2025-06-06 17:45:39 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 27966795776, "system_memory_percent": 16.7}
2025-06-06 17:46:43 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 27968884736, "system_memory_percent": 16.7}
2025-06-06 17:47:46 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 27958456320, "system_memory_percent": 16.7}
2025-06-06 17:48:49 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 27971571712, "system_memory_percent": 16.7}
2025-06-06 17:49:52 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 27961909248, "system_memory_percent": 16.7}
2025-06-06 17:50:55 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 27955998720, "system_memory_percent": 16.7}
2025-06-06 17:51:58 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 27961364480, "system_memory_percent": 16.7}
2025-06-06 17:53:02 [INFO] metrics: Gauges: {"memory_usage_rss": 90591232, "memory_usage_vms": 637476864, "memory_usage_percent": 0.2698040398266237, "system_memory_total": 33576677376, "system_memory_available": 27961896960, "system_memory_percent": 16.7}
{"timestamp": "2025-06-08T08:22:26.815397", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-08 08:22:27 [INFO] memory_optimizer: Memory optimizer initialized with max_memory_percent=80.0%, gc_interval=60s, check_interval=30s, auto_optimize=True
2025-06-08 08:22:27 [INFO] pyspider.libs.redis_fallback: ✅ Redis接続成功: redis://localhost:6379/0
2025-06-08 08:22:27 [INFO] pyspider.libs.redis_fallback: 🔍 Redis接続監視を開始しました
2025-06-08 08:22:27 [INFO] pyspider.libs.redis_fallback: ✅ Redis フォールバック機能を初期化しました
2025-06-08 08:22:27 [ERROR] pyspider.errors: Uncaught exception occurred:
2025-06-08 08:22:27 [ERROR] pyspider.errors: Exception: NameError: name 'memory_optimizer' is not defined

  File "<frozen runpy>", line 198, in _run_module_as_main
  Local variables:
    mod_name = 'pyspider.run'
    alter_argv = True
    mod_spec = ModuleSpec(name='pyspider.run', loader=<_frozen_importlib_external.SourceFileLoader object at 0x7...
    code = <code object <module> at 0x5c782f7a73b0, file "/home/<USER>/workplace/python/pyspiderNX2/pyspi...
    main_globals = {'__name__': '__main__', '__doc__': None, '__package__': 'pyspider', '__loader__': <_frozen_impor...

  File "<frozen runpy>", line 88, in _run_code
  Local variables:
    code = <code object <module> at 0x5c782f7a73b0, file "/home/<USER>/workplace/python/pyspiderNX2/pyspi...
    run_globals = {'__name__': '__main__', '__doc__': None, '__package__': 'pyspider', '__loader__': <_frozen_impor...
    init_globals = None
    mod_name = '__main__'
    mod_spec = ModuleSpec(name='pyspider.run', loader=<_frozen_importlib_external.SourceFileLoader object at 0x7...
    pkg_name = 'pyspider'
    script_name = None
    loader = <_frozen_importlib_external.SourceFileLoader object at 0x7bfeb7a90c50>
    fname = '/home/<USER>/workplace/python/pyspiderNX2/pyspider/run.py'
    cached = '/home/<USER>/workplace/python/pyspiderNX2/pyspider/__pycache__/run.cpython-313.pyc'

  File "/home/<USER>/workplace/python/pyspiderNX2/pyspider/run.py", line 1152, in <module>
    main()
  Local variables:
    os = <module 'os' (frozen)>
    sys = <module 'sys' (built-in)>
    copy = <module 'copy' from '/home/<USER>/.pyenv/versions/3.13.2/lib/python3.13/copy.py'>
    time = <module 'time' (built-in)>
    shutil = <module 'shutil' from '/home/<USER>/.pyenv/versions/3.13.2/lib/python3.13/shutil.py'>
    logging = <module 'logging' from '/home/<USER>/.pyenv/versions/3.13.2/lib/python3.13/logging/__init__.py'>
    click = <module 'click' from '/home/<USER>/.pyenv/versions/3.13.2/lib/python3.13/site-packages/click/_...
    pyspider = <module 'pyspider' from '/home/<USER>/workplace/python/pyspiderNX2/pyspider/__init__.py'>
    connect_message_queue = <function connect_message_queue at 0x7bfeb743c180>
    connect_database = <function connect_database at 0x7bfeb748b740>
    utils = <module 'pyspider.libs.utils' from '/home/<USER>/workplace/python/pyspiderNX2/pyspider/libs/ut...
    create_pid_file = <function create_pid_file at 0x7bfeb7aa3100>
    read_config = <function read_config at 0x7bfeb6796020>
    connect_db = <function connect_db at 0x7bfeb6797a60>
    load_cls = <function load_cls at 0x7bfeb6797b00>
    connect_rpc = <function connect_rpc at 0x7bfeb6797ba0>
    cli = <Group cli>
    scheduler = <Command scheduler>
    fetcher = <Command fetcher>
    processor = <Command processor>
    result_worker = <Command result-worker>
    webui = <Command webui>
    puppeteer = <Command puppeteer>
    all = <Command all>
    bench = <Command bench>
    one = <Command one>
    send_message = <Command send-message>
    main = <function main at 0x7bfeb6797c40>

  File "/home/<USER>/workplace/python/pyspiderNX2/pyspider/run.py", line 1149, in main
    cli()

  File "/home/<USER>/.pyenv/versions/3.13.2/lib/python3.13/site-packages/click/core.py", line 1442, in __call__
    return self.main(*args, **kwargs)
  Local variables:
    self = <Group cli>
    args = ()
    kwargs = {}

  File "/home/<USER>/.pyenv/versions/3.13.2/lib/python3.13/site-packages/click/core.py", line 1363, in main
    rv = self.invoke(ctx)
  Local variables:
    self = <Group cli>
    args = ['scheduler']
    prog_name = 'python -m pyspider.run'
    complete_var = None
    standalone_mode = True
    windows_expand_args = True
    extra = {}
    ctx = <click.core.Context object at 0x7bfeb6729a90>

  File "/home/<USER>/.pyenv/versions/3.13.2/lib/python3.13/site-packages/click/core.py", line 1830, in invoke
    return _process_result(sub_ctx.command.invoke(sub_ctx))
  Local variables:
    self = <Group cli>
    ctx = <click.core.Context object at 0x7bfeb6729a90>
    _process_result = <function Group.invoke.<locals>._process_result at 0x7bfeb67ab240>
    args = []
    cmd_name = 'scheduler'
    cmd = <Command scheduler>
    sub_ctx = <click.core.Context object at 0x7bfeb67b6710>

  File "/home/<USER>/.pyenv/versions/3.13.2/lib/python3.13/site-packages/click/core.py", line 1226, in invoke
    return ctx.invoke(self.callback, **ctx.params)
  Local variables:
    self = <Command scheduler>
    ctx = <click.core.Context object at 0x7bfeb67b6710>

  File "/home/<USER>/.pyenv/versions/3.13.2/lib/python3.13/site-packages/click/core.py", line 794, in invoke
    return callback(*args, **kwargs)
  Local variables:
    self = <click.core.Context object at 0x7bfeb67b6710>
    callback = <function scheduler at 0x7bfeb67a84a0>
    args = ()
    kwargs = {'xmlrpc': False, 'no_xmlrpc': False, 'xmlrpc_host': '0.0.0.0', 'xmlrpc_port': 23333, 'inqueue_li...
    ctx = <click.core.Context object at 0x7bfeb67b6710>

  File "/home/<USER>/.pyenv/versions/3.13.2/lib/python3.13/site-packages/click/decorators.py", line 34, in new_func
    return f(get_current_context(), *args, **kwargs)
  Local variables:
    args = ()
    kwargs = {'xmlrpc': False, 'no_xmlrpc': False, 'xmlrpc_host': '0.0.0.0', 'xmlrpc_port': 23333, 'inqueue_li...
    f = <function scheduler at 0x7bfeb67a8400>

  File "/home/<USER>/workplace/python/pyspiderNX2/pyspider/run.py", line 409, in scheduler
    scheduler = Scheduler(**kwargs)
  Local variables:
    ctx = <click.core.Context object at 0x7bfeb67b6710>
    xmlrpc = False
    no_xmlrpc = False
    xmlrpc_host = '0.0.0.0'
    xmlrpc_port = 23333
    inqueue_limit = 0
    delete_time = 86400
    active_tasks = 100
    loop_limit = 1000
    fail_pause_num = 10
    scheduler_cls = <class 'pyspider.scheduler.scheduler.ThreadBaseScheduler'>
    threads = None
    get_object = False
    g = {'instances': [], 'config': {'taskdb': 'sqlite+taskdb:///data/pyspider_taskdb.db', 'projectdb': '...
    Scheduler = <class 'pyspider.scheduler.scheduler.ThreadBaseScheduler'>
    kwargs = {'taskdb': <pyspider.database.sqlite.taskdb.TaskDB object at 0x7bfeb672aa50>, 'projectdb': <pyspi...

  File "/home/<USER>/workplace/python/pyspiderNX2/pyspider/scheduler/scheduler.py", line 1379, in __init__
    super(ThreadBaseScheduler, self).__init__(*args, **kwargs)
  Local variables:
    self = <pyspider.scheduler.scheduler.ThreadBaseScheduler object at 0x7bfeb4cd4050>
    threads = 4
    args = ()
    kwargs = {'taskdb': <pyspider.database.sqlite.taskdb.TaskDB object at 0x7bfeb672aa50>, 'projectdb': <pyspi...

  File "/home/<USER>/workplace/python/pyspiderNX2/pyspider/scheduler/scheduler.py", line 204, in __init__
    self.memory_optimizer = memory_optimizer
  Local variables:
    self = <pyspider.scheduler.scheduler.ThreadBaseScheduler object at 0x7bfeb4cd4050>
    taskdb = <pyspider.database.sqlite.taskdb.TaskDB object at 0x7bfeb672aa50>
    projectdb = <pyspider.database.sqlite.projectdb.ProjectDB object at 0x7bfeb4d76900>
    newtask_queue = <pyspider.message_queue.redis_queue.RedisQueue object at 0x7bfeb4d77230>
    status_queue = <pyspider.message_queue.redis_queue.RedisQueue object at 0x7bfeb4da6e90>
    out_queue = <pyspider.message_queue.redis_queue.RedisQueue object at 0x7bfeb4da7c50>
    data_path = './data'
    resultdb = <pyspider.database.sqlite.resultdb.ResultDB object at 0x7bfeb4d76a50>
    redis_url = 'redis://localhost:6379/0'

{"timestamp": "2025-06-08T08:23:49.792035", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-08 08:23:50 [INFO] memory_optimizer: Memory optimizer initialized with max_memory_percent=80.0%, gc_interval=60s, check_interval=30s, auto_optimize=True
2025-06-08 08:23:50 [INFO] pyspider.libs.redis_fallback: ✅ Redis接続成功: redis://localhost:6379/0
2025-06-08 08:23:50 [INFO] pyspider.libs.redis_fallback: 🔍 Redis接続監視を開始しました
2025-06-08 08:23:50 [INFO] pyspider.libs.redis_fallback: ✅ Redis フォールバック機能を初期化しました
2025-06-08 08:23:50 [INFO] memory_optimizer: Memory monitoring started
2025-06-08 08:23:50 [INFO] root: Memory optimizer started for scheduler
2025-06-08 08:23:50 [INFO] root: Created PID file for scheduler: /tmp/pyspider_scheduler.pid
{"timestamp": "2025-06-08T08:23:52.022256", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-08 08:23:52 [INFO] root: Created PID file for fetcher: /tmp/pyspider_fetcher.pid
{"timestamp": "2025-06-08T08:23:54.251712", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-08 08:23:54 [INFO] memory_optimizer: Memory optimizer initialized with max_memory_percent=80.0%, gc_interval=60s, check_interval=30s, auto_optimize=True
2025-06-08 08:23:54 [INFO] memory_optimizer: Memory monitoring started
2025-06-08 08:23:54 [INFO] root: Created PID file for processor: /tmp/pyspider_processor.pid
{"timestamp": "2025-06-08T08:23:56.291233", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-08 08:23:56 [INFO] memory_optimizer: Memory optimizer initialized with max_memory_percent=80.0%, gc_interval=60s, check_interval=30s, auto_optimize=True
2025-06-08 08:23:56 [INFO] memory_optimizer: Memory monitoring started
2025-06-08 08:23:56 [INFO] root: Created PID file for result_worker: /tmp/pyspider_result_worker.pid
2025-06-08 08:23:56 [INFO] result: result_worker starting...
{"timestamp": "2025-06-08T08:23:58.346904", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-08 08:23:58 [INFO] memory_optimizer: Memory optimizer initialized with max_memory_percent=80.0%, gc_interval=60s, check_interval=30s, auto_optimize=True
2025-06-08 08:23:58 [INFO] memory_optimizer: Memory monitoring started
2025-06-08 08:23:59 [INFO] libs.cache: Using Redis cache
2025-06-08 08:23:59 [ERROR] pyspider.errors: Uncaught exception occurred:
2025-06-08 08:23:59 [ERROR] pyspider.errors: Exception: ModuleNotFoundError: No module named 'pyspider.webui.api_v2.utils'

  File "<frozen runpy>", line 198, in _run_module_as_main
  Local variables:
    mod_name = 'pyspider.run'
    alter_argv = True
    mod_spec = ModuleSpec(name='pyspider.run', loader=<_frozen_importlib_external.SourceFileLoader object at 0x7...
    code = <code object <module> at 0x5b4875ae2390, file "/home/<USER>/workplace/python/pyspiderNX2/pyspi...
    main_globals = {'__name__': '__main__', '__doc__': None, '__package__': 'pyspider', '__loader__': <_frozen_impor...

  File "<frozen runpy>", line 88, in _run_code
  Local variables:
    code = <code object <module> at 0x5b4875ae2390, file "/home/<USER>/workplace/python/pyspiderNX2/pyspi...
    run_globals = {'__name__': '__main__', '__doc__': None, '__package__': 'pyspider', '__loader__': <_frozen_impor...
    init_globals = None
    mod_name = '__main__'
    mod_spec = ModuleSpec(name='pyspider.run', loader=<_frozen_importlib_external.SourceFileLoader object at 0x7...
    pkg_name = 'pyspider'
    script_name = None
    loader = <_frozen_importlib_external.SourceFileLoader object at 0x71d39b2a8c50>
    fname = '/home/<USER>/workplace/python/pyspiderNX2/pyspider/run.py'
    cached = '/home/<USER>/workplace/python/pyspiderNX2/pyspider/__pycache__/run.cpython-313.pyc'

  File "/home/<USER>/workplace/python/pyspiderNX2/pyspider/run.py", line 1152, in <module>
    main()
  Local variables:
    os = <module 'os' (frozen)>
    sys = <module 'sys' (built-in)>
    copy = <module 'copy' from '/home/<USER>/.pyenv/versions/3.13.2/lib/python3.13/copy.py'>
    time = <module 'time' (built-in)>
    shutil = <module 'shutil' from '/home/<USER>/.pyenv/versions/3.13.2/lib/python3.13/shutil.py'>
    logging = <module 'logging' from '/home/<USER>/.pyenv/versions/3.13.2/lib/python3.13/logging/__init__.py'>
    click = <module 'click' from '/home/<USER>/.pyenv/versions/3.13.2/lib/python3.13/site-packages/click/_...
    pyspider = <module 'pyspider' from '/home/<USER>/workplace/python/pyspiderNX2/pyspider/__init__.py'>
    connect_message_queue = <function connect_message_queue at 0x71d39afbfd80>
    connect_database = <function connect_database at 0x71d39adb7380>
    utils = <module 'pyspider.libs.utils' from '/home/<USER>/workplace/python/pyspiderNX2/pyspider/libs/ut...
    create_pid_file = <function create_pid_file at 0x71d39b2b9f80>
    read_config = <function read_config at 0x71d39a0b9c60>
    connect_db = <function connect_db at 0x71d39a0bb6a0>
    load_cls = <function load_cls at 0x71d39a0bb740>
    connect_rpc = <function connect_rpc at 0x71d39a0bb7e0>
    cli = <Group cli>
    scheduler = <Command scheduler>
    fetcher = <Command fetcher>
    processor = <Command processor>
    result_worker = <Command result-worker>
    webui = <Command webui>
    puppeteer = <Command puppeteer>
    all = <Command all>
    bench = <Command bench>
    one = <Command one>
    send_message = <Command send-message>
    main = <function main at 0x71d39a0bb880>

  File "/home/<USER>/workplace/python/pyspiderNX2/pyspider/run.py", line 1149, in main
    cli()

  File "/home/<USER>/.pyenv/versions/3.13.2/lib/python3.13/site-packages/click/core.py", line 1442, in __call__
    return self.main(*args, **kwargs)
  Local variables:
    self = <Group cli>
    args = ()
    kwargs = {}

  File "/home/<USER>/.pyenv/versions/3.13.2/lib/python3.13/site-packages/click/core.py", line 1363, in main
    rv = self.invoke(ctx)
  Local variables:
    self = <Group cli>
    args = ['webui', '--scheduler-rpc=http://localhost:23333/']
    prog_name = 'python -m pyspider.run'
    complete_var = None
    standalone_mode = True
    windows_expand_args = True
    extra = {}
    ctx = <click.core.Context object at 0x71d39a049a90>

  File "/home/<USER>/.pyenv/versions/3.13.2/lib/python3.13/site-packages/click/core.py", line 1828, in invoke
    sub_ctx = cmd.make_context(cmd_name, args, parent=ctx)
  Local variables:
    self = <Group cli>
    ctx = <click.core.Context object at 0x71d39a049a90>
    _process_result = <function Group.invoke.<locals>._process_result at 0x71d39a0d2e80>
    args = []
    cmd_name = 'webui'
    cmd = <Command webui>

  File "/home/<USER>/.pyenv/versions/3.13.2/lib/python3.13/site-packages/click/core.py", line 1186, in make_context
    self.parse_args(ctx, args)
  Local variables:
    self = <Command webui>
    info_name = 'webui'
    args = []
    parent = <click.core.Context object at 0x71d39a049a90>
    extra = {}
    ctx = <click.core.Context object at 0x71d39a0dead0>

  File "/home/<USER>/.pyenv/versions/3.13.2/lib/python3.13/site-packages/click/core.py", line 1197, in parse_args
    value, args = param.handle_parse_result(ctx, opts, args)
  Local variables:
    self = <Command webui>
    ctx = <click.core.Context object at 0x71d39a0dead0>
    args = []
    parser = <click.parser._OptionParser object at 0x71d39a0dee90>
    opts = {'scheduler_rpc': 'http://localhost:23333/'}
    param_order = [<Option scheduler_rpc>]
    param = <Option webui_instance>
    value = False

  File "/home/<USER>/.pyenv/versions/3.13.2/lib/python3.13/site-packages/click/core.py", line 2416, in handle_parse_result
    value = self.process_value(ctx, value)
  Local variables:
    self = <Option webui_instance>
    ctx = <click.core.Context object at 0x71d39a0dead0>
    opts = {'scheduler_rpc': 'http://localhost:23333/'}
    args = []
    value = 'pyspider.webui.app.app'
    source = <ParameterSource.DEFAULT: 3>

  File "/home/<USER>/.pyenv/versions/3.13.2/lib/python3.13/site-packages/click/core.py", line 2355, in process_value
    value = self.callback(ctx, self, value)
  Local variables:
    self = <Option webui_instance>
    ctx = <click.core.Context object at 0x71d39a0dead0>
    value = 'pyspider.webui.app.app'

  File "/home/<USER>/workplace/python/pyspiderNX2/pyspider/run.py", line 61, in load_cls
    return utils.load_object(value)
  Local variables:
    ctx = <click.core.Context object at 0x71d39a0dead0>
    param = <Option webui_instance>
    value = 'pyspider.webui.app.app'

  File "/home/<USER>/workplace/python/pyspiderNX2/pyspider/libs/utils.py", line 378, in load_object
    module = __import__(module_name, globals(), locals(), [object_name])
  Local variables:
    name = 'pyspider.webui.app.app'
    module_name = 'pyspider.webui.app'
    object_name = 'app'

  File "/home/<USER>/workplace/python/pyspiderNX2/pyspider/webui/__init__.py", line 9, in <module>
    from pyspider.webui.app import app
  Local variables:
    components_status = <module 'pyspider.webui.components_status' from '/home/<USER>/workplace/python/pyspiderNX2/pys...
    index = <module 'pyspider.webui.index' from '/home/<USER>/workplace/python/pyspiderNX2/pyspider/webui/...
    debug = <module 'pyspider.webui.debug' from '/home/<USER>/workplace/python/pyspiderNX2/pyspider/webui/...
    result = <module 'pyspider.webui.result' from '/home/<USER>/workplace/python/pyspiderNX2/pyspider/webui...
    task = <module 'pyspider.webui.task' from '/home/<USER>/workplace/python/pyspiderNX2/pyspider/webui/t...
    login = <module 'pyspider.webui.login' from '/home/<USER>/workplace/python/pyspiderNX2/pyspider/webui/...
    products = <module 'pyspider.webui.products' from '/home/<USER>/workplace/python/pyspiderNX2/pyspider/web...
    selector_tester = <module 'pyspider.webui.selector_tester' from '/home/<USER>/workplace/python/pyspiderNX2/pyspi...
    api = <module 'pyspider.webui.api' from '/home/<USER>/workplace/python/pyspiderNX2/pyspider/webui/ap...
    api_utils = <module 'pyspider.webui.api_utils' from '/home/<USER>/workplace/python/pyspiderNX2/pyspider/we...
    metrics = <module 'pyspider.webui.metrics' from '/home/<USER>/workplace/python/pyspiderNX2/pyspider/webu...
    api_v2 = <module 'pyspider.webui.api_v2' from '/home/<USER>/workplace/python/pyspiderNX2/pyspider/webui...

  File "/home/<USER>/workplace/python/pyspiderNX2/pyspider/webui/app.py", line 232, in <module>
    import pyspider.webui.api_v2.file_output
  Local variables:
    os = <module 'os' (frozen)>
    sys = <module 'sys' (built-in)>
    logging = <module 'logging' from '/home/<USER>/.pyenv/versions/3.13.2/lib/python3.13/logging/__init__.py'>
    builtins = <module 'builtins' (built-in)>
    logger = <Logger webui (INFO)>
    urljoin = <function urljoin at 0x71d39adb5800>
    Flask = <class 'flask.app.Flask'>
    CORS = <class 'flask_cors.extension.CORS'>
    tornado_fetcher = <module 'pyspider.fetcher.tornado_fetcher' from '/home/<USER>/workplace/python/pyspiderNX2/pys...
    memory_optimizer = <pyspider.libs.memory_optimizer.MemoryOptimizer object at 0x71d398b36510>
    QuitableFlask = <class 'pyspider.webui.app.QuitableFlask'>
    app = <QuitableFlask 'webui'>
    get_config = <function get_config at 0x71d39a0fc680>
    config = <pyspider.config.unified_config.UnifiedConfig object at 0x71d39a04a900>
    session_key = b'\xa4.\xe5\xc5\x05\xb5\x90a@\xa2\x90\xd82\xbd\xf5}\xaaR\x80\xe4\x8c\x93\x04\x1e\xad3\xcb-\xa6AF\t'
    webui_config = {'port': 5000, 'username': 'admin', 'password': 'PySpider2024!SecurePass#', 'need-auth': True, 's...
    Environment = <class 'jinja2.environment.Environment'>
    FileSystemLoader = <class 'jinja2.loaders.FileSystemLoader'>
    vue_jinja_env = <jinja2.environment.Environment object at 0x71d398acd7f0>
    unified_config = <pyspider.config.unified_config.UnifiedConfig object at 0x71d39a04a900>
    auth_config = {'need_auth': True, 'webui_username': 'admin', 'webui_password': 'PySpider2024!SecurePass#'}
    cdn_url_handler = <function cdn_url_handler at 0x71d397f111c0>
    pyspider = <module 'pyspider' from '/home/<USER>/workplace/python/pyspiderNX2/pyspider/__init__.py'>

  File "/home/<USER>/workplace/python/pyspiderNX2/pyspider/webui/api_v2/file_output.py", line 14, in <module>
    from pyspider.webui.api_v2.utils import api_success, api_error, handle_api_exception
  Local variables:
    os = <module 'os' (frozen)>
    json = <module 'json' from '/home/<USER>/.pyenv/versions/3.13.2/lib/python3.13/json/__init__.py'>
    logging = <module 'logging' from '/home/<USER>/.pyenv/versions/3.13.2/lib/python3.13/logging/__init__.py'>
    Path = <class 'pathlib._local.Path'>
    datetime = <class 'datetime.datetime'>
    Blueprint = <class 'flask.blueprints.Blueprint'>
    request = <LocalProxy unbound>
    jsonify = <function jsonify at 0x71d399af8220>
    Response = <class 'flask.wrappers.Response'>

{"timestamp": "2025-06-08T08:25:00.923790", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-08 08:25:01 [INFO] memory_optimizer: Memory optimizer initialized with max_memory_percent=80.0%, gc_interval=60s, check_interval=30s, auto_optimize=True
2025-06-08 08:25:01 [INFO] pyspider.libs.redis_fallback: ✅ Redis接続成功: redis://localhost:6379/0
2025-06-08 08:25:01 [INFO] pyspider.libs.redis_fallback: 🔍 Redis接続監視を開始しました
2025-06-08 08:25:01 [INFO] pyspider.libs.redis_fallback: ✅ Redis フォールバック機能を初期化しました
2025-06-08 08:25:01 [INFO] memory_optimizer: Memory monitoring started
2025-06-08 08:25:01 [INFO] root: Memory optimizer started for scheduler
2025-06-08 08:25:01 [INFO] root: Created PID file for scheduler: /tmp/pyspider_scheduler.pid
{"timestamp": "2025-06-08T08:25:02.995375", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-08 08:25:03 [INFO] root: Created PID file for fetcher: /tmp/pyspider_fetcher.pid
{"timestamp": "2025-06-08T08:25:05.179739", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-08 08:25:05 [INFO] memory_optimizer: Memory optimizer initialized with max_memory_percent=80.0%, gc_interval=60s, check_interval=30s, auto_optimize=True
2025-06-08 08:25:05 [INFO] memory_optimizer: Memory monitoring started
2025-06-08 08:25:05 [INFO] root: Created PID file for processor: /tmp/pyspider_processor.pid
{"timestamp": "2025-06-08T08:25:07.211779", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-08 08:25:07 [INFO] memory_optimizer: Memory optimizer initialized with max_memory_percent=80.0%, gc_interval=60s, check_interval=30s, auto_optimize=True
2025-06-08 08:25:07 [INFO] memory_optimizer: Memory monitoring started
2025-06-08 08:25:07 [INFO] root: Created PID file for result_worker: /tmp/pyspider_result_worker.pid
2025-06-08 08:25:07 [INFO] result: result_worker starting...
{"timestamp": "2025-06-08T08:25:09.339501", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-08 08:25:09 [INFO] memory_optimizer: Memory optimizer initialized with max_memory_percent=80.0%, gc_interval=60s, check_interval=30s, auto_optimize=True
2025-06-08 08:25:09 [INFO] memory_optimizer: Memory monitoring started
2025-06-08 08:25:10 [INFO] libs.cache: Using Redis cache
2025-06-08 08:25:10 [INFO] alert_manager: Added alert rule: high_cpu_usage
2025-06-08 08:25:10 [INFO] alert_manager: Added alert rule: high_memory_usage
2025-06-08 08:25:10 [INFO] alert_manager: Added alert rule: high_disk_usage
2025-06-08 08:25:10 [INFO] alert_manager: Added alert rule: too_many_tasks
2025-06-08 08:25:10 [INFO] alert_manager: Added alert rule: too_many_errors
2025-06-08 08:25:10 [INFO] pyspider.webui.metrics: Scheduler is not directly accessible. Using default values.
2025-06-08 08:25:10 [INFO] alert_manager: Started alert check thread (interval: 60s)
2025-06-08 08:25:10 [INFO] alert_manager: Alert manager initialized (auto_check: True)
2025-06-08 08:25:10 [INFO] root: Created PID file for webui: /tmp/pyspider_webui.pid
2025-06-08 08:25:10 [INFO] root: Successfully connected to RPC server: http://localhost:23333/
2025-06-08 08:26:01 [INFO] metrics: Gauges: {"memory_usage_rss": 67653632, "memory_usage_vms": 381493248, "memory_usage_percent": 0.20149002833571703, "system_memory_total": 33576665088, "system_memory_available": 23010394112, "system_memory_percent": 31.5}
2025-06-08 08:26:05 [INFO] metrics: Gauges: {"memory_usage_rss": 62062592, "memory_usage_vms": 151937024, "memory_usage_percent": 0.18483846396699063, "system_memory_total": 33576665088, "system_memory_available": 23235551232, "system_memory_percent": 30.8}
2025-06-08 08:26:07 [INFO] metrics: Gauges: {"memory_usage_rss": 45088768, "memory_usage_vms": 127471616, "memory_usage_percent": 0.1342860223962931, "system_memory_total": 33576665088, "system_memory_available": 23235289088, "system_memory_percent": 30.8}
2025-06-08 08:26:09 [INFO] metrics: Gauges: {"memory_usage_rss": 95068160, "memory_usage_vms": 268673024, "memory_usage_percent": 0.27884355922369797, "system_memory_total": 33576665088, "system_memory_available": 23232708608, "system_memory_percent": 30.8}
2025-06-08 08:27:01 [INFO] metrics: Gauges: {"memory_usage_rss": 67653632, "memory_usage_vms": 381493248, "memory_usage_percent": 0.20149002833571703, "system_memory_total": 33576665088, "system_memory_available": 23282999296, "system_memory_percent": 30.7}
2025-06-08 08:27:05 [INFO] metrics: Gauges: {"memory_usage_rss": 62062592, "memory_usage_vms": 151937024, "memory_usage_percent": 0.18483846396699063, "system_memory_total": 33576665088, "system_memory_available": 23224504320, "system_memory_percent": 30.8}
2025-06-08 08:27:07 [INFO] metrics: Gauges: {"memory_usage_rss": 45088768, "memory_usage_vms": 127471616, "memory_usage_percent": 0.1342860223962931, "system_memory_total": 33576665088, "system_memory_available": 23264198656, "system_memory_percent": 30.7}
2025-06-08 08:27:09 [INFO] metrics: Gauges: {"memory_usage_rss": 95068160, "memory_usage_vms": 269856768, "memory_usage_percent": 0.28313758900962593, "system_memory_total": 33576665088, "system_memory_available": 23266512896, "system_memory_percent": 30.7}
2025-06-08 08:28:01 [INFO] metrics: Gauges: {"memory_usage_rss": 67653632, "memory_usage_vms": 381493248, "memory_usage_percent": 0.20149002833571703, "system_memory_total": 33576665088, "system_memory_available": 23284789248, "system_memory_percent": 30.7}
2025-06-08 08:28:05 [INFO] metrics: Gauges: {"memory_usage_rss": 62062592, "memory_usage_vms": 151937024, "memory_usage_percent": 0.18483846396699063, "system_memory_total": 33576665088, "system_memory_available": 23288041472, "system_memory_percent": 30.6}
2025-06-08 08:28:07 [INFO] metrics: Gauges: {"memory_usage_rss": 45088768, "memory_usage_vms": 127471616, "memory_usage_percent": 0.1342860223962931, "system_memory_total": 33576665088, "system_memory_available": 23290183680, "system_memory_percent": 30.6}
2025-06-08 08:28:09 [INFO] metrics: Gauges: {"memory_usage_rss": 95068160, "memory_usage_vms": 269856768, "memory_usage_percent": 0.28313758900962593, "system_memory_total": 33576665088, "system_memory_available": 23295303680, "system_memory_percent": 30.6}
2025-06-08 08:29:01 [INFO] metrics: Gauges: {"memory_usage_rss": 67653632, "memory_usage_vms": 381493248, "memory_usage_percent": 0.20149002833571703, "system_memory_total": 33576665088, "system_memory_available": 23362465792, "system_memory_percent": 30.4}
2025-06-08 08:29:05 [INFO] metrics: Gauges: {"memory_usage_rss": 62062592, "memory_usage_vms": 151937024, "memory_usage_percent": 0.18483846396699063, "system_memory_total": 33576665088, "system_memory_available": 23341121536, "system_memory_percent": 30.5}
2025-06-08 08:29:07 [INFO] metrics: Gauges: {"memory_usage_rss": 45088768, "memory_usage_vms": 127471616, "memory_usage_percent": 0.1342860223962931, "system_memory_total": 33576665088, "system_memory_available": 23348973568, "system_memory_percent": 30.5}
2025-06-08 08:29:09 [INFO] metrics: Gauges: {"memory_usage_rss": 95068160, "memory_usage_vms": 269856768, "memory_usage_percent": 0.28313758900962593, "system_memory_total": 33576665088, "system_memory_available": 23349854208, "system_memory_percent": 30.5}
{"timestamp": "2025-06-08T08:37:16.040524", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-08 08:37:16 [INFO] memory_optimizer: Memory optimizer initialized with max_memory_percent=80.0%, gc_interval=60s, check_interval=30s, auto_optimize=True
2025-06-08 08:37:17 [INFO] pyspider.libs.redis_fallback: ✅ Redis接続成功: redis://localhost:6379/0
2025-06-08 08:37:17 [INFO] pyspider.libs.redis_fallback: 🔍 Redis接続監視を開始しました
2025-06-08 08:37:17 [INFO] pyspider.libs.redis_fallback: ✅ Redis フォールバック機能を初期化しました
2025-06-08 08:37:17 [INFO] memory_optimizer: Memory monitoring started
2025-06-08 08:37:17 [INFO] root: Memory optimizer started for scheduler
2025-06-08 08:37:17 [INFO] root: Created PID file for scheduler: /tmp/pyspider_scheduler.pid
{"timestamp": "2025-06-08T08:37:18.279412", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-08 08:37:18 [INFO] root: Created PID file for fetcher: /tmp/pyspider_fetcher.pid
{"timestamp": "2025-06-08T08:37:20.431292", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-08 08:37:20 [INFO] memory_optimizer: Memory optimizer initialized with max_memory_percent=80.0%, gc_interval=60s, check_interval=30s, auto_optimize=True
2025-06-08 08:37:21 [INFO] memory_optimizer: Memory monitoring started
2025-06-08 08:37:21 [INFO] root: Created PID file for processor: /tmp/pyspider_processor.pid
{"timestamp": "2025-06-08T08:37:22.454469", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-08 08:37:22 [INFO] memory_optimizer: Memory optimizer initialized with max_memory_percent=80.0%, gc_interval=60s, check_interval=30s, auto_optimize=True
2025-06-08 08:37:22 [INFO] memory_optimizer: Memory monitoring started
2025-06-08 08:37:22 [INFO] root: Created PID file for result_worker: /tmp/pyspider_result_worker.pid
2025-06-08 08:37:22 [INFO] result: result_worker starting...
{"timestamp": "2025-06-08T08:37:24.566124", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-08 08:37:24 [INFO] memory_optimizer: Memory optimizer initialized with max_memory_percent=80.0%, gc_interval=60s, check_interval=30s, auto_optimize=True
2025-06-08 08:37:24 [INFO] memory_optimizer: Memory monitoring started
2025-06-08 08:37:25 [INFO] libs.cache: Using Redis cache
2025-06-08 08:37:25 [INFO] alert_manager: Added alert rule: high_cpu_usage
2025-06-08 08:37:25 [INFO] alert_manager: Added alert rule: high_memory_usage
2025-06-08 08:37:25 [INFO] alert_manager: Added alert rule: high_disk_usage
2025-06-08 08:37:25 [INFO] alert_manager: Added alert rule: too_many_tasks
2025-06-08 08:37:25 [INFO] alert_manager: Added alert rule: too_many_errors
2025-06-08 08:37:25 [INFO] pyspider.webui.metrics: Scheduler is not directly accessible. Using default values.
2025-06-08 08:37:25 [INFO] alert_manager: Started alert check thread (interval: 60s)
2025-06-08 08:37:25 [INFO] alert_manager: Alert manager initialized (auto_check: True)
2025-06-08 08:37:26 [INFO] root: Created PID file for webui: /tmp/pyspider_webui.pid
2025-06-08 08:37:26 [INFO] root: Successfully connected to RPC server: http://localhost:23333/
2025-06-08 08:38:16 [INFO] metrics: Gauges: {"memory_usage_rss": 90542080, "memory_usage_vms": 636583936, "memory_usage_percent": 0.26965775118732366, "system_memory_total": 33576665088, "system_memory_available": 23192379392, "system_memory_percent": 30.9}
{"timestamp": "2025-06-08T08:55:30.484457", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-08 08:55:30 [INFO] memory_optimizer: Memory optimizer initialized with max_memory_percent=80.0%, gc_interval=60s, check_interval=30s, auto_optimize=True
2025-06-08 08:55:31 [INFO] pyspider.libs.redis_fallback: ✅ Redis接続成功: redis://localhost:6379/0
2025-06-08 08:55:31 [INFO] pyspider.libs.redis_fallback: 🔍 Redis接続監視を開始しました
2025-06-08 08:55:31 [INFO] pyspider.libs.redis_fallback: ✅ Redis フォールバック機能を初期化しました
2025-06-08 08:55:31 [INFO] memory_optimizer: Memory monitoring started
2025-06-08 08:55:31 [INFO] root: Memory optimizer started for scheduler
2025-06-08 08:55:31 [INFO] root: Created PID file for scheduler: /tmp/pyspider_scheduler.pid
{"timestamp": "2025-06-08T08:55:32.678501", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-08 08:55:32 [INFO] root: Created PID file for fetcher: /tmp/pyspider_fetcher.pid
{"timestamp": "2025-06-08T08:55:34.864681", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-08 08:55:35 [INFO] memory_optimizer: Memory optimizer initialized with max_memory_percent=80.0%, gc_interval=60s, check_interval=30s, auto_optimize=True
2025-06-08 08:55:35 [INFO] memory_optimizer: Memory monitoring started
2025-06-08 08:55:35 [INFO] root: Created PID file for processor: /tmp/pyspider_processor.pid
{"timestamp": "2025-06-08T08:55:36.960341", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-08 08:55:37 [INFO] memory_optimizer: Memory optimizer initialized with max_memory_percent=80.0%, gc_interval=60s, check_interval=30s, auto_optimize=True
2025-06-08 08:55:37 [INFO] memory_optimizer: Memory monitoring started
2025-06-08 08:55:37 [INFO] root: Created PID file for result_worker: /tmp/pyspider_result_worker.pid
2025-06-08 08:55:37 [INFO] result: result_worker starting...
{"timestamp": "2025-06-08T08:55:38.991545", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-08 08:55:39 [INFO] memory_optimizer: Memory optimizer initialized with max_memory_percent=80.0%, gc_interval=60s, check_interval=30s, auto_optimize=True
2025-06-08 08:55:39 [INFO] memory_optimizer: Memory monitoring started
2025-06-08 08:55:39 [INFO] libs.cache: Using Redis cache
2025-06-08 08:55:40 [INFO] alert_manager: Added alert rule: high_cpu_usage
2025-06-08 08:55:40 [INFO] alert_manager: Added alert rule: high_memory_usage
2025-06-08 08:55:40 [INFO] alert_manager: Added alert rule: high_disk_usage
2025-06-08 08:55:40 [INFO] alert_manager: Added alert rule: too_many_tasks
2025-06-08 08:55:40 [INFO] alert_manager: Added alert rule: too_many_errors
2025-06-08 08:55:40 [INFO] pyspider.webui.metrics: Scheduler is not directly accessible. Using default values.
2025-06-08 08:55:40 [INFO] alert_manager: Started alert check thread (interval: 60s)
2025-06-08 08:55:40 [INFO] alert_manager: Alert manager initialized (auto_check: True)
2025-06-08 08:55:40 [INFO] root: Created PID file for webui: /tmp/pyspider_webui.pid
2025-06-08 08:55:40 [INFO] root: Successfully connected to RPC server: http://localhost:23333/
2025-06-08 08:56:30 [INFO] metrics: Gauges: {"memory_usage_rss": 88612864, "memory_usage_vms": 636190720, "memory_usage_percent": 0.26391204655899386, "system_memory_total": 33576665088, "system_memory_available": 22852358144, "system_memory_percent": 31.9}
2025-06-08 08:56:35 [INFO] metrics: Gauges: {"memory_usage_rss": 84033536, "memory_usage_vms": 179490816, "memory_usage_percent": 0.25027362240937034, "system_memory_total": 33576665088, "system_memory_available": 22726656000, "system_memory_percent": 32.3}
2025-06-08 08:56:37 [INFO] metrics: Gauges: {"memory_usage_rss": 64618496, "memory_usage_vms": 154599424, "memory_usage_percent": 0.1924506076784084, "system_memory_total": 33576665088, "system_memory_available": 22709919744, "system_memory_percent": 32.4}
2025-06-08 08:56:39 [INFO] metrics: Gauges: {"memory_usage_rss": 94691328, "memory_usage_vms": 267608064, "memory_usage_percent": 0.2785019886725446, "system_memory_total": 33576665088, "system_memory_available": 22709661696, "system_memory_percent": 32.4}
2025-06-08 08:57:30 [INFO] metrics: Gauges: {"memory_usage_rss": 88612864, "memory_usage_vms": 636190720, "memory_usage_percent": 0.26391204655899386, "system_memory_total": 33576665088, "system_memory_available": 22467158016, "system_memory_percent": 33.1}
2025-06-08 08:57:35 [INFO] metrics: Gauges: {"memory_usage_rss": 84033536, "memory_usage_vms": 179490816, "memory_usage_percent": 0.25027362240937034, "system_memory_total": 33576665088, "system_memory_available": 22778454016, "system_memory_percent": 32.2}
2025-06-08 08:57:37 [INFO] metrics: Gauges: {"memory_usage_rss": 64618496, "memory_usage_vms": 154599424, "memory_usage_percent": 0.1924506076784084, "system_memory_total": 33576665088, "system_memory_available": 22780534784, "system_memory_percent": 32.2}
2025-06-08 08:57:39 [INFO] metrics: Gauges: {"memory_usage_rss": 94691328, "memory_usage_vms": 269766656, "memory_usage_percent": 0.2820152857701221, "system_memory_total": 33576665088, "system_memory_available": 22792646656, "system_memory_percent": 32.1}
2025-06-08 08:58:31 [INFO] metrics: Gauges: {"memory_usage_rss": 88612864, "memory_usage_vms": 636190720, "memory_usage_percent": 0.26391204655899386, "system_memory_total": 33576665088, "system_memory_available": 21973815296, "system_memory_percent": 34.6}
2025-06-08 08:58:35 [INFO] metrics: Gauges: {"memory_usage_rss": 84033536, "memory_usage_vms": 179490816, "memory_usage_percent": 0.25027362240937034, "system_memory_total": 33576665088, "system_memory_available": 22032265216, "system_memory_percent": 34.4}
2025-06-08 08:58:37 [INFO] metrics: Gauges: {"memory_usage_rss": 64618496, "memory_usage_vms": 154599424, "memory_usage_percent": 0.1924506076784084, "system_memory_total": 33576665088, "system_memory_available": 22133796864, "system_memory_percent": 34.1}
2025-06-08 08:58:39 [INFO] metrics: Gauges: {"memory_usage_rss": 94822400, "memory_usage_vms": 269766656, "memory_usage_percent": 0.2824056521142973, "system_memory_total": 33576665088, "system_memory_available": 22130987008, "system_memory_percent": 34.1}
2025-06-08 08:59:31 [INFO] metrics: Gauges: {"memory_usage_rss": 88612864, "memory_usage_vms": 636190720, "memory_usage_percent": 0.26391204655899386, "system_memory_total": 33576665088, "system_memory_available": 21822922752, "system_memory_percent": 35.0}
2025-06-08 08:59:35 [INFO] metrics: Gauges: {"memory_usage_rss": 84033536, "memory_usage_vms": 179490816, "memory_usage_percent": 0.25027362240937034, "system_memory_total": 33576665088, "system_memory_available": 21945712640, "system_memory_percent": 34.6}
2025-06-08 08:59:37 [INFO] metrics: Gauges: {"memory_usage_rss": 64618496, "memory_usage_vms": 154599424, "memory_usage_percent": 0.1924506076784084, "system_memory_total": 33576665088, "system_memory_available": 21957128192, "system_memory_percent": 34.6}
2025-06-08 08:59:39 [INFO] metrics: Gauges: {"memory_usage_rss": 94822400, "memory_usage_vms": 269766656, "memory_usage_percent": 0.2824056521142973, "system_memory_total": 33576665088, "system_memory_available": 21956321280, "system_memory_percent": 34.6}
2025-06-08 09:00:00 [INFO] metrics: Counters: {"fetch_count[project=bunbunbun]": 2, "fetch_count[project=tinti]": 1, "fetch_count[project=iuiu]": 1, "fetch_count[project=douyo]": 1, "fetch_count[project=mysql_detailed_test_2]": 1, "fetch_count[project=jijijiji]": 1, "fetch_count[project=kokoko]": 1, "fetch_count[project=mysql_detailed_test]": 1}
2025-06-08 09:00:31 [INFO] metrics: Gauges: {"memory_usage_rss": 88612864, "memory_usage_vms": 636190720, "memory_usage_percent": 0.26391204655899386, "system_memory_total": 33576665088, "system_memory_available": 21882912768, "system_memory_percent": 34.8}
2025-06-08 09:00:35 [INFO] metrics: Gauges: {"memory_usage_rss": 84033536, "memory_usage_vms": 179490816, "memory_usage_percent": 0.25027362240937034, "system_memory_total": 33576665088, "system_memory_available": 21967032320, "system_memory_percent": 34.6}
2025-06-08 09:00:37 [INFO] metrics: Gauges: {"memory_usage_rss": 64618496, "memory_usage_vms": 154599424, "memory_usage_percent": 0.1924506076784084, "system_memory_total": 33576665088, "system_memory_available": 21966028800, "system_memory_percent": 34.6}
2025-06-08 09:00:39 [INFO] metrics: Gauges: {"memory_usage_rss": 94822400, "memory_usage_vms": 269766656, "memory_usage_percent": 0.2824056521142973, "system_memory_total": 33576665088, "system_memory_available": 21929766912, "system_memory_percent": 34.7}
2025-06-08 09:01:31 [INFO] metrics: Gauges: {"memory_usage_rss": 88612864, "memory_usage_vms": 636190720, "memory_usage_percent": 0.26391204655899386, "system_memory_total": 33576665088, "system_memory_available": 21561393152, "system_memory_percent": 35.8}
2025-06-08 09:01:35 [INFO] metrics: Gauges: {"memory_usage_rss": 84033536, "memory_usage_vms": 179490816, "memory_usage_percent": 0.25027362240937034, "system_memory_total": 33576665088, "system_memory_available": 21668642816, "system_memory_percent": 35.5}
2025-06-08 09:01:37 [INFO] metrics: Gauges: {"memory_usage_rss": 64618496, "memory_usage_vms": 154599424, "memory_usage_percent": 0.1924506076784084, "system_memory_total": 33576665088, "system_memory_available": 21672079360, "system_memory_percent": 35.5}
2025-06-08 09:01:39 [INFO] metrics: Gauges: {"memory_usage_rss": 94822400, "memory_usage_vms": 269766656, "memory_usage_percent": 0.2824056521142973, "system_memory_total": 33576665088, "system_memory_available": 21598539776, "system_memory_percent": 35.7}
{"timestamp": "2025-06-08T09:02:21.703439", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-08 09:02:22 [INFO] memory_optimizer: Memory optimizer initialized with max_memory_percent=80.0%, gc_interval=60s, check_interval=30s, auto_optimize=True
2025-06-08 09:02:22 [INFO] pyspider.libs.redis_fallback: ✅ Redis接続成功: redis://localhost:6379/0
2025-06-08 09:02:22 [INFO] pyspider.libs.redis_fallback: 🔍 Redis接続監視を開始しました
2025-06-08 09:02:22 [INFO] pyspider.libs.redis_fallback: ✅ Redis フォールバック機能を初期化しました
2025-06-08 09:02:22 [INFO] memory_optimizer: Memory monitoring started
2025-06-08 09:02:22 [INFO] root: Memory optimizer started for scheduler
2025-06-08 09:02:22 [INFO] root: Created PID file for scheduler: /tmp/pyspider_scheduler.pid
{"timestamp": "2025-06-08T09:02:23.892987", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-08 09:02:24 [INFO] root: Created PID file for fetcher: /tmp/pyspider_fetcher.pid
{"timestamp": "2025-06-08T09:02:26.117794", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-08 09:02:26 [INFO] memory_optimizer: Memory optimizer initialized with max_memory_percent=80.0%, gc_interval=60s, check_interval=30s, auto_optimize=True
2025-06-08 09:02:26 [INFO] memory_optimizer: Memory monitoring started
2025-06-08 09:02:26 [INFO] root: Created PID file for processor: /tmp/pyspider_processor.pid
{"timestamp": "2025-06-08T09:02:28.362457", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-08 09:02:28 [INFO] memory_optimizer: Memory optimizer initialized with max_memory_percent=80.0%, gc_interval=60s, check_interval=30s, auto_optimize=True
2025-06-08 09:02:28 [INFO] memory_optimizer: Memory monitoring started
2025-06-08 09:02:28 [INFO] root: Created PID file for result_worker: /tmp/pyspider_result_worker.pid
2025-06-08 09:02:28 [INFO] result: result_worker starting...
{"timestamp": "2025-06-08T09:02:30.442907", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-08 09:02:31 [INFO] memory_optimizer: Memory optimizer initialized with max_memory_percent=80.0%, gc_interval=60s, check_interval=30s, auto_optimize=True
2025-06-08 09:02:31 [INFO] memory_optimizer: Memory monitoring started
2025-06-08 09:02:31 [INFO] libs.cache: Using Redis cache
2025-06-08 09:02:31 [INFO] alert_manager: Added alert rule: high_cpu_usage
2025-06-08 09:02:31 [INFO] alert_manager: Added alert rule: high_memory_usage
2025-06-08 09:02:31 [INFO] alert_manager: Added alert rule: high_disk_usage
2025-06-08 09:02:31 [INFO] alert_manager: Added alert rule: too_many_tasks
2025-06-08 09:02:31 [INFO] alert_manager: Added alert rule: too_many_errors
2025-06-08 09:02:31 [INFO] pyspider.webui.metrics: Scheduler is not directly accessible. Using default values.
2025-06-08 09:02:31 [INFO] alert_manager: Started alert check thread (interval: 60s)
2025-06-08 09:02:31 [INFO] alert_manager: Alert manager initialized (auto_check: True)
2025-06-08 09:02:31 [INFO] prometheus_metrics: Prometheus metrics initialized with prefix 'pyspider'
2025-06-08 09:02:31 [INFO] performance_metrics: Started GC stats collection thread (interval: 60s)
2025-06-08 09:02:31 [INFO] performance_metrics: Started process stats collection thread (interval: 10s)
2025-06-08 09:02:31 [INFO] performance_metrics: Performance metrics initialized (tracemalloc: False, auto_collect: True)
2025-06-08 09:02:31 [INFO] root: Created PID file for webui: /tmp/pyspider_webui.pid
2025-06-08 09:02:32 [INFO] root: Successfully connected to RPC server: http://localhost:23333/
2025-06-08 09:02:38 [INFO] result: Processing batch of 1 results for project tinti
2025-06-08 09:02:43 [INFO] result: Processing batch of 1 results for project tinti
2025-06-08 09:03:22 [INFO] metrics: Gauges: {"memory_usage_rss": 89513984, "memory_usage_vms": 636190720, "memory_usage_percent": 0.26659581517519887, "system_memory_total": 33576665088, "system_memory_available": 21950832640, "system_memory_percent": 34.6}
2025-06-08 09:03:26 [INFO] metrics: Gauges: {"memory_usage_rss": 97304576, "memory_usage_vms": 193343488, "memory_usage_percent": 0.2897982147571165, "system_memory_total": 33576665088, "system_memory_available": 21952475136, "system_memory_percent": 34.6}
2025-06-08 09:03:28 [INFO] metrics: Gauges: {"memory_usage_rss": 65253376, "memory_usage_vms": 154791936, "memory_usage_percent": 0.19434144465800735, "system_memory_total": 33576665088, "system_memory_available": 21953781760, "system_memory_percent": 34.6}
2025-06-08 09:03:31 [INFO] metrics: Gauges: {"memory_usage_rss": 100159488, "memory_usage_vms": 425947136, "memory_usage_percent": 0.2971297826586583, "system_memory_total": 33576665088, "system_memory_available": 21966401536, "system_memory_percent": 34.6}
2025-06-08 09:04:22 [INFO] metrics: Gauges: {"memory_usage_rss": 89513984, "memory_usage_vms": 636190720, "memory_usage_percent": 0.26659581517519887, "system_memory_total": 33576665088, "system_memory_available": 21706911744, "system_memory_percent": 35.4}
2025-06-08 09:04:26 [INFO] metrics: Gauges: {"memory_usage_rss": 97304576, "memory_usage_vms": 193343488, "memory_usage_percent": 0.2897982147571165, "system_memory_total": 33576665088, "system_memory_available": 21875232768, "system_memory_percent": 34.8}
2025-06-08 09:04:28 [INFO] metrics: Gauges: {"memory_usage_rss": 65253376, "memory_usage_vms": 154791936, "memory_usage_percent": 0.19434144465800735, "system_memory_total": 33576665088, "system_memory_available": 21870739456, "system_memory_percent": 34.9}
2025-06-08 09:04:31 [INFO] metrics: Gauges: {"memory_usage_rss": 100421632, "memory_usage_vms": 426262528, "memory_usage_percent": 0.29869124803535935, "system_memory_total": 33576665088, "system_memory_available": 21867909120, "system_memory_percent": 34.9}
2025-06-08 09:05:22 [INFO] metrics: Gauges: {"memory_usage_rss": 89513984, "memory_usage_vms": 636190720, "memory_usage_percent": 0.26659581517519887, "system_memory_total": 33576665088, "system_memory_available": 21980581888, "system_memory_percent": 34.5}
2025-06-08 09:05:26 [INFO] metrics: Gauges: {"memory_usage_rss": 97304576, "memory_usage_vms": 193343488, "memory_usage_percent": 0.2897982147571165, "system_memory_total": 33576665088, "system_memory_available": 21964759040, "system_memory_percent": 34.6}
2025-06-08 09:05:28 [INFO] metrics: Gauges: {"memory_usage_rss": 65253376, "memory_usage_vms": 154791936, "memory_usage_percent": 0.19434144465800735, "system_memory_total": 33576665088, "system_memory_available": 21962723328, "system_memory_percent": 34.6}
2025-06-08 09:05:31 [INFO] metrics: Gauges: {"memory_usage_rss": 100421632, "memory_usage_vms": 427311104, "memory_usage_percent": 0.2990816143795347, "system_memory_total": 33576665088, "system_memory_available": 21980446720, "system_memory_percent": 34.5}
2025-06-08 09:06:22 [INFO] metrics: Gauges: {"memory_usage_rss": 89513984, "memory_usage_vms": 636190720, "memory_usage_percent": 0.26659581517519887, "system_memory_total": 33576665088, "system_memory_available": 21389537280, "system_memory_percent": 36.3}
2025-06-08 09:06:26 [INFO] metrics: Gauges: {"memory_usage_rss": 97304576, "memory_usage_vms": 193343488, "memory_usage_percent": 0.2897982147571165, "system_memory_total": 33576665088, "system_memory_available": 21645131776, "system_memory_percent": 35.5}
2025-06-08 09:06:28 [INFO] metrics: Gauges: {"memory_usage_rss": 65253376, "memory_usage_vms": 154791936, "memory_usage_percent": 0.19434144465800735, "system_memory_total": 33576665088, "system_memory_available": 21641805824, "system_memory_percent": 35.5}
2025-06-08 09:06:31 [INFO] metrics: Gauges: {"memory_usage_rss": 100421632, "memory_usage_vms": 427311104, "memory_usage_percent": 0.2990816143795347, "system_memory_total": 33576665088, "system_memory_available": 21649997824, "system_memory_percent": 35.5}
2025-06-08 09:07:22 [INFO] metrics: Gauges: {"memory_usage_rss": 89513984, "memory_usage_vms": 636190720, "memory_usage_percent": 0.26659581517519887, "system_memory_total": 33576665088, "system_memory_available": 21285744640, "system_memory_percent": 36.6}
2025-06-08 09:07:26 [INFO] metrics: Gauges: {"memory_usage_rss": 97304576, "memory_usage_vms": 193343488, "memory_usage_percent": 0.2897982147571165, "system_memory_total": 33576665088, "system_memory_available": 21689643008, "system_memory_percent": 35.4}
2025-06-08 09:07:28 [INFO] metrics: Gauges: {"memory_usage_rss": 65253376, "memory_usage_vms": 154791936, "memory_usage_percent": 0.19434144465800735, "system_memory_total": 33576665088, "system_memory_available": 21672419328, "system_memory_percent": 35.5}
2025-06-08 09:07:31 [INFO] metrics: Gauges: {"memory_usage_rss": 101470208, "memory_usage_vms": 427311104, "memory_usage_percent": 0.2990816143795347, "system_memory_total": 33576665088, "system_memory_available": 21655867392, "system_memory_percent": 35.5}
{"timestamp": "2025-06-08T09:08:50.357024", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-08 09:08:50 [INFO] memory_optimizer: Memory optimizer initialized with max_memory_percent=80.0%, gc_interval=60s, check_interval=30s, auto_optimize=True
2025-06-08 09:08:51 [INFO] pyspider.libs.redis_fallback: ✅ Redis接続成功: redis://localhost:6379/0
2025-06-08 09:08:51 [INFO] pyspider.libs.redis_fallback: 🔍 Redis接続監視を開始しました
2025-06-08 09:08:51 [INFO] pyspider.libs.redis_fallback: ✅ Redis フォールバック機能を初期化しました
2025-06-08 09:08:51 [INFO] memory_optimizer: Memory monitoring started
2025-06-08 09:08:51 [INFO] root: Memory optimizer started for scheduler
2025-06-08 09:08:51 [INFO] root: Created PID file for scheduler: /tmp/pyspider_scheduler.pid
{"timestamp": "2025-06-08T09:08:52.586115", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-08 09:08:52 [INFO] root: Created PID file for fetcher: /tmp/pyspider_fetcher.pid
{"timestamp": "2025-06-08T09:08:54.780118", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-08 09:08:55 [INFO] memory_optimizer: Memory optimizer initialized with max_memory_percent=80.0%, gc_interval=60s, check_interval=30s, auto_optimize=True
2025-06-08 09:08:55 [INFO] memory_optimizer: Memory monitoring started
2025-06-08 09:08:55 [INFO] root: Created PID file for processor: /tmp/pyspider_processor.pid
{"timestamp": "2025-06-08T09:08:56.815419", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-08 09:08:56 [INFO] memory_optimizer: Memory optimizer initialized with max_memory_percent=80.0%, gc_interval=60s, check_interval=30s, auto_optimize=True
2025-06-08 09:08:57 [INFO] memory_optimizer: Memory monitoring started
2025-06-08 09:08:57 [INFO] root: Created PID file for result_worker: /tmp/pyspider_result_worker.pid
2025-06-08 09:08:57 [INFO] result: result_worker starting...
{"timestamp": "2025-06-08T09:08:58.810597", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-08 09:08:59 [INFO] memory_optimizer: Memory optimizer initialized with max_memory_percent=80.0%, gc_interval=60s, check_interval=30s, auto_optimize=True
2025-06-08 09:08:59 [INFO] memory_optimizer: Memory monitoring started
2025-06-08 09:08:59 [INFO] libs.cache: Using Redis cache
2025-06-08 09:08:59 [INFO] alert_manager: Added alert rule: high_cpu_usage
2025-06-08 09:08:59 [INFO] alert_manager: Added alert rule: high_memory_usage
2025-06-08 09:08:59 [INFO] alert_manager: Added alert rule: high_disk_usage
2025-06-08 09:08:59 [INFO] alert_manager: Added alert rule: too_many_tasks
2025-06-08 09:08:59 [INFO] alert_manager: Added alert rule: too_many_errors
2025-06-08 09:08:59 [INFO] pyspider.webui.metrics: Scheduler is not directly accessible. Using default values.
2025-06-08 09:08:59 [INFO] alert_manager: Started alert check thread (interval: 60s)
2025-06-08 09:08:59 [INFO] alert_manager: Alert manager initialized (auto_check: True)
2025-06-08 09:08:59 [INFO] prometheus_metrics: Prometheus metrics initialized with prefix 'pyspider'
2025-06-08 09:08:59 [INFO] performance_metrics: Started GC stats collection thread (interval: 60s)
2025-06-08 09:08:59 [INFO] performance_metrics: Started process stats collection thread (interval: 10s)
2025-06-08 09:08:59 [INFO] performance_metrics: Performance metrics initialized (tracemalloc: False, auto_collect: True)
2025-06-08 09:09:00 [INFO] prometheus_endpoint: Prometheus metrics initialized
2025-06-08 09:09:00 [INFO] root: Created PID file for webui: /tmp/pyspider_webui.pid
2025-06-08 09:09:00 [INFO] root: Successfully connected to RPC server: http://localhost:23333/
2025-06-08 09:09:50 [INFO] metrics: Gauges: {"memory_usage_rss": 88563712, "memory_usage_vms": 636198912, "memory_usage_percent": 0.2637656591799281, "system_memory_total": 33576665088, "system_memory_available": 21737766912, "system_memory_percent": 35.3}
2025-06-08 09:09:55 [INFO] metrics: Gauges: {"memory_usage_rss": 83972096, "memory_usage_vms": 179486720, "memory_usage_percent": 0.2500906381855382, "system_memory_total": 33576665088, "system_memory_available": 21779091456, "system_memory_percent": 35.1}
2025-06-08 09:09:57 [INFO] metrics: Gauges: {"memory_usage_rss": 64991232, "memory_usage_vms": 154656768, "memory_usage_percent": 0.19356071196965682, "system_memory_total": 33576665088, "system_memory_available": 21772374016, "system_memory_percent": 35.2}
2025-06-08 09:09:59 [INFO] metrics: Gauges: {"memory_usage_rss": 100257792, "memory_usage_vms": 427261952, "memory_usage_percent": 0.29859365644931557, "system_memory_total": 33576665088, "system_memory_available": 21771628544, "system_memory_percent": 35.2}
2025-06-08 09:10:50 [INFO] metrics: Gauges: {"memory_usage_rss": 88694784, "memory_usage_vms": 636198912, "memory_usage_percent": 0.26415602552410344, "system_memory_total": 33576665088, "system_memory_available": 21002588160, "system_memory_percent": 37.4}
2025-06-08 09:10:55 [INFO] metrics: Gauges: {"memory_usage_rss": 83972096, "memory_usage_vms": 179486720, "memory_usage_percent": 0.2500906381855382, "system_memory_total": 33576665088, "system_memory_available": 21598724096, "system_memory_percent": 35.7}
2025-06-08 09:10:57 [INFO] metrics: Gauges: {"memory_usage_rss": 64991232, "memory_usage_vms": 154656768, "memory_usage_percent": 0.19356071196965682, "system_memory_total": 33576665088, "system_memory_available": 21599354880, "system_memory_percent": 35.7}
2025-06-08 09:10:59 [INFO] metrics: Gauges: {"memory_usage_rss": 100519936, "memory_usage_vms": 427261952, "memory_usage_percent": 0.2993743891376661, "system_memory_total": 33576665088, "system_memory_available": 21588803584, "system_memory_percent": 35.7}
2025-06-08 09:11:50 [INFO] metrics: Gauges: {"memory_usage_rss": 88694784, "memory_usage_vms": 636198912, "memory_usage_percent": 0.26415602552410344, "system_memory_total": 33576665088, "system_memory_available": 21320290304, "system_memory_percent": 36.5}
2025-06-08 09:11:55 [INFO] metrics: Gauges: {"memory_usage_rss": 83972096, "memory_usage_vms": 179486720, "memory_usage_percent": 0.2500906381855382, "system_memory_total": 33576665088, "system_memory_available": 22058717184, "system_memory_percent": 34.3}
2025-06-08 09:11:57 [INFO] metrics: Gauges: {"memory_usage_rss": 64991232, "memory_usage_vms": 154656768, "memory_usage_percent": 0.19356071196965682, "system_memory_total": 33576665088, "system_memory_available": 22004576256, "system_memory_percent": 34.5}
2025-06-08 09:11:59 [INFO] metrics: Gauges: {"memory_usage_rss": 100519936, "memory_usage_vms": 427261952, "memory_usage_percent": 0.2993743891376661, "system_memory_total": 33576665088, "system_memory_available": 21876080640, "system_memory_percent": 34.8}
2025-06-08 09:12:51 [INFO] metrics: Gauges: {"memory_usage_rss": 88694784, "memory_usage_vms": 636198912, "memory_usage_percent": 0.26415602552410344, "system_memory_total": 33576665088, "system_memory_available": 21931683840, "system_memory_percent": 34.7}
2025-06-08 09:12:55 [INFO] metrics: Gauges: {"memory_usage_rss": 83972096, "memory_usage_vms": 179486720, "memory_usage_percent": 0.2500906381855382, "system_memory_total": 33576665088, "system_memory_available": 21925613568, "system_memory_percent": 34.7}
2025-06-08 09:12:57 [INFO] metrics: Gauges: {"memory_usage_rss": 64991232, "memory_usage_vms": 154656768, "memory_usage_percent": 0.19356071196965682, "system_memory_total": 33576665088, "system_memory_available": 21750640640, "system_memory_percent": 35.2}
2025-06-08 09:12:59 [INFO] metrics: Gauges: {"memory_usage_rss": 100519936, "memory_usage_vms": 427261952, "memory_usage_percent": 0.2993743891376661, "system_memory_total": 33576665088, "system_memory_available": 20945080320, "system_memory_percent": 37.6}
2025-06-08 09:13:51 [INFO] metrics: Gauges: {"memory_usage_rss": 88694784, "memory_usage_vms": 636198912, "memory_usage_percent": 0.26415602552410344, "system_memory_total": 33576665088, "system_memory_available": 21521911808, "system_memory_percent": 35.9}
2025-06-08 09:13:55 [INFO] metrics: Gauges: {"memory_usage_rss": 83972096, "memory_usage_vms": 179486720, "memory_usage_percent": 0.2500906381855382, "system_memory_total": 33576665088, "system_memory_available": 21482532864, "system_memory_percent": 36.0}
2025-06-08 09:13:57 [INFO] metrics: Gauges: {"memory_usage_rss": 64991232, "memory_usage_vms": 154656768, "memory_usage_percent": 0.19356071196965682, "system_memory_total": 33576665088, "system_memory_available": 21478146048, "system_memory_percent": 36.0}
2025-06-08 09:13:59 [INFO] metrics: Gauges: {"memory_usage_rss": 100519936, "memory_usage_vms": 427405312, "memory_usage_percent": 0.2993743891376661, "system_memory_total": 33576665088, "system_memory_available": 21531676672, "system_memory_percent": 35.9}
2025-06-08 09:14:51 [INFO] metrics: Gauges: {"memory_usage_rss": 88694784, "memory_usage_vms": 636198912, "memory_usage_percent": 0.26415602552410344, "system_memory_total": 33576665088, "system_memory_available": 21159862272, "system_memory_percent": 37.0}
2025-06-08 09:14:55 [INFO] metrics: Gauges: {"memory_usage_rss": 83972096, "memory_usage_vms": 179486720, "memory_usage_percent": 0.2500906381855382, "system_memory_total": 33576665088, "system_memory_available": 21286383616, "system_memory_percent": 36.6}
2025-06-08 09:14:57 [INFO] metrics: Gauges: {"memory_usage_rss": 64991232, "memory_usage_vms": 154656768, "memory_usage_percent": 0.19356071196965682, "system_memory_total": 33576665088, "system_memory_available": 21189369856, "system_memory_percent": 36.9}
2025-06-08 09:14:59 [INFO] metrics: Gauges: {"memory_usage_rss": 100519936, "memory_usage_vms": 427405312, "memory_usage_percent": 0.2993743891376661, "system_memory_total": 33576665088, "system_memory_available": 21092589568, "system_memory_percent": 37.2}
2025-06-08 09:15:51 [INFO] metrics: Gauges: {"memory_usage_rss": 88694784, "memory_usage_vms": 636198912, "memory_usage_percent": 0.26415602552410344, "system_memory_total": 33576665088, "system_memory_available": 21151137792, "system_memory_percent": 37.0}
2025-06-08 09:15:55 [INFO] metrics: Gauges: {"memory_usage_rss": 83972096, "memory_usage_vms": 179486720, "memory_usage_percent": 0.2500906381855382, "system_memory_total": 33576665088, "system_memory_available": 20867776512, "system_memory_percent": 37.9}
2025-06-08 09:15:57 [INFO] metrics: Gauges: {"memory_usage_rss": 64991232, "memory_usage_vms": 154656768, "memory_usage_percent": 0.19356071196965682, "system_memory_total": 33576665088, "system_memory_available": 21262548992, "system_memory_percent": 36.7}
2025-06-08 09:15:59 [INFO] metrics: Gauges: {"memory_usage_rss": 100519936, "memory_usage_vms": 427405312, "memory_usage_percent": 0.2993743891376661, "system_memory_total": 33576665088, "system_memory_available": 21180870656, "system_memory_percent": 36.9}
2025-06-08 09:16:51 [INFO] metrics: Gauges: {"memory_usage_rss": 88694784, "memory_usage_vms": 636198912, "memory_usage_percent": 0.26415602552410344, "system_memory_total": 33576665088, "system_memory_available": 21344690176, "system_memory_percent": 36.4}
2025-06-08 09:16:55 [INFO] metrics: Gauges: {"memory_usage_rss": 83972096, "memory_usage_vms": 179486720, "memory_usage_percent": 0.2500906381855382, "system_memory_total": 33576665088, "system_memory_available": 20417896448, "system_memory_percent": 39.2}
2025-06-08 09:16:57 [INFO] metrics: Gauges: {"memory_usage_rss": 64991232, "memory_usage_vms": 154656768, "memory_usage_percent": 0.19356071196965682, "system_memory_total": 33576665088, "system_memory_available": 21235924992, "system_memory_percent": 36.8}
2025-06-08 09:16:59 [INFO] metrics: Gauges: {"memory_usage_rss": 100519936, "memory_usage_vms": 427405312, "memory_usage_percent": 0.2993743891376661, "system_memory_total": 33576665088, "system_memory_available": 21169270784, "system_memory_percent": 37.0}
2025-06-08 09:17:51 [INFO] metrics: Gauges: {"memory_usage_rss": 88694784, "memory_usage_vms": 636198912, "memory_usage_percent": 0.26415602552410344, "system_memory_total": 33576665088, "system_memory_available": 23216775168, "system_memory_percent": 30.9}
2025-06-08 09:17:55 [INFO] metrics: Gauges: {"memory_usage_rss": 83972096, "memory_usage_vms": 179486720, "memory_usage_percent": 0.2500906381855382, "system_memory_total": 33576665088, "system_memory_available": 21203447808, "system_memory_percent": 36.9}
2025-06-08 09:17:57 [INFO] metrics: Gauges: {"memory_usage_rss": 64991232, "memory_usage_vms": 154656768, "memory_usage_percent": 0.19356071196965682, "system_memory_total": 33576665088, "system_memory_available": 20761264128, "system_memory_percent": 38.2}
2025-06-08 09:17:59 [INFO] metrics: Gauges: {"memory_usage_rss": 100519936, "memory_usage_vms": 427405312, "memory_usage_percent": 0.2993743891376661, "system_memory_total": 33576665088, "system_memory_available": 21347131392, "system_memory_percent": 36.4}
2025-06-08 09:18:51 [INFO] metrics: Gauges: {"memory_usage_rss": 88694784, "memory_usage_vms": 636198912, "memory_usage_percent": 0.26415602552410344, "system_memory_total": 33576665088, "system_memory_available": 23367331840, "system_memory_percent": 30.4}
2025-06-08 09:18:55 [INFO] metrics: Gauges: {"memory_usage_rss": 83972096, "memory_usage_vms": 179486720, "memory_usage_percent": 0.2500906381855382, "system_memory_total": 33576665088, "system_memory_available": 23102218240, "system_memory_percent": 31.2}
2025-06-08 09:18:57 [INFO] metrics: Gauges: {"memory_usage_rss": 64991232, "memory_usage_vms": 154656768, "memory_usage_percent": 0.19356071196965682, "system_memory_total": 33576665088, "system_memory_available": 23311646720, "system_memory_percent": 30.6}
2025-06-08 09:18:59 [INFO] metrics: Gauges: {"memory_usage_rss": 100519936, "memory_usage_vms": 427405312, "memory_usage_percent": 0.2993743891376661, "system_memory_total": 33576665088, "system_memory_available": 23231528960, "system_memory_percent": 30.8}
2025-06-08 09:19:51 [INFO] metrics: Gauges: {"memory_usage_rss": 88694784, "memory_usage_vms": 636198912, "memory_usage_percent": 0.26415602552410344, "system_memory_total": 33576665088, "system_memory_available": 23184867328, "system_memory_percent": 30.9}
2025-06-08 09:19:55 [INFO] metrics: Gauges: {"memory_usage_rss": 83972096, "memory_usage_vms": 179486720, "memory_usage_percent": 0.2500906381855382, "system_memory_total": 33576665088, "system_memory_available": 23209373696, "system_memory_percent": 30.9}
2025-06-08 09:19:57 [INFO] metrics: Gauges: {"memory_usage_rss": 64991232, "memory_usage_vms": 154656768, "memory_usage_percent": 0.19356071196965682, "system_memory_total": 33576665088, "system_memory_available": 23210504192, "system_memory_percent": 30.9}
2025-06-08 09:19:59 [INFO] metrics: Gauges: {"memory_usage_rss": 100519936, "memory_usage_vms": 427405312, "memory_usage_percent": 0.2993743891376661, "system_memory_total": 33576665088, "system_memory_available": 23214735360, "system_memory_percent": 30.9}
2025-06-08 09:20:51 [INFO] metrics: Gauges: {"memory_usage_rss": 88694784, "memory_usage_vms": 636198912, "memory_usage_percent": 0.26415602552410344, "system_memory_total": 33576665088, "system_memory_available": 23270375424, "system_memory_percent": 30.7}
2025-06-08 09:20:55 [INFO] metrics: Gauges: {"memory_usage_rss": 83972096, "memory_usage_vms": 179486720, "memory_usage_percent": 0.2500906381855382, "system_memory_total": 33576665088, "system_memory_available": 23157825536, "system_memory_percent": 31.0}
2025-06-08 09:20:58 [INFO] metrics: Gauges: {"memory_usage_rss": 64991232, "memory_usage_vms": 154656768, "memory_usage_percent": 0.19356071196965682, "system_memory_total": 33576665088, "system_memory_available": 23143940096, "system_memory_percent": 31.1}
2025-06-08 09:20:59 [INFO] metrics: Gauges: {"memory_usage_rss": 100519936, "memory_usage_vms": 427405312, "memory_usage_percent": 0.2993743891376661, "system_memory_total": 33576665088, "system_memory_available": 23128150016, "system_memory_percent": 31.1}
2025-06-08 09:21:51 [INFO] metrics: Gauges: {"memory_usage_rss": 88694784, "memory_usage_vms": 636198912, "memory_usage_percent": 0.26415602552410344, "system_memory_total": 33576665088, "system_memory_available": 23239028736, "system_memory_percent": 30.8}
2025-06-08 09:21:55 [INFO] metrics: Gauges: {"memory_usage_rss": 83972096, "memory_usage_vms": 179486720, "memory_usage_percent": 0.2500906381855382, "system_memory_total": 33576665088, "system_memory_available": 23334207488, "system_memory_percent": 30.5}
2025-06-08 09:21:58 [INFO] metrics: Gauges: {"memory_usage_rss": 64991232, "memory_usage_vms": 154656768, "memory_usage_percent": 0.19356071196965682, "system_memory_total": 33576665088, "system_memory_available": 23325786112, "system_memory_percent": 30.5}
2025-06-08 09:21:59 [INFO] metrics: Gauges: {"memory_usage_rss": 100519936, "memory_usage_vms": 427405312, "memory_usage_percent": 0.2993743891376661, "system_memory_total": 33576665088, "system_memory_available": 23312076800, "system_memory_percent": 30.6}
2025-06-08 09:22:51 [INFO] metrics: Gauges: {"memory_usage_rss": 88694784, "memory_usage_vms": 636198912, "memory_usage_percent": 0.26415602552410344, "system_memory_total": 33576665088, "system_memory_available": 23243366400, "system_memory_percent": 30.8}
2025-06-08 09:22:55 [INFO] metrics: Gauges: {"memory_usage_rss": 83972096, "memory_usage_vms": 179486720, "memory_usage_percent": 0.2500906381855382, "system_memory_total": 33576665088, "system_memory_available": 23181508608, "system_memory_percent": 31.0}
2025-06-08 09:22:58 [INFO] metrics: Gauges: {"memory_usage_rss": 64991232, "memory_usage_vms": 154656768, "memory_usage_percent": 0.19356071196965682, "system_memory_total": 33576665088, "system_memory_available": 22713917440, "system_memory_percent": 32.4}
2025-06-08 09:22:59 [INFO] metrics: Gauges: {"memory_usage_rss": 100519936, "memory_usage_vms": 427405312, "memory_usage_percent": 0.2993743891376661, "system_memory_total": 33576665088, "system_memory_available": 22669479936, "system_memory_percent": 32.5}
2025-06-08 09:23:51 [INFO] metrics: Gauges: {"memory_usage_rss": 88694784, "memory_usage_vms": 636198912, "memory_usage_percent": 0.26415602552410344, "system_memory_total": 33576665088, "system_memory_available": 22907871232, "system_memory_percent": 31.8}
2025-06-08 09:23:55 [INFO] metrics: Gauges: {"memory_usage_rss": 83972096, "memory_usage_vms": 179486720, "memory_usage_percent": 0.2500906381855382, "system_memory_total": 33576665088, "system_memory_available": 23236395008, "system_memory_percent": 30.8}
2025-06-08 09:23:58 [INFO] metrics: Gauges: {"memory_usage_rss": 64991232, "memory_usage_vms": 154656768, "memory_usage_percent": 0.19356071196965682, "system_memory_total": 33576665088, "system_memory_available": 23233970176, "system_memory_percent": 30.8}
2025-06-08 09:23:59 [INFO] metrics: Gauges: {"memory_usage_rss": 100519936, "memory_usage_vms": 427405312, "memory_usage_percent": 0.2993743891376661, "system_memory_total": 33576665088, "system_memory_available": 23238578176, "system_memory_percent": 30.8}
2025-06-08 09:24:51 [INFO] metrics: Gauges: {"memory_usage_rss": 88694784, "memory_usage_vms": 636198912, "memory_usage_percent": 0.26415602552410344, "system_memory_total": 33576665088, "system_memory_available": 22765203456, "system_memory_percent": 32.2}
2025-06-08 09:24:55 [INFO] metrics: Gauges: {"memory_usage_rss": 83972096, "memory_usage_vms": 179486720, "memory_usage_percent": 0.2500906381855382, "system_memory_total": 33576665088, "system_memory_available": 22536945664, "system_memory_percent": 32.9}
2025-06-08 09:24:58 [INFO] metrics: Gauges: {"memory_usage_rss": 64991232, "memory_usage_vms": 154656768, "memory_usage_percent": 0.19356071196965682, "system_memory_total": 33576665088, "system_memory_available": 22580449280, "system_memory_percent": 32.7}
2025-06-08 09:24:59 [INFO] metrics: Gauges: {"memory_usage_rss": 100651008, "memory_usage_vms": 427405312, "memory_usage_percent": 0.2997647554818414, "system_memory_total": 33576665088, "system_memory_available": 22761099264, "system_memory_percent": 32.2}
2025-06-08 09:25:43 [ERROR] pyspider.webui.api_v2.file_output: Error getting file output stats: cannot import name 'g' from 'pyspider.run' (/home/<USER>/workplace/python/pyspiderNX2/pyspider/run.py)
2025-06-08 09:25:43 [ERROR] pyspider.webui.api_v2.file_output: Error getting file output stats: cannot import name 'g' from 'pyspider.run' (/home/<USER>/workplace/python/pyspiderNX2/pyspider/run.py)
2025-06-08 09:25:43 [ERROR] pyspider.webui.api_v2.file_output: Error getting file output stats: cannot import name 'g' from 'pyspider.run' (/home/<USER>/workplace/python/pyspiderNX2/pyspider/run.py)
2025-06-08 09:25:51 [INFO] metrics: Gauges: {"memory_usage_rss": 88694784, "memory_usage_vms": 636198912, "memory_usage_percent": 0.26415602552410344, "system_memory_total": 33576665088, "system_memory_available": 23121432576, "system_memory_percent": 31.1}
2025-06-08 09:25:55 [INFO] metrics: Gauges: {"memory_usage_rss": 83972096, "memory_usage_vms": 179486720, "memory_usage_percent": 0.2500906381855382, "system_memory_total": 33576665088, "system_memory_available": 22567317504, "system_memory_percent": 32.8}
2025-06-08 09:25:58 [INFO] metrics: Gauges: {"memory_usage_rss": 64991232, "memory_usage_vms": 154656768, "memory_usage_percent": 0.19356071196965682, "system_memory_total": 33576665088, "system_memory_available": 22469476352, "system_memory_percent": 33.1}
2025-06-08 09:25:59 [INFO] metrics: Gauges: {"memory_usage_rss": 100782080, "memory_usage_vms": 427405312, "memory_usage_percent": 0.2997647554818414, "system_memory_total": 33576665088, "system_memory_available": 22468718592, "system_memory_percent": 33.1}
2025-06-08 09:26:51 [INFO] metrics: Gauges: {"memory_usage_rss": 88694784, "memory_usage_vms": 636198912, "memory_usage_percent": 0.26415602552410344, "system_memory_total": 33576665088, "system_memory_available": 23346053120, "system_memory_percent": 30.5}
2025-06-08 09:26:55 [INFO] metrics: Gauges: {"memory_usage_rss": 83972096, "memory_usage_vms": 179486720, "memory_usage_percent": 0.2500906381855382, "system_memory_total": 33576665088, "system_memory_available": 23096377344, "system_memory_percent": 31.2}
2025-06-08 09:26:58 [INFO] metrics: Gauges: {"memory_usage_rss": 64991232, "memory_usage_vms": 154656768, "memory_usage_percent": 0.19356071196965682, "system_memory_total": 33576665088, "system_memory_available": 23216349184, "system_memory_percent": 30.9}
2025-06-08 09:26:59 [INFO] metrics: Gauges: {"memory_usage_rss": 100782080, "memory_usage_vms": 427556864, "memory_usage_percent": 0.3001551218260166, "system_memory_total": 33576665088, "system_memory_available": 23193505792, "system_memory_percent": 30.9}
2025-06-08 09:27:51 [INFO] metrics: Gauges: {"memory_usage_rss": 88694784, "memory_usage_vms": 636198912, "memory_usage_percent": 0.26415602552410344, "system_memory_total": 33576665088, "system_memory_available": 23093579776, "system_memory_percent": 31.2}
2025-06-08 09:27:55 [INFO] metrics: Gauges: {"memory_usage_rss": 83972096, "memory_usage_vms": 179486720, "memory_usage_percent": 0.2500906381855382, "system_memory_total": 33576665088, "system_memory_available": 23314587648, "system_memory_percent": 30.6}
2025-06-08 09:27:58 [INFO] metrics: Gauges: {"memory_usage_rss": 64991232, "memory_usage_vms": 154656768, "memory_usage_percent": 0.19356071196965682, "system_memory_total": 33576665088, "system_memory_available": 23322972160, "system_memory_percent": 30.5}
2025-06-08 09:27:59 [INFO] metrics: Gauges: {"memory_usage_rss": 100782080, "memory_usage_vms": 427556864, "memory_usage_percent": 0.3001551218260166, "system_memory_total": 33576665088, "system_memory_available": 23327571968, "system_memory_percent": 30.5}
2025-06-08 09:28:51 [INFO] metrics: Gauges: {"memory_usage_rss": 88694784, "memory_usage_vms": 636198912, "memory_usage_percent": 0.26415602552410344, "system_memory_total": 33576665088, "system_memory_available": 22611505152, "system_memory_percent": 32.7}
2025-06-08 09:28:55 [INFO] metrics: Gauges: {"memory_usage_rss": 83972096, "memory_usage_vms": 179486720, "memory_usage_percent": 0.2500906381855382, "system_memory_total": 33576665088, "system_memory_available": 23079112704, "system_memory_percent": 31.3}
2025-06-08 09:28:58 [INFO] metrics: Gauges: {"memory_usage_rss": 64991232, "memory_usage_vms": 154656768, "memory_usage_percent": 0.19356071196965682, "system_memory_total": 33576665088, "system_memory_available": 23060537344, "system_memory_percent": 31.3}
2025-06-08 09:28:59 [INFO] metrics: Gauges: {"memory_usage_rss": 100782080, "memory_usage_vms": 427556864, "memory_usage_percent": 0.3001551218260166, "system_memory_total": 33576665088, "system_memory_available": 23031902208, "system_memory_percent": 31.4}
2025-06-08 09:29:45 [ERROR] pyspider.webui.api_v2.file_output: Error getting file output stats: cannot import name 'g' from 'pyspider.run' (/home/<USER>/workplace/python/pyspiderNX2/pyspider/run.py)
2025-06-08 09:29:45 [ERROR] pyspider.webui.api_v2.file_output: Error getting file output stats: cannot import name 'g' from 'pyspider.run' (/home/<USER>/workplace/python/pyspiderNX2/pyspider/run.py)
2025-06-08 09:29:45 [ERROR] pyspider.webui.api_v2.file_output: Error getting file output stats: cannot import name 'g' from 'pyspider.run' (/home/<USER>/workplace/python/pyspiderNX2/pyspider/run.py)
2025-06-08 09:29:51 [INFO] metrics: Gauges: {"memory_usage_rss": 88694784, "memory_usage_vms": 636198912, "memory_usage_percent": 0.26415602552410344, "system_memory_total": 33576665088, "system_memory_available": 22605250560, "system_memory_percent": 32.7}
2025-06-08 09:29:55 [INFO] metrics: Gauges: {"memory_usage_rss": 83972096, "memory_usage_vms": 179486720, "memory_usage_percent": 0.2500906381855382, "system_memory_total": 33576665088, "system_memory_available": 22819237888, "system_memory_percent": 32.0}
2025-06-08 09:29:58 [INFO] metrics: Gauges: {"memory_usage_rss": 64991232, "memory_usage_vms": 154656768, "memory_usage_percent": 0.19356071196965682, "system_memory_total": 33576665088, "system_memory_available": 22817619968, "system_memory_percent": 32.0}
2025-06-08 09:29:59 [INFO] metrics: Gauges: {"memory_usage_rss": 100782080, "memory_usage_vms": 427556864, "memory_usage_percent": 0.3001551218260166, "system_memory_total": 33576665088, "system_memory_available": 22817366016, "system_memory_percent": 32.0}
2025-06-08 09:30:18 [ERROR] pyspider.webui.api_v2.file_output: Error getting file output stats: cannot import name 'g' from 'pyspider.run' (/home/<USER>/workplace/python/pyspiderNX2/pyspider/run.py)
2025-06-08 09:30:18 [ERROR] pyspider.webui.api_v2.file_output: Error getting file output stats: cannot import name 'g' from 'pyspider.run' (/home/<USER>/workplace/python/pyspiderNX2/pyspider/run.py)
2025-06-08 09:30:18 [ERROR] pyspider.webui.api_v2.file_output: Error getting file output stats: cannot import name 'g' from 'pyspider.run' (/home/<USER>/workplace/python/pyspiderNX2/pyspider/run.py)
2025-06-08 09:30:51 [INFO] metrics: Gauges: {"memory_usage_rss": 88694784, "memory_usage_vms": 636198912, "memory_usage_percent": 0.26415602552410344, "system_memory_total": 33576665088, "system_memory_available": 22154424320, "system_memory_percent": 34.0}
2025-06-08 09:30:55 [INFO] metrics: Gauges: {"memory_usage_rss": 83972096, "memory_usage_vms": 179486720, "memory_usage_percent": 0.2500906381855382, "system_memory_total": 33576665088, "system_memory_available": 21736599552, "system_memory_percent": 35.3}
2025-06-08 09:30:58 [INFO] metrics: Gauges: {"memory_usage_rss": 64991232, "memory_usage_vms": 154656768, "memory_usage_percent": 0.19356071196965682, "system_memory_total": 33576665088, "system_memory_available": 21559513088, "system_memory_percent": 35.8}
2025-06-08 09:30:59 [INFO] metrics: Gauges: {"memory_usage_rss": 100782080, "memory_usage_vms": 427556864, "memory_usage_percent": 0.3001551218260166, "system_memory_total": 33576665088, "system_memory_available": 22011068416, "system_memory_percent": 34.4}
2025-06-08 09:31:50 [ERROR] pyspider.webui.api_v2.file_output: Error getting file output stats: cannot import name 'g' from 'pyspider.run' (/home/<USER>/workplace/python/pyspiderNX2/pyspider/run.py)
2025-06-08 09:31:50 [ERROR] pyspider.webui.api_v2.file_output: Error getting file output stats: cannot import name 'g' from 'pyspider.run' (/home/<USER>/workplace/python/pyspiderNX2/pyspider/run.py)
2025-06-08 09:31:50 [ERROR] pyspider.webui.api_v2.file_output: Error getting file output stats: cannot import name 'g' from 'pyspider.run' (/home/<USER>/workplace/python/pyspiderNX2/pyspider/run.py)
2025-06-08 09:31:51 [INFO] metrics: Gauges: {"memory_usage_rss": 88694784, "memory_usage_vms": 636198912, "memory_usage_percent": 0.26415602552410344, "system_memory_total": 33576665088, "system_memory_available": 22133383168, "system_memory_percent": 34.1}
2025-06-08 09:31:55 [INFO] metrics: Gauges: {"memory_usage_rss": 83972096, "memory_usage_vms": 179486720, "memory_usage_percent": 0.2500906381855382, "system_memory_total": 33576665088, "system_memory_available": 21912170496, "system_memory_percent": 34.7}
2025-06-08 09:31:58 [INFO] metrics: Gauges: {"memory_usage_rss": 64991232, "memory_usage_vms": 154656768, "memory_usage_percent": 0.19356071196965682, "system_memory_total": 33576665088, "system_memory_available": 21916995584, "system_memory_percent": 34.7}
2025-06-08 09:31:59 [INFO] metrics: Gauges: {"memory_usage_rss": 100782080, "memory_usage_vms": 427556864, "memory_usage_percent": 0.3001551218260166, "system_memory_total": 33576665088, "system_memory_available": 22196867072, "system_memory_percent": 33.9}
2025-06-08 09:32:51 [INFO] metrics: Gauges: {"memory_usage_rss": 88694784, "memory_usage_vms": 636198912, "memory_usage_percent": 0.26415602552410344, "system_memory_total": 33576665088, "system_memory_available": 22119043072, "system_memory_percent": 34.1}
2025-06-08 09:32:55 [INFO] metrics: Gauges: {"memory_usage_rss": 83972096, "memory_usage_vms": 179486720, "memory_usage_percent": 0.2500906381855382, "system_memory_total": 33576665088, "system_memory_available": 22134620160, "system_memory_percent": 34.1}
2025-06-08 09:32:58 [INFO] metrics: Gauges: {"memory_usage_rss": 64991232, "memory_usage_vms": 154656768, "memory_usage_percent": 0.19356071196965682, "system_memory_total": 33576665088, "system_memory_available": 22140960768, "system_memory_percent": 34.1}
2025-06-08 09:32:59 [INFO] metrics: Gauges: {"memory_usage_rss": 100913152, "memory_usage_vms": 427556864, "memory_usage_percent": 0.30054548817019194, "system_memory_total": 33576665088, "system_memory_available": 22135496704, "system_memory_percent": 34.1}
2025-06-08 09:33:51 [INFO] metrics: Gauges: {"memory_usage_rss": 88694784, "memory_usage_vms": 636198912, "memory_usage_percent": 0.26415602552410344, "system_memory_total": 33576665088, "system_memory_available": 22324244480, "system_memory_percent": 33.5}
2025-06-08 09:33:55 [INFO] metrics: Gauges: {"memory_usage_rss": 83972096, "memory_usage_vms": 179486720, "memory_usage_percent": 0.2500906381855382, "system_memory_total": 33576665088, "system_memory_available": 22319308800, "system_memory_percent": 33.5}
2025-06-08 09:33:58 [INFO] metrics: Gauges: {"memory_usage_rss": 64991232, "memory_usage_vms": 154656768, "memory_usage_percent": 0.19356071196965682, "system_memory_total": 33576665088, "system_memory_available": 22320721920, "system_memory_percent": 33.5}
2025-06-08 09:33:59 [INFO] metrics: Gauges: {"memory_usage_rss": 100913152, "memory_usage_vms": 427556864, "memory_usage_percent": 0.30054548817019194, "system_memory_total": 33576665088, "system_memory_available": 22320721920, "system_memory_percent": 33.5}
2025-06-08 09:34:51 [INFO] metrics: Gauges: {"memory_usage_rss": 88694784, "memory_usage_vms": 636198912, "memory_usage_percent": 0.26415602552410344, "system_memory_total": 33576665088, "system_memory_available": 22110298112, "system_memory_percent": 34.1}
2025-06-08 09:34:55 [INFO] metrics: Gauges: {"memory_usage_rss": 83972096, "memory_usage_vms": 179486720, "memory_usage_percent": 0.2500906381855382, "system_memory_total": 33576665088, "system_memory_available": 22328594432, "system_memory_percent": 33.5}
2025-06-08 09:34:58 [INFO] metrics: Gauges: {"memory_usage_rss": 64991232, "memory_usage_vms": 154656768, "memory_usage_percent": 0.19356071196965682, "system_memory_total": 33576665088, "system_memory_available": 22336000000, "system_memory_percent": 33.5}
2025-06-08 09:34:59 [INFO] metrics: Gauges: {"memory_usage_rss": 100913152, "memory_usage_vms": 427556864, "memory_usage_percent": 0.30054548817019194, "system_memory_total": 33576665088, "system_memory_available": 22335746048, "system_memory_percent": 33.5}
{"timestamp": "2025-06-08T09:41:03.795888", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-08 09:41:04 [INFO] memory_optimizer: Memory optimizer initialized with max_memory_percent=80.0%, gc_interval=60s, check_interval=30s, auto_optimize=True
2025-06-08 09:41:04 [INFO] pyspider.libs.redis_fallback: ✅ Redis接続成功: redis://localhost:6379/0
2025-06-08 09:41:04 [INFO] pyspider.libs.redis_fallback: 🔍 Redis接続監視を開始しました
2025-06-08 09:41:04 [INFO] pyspider.libs.redis_fallback: ✅ Redis フォールバック機能を初期化しました
2025-06-08 09:41:04 [INFO] memory_optimizer: Memory monitoring started
2025-06-08 09:41:04 [INFO] root: Memory optimizer started for scheduler
2025-06-08 09:41:04 [INFO] root: Created PID file for scheduler: /tmp/pyspider_scheduler.pid
{"timestamp": "2025-06-08T09:41:05.886990", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-08 09:41:06 [INFO] root: Created PID file for fetcher: /tmp/pyspider_fetcher.pid
{"timestamp": "2025-06-08T09:41:07.977794", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-08 09:41:08 [INFO] memory_optimizer: Memory optimizer initialized with max_memory_percent=80.0%, gc_interval=60s, check_interval=30s, auto_optimize=True
2025-06-08 09:41:08 [INFO] memory_optimizer: Memory monitoring started
2025-06-08 09:41:08 [INFO] root: Created PID file for processor: /tmp/pyspider_processor.pid
{"timestamp": "2025-06-08T09:41:10.103881", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-08 09:41:10 [INFO] memory_optimizer: Memory optimizer initialized with max_memory_percent=80.0%, gc_interval=60s, check_interval=30s, auto_optimize=True
2025-06-08 09:41:10 [INFO] memory_optimizer: Memory monitoring started
2025-06-08 09:41:10 [INFO] root: Created PID file for result_worker: /tmp/pyspider_result_worker.pid
2025-06-08 09:41:10 [INFO] result: result_worker starting...
{"timestamp": "2025-06-08T09:41:12.089009", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203, "extra": {"taskName": null}}
2025-06-08 09:41:12 [INFO] memory_optimizer: Memory optimizer initialized with max_memory_percent=80.0%, gc_interval=60s, check_interval=30s, auto_optimize=True
2025-06-08 09:41:12 [INFO] memory_optimizer: Memory monitoring started
2025-06-08 09:41:12 [INFO] libs.cache: Using Redis cache
2025-06-08 09:41:12 [INFO] alert_manager: Added alert rule: high_cpu_usage
2025-06-08 09:41:12 [INFO] alert_manager: Added alert rule: high_memory_usage
2025-06-08 09:41:12 [INFO] alert_manager: Added alert rule: high_disk_usage
2025-06-08 09:41:12 [INFO] alert_manager: Added alert rule: too_many_tasks
2025-06-08 09:41:12 [INFO] alert_manager: Added alert rule: too_many_errors
2025-06-08 09:41:12 [INFO] pyspider.webui.metrics: Scheduler is not directly accessible. Using default values.
2025-06-08 09:41:12 [INFO] alert_manager: Started alert check thread (interval: 60s)
2025-06-08 09:41:12 [INFO] alert_manager: Alert manager initialized (auto_check: True)
2025-06-08 09:41:12 [INFO] prometheus_metrics: Prometheus metrics initialized with prefix 'pyspider'
2025-06-08 09:41:12 [INFO] performance_metrics: Started GC stats collection thread (interval: 60s)
2025-06-08 09:41:12 [INFO] performance_metrics: Started process stats collection thread (interval: 10s)
2025-06-08 09:41:12 [INFO] performance_metrics: Performance metrics initialized (tracemalloc: False, auto_collect: True)
2025-06-08 09:41:12 [INFO] prometheus_endpoint: Prometheus metrics initialized
2025-06-08 09:41:12 [INFO] root: Created PID file for webui: /tmp/pyspider_webui.pid
2025-06-08 09:41:13 [INFO] root: Successfully connected to RPC server: http://localhost:23333/
2025-06-08 09:41:57 [ERROR] pyspider.webui.api_v2.file_output: Error getting file output stats: cannot import name 'g' from 'pyspider.run' (/home/<USER>/workplace/python/pyspiderNX2/pyspider/run.py)
2025-06-08 09:41:57 [ERROR] pyspider.webui.api_v2.file_output: Error getting file output stats: cannot import name 'g' from 'pyspider.run' (/home/<USER>/workplace/python/pyspiderNX2/pyspider/run.py)
2025-06-08 09:42:04 [INFO] metrics: Gauges: {"memory_usage_rss": 88715264, "memory_usage_vms": 636190720, "memory_usage_percent": 0.2642170202653808, "system_memory_total": 33576665088, "system_memory_available": 27589476352, "system_memory_percent": 17.8}
2025-06-08 09:42:08 [INFO] metrics: Gauges: {"memory_usage_rss": 83943424, "memory_usage_vms": 179486720, "memory_usage_percent": 0.25000524554774983, "system_memory_total": 33576665088, "system_memory_available": 28219846656, "system_memory_percent": 16.0}
2025-06-08 09:42:10 [INFO] metrics: Gauges: {"memory_usage_rss": 64536576, "memory_usage_vms": 154652672, "memory_usage_percent": 0.19220662871329885, "system_memory_total": 33576665088, "system_memory_available": 28209782784, "system_memory_percent": 16.0}
2025-06-08 09:42:12 [INFO] metrics: Gauges: {"memory_usage_rss": 100200448, "memory_usage_vms": 425930752, "memory_usage_percent": 0.29725177214121307, "system_memory_total": 33576665088, "system_memory_available": 28193300480, "system_memory_percent": 16.0}
2025-06-08 09:43:04 [INFO] metrics: Gauges: {"memory_usage_rss": 88715264, "memory_usage_vms": 636190720, "memory_usage_percent": 0.2642170202653808, "system_memory_total": 33576665088, "system_memory_available": 27513737216, "system_memory_percent": 18.1}
2025-06-08 09:43:08 [INFO] metrics: Gauges: {"memory_usage_rss": 83943424, "memory_usage_vms": 179486720, "memory_usage_percent": 0.25000524554774983, "system_memory_total": 33576665088, "system_memory_available": 27283173376, "system_memory_percent": 18.7}
2025-06-08 09:43:10 [INFO] metrics: Gauges: {"memory_usage_rss": 64536576, "memory_usage_vms": 154652672, "memory_usage_percent": 0.19220662871329885, "system_memory_total": 33576665088, "system_memory_available": 27621003264, "system_memory_percent": 17.7}
2025-06-08 09:43:12 [INFO] metrics: Gauges: {"memory_usage_rss": 100593664, "memory_usage_vms": 427286528, "memory_usage_percent": 0.2995939702062647, "system_memory_total": 33576665088, "system_memory_available": 27556077568, "system_memory_percent": 17.9}
2025-06-08 09:44:04 [INFO] metrics: Gauges: {"memory_usage_rss": 88715264, "memory_usage_vms": 636190720, "memory_usage_percent": 0.2642170202653808, "system_memory_total": 33576665088, "system_memory_available": 27186425856, "system_memory_percent": 19.0}
2025-06-08 09:44:08 [INFO] metrics: Gauges: {"memory_usage_rss": 83943424, "memory_usage_vms": 179486720, "memory_usage_percent": 0.25000524554774983, "system_memory_total": 33576665088, "system_memory_available": 27217784832, "system_memory_percent": 18.9}
2025-06-08 09:44:10 [INFO] metrics: Gauges: {"memory_usage_rss": 64536576, "memory_usage_vms": 154652672, "memory_usage_percent": 0.19220662871329885, "system_memory_total": 33576665088, "system_memory_available": 27217784832, "system_memory_percent": 18.9}
2025-06-08 09:44:12 [INFO] metrics: Gauges: {"memory_usage_rss": 100593664, "memory_usage_vms": 427286528, "memory_usage_percent": 0.2995939702062647, "system_memory_total": 33576665088, "system_memory_available": 27216007168, "system_memory_percent": 18.9}
2025-06-08 09:45:04 [INFO] metrics: Gauges: {"memory_usage_rss": 88715264, "memory_usage_vms": 636190720, "memory_usage_percent": 0.2642170202653808, "system_memory_total": 33576665088, "system_memory_available": 26927431680, "system_memory_percent": 19.8}
2025-06-08 09:45:08 [INFO] metrics: Gauges: {"memory_usage_rss": 83943424, "memory_usage_vms": 179486720, "memory_usage_percent": 0.25000524554774983, "system_memory_total": 33576665088, "system_memory_available": 27094319104, "system_memory_percent": 19.3}
2025-06-08 09:45:10 [INFO] metrics: Gauges: {"memory_usage_rss": 64536576, "memory_usage_vms": 154652672, "memory_usage_percent": 0.19220662871329885, "system_memory_total": 33576665088, "system_memory_available": 27093807104, "system_memory_percent": 19.3}
2025-06-08 09:45:12 [INFO] metrics: Gauges: {"memory_usage_rss": 100593664, "memory_usage_vms": 427286528, "memory_usage_percent": 0.2995939702062647, "system_memory_total": 33576665088, "system_memory_available": 27091742720, "system_memory_percent": 19.3}
2025-06-08 09:46:04 [INFO] metrics: Gauges: {"memory_usage_rss": 88715264, "memory_usage_vms": 636190720, "memory_usage_percent": 0.2642170202653808, "system_memory_total": 33576665088, "system_memory_available": 26998677504, "system_memory_percent": 19.6}
2025-06-08 09:46:08 [INFO] metrics: Gauges: {"memory_usage_rss": 83943424, "memory_usage_vms": 179486720, "memory_usage_percent": 0.25000524554774983, "system_memory_total": 33576665088, "system_memory_available": 26844278784, "system_memory_percent": 20.1}
2025-06-08 09:46:10 [INFO] metrics: Gauges: {"memory_usage_rss": 64536576, "memory_usage_vms": 154652672, "memory_usage_percent": 0.19220662871329885, "system_memory_total": 33576665088, "system_memory_available": 26843873280, "system_memory_percent": 20.1}
2025-06-08 09:46:12 [INFO] metrics: Gauges: {"memory_usage_rss": 100724736, "memory_usage_vms": 427286528, "memory_usage_percent": 0.29998433655044, "system_memory_total": 33576665088, "system_memory_available": 26822479872, "system_memory_percent": 20.1}
2025-06-08 09:47:04 [INFO] metrics: Gauges: {"memory_usage_rss": 88715264, "memory_usage_vms": 636190720, "memory_usage_percent": 0.2642170202653808, "system_memory_total": 33576665088, "system_memory_available": 27149279232, "system_memory_percent": 19.1}
2025-06-08 09:47:08 [INFO] metrics: Gauges: {"memory_usage_rss": 83943424, "memory_usage_vms": 179486720, "memory_usage_percent": 0.25000524554774983, "system_memory_total": 33576665088, "system_memory_available": 26940198912, "system_memory_percent": 19.8}
2025-06-08 09:47:10 [INFO] metrics: Gauges: {"memory_usage_rss": 64536576, "memory_usage_vms": 154652672, "memory_usage_percent": 0.19220662871329885, "system_memory_total": 33576665088, "system_memory_available": 26939940864, "system_memory_percent": 19.8}
2025-06-08 09:47:12 [INFO] metrics: Gauges: {"memory_usage_rss": 100724736, "memory_usage_vms": 427286528, "memory_usage_percent": 0.29998433655044, "system_memory_total": 33576665088, "system_memory_available": 26934910976, "system_memory_percent": 19.8}
2025-06-08 09:48:04 [INFO] metrics: Gauges: {"memory_usage_rss": 88715264, "memory_usage_vms": 636190720, "memory_usage_percent": 0.2642170202653808, "system_memory_total": 33576665088, "system_memory_available": 26836697088, "system_memory_percent": 20.1}
2025-06-08 09:48:08 [INFO] metrics: Gauges: {"memory_usage_rss": 83943424, "memory_usage_vms": 179486720, "memory_usage_percent": 0.25000524554774983, "system_memory_total": 33576665088, "system_memory_available": 26380505088, "system_memory_percent": 21.4}
2025-06-08 09:48:10 [INFO] metrics: Gauges: {"memory_usage_rss": 64536576, "memory_usage_vms": 154652672, "memory_usage_percent": 0.19220662871329885, "system_memory_total": 33576665088, "system_memory_available": 26557186048, "system_memory_percent": 20.9}
2025-06-08 09:48:12 [INFO] metrics: Gauges: {"memory_usage_rss": 100724736, "memory_usage_vms": 427286528, "memory_usage_percent": 0.29998433655044, "system_memory_total": 33576665088, "system_memory_available": 26820808704, "system_memory_percent": 20.1}
2025-06-08 09:49:04 [INFO] metrics: Gauges: {"memory_usage_rss": 88715264, "memory_usage_vms": 636190720, "memory_usage_percent": 0.2642170202653808, "system_memory_total": 33576665088, "system_memory_available": 26923331584, "system_memory_percent": 19.8}
2025-06-08 09:49:08 [INFO] metrics: Gauges: {"memory_usage_rss": 83943424, "memory_usage_vms": 179486720, "memory_usage_percent": 0.25000524554774983, "system_memory_total": 33576665088, "system_memory_available": 26956066816, "system_memory_percent": 19.7}
2025-06-08 09:49:10 [INFO] metrics: Gauges: {"memory_usage_rss": 64536576, "memory_usage_vms": 154652672, "memory_usage_percent": 0.19220662871329885, "system_memory_total": 33576665088, "system_memory_available": 26957750272, "system_memory_percent": 19.7}
2025-06-08 09:49:12 [INFO] metrics: Gauges: {"memory_usage_rss": 100855808, "memory_usage_vms": 427425792, "memory_usage_percent": 0.30037470289461526, "system_memory_total": 33576665088, "system_memory_available": 26937053184, "system_memory_percent": 19.8}
2025-06-08 09:50:04 [INFO] metrics: Gauges: {"memory_usage_rss": 88715264, "memory_usage_vms": 636190720, "memory_usage_percent": 0.2642170202653808, "system_memory_total": 33576665088, "system_memory_available": 26876325888, "system_memory_percent": 20.0}
2025-06-08 09:50:08 [INFO] metrics: Gauges: {"memory_usage_rss": 83943424, "memory_usage_vms": 179486720, "memory_usage_percent": 0.25000524554774983, "system_memory_total": 33576665088, "system_memory_available": 27041333248, "system_memory_percent": 19.5}
2025-06-08 09:50:10 [INFO] metrics: Gauges: {"memory_usage_rss": 64536576, "memory_usage_vms": 154652672, "memory_usage_percent": 0.19220662871329885, "system_memory_total": 33576665088, "system_memory_available": 27043360768, "system_memory_percent": 19.5}
2025-06-08 09:50:12 [INFO] metrics: Gauges: {"memory_usage_rss": 100986880, "memory_usage_vms": 427425792, "memory_usage_percent": 0.30037470289461526, "system_memory_total": 33576665088, "system_memory_available": 27046666240, "system_memory_percent": 19.4}
2025-06-08 09:51:04 [INFO] metrics: Gauges: {"memory_usage_rss": 88715264, "memory_usage_vms": 636190720, "memory_usage_percent": 0.2642170202653808, "system_memory_total": 33576665088, "system_memory_available": 26778230784, "system_memory_percent": 20.2}
2025-06-08 09:51:08 [INFO] metrics: Gauges: {"memory_usage_rss": 83943424, "memory_usage_vms": 179486720, "memory_usage_percent": 0.25000524554774983, "system_memory_total": 33576665088, "system_memory_available": 26886950912, "system_memory_percent": 19.9}
2025-06-08 09:51:10 [INFO] metrics: Gauges: {"memory_usage_rss": 64536576, "memory_usage_vms": 154652672, "memory_usage_percent": 0.19220662871329885, "system_memory_total": 33576665088, "system_memory_available": 26886692864, "system_memory_percent": 19.9}
2025-06-08 09:51:12 [INFO] metrics: Gauges: {"memory_usage_rss": 101117952, "memory_usage_vms": 427425792, "memory_usage_percent": 0.3011554355829658, "system_memory_total": 33576665088, "system_memory_available": 26884886528, "system_memory_percent": 19.9}
2025-06-08 09:52:04 [INFO] metrics: Gauges: {"memory_usage_rss": 88715264, "memory_usage_vms": 636190720, "memory_usage_percent": 0.2642170202653808, "system_memory_total": 33576665088, "system_memory_available": 28067651584, "system_memory_percent": 16.4}
2025-06-08 09:52:08 [INFO] metrics: Gauges: {"memory_usage_rss": 83943424, "memory_usage_vms": 179486720, "memory_usage_percent": 0.25000524554774983, "system_memory_total": 33576665088, "system_memory_available": 28224065536, "system_memory_percent": 15.9}
2025-06-08 09:52:10 [INFO] metrics: Gauges: {"memory_usage_rss": 64536576, "memory_usage_vms": 154652672, "memory_usage_percent": 0.19220662871329885, "system_memory_total": 33576665088, "system_memory_available": 28142010368, "system_memory_percent": 16.2}
2025-06-08 09:52:12 [INFO] metrics: Gauges: {"memory_usage_rss": 101117952, "memory_usage_vms": 427425792, "memory_usage_percent": 0.3011554355829658, "system_memory_total": 33576665088, "system_memory_available": 28060319744, "system_memory_percent": 16.4}
2025-06-08 09:53:04 [INFO] metrics: Gauges: {"memory_usage_rss": 88715264, "memory_usage_vms": 636190720, "memory_usage_percent": 0.2642170202653808, "system_memory_total": 33576665088, "system_memory_available": 27264684032, "system_memory_percent": 18.8}
2025-06-08 09:53:08 [INFO] metrics: Gauges: {"memory_usage_rss": 83943424, "memory_usage_vms": 179486720, "memory_usage_percent": 0.25000524554774983, "system_memory_total": 33576665088, "system_memory_available": 27245256704, "system_memory_percent": 18.9}
2025-06-08 09:53:10 [INFO] metrics: Gauges: {"memory_usage_rss": 64536576, "memory_usage_vms": 154652672, "memory_usage_percent": 0.19220662871329885, "system_memory_total": 33576665088, "system_memory_available": 26886856704, "system_memory_percent": 19.9}
2025-06-08 09:53:12 [INFO] metrics: Gauges: {"memory_usage_rss": 101117952, "memory_usage_vms": 427425792, "memory_usage_percent": 0.3011554355829658, "system_memory_total": 33576665088, "system_memory_available": 26754416640, "system_memory_percent": 20.3}
2025-06-08 09:54:04 [INFO] metrics: Gauges: {"memory_usage_rss": 88715264, "memory_usage_vms": 636190720, "memory_usage_percent": 0.2642170202653808, "system_memory_total": 33576665088, "system_memory_available": 27388051456, "system_memory_percent": 18.4}
2025-06-08 09:54:08 [INFO] metrics: Gauges: {"memory_usage_rss": 83943424, "memory_usage_vms": 179486720, "memory_usage_percent": 0.25000524554774983, "system_memory_total": 33576665088, "system_memory_available": 27198955520, "system_memory_percent": 19.0}
2025-06-08 09:54:10 [INFO] metrics: Gauges: {"memory_usage_rss": 64536576, "memory_usage_vms": 154652672, "memory_usage_percent": 0.19220662871329885, "system_memory_total": 33576665088, "system_memory_available": 27197186048, "system_memory_percent": 19.0}
2025-06-08 09:54:12 [INFO] metrics: Gauges: {"memory_usage_rss": 102035456, "memory_usage_vms": 427425792, "memory_usage_percent": 0.3011554355829658, "system_memory_total": 33576665088, "system_memory_available": 27178639360, "system_memory_percent": 19.1}
2025-06-08 09:55:04 [INFO] metrics: Gauges: {"memory_usage_rss": 88715264, "memory_usage_vms": 636190720, "memory_usage_percent": 0.2642170202653808, "system_memory_total": 33576665088, "system_memory_available": 27138420736, "system_memory_percent": 19.2}
2025-06-08 09:55:08 [INFO] metrics: Gauges: {"memory_usage_rss": 83943424, "memory_usage_vms": 179486720, "memory_usage_percent": 0.25000524554774983, "system_memory_total": 33576665088, "system_memory_available": 27126231040, "system_memory_percent": 19.2}
2025-06-08 09:55:10 [INFO] metrics: Gauges: {"memory_usage_rss": 64536576, "memory_usage_vms": 154652672, "memory_usage_percent": 0.19220662871329885, "system_memory_total": 33576665088, "system_memory_available": 26961850368, "system_memory_percent": 19.7}
2025-06-08 09:55:12 [INFO] metrics: Gauges: {"memory_usage_rss": 102035456, "memory_usage_vms": 427999232, "memory_usage_percent": 0.3038879999921927, "system_memory_total": 33576665088, "system_memory_available": 27165097984, "system_memory_percent": 19.1}
2025-06-08 09:56:04 [INFO] metrics: Gauges: {"memory_usage_rss": 88715264, "memory_usage_vms": 636190720, "memory_usage_percent": 0.2642170202653808, "system_memory_total": 33576665088, "system_memory_available": 27012681728, "system_memory_percent": 19.5}
2025-06-08 09:56:08 [INFO] metrics: Gauges: {"memory_usage_rss": 83943424, "memory_usage_vms": 179486720, "memory_usage_percent": 0.25000524554774983, "system_memory_total": 33576665088, "system_memory_available": 26839244800, "system_memory_percent": 20.1}
2025-06-08 09:56:10 [INFO] metrics: Gauges: {"memory_usage_rss": 64536576, "memory_usage_vms": 154652672, "memory_usage_percent": 0.19220662871329885, "system_memory_total": 33576665088, "system_memory_available": 26475630592, "system_memory_percent": 21.1}
2025-06-08 09:56:12 [INFO] metrics: Gauges: {"memory_usage_rss": 102035456, "memory_usage_vms": 427999232, "memory_usage_percent": 0.3038879999921927, "system_memory_total": 33576665088, "system_memory_available": 26596528128, "system_memory_percent": 20.8}
2025-06-08 09:57:04 [INFO] metrics: Gauges: {"memory_usage_rss": 88715264, "memory_usage_vms": 636190720, "memory_usage_percent": 0.2642170202653808, "system_memory_total": 33576665088, "system_memory_available": 26955694080, "system_memory_percent": 19.7}
2025-06-08 09:57:08 [INFO] metrics: Gauges: {"memory_usage_rss": 83943424, "memory_usage_vms": 179486720, "memory_usage_percent": 0.25000524554774983, "system_memory_total": 33576665088, "system_memory_available": 27149840384, "system_memory_percent": 19.1}
2025-06-08 09:57:10 [INFO] metrics: Gauges: {"memory_usage_rss": 64536576, "memory_usage_vms": 154652672, "memory_usage_percent": 0.19220662871329885, "system_memory_total": 33576665088, "system_memory_available": 26675834880, "system_memory_percent": 20.6}
2025-06-08 09:57:12 [INFO] metrics: Gauges: {"memory_usage_rss": 102035456, "memory_usage_vms": 427999232, "memory_usage_percent": 0.3038879999921927, "system_memory_total": 33576665088, "system_memory_available": 26527633408, "system_memory_percent": 21.0}
2025-06-08 09:58:04 [INFO] metrics: Gauges: {"memory_usage_rss": 88715264, "memory_usage_vms": 636190720, "memory_usage_percent": 0.2642170202653808, "system_memory_total": 33576665088, "system_memory_available": 27125391360, "system_memory_percent": 19.2}
2025-06-08 09:58:08 [INFO] metrics: Gauges: {"memory_usage_rss": 83943424, "memory_usage_vms": 179486720, "memory_usage_percent": 0.25000524554774983, "system_memory_total": 33576665088, "system_memory_available": 26987479040, "system_memory_percent": 19.6}
2025-06-08 09:58:10 [INFO] metrics: Gauges: {"memory_usage_rss": 64536576, "memory_usage_vms": 154652672, "memory_usage_percent": 0.19220662871329885, "system_memory_total": 33576665088, "system_memory_available": 26987220992, "system_memory_percent": 19.6}
2025-06-08 09:58:12 [INFO] metrics: Gauges: {"memory_usage_rss": 102035456, "memory_usage_vms": 427999232, "memory_usage_percent": 0.3038879999921927, "system_memory_total": 33576665088, "system_memory_available": 26895572992, "system_memory_percent": 19.9}
2025-06-08 09:59:04 [INFO] metrics: Gauges: {"memory_usage_rss": 88715264, "memory_usage_vms": 636190720, "memory_usage_percent": 0.2642170202653808, "system_memory_total": 33576665088, "system_memory_available": 26490728448, "system_memory_percent": 21.1}
2025-06-08 09:59:08 [INFO] metrics: Gauges: {"memory_usage_rss": 83943424, "memory_usage_vms": 179486720, "memory_usage_percent": 0.25000524554774983, "system_memory_total": 33576665088, "system_memory_available": 26812764160, "system_memory_percent": 20.1}
2025-06-08 09:59:10 [INFO] metrics: Gauges: {"memory_usage_rss": 64536576, "memory_usage_vms": 154652672, "memory_usage_percent": 0.19220662871329885, "system_memory_total": 33576665088, "system_memory_available": 26820890624, "system_memory_percent": 20.1}
2025-06-08 09:59:12 [INFO] metrics: Gauges: {"memory_usage_rss": 102035456, "memory_usage_vms": 427999232, "memory_usage_percent": 0.3038879999921927, "system_memory_total": 33576665088, "system_memory_available": 26820415488, "system_memory_percent": 20.1}
2025-06-08 10:00:04 [INFO] metrics: Gauges: {"memory_usage_rss": 88715264, "memory_usage_vms": 636190720, "memory_usage_percent": 0.2642170202653808, "system_memory_total": 33576665088, "system_memory_available": 26991554560, "system_memory_percent": 19.6}
2025-06-08 10:00:08 [INFO] metrics: Gauges: {"memory_usage_rss": 83943424, "memory_usage_vms": 179486720, "memory_usage_percent": 0.25000524554774983, "system_memory_total": 33576665088, "system_memory_available": 27007373312, "system_memory_percent": 19.6}
2025-06-08 10:00:10 [INFO] metrics: Gauges: {"memory_usage_rss": 64536576, "memory_usage_vms": 154652672, "memory_usage_percent": 0.19220662871329885, "system_memory_total": 33576665088, "system_memory_available": 27284271104, "system_memory_percent": 18.7}
2025-06-08 10:00:12 [INFO] metrics: Gauges: {"memory_usage_rss": 102035456, "memory_usage_vms": 427999232, "memory_usage_percent": 0.3038879999921927, "system_memory_total": 33576665088, "system_memory_available": 27282706432, "system_memory_percent": 18.7}
2025-06-08 10:01:04 [INFO] metrics: Gauges: {"memory_usage_rss": 88715264, "memory_usage_vms": 636190720, "memory_usage_percent": 0.2642170202653808, "system_memory_total": 33576665088, "system_memory_available": 27216326656, "system_memory_percent": 18.9}
2025-06-08 10:01:08 [INFO] metrics: Gauges: {"memory_usage_rss": 83943424, "memory_usage_vms": 179486720, "memory_usage_percent": 0.25000524554774983, "system_memory_total": 33576665088, "system_memory_available": 27252486144, "system_memory_percent": 18.8}
2025-06-08 10:01:10 [INFO] metrics: Gauges: {"memory_usage_rss": 64536576, "memory_usage_vms": 154652672, "memory_usage_percent": 0.19220662871329885, "system_memory_total": 33576665088, "system_memory_available": 27252486144, "system_memory_percent": 18.8}
2025-06-08 10:01:12 [INFO] metrics: Gauges: {"memory_usage_rss": 102035456, "memory_usage_vms": 427999232, "memory_usage_percent": 0.3038879999921927, "system_memory_total": 33576665088, "system_memory_available": 27250679808, "system_memory_percent": 18.8}
2025-06-08 10:02:04 [INFO] metrics: Gauges: {"memory_usage_rss": 88715264, "memory_usage_vms": 636190720, "memory_usage_percent": 0.2642170202653808, "system_memory_total": 33576665088, "system_memory_available": 27241521152, "system_memory_percent": 18.9}
2025-06-08 10:02:08 [INFO] metrics: Gauges: {"memory_usage_rss": 83943424, "memory_usage_vms": 179486720, "memory_usage_percent": 0.25000524554774983, "system_memory_total": 33576665088, "system_memory_available": 27243872256, "system_memory_percent": 18.9}
2025-06-08 10:02:10 [INFO] metrics: Gauges: {"memory_usage_rss": 64536576, "memory_usage_vms": 154652672, "memory_usage_percent": 0.19220662871329885, "system_memory_total": 33576665088, "system_memory_available": 27233820672, "system_memory_percent": 18.9}
2025-06-08 10:02:12 [INFO] metrics: Gauges: {"memory_usage_rss": 102035456, "memory_usage_vms": 427999232, "memory_usage_percent": 0.3038879999921927, "system_memory_total": 33576665088, "system_memory_available": 27238256640, "system_memory_percent": 18.9}
2025-06-08 10:03:04 [INFO] metrics: Gauges: {"memory_usage_rss": 88715264, "memory_usage_vms": 636190720, "memory_usage_percent": 0.2642170202653808, "system_memory_total": 33576665088, "system_memory_available": 26767904768, "system_memory_percent": 20.3}
2025-06-08 10:03:08 [INFO] metrics: Gauges: {"memory_usage_rss": 83943424, "memory_usage_vms": 179486720, "memory_usage_percent": 0.25000524554774983, "system_memory_total": 33576665088, "system_memory_available": 26904563712, "system_memory_percent": 19.9}
2025-06-08 10:03:10 [INFO] metrics: Gauges: {"memory_usage_rss": 64536576, "memory_usage_vms": 154652672, "memory_usage_percent": 0.19220662871329885, "system_memory_total": 33576665088, "system_memory_available": 26341244928, "system_memory_percent": 21.5}
2025-06-08 10:03:12 [INFO] metrics: Gauges: {"memory_usage_rss": 102035456, "memory_usage_vms": 427999232, "memory_usage_percent": 0.3038879999921927, "system_memory_total": 33576665088, "system_memory_available": 27038355456, "system_memory_percent": 19.5}
2025-06-08 10:04:04 [INFO] metrics: Gauges: {"memory_usage_rss": 88715264, "memory_usage_vms": 636190720, "memory_usage_percent": 0.2642170202653808, "system_memory_total": 33576665088, "system_memory_available": 26830852096, "system_memory_percent": 20.1}
2025-06-08 10:04:08 [INFO] metrics: Gauges: {"memory_usage_rss": 83943424, "memory_usage_vms": 179486720, "memory_usage_percent": 0.25000524554774983, "system_memory_total": 33576665088, "system_memory_available": 26222141440, "system_memory_percent": 21.9}
2025-06-08 10:04:10 [INFO] metrics: Gauges: {"memory_usage_rss": 64536576, "memory_usage_vms": 154652672, "memory_usage_percent": 0.19220662871329885, "system_memory_total": 33576665088, "system_memory_available": 26489036800, "system_memory_percent": 21.1}
2025-06-08 10:04:12 [INFO] metrics: Gauges: {"memory_usage_rss": 102035456, "memory_usage_vms": 427999232, "memory_usage_percent": 0.3038879999921927, "system_memory_total": 33576665088, "system_memory_available": 26798002176, "system_memory_percent": 20.2}
2025-06-08 10:05:04 [INFO] metrics: Gauges: {"memory_usage_rss": 88715264, "memory_usage_vms": 636190720, "memory_usage_percent": 0.2642170202653808, "system_memory_total": 33576665088, "system_memory_available": 26705797120, "system_memory_percent": 20.5}
2025-06-08 10:05:08 [INFO] metrics: Gauges: {"memory_usage_rss": 83943424, "memory_usage_vms": 179486720, "memory_usage_percent": 0.25000524554774983, "system_memory_total": 33576665088, "system_memory_available": 26690981888, "system_memory_percent": 20.5}
2025-06-08 10:05:10 [INFO] metrics: Gauges: {"memory_usage_rss": 64536576, "memory_usage_vms": 154652672, "memory_usage_percent": 0.19220662871329885, "system_memory_total": 33576665088, "system_memory_available": 26721964032, "system_memory_percent": 20.4}
2025-06-08 10:05:12 [INFO] metrics: Gauges: {"memory_usage_rss": 102035456, "memory_usage_vms": 427999232, "memory_usage_percent": 0.3038879999921927, "system_memory_total": 33576665088, "system_memory_available": 26719653888, "system_memory_percent": 20.4}
2025-06-08 10:06:04 [INFO] metrics: Gauges: {"memory_usage_rss": 88715264, "memory_usage_vms": 636190720, "memory_usage_percent": 0.2642170202653808, "system_memory_total": 33576665088, "system_memory_available": 26996748288, "system_memory_percent": 19.6}
2025-06-08 10:06:08 [INFO] metrics: Gauges: {"memory_usage_rss": 83943424, "memory_usage_vms": 179486720, "memory_usage_percent": 0.25000524554774983, "system_memory_total": 33576665088, "system_memory_available": 26985783296, "system_memory_percent": 19.6}
2025-06-08 10:06:10 [INFO] metrics: Gauges: {"memory_usage_rss": 64536576, "memory_usage_vms": 154652672, "memory_usage_percent": 0.19220662871329885, "system_memory_total": 33576665088, "system_memory_available": 26985783296, "system_memory_percent": 19.6}
2025-06-08 10:06:12 [INFO] metrics: Gauges: {"memory_usage_rss": 102035456, "memory_usage_vms": 427999232, "memory_usage_percent": 0.3038879999921927, "system_memory_total": 33576665088, "system_memory_available": 26991308800, "system_memory_percent": 19.6}
