2025-06-08 11:05:00 [WARNING] database.sqlalchemy.taskdb: Error creating database: (psycopg2.errors.SyntaxError) syntax error at or near "NOT"
LINE 1: CREATE DATABASE IF NOT EXISTS pyspider_taskdb
                           ^

[SQL: CREATE DATABASE IF NOT EXISTS pyspider_taskdb]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-06-08 11:05:00 [WARNING] database.sqlalchemy.projectdb: Error creating database: (psycopg2.errors.SyntaxError) syntax error at or near "NOT"
LINE 1: CREATE DATABASE IF NOT EXISTS pyspider_projectdb
                           ^

[SQL: CREATE DATABASE IF NOT EXISTS pyspider_projectdb]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-06-08 11:05:00 [WARNING] database.sqlalchemy.resultdb: Error creating database: (psycopg2.errors.SyntaxError) syntax error at or near "NOT"
LINE 1: CREATE DATABASE IF NOT EXISTS pyspider_resultdb
                           ^

[SQL: CREATE DATABASE IF NOT EXISTS pyspider_resultdb]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-06-08 11:05:00 [WARNING] database.sqlalchemy.taskdb: Error creating database: (psycopg2.errors.SyntaxError) syntax error at or near "NOT"
LINE 1: CREATE DATABASE IF NOT EXISTS pyspider_taskdb
                           ^

[SQL: CREATE DATABASE IF NOT EXISTS pyspider_taskdb]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-06-08 11:05:00 [WARNING] database.sqlalchemy.taskdb: Error creating database: (psycopg2.errors.SyntaxError) syntax error at or near "NOT"
LINE 1: CREATE DATABASE IF NOT EXISTS pyspider_taskdb
                           ^

[SQL: CREATE DATABASE IF NOT EXISTS pyspider_taskdb]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-06-08 11:05:00 [WARNING] database.sqlalchemy.taskdb: Error creating database: (psycopg2.errors.SyntaxError) syntax error at or near "NOT"
LINE 1: CREATE DATABASE IF NOT EXISTS pyspider_taskdb
                           ^

[SQL: CREATE DATABASE IF NOT EXISTS pyspider_taskdb]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-06-08 11:05:05 [WARNING] database.sqlalchemy.projectdb: Error creating database: (psycopg2.errors.SyntaxError) syntax error at or near "NOT"
LINE 1: CREATE DATABASE IF NOT EXISTS pyspider_projectdb
                           ^

[SQL: CREATE DATABASE IF NOT EXISTS pyspider_projectdb]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-06-08 11:05:06 [WARNING] database.sqlalchemy.resultdb: Error creating database: (psycopg2.errors.SyntaxError) syntax error at or near "NOT"
LINE 1: CREATE DATABASE IF NOT EXISTS pyspider_resultdb
                           ^

[SQL: CREATE DATABASE IF NOT EXISTS pyspider_resultdb]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-06-08 11:05:09 [WARNING] database.sqlalchemy.taskdb: Error creating database: (psycopg2.errors.SyntaxError) syntax error at or near "NOT"
LINE 1: CREATE DATABASE IF NOT EXISTS pyspider_taskdb
                           ^

[SQL: CREATE DATABASE IF NOT EXISTS pyspider_taskdb]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-06-08 11:05:09 [WARNING] database.sqlalchemy.projectdb: Error creating database: (psycopg2.errors.SyntaxError) syntax error at or near "NOT"
LINE 1: CREATE DATABASE IF NOT EXISTS pyspider_projectdb
                           ^

[SQL: CREATE DATABASE IF NOT EXISTS pyspider_projectdb]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-06-08 11:05:09 [WARNING] database.sqlalchemy.resultdb: Error creating database: (psycopg2.errors.SyntaxError) syntax error at or near "NOT"
LINE 1: CREATE DATABASE IF NOT EXISTS pyspider_resultdb
                           ^

[SQL: CREATE DATABASE IF NOT EXISTS pyspider_resultdb]
(Background on this error at: https://sqlalche.me/e/20/f405)
