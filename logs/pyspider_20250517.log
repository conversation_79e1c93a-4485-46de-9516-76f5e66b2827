2025-05-17 17:34:40,515 [INFO] scheduler: scheduler starting...
2025-05-17 17:34:40,575 [INFO] scheduler: scheduler.xmlrpc listening on 0.0.0.0:23333
2025-05-17 17:34:40,616 [INFO] scheduler: project yahoo updated, status:RUNNING, paused:False, 0 tasks
2025-05-17 17:34:40,617 [INFO] scheduler: select yahoo:_on_get_info data:,_on_get_info
2025-05-17 17:34:40,621 [INFO] scheduler: project test_project updated, status:RUNNING, paused:False, 0 tasks
2025-05-17 17:34:40,621 [INFO] scheduler: select test_project:_on_get_info data:,_on_get_info
2025-05-17 17:34:40,623 [INFO] scheduler: project yahooworld updated, status:RUNNING, paused:False, 0 tasks
2025-05-17 17:34:40,624 [INFO] scheduler: in 5m: new:0,success:0,retry:0,failed:0
2025-05-17 17:34:40,624 [INFO] scheduler: select yahooworld:_on_get_info data:,_on_get_info
2025-05-17 17:34:40,625 [INFO] scheduler: project mehoo updated, status:RUNNING, paused:False, 0 tasks
2025-05-17 17:34:40,625 [INFO] scheduler: select mehoo:_on_get_info data:,_on_get_info
2025-05-17 17:34:40,627 [INFO] scheduler: project roastmeat updated, status:RUNNING, paused:False, 0 tasks
2025-05-17 17:34:40,627 [INFO] scheduler: select roastmeat:_on_get_info data:,_on_get_info
2025-05-17 17:34:40,629 [INFO] scheduler: project nohoo updated, status:RUNNING, paused:False, 0 tasks
2025-05-17 17:34:40,629 [INFO] scheduler: select nohoo:_on_get_info data:,_on_get_info
2025-05-17 17:34:43,355 [INFO] fetcher: fetcher starting...
2025-05-17 17:34:43,369 [INFO] fetcher: fetcher.xmlrpc listening on 0.0.0.0:24444
2025-05-17 17:34:43,459 [INFO] fetcher: on fetch data:{'taskid': '_on_get_info', 'project': 'yahoo', 'url': 'data:,_on_get_info', 'fetch': {'method': None, 'fetch_type': None}}
2025-05-17 17:34:43,459 [INFO] fetcher: [200] yahoo:_on_get_info data:,_on_get_info 0s
2025-05-17 17:34:43,461 [INFO] fetcher: on fetch data:{'taskid': '_on_get_info', 'project': 'test_project', 'url': 'data:,_on_get_info', 'fetch': {'method': None, 'fetch_type': None}}
2025-05-17 17:34:43,461 [INFO] fetcher: [200] test_project:_on_get_info data:,_on_get_info 0s
2025-05-17 17:34:43,462 [INFO] fetcher: on fetch data:{'taskid': '_on_get_info', 'project': 'yahooworld', 'url': 'data:,_on_get_info', 'fetch': {'method': None, 'fetch_type': None}}
2025-05-17 17:34:43,462 [INFO] fetcher: [200] yahooworld:_on_get_info data:,_on_get_info 0s
2025-05-17 17:34:43,464 [INFO] fetcher: on fetch data:{'taskid': '_on_get_info', 'project': 'mehoo', 'url': 'data:,_on_get_info', 'fetch': {'method': None, 'fetch_type': None}}
2025-05-17 17:34:43,464 [INFO] fetcher: [200] mehoo:_on_get_info data:,_on_get_info 0s
2025-05-17 17:34:43,465 [INFO] fetcher: on fetch data:{'taskid': '_on_get_info', 'project': 'roastmeat', 'url': 'data:,_on_get_info', 'fetch': {'method': None, 'fetch_type': None}}
2025-05-17 17:34:43,466 [INFO] fetcher: [200] roastmeat:_on_get_info data:,_on_get_info 0s
2025-05-17 17:34:43,467 [INFO] fetcher: on fetch data:{'taskid': '_on_get_info', 'project': 'nohoo', 'url': 'data:,_on_get_info', 'fetch': {'method': None, 'fetch_type': None}}
2025-05-17 17:34:43,467 [INFO] fetcher: [200] nohoo:_on_get_info data:,_on_get_info 0s
2025-05-17 17:34:45,482 [INFO] processor: processor starting...
2025-05-17 17:34:45,488 [INFO] processor: process yahoo:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:None
2025-05-17 17:34:45,490 [INFO] processor: process test_project:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:None
2025-05-17 17:34:45,492 [INFO] processor: process yahooworld:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:None
2025-05-17 17:34:45,493 [INFO] processor: process mehoo:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:None
2025-05-17 17:34:45,495 [INFO] processor: process roastmeat:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:None
2025-05-17 17:34:45,496 [INFO] processor: process nohoo:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:None
2025-05-17 17:34:45,590 [INFO] scheduler: yahoo on_get_info {'min_tick': 86400, 'retry_delay': {}, 'crawl_config': {}}
2025-05-17 17:34:45,592 [INFO] scheduler: test_project on_get_info {'min_tick': 0, 'retry_delay': {}, 'crawl_config': {'headers': {'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'}, 'connect_timeout': 20, 'timeout': 90, 'retries': 3}}
2025-05-17 17:34:45,594 [INFO] scheduler: yahooworld on_get_info {'min_tick': 0, 'retry_delay': {}, 'crawl_config': {'headers': {'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'}, 'connect_timeout': 20, 'timeout': 90, 'retries': 3}}
2025-05-17 17:34:45,597 [INFO] scheduler: mehoo on_get_info {'min_tick': 0, 'retry_delay': {}, 'crawl_config': {}}
2025-05-17 17:34:45,599 [INFO] scheduler: roastmeat on_get_info {'min_tick': 0, 'retry_delay': {}, 'crawl_config': {}}
2025-05-17 17:34:45,600 [INFO] scheduler: nohoo on_get_info {'min_tick': 0, 'retry_delay': {}, 'crawl_config': {}}
2025-05-17 17:34:47,228 [INFO] result: result_worker starting...
2025-05-17 17:34:49,354 [INFO] webui: CORS enabled for all routes
2025-05-17 17:34:49,717 [INFO] libs.cache: Using Redis cache
2025-05-17 17:34:49,819 [INFO] webui: WebDAV mode has been removed
2025-05-17 17:34:49,819 [INFO] webui: webui running on 0.0.0.0:5000
2025-05-17 17:37:31,569 [INFO] scheduler: scheduler starting...
2025-05-17 17:37:31,620 [INFO] scheduler: scheduler.xmlrpc listening on 0.0.0.0:23333
2025-05-17 17:37:31,671 [INFO] scheduler: project yahoo updated, status:RUNNING, paused:False, 0 tasks
2025-05-17 17:37:31,673 [INFO] scheduler: select yahoo:_on_get_info data:,_on_get_info
2025-05-17 17:37:31,678 [INFO] scheduler: project test_project updated, status:RUNNING, paused:False, 0 tasks
2025-05-17 17:37:31,679 [INFO] scheduler: select test_project:_on_get_info data:,_on_get_info
2025-05-17 17:37:31,681 [INFO] scheduler: in 5m: new:0,success:0,retry:0,failed:0
2025-05-17 17:37:31,682 [INFO] scheduler: project yahooworld updated, status:RUNNING, paused:False, 0 tasks
2025-05-17 17:37:31,683 [INFO] scheduler: select yahooworld:_on_get_info data:,_on_get_info
2025-05-17 17:37:31,684 [INFO] scheduler: project mehoo updated, status:RUNNING, paused:False, 0 tasks
2025-05-17 17:37:31,684 [INFO] scheduler: select mehoo:_on_get_info data:,_on_get_info
2025-05-17 17:37:31,686 [INFO] scheduler: project roastmeat updated, status:RUNNING, paused:False, 0 tasks
2025-05-17 17:37:31,686 [INFO] scheduler: select roastmeat:_on_get_info data:,_on_get_info
2025-05-17 17:37:31,688 [INFO] scheduler: project nohoo updated, status:RUNNING, paused:False, 0 tasks
2025-05-17 17:37:31,688 [INFO] scheduler: select nohoo:_on_get_info data:,_on_get_info
2025-05-17 17:37:34,455 [INFO] fetcher: fetcher starting...
2025-05-17 17:37:34,470 [INFO] fetcher: fetcher.xmlrpc listening on 0.0.0.0:24444
2025-05-17 17:37:34,564 [INFO] fetcher: on fetch data:{'taskid': '_on_get_info', 'project': 'yahoo', 'url': 'data:,_on_get_info', 'fetch': {'method': None, 'fetch_type': None}}
2025-05-17 17:37:34,565 [INFO] fetcher: [200] yahoo:_on_get_info data:,_on_get_info 0s
2025-05-17 17:37:34,569 [INFO] fetcher: on fetch data:{'taskid': '_on_get_info', 'project': 'test_project', 'url': 'data:,_on_get_info', 'fetch': {'method': None, 'fetch_type': None}}
2025-05-17 17:37:34,570 [INFO] fetcher: [200] test_project:_on_get_info data:,_on_get_info 0s
2025-05-17 17:37:34,574 [INFO] fetcher: on fetch data:{'taskid': '_on_get_info', 'project': 'yahooworld', 'url': 'data:,_on_get_info', 'fetch': {'method': None, 'fetch_type': None}}
2025-05-17 17:37:34,575 [INFO] fetcher: [200] yahooworld:_on_get_info data:,_on_get_info 0s
2025-05-17 17:37:34,579 [INFO] fetcher: on fetch data:{'taskid': '_on_get_info', 'project': 'mehoo', 'url': 'data:,_on_get_info', 'fetch': {'method': None, 'fetch_type': None}}
2025-05-17 17:37:34,580 [INFO] fetcher: [200] mehoo:_on_get_info data:,_on_get_info 0s
2025-05-17 17:37:34,584 [INFO] fetcher: on fetch data:{'taskid': '_on_get_info', 'project': 'roastmeat', 'url': 'data:,_on_get_info', 'fetch': {'method': None, 'fetch_type': None}}
2025-05-17 17:37:34,585 [INFO] fetcher: [200] roastmeat:_on_get_info data:,_on_get_info 0s
2025-05-17 17:37:34,588 [INFO] fetcher: on fetch data:{'taskid': '_on_get_info', 'project': 'nohoo', 'url': 'data:,_on_get_info', 'fetch': {'method': None, 'fetch_type': None}}
2025-05-17 17:37:34,589 [INFO] fetcher: [200] nohoo:_on_get_info data:,_on_get_info 0s
2025-05-17 17:37:36,596 [INFO] processor: processor starting...
2025-05-17 17:37:36,601 [INFO] processor: process yahoo:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:None
2025-05-17 17:37:36,603 [INFO] processor: process test_project:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:None
2025-05-17 17:37:36,604 [INFO] processor: process yahooworld:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:None
2025-05-17 17:37:36,606 [INFO] processor: process mehoo:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:None
2025-05-17 17:37:36,607 [INFO] processor: process roastmeat:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:None
2025-05-17 17:37:36,609 [INFO] processor: process nohoo:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:None
2025-05-17 17:37:36,661 [INFO] scheduler: yahoo on_get_info {'min_tick': 86400, 'retry_delay': {}, 'crawl_config': {}}
2025-05-17 17:37:36,662 [INFO] scheduler: test_project on_get_info {'min_tick': 0, 'retry_delay': {}, 'crawl_config': {'headers': {'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'}, 'connect_timeout': 20, 'timeout': 90, 'retries': 3}}
2025-05-17 17:37:36,662 [INFO] scheduler: yahooworld on_get_info {'min_tick': 0, 'retry_delay': {}, 'crawl_config': {'headers': {'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'}, 'connect_timeout': 20, 'timeout': 90, 'retries': 3}}
2025-05-17 17:37:36,663 [INFO] scheduler: mehoo on_get_info {'min_tick': 0, 'retry_delay': {}, 'crawl_config': {}}
2025-05-17 17:37:36,663 [INFO] scheduler: roastmeat on_get_info {'min_tick': 0, 'retry_delay': {}, 'crawl_config': {}}
2025-05-17 17:37:36,664 [INFO] scheduler: nohoo on_get_info {'min_tick': 0, 'retry_delay': {}, 'crawl_config': {}}
2025-05-17 17:37:38,387 [INFO] result: result_worker starting...
2025-05-17 17:37:40,509 [INFO] webui: CORS enabled for all routes
2025-05-17 17:37:40,817 [INFO] libs.cache: Using Redis cache
2025-05-17 17:37:40,898 [INFO] webui: WebDAV mode has been removed
2025-05-17 17:37:40,899 [INFO] webui: webui running on 0.0.0.0:5000
2025-05-17 17:40:03,542 [INFO] scheduler: scheduler starting...
2025-05-17 17:40:03,593 [INFO] scheduler: scheduler.xmlrpc listening on 0.0.0.0:23333
2025-05-17 17:40:03,643 [INFO] scheduler: project yahoo updated, status:RUNNING, paused:False, 0 tasks
2025-05-17 17:40:03,646 [INFO] scheduler: select yahoo:_on_get_info data:,_on_get_info
2025-05-17 17:40:03,652 [INFO] scheduler: project test_project updated, status:RUNNING, paused:False, 0 tasks
2025-05-17 17:40:03,652 [INFO] scheduler: select test_project:_on_get_info data:,_on_get_info
2025-05-17 17:40:03,655 [INFO] scheduler: project yahooworld updated, status:RUNNING, paused:False, 0 tasks
2025-05-17 17:40:03,655 [INFO] scheduler: in 5m: new:0,success:0,retry:0,failed:0
2025-05-17 17:40:03,656 [INFO] scheduler: select yahooworld:_on_get_info data:,_on_get_info
2025-05-17 17:40:03,658 [INFO] scheduler: project mehoo updated, status:RUNNING, paused:False, 0 tasks
2025-05-17 17:40:03,659 [INFO] scheduler: select mehoo:_on_get_info data:,_on_get_info
2025-05-17 17:40:03,660 [INFO] scheduler: project roastmeat updated, status:RUNNING, paused:False, 0 tasks
2025-05-17 17:40:03,660 [INFO] scheduler: select roastmeat:_on_get_info data:,_on_get_info
2025-05-17 17:40:03,661 [INFO] scheduler: project nohoo updated, status:RUNNING, paused:False, 0 tasks
2025-05-17 17:40:03,662 [INFO] scheduler: select nohoo:_on_get_info data:,_on_get_info
2025-05-17 17:40:06,443 [INFO] fetcher: fetcher starting...
2025-05-17 17:40:06,458 [INFO] fetcher: fetcher.xmlrpc listening on 0.0.0.0:24444
2025-05-17 17:40:06,552 [INFO] fetcher: on fetch data:{'taskid': '_on_get_info', 'project': 'yahoo', 'url': 'data:,_on_get_info', 'fetch': {'method': None, 'fetch_type': None}}
2025-05-17 17:40:06,552 [INFO] fetcher: [200] yahoo:_on_get_info data:,_on_get_info 0s
2025-05-17 17:40:06,557 [INFO] fetcher: on fetch data:{'taskid': '_on_get_info', 'project': 'test_project', 'url': 'data:,_on_get_info', 'fetch': {'method': None, 'fetch_type': None}}
2025-05-17 17:40:06,558 [INFO] fetcher: [200] test_project:_on_get_info data:,_on_get_info 0s
2025-05-17 17:40:06,563 [INFO] fetcher: on fetch data:{'taskid': '_on_get_info', 'project': 'yahooworld', 'url': 'data:,_on_get_info', 'fetch': {'method': None, 'fetch_type': None}}
2025-05-17 17:40:06,564 [INFO] fetcher: [200] yahooworld:_on_get_info data:,_on_get_info 0s
2025-05-17 17:40:06,568 [INFO] fetcher: on fetch data:{'taskid': '_on_get_info', 'project': 'mehoo', 'url': 'data:,_on_get_info', 'fetch': {'method': None, 'fetch_type': None}}
2025-05-17 17:40:06,569 [INFO] fetcher: [200] mehoo:_on_get_info data:,_on_get_info 0s
2025-05-17 17:40:06,571 [INFO] fetcher: on fetch data:{'taskid': '_on_get_info', 'project': 'roastmeat', 'url': 'data:,_on_get_info', 'fetch': {'method': None, 'fetch_type': None}}
2025-05-17 17:40:06,572 [INFO] fetcher: [200] roastmeat:_on_get_info data:,_on_get_info 0s
2025-05-17 17:40:06,574 [INFO] fetcher: on fetch data:{'taskid': '_on_get_info', 'project': 'nohoo', 'url': 'data:,_on_get_info', 'fetch': {'method': None, 'fetch_type': None}}
2025-05-17 17:40:06,576 [INFO] fetcher: [200] nohoo:_on_get_info data:,_on_get_info 0s
2025-05-17 17:40:08,592 [INFO] processor: processor starting...
2025-05-17 17:40:08,598 [INFO] processor: process yahoo:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:None
2025-05-17 17:40:08,599 [INFO] processor: process test_project:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:None
2025-05-17 17:40:08,601 [INFO] processor: process yahooworld:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:None
2025-05-17 17:40:08,603 [INFO] processor: process mehoo:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:None
2025-05-17 17:40:08,604 [INFO] processor: process roastmeat:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:None
2025-05-17 17:40:08,605 [INFO] processor: process nohoo:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:None
2025-05-17 17:40:08,635 [INFO] scheduler: yahoo on_get_info {'min_tick': 86400, 'retry_delay': {}, 'crawl_config': {}}
2025-05-17 17:40:08,636 [INFO] scheduler: test_project on_get_info {'min_tick': 0, 'retry_delay': {}, 'crawl_config': {'headers': {'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'}, 'connect_timeout': 20, 'timeout': 90, 'retries': 3}}
2025-05-17 17:40:08,637 [INFO] scheduler: yahooworld on_get_info {'min_tick': 0, 'retry_delay': {}, 'crawl_config': {'headers': {'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'}, 'connect_timeout': 20, 'timeout': 90, 'retries': 3}}
2025-05-17 17:40:08,637 [INFO] scheduler: mehoo on_get_info {'min_tick': 0, 'retry_delay': {}, 'crawl_config': {}}
2025-05-17 17:40:08,638 [INFO] scheduler: roastmeat on_get_info {'min_tick': 0, 'retry_delay': {}, 'crawl_config': {}}
2025-05-17 17:40:08,638 [INFO] scheduler: nohoo on_get_info {'min_tick': 0, 'retry_delay': {}, 'crawl_config': {}}
2025-05-17 17:40:11,751 [INFO] result: result_worker starting...
2025-05-17 17:40:13,867 [INFO] webui: CORS enabled for all routes
2025-05-17 17:40:14,184 [INFO] libs.cache: Using Redis cache
2025-05-17 17:40:14,270 [INFO] webui: WebDAV mode has been removed
2025-05-17 17:40:14,271 [INFO] webui: webui running on 0.0.0.0:5000
2025-05-17 17:41:03,654 [INFO] scheduler: in 5m: new:0,success:0,retry:0,failed:0
2025-05-17 17:42:03,666 [INFO] scheduler: in 5m: new:0,success:0,retry:0,failed:0
2025-05-17 17:43:03,708 [INFO] scheduler: in 5m: new:0,success:0,retry:0,failed:0
2025-05-17 17:44:03,750 [INFO] scheduler: in 5m: new:0,success:0,retry:0,failed:0
2025-05-17 17:44:56,353 [INFO] scheduler: scheduler starting...
2025-05-17 17:44:56,407 [INFO] scheduler: scheduler.xmlrpc listening on 0.0.0.0:23333
2025-05-17 17:44:56,455 [INFO] scheduler: project yahoo updated, status:RUNNING, paused:False, 0 tasks
2025-05-17 17:44:56,458 [INFO] scheduler: select yahoo:_on_get_info data:,_on_get_info
2025-05-17 17:44:56,463 [INFO] scheduler: project test_project updated, status:RUNNING, paused:False, 0 tasks
2025-05-17 17:44:56,465 [INFO] scheduler: select test_project:_on_get_info data:,_on_get_info
2025-05-17 17:44:56,467 [INFO] scheduler: project yahooworld updated, status:RUNNING, paused:False, 0 tasks
2025-05-17 17:44:56,469 [INFO] scheduler: select yahooworld:_on_get_info data:,_on_get_info
2025-05-17 17:44:56,470 [INFO] scheduler: in 5m: new:0,success:0,retry:0,failed:0
2025-05-17 17:44:56,472 [INFO] scheduler: project mehoo updated, status:RUNNING, paused:False, 0 tasks
2025-05-17 17:44:56,473 [INFO] scheduler: select mehoo:_on_get_info data:,_on_get_info
2025-05-17 17:44:56,475 [INFO] scheduler: project roastmeat updated, status:RUNNING, paused:False, 0 tasks
2025-05-17 17:44:56,475 [INFO] scheduler: select roastmeat:_on_get_info data:,_on_get_info
2025-05-17 17:44:56,477 [INFO] scheduler: project nohoo updated, status:RUNNING, paused:False, 0 tasks
2025-05-17 17:44:56,477 [INFO] scheduler: select nohoo:_on_get_info data:,_on_get_info
2025-05-17 17:44:59,266 [INFO] fetcher: fetcher starting...
2025-05-17 17:44:59,281 [INFO] fetcher: fetcher.xmlrpc listening on 0.0.0.0:24444
2025-05-17 17:44:59,373 [INFO] fetcher: on fetch data:{'taskid': '_on_get_info', 'project': 'yahoo', 'url': 'data:,_on_get_info', 'fetch': {'method': None, 'fetch_type': None}}
2025-05-17 17:44:59,374 [INFO] fetcher: [200] yahoo:_on_get_info data:,_on_get_info 0s
2025-05-17 17:44:59,378 [INFO] fetcher: on fetch data:{'taskid': '_on_get_info', 'project': 'test_project', 'url': 'data:,_on_get_info', 'fetch': {'method': None, 'fetch_type': None}}
2025-05-17 17:44:59,379 [INFO] fetcher: [200] test_project:_on_get_info data:,_on_get_info 0s
2025-05-17 17:44:59,382 [INFO] fetcher: on fetch data:{'taskid': '_on_get_info', 'project': 'yahooworld', 'url': 'data:,_on_get_info', 'fetch': {'method': None, 'fetch_type': None}}
2025-05-17 17:44:59,383 [INFO] fetcher: [200] yahooworld:_on_get_info data:,_on_get_info 0s
2025-05-17 17:44:59,386 [INFO] fetcher: on fetch data:{'taskid': '_on_get_info', 'project': 'mehoo', 'url': 'data:,_on_get_info', 'fetch': {'method': None, 'fetch_type': None}}
2025-05-17 17:44:59,387 [INFO] fetcher: [200] mehoo:_on_get_info data:,_on_get_info 0s
2025-05-17 17:44:59,390 [INFO] fetcher: on fetch data:{'taskid': '_on_get_info', 'project': 'roastmeat', 'url': 'data:,_on_get_info', 'fetch': {'method': None, 'fetch_type': None}}
2025-05-17 17:44:59,391 [INFO] fetcher: [200] roastmeat:_on_get_info data:,_on_get_info 0s
2025-05-17 17:44:59,394 [INFO] fetcher: on fetch data:{'taskid': '_on_get_info', 'project': 'nohoo', 'url': 'data:,_on_get_info', 'fetch': {'method': None, 'fetch_type': None}}
2025-05-17 17:44:59,395 [INFO] fetcher: [200] nohoo:_on_get_info data:,_on_get_info 0s
2025-05-17 17:45:01,403 [INFO] processor: processor starting...
2025-05-17 17:45:01,408 [INFO] processor: process yahoo:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:None
2025-05-17 17:45:01,409 [INFO] processor: process test_project:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:None
2025-05-17 17:45:01,410 [INFO] processor: process yahooworld:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:None
2025-05-17 17:45:01,412 [INFO] processor: process mehoo:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:None
2025-05-17 17:45:01,413 [INFO] processor: process roastmeat:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:None
2025-05-17 17:45:01,414 [INFO] processor: process nohoo:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:None
2025-05-17 17:45:01,441 [INFO] scheduler: yahoo on_get_info {'min_tick': 86400, 'retry_delay': {}, 'crawl_config': {}}
2025-05-17 17:45:01,442 [INFO] scheduler: test_project on_get_info {'min_tick': 0, 'retry_delay': {}, 'crawl_config': {'headers': {'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'}, 'connect_timeout': 20, 'timeout': 90, 'retries': 3}}
2025-05-17 17:45:01,445 [INFO] scheduler: yahooworld on_get_info {'min_tick': 0, 'retry_delay': {}, 'crawl_config': {'headers': {'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'}, 'connect_timeout': 20, 'timeout': 90, 'retries': 3}}
2025-05-17 17:45:01,446 [INFO] scheduler: mehoo on_get_info {'min_tick': 0, 'retry_delay': {}, 'crawl_config': {}}
2025-05-17 17:45:01,448 [INFO] scheduler: roastmeat on_get_info {'min_tick': 0, 'retry_delay': {}, 'crawl_config': {}}
2025-05-17 17:45:01,449 [INFO] scheduler: nohoo on_get_info {'min_tick': 0, 'retry_delay': {}, 'crawl_config': {}}
2025-05-17 17:45:03,162 [INFO] result: result_worker starting...
2025-05-17 17:45:05,286 [INFO] webui: CORS enabled for all routes
2025-05-17 17:45:05,605 [INFO] libs.cache: Using Redis cache
2025-05-17 17:45:05,690 [INFO] webui: WebDAV mode has been removed
2025-05-17 17:45:05,690 [INFO] webui: webui running on 0.0.0.0:5000
2025-05-17 17:45:56,557 [INFO] scheduler: in 5m: new:0,success:0,retry:0,failed:0
2025-05-17 17:47:26,519 [INFO] scheduler: scheduler starting...
2025-05-17 17:47:26,570 [INFO] scheduler: scheduler.xmlrpc listening on 0.0.0.0:23333
2025-05-17 17:47:26,620 [INFO] scheduler: project yahoo updated, status:RUNNING, paused:False, 0 tasks
2025-05-17 17:47:26,622 [INFO] scheduler: select yahoo:_on_get_info data:,_on_get_info
2025-05-17 17:47:26,627 [INFO] scheduler: project test_project updated, status:RUNNING, paused:False, 0 tasks
2025-05-17 17:47:26,629 [INFO] scheduler: select test_project:_on_get_info data:,_on_get_info
2025-05-17 17:47:26,631 [INFO] scheduler: project yahooworld updated, status:RUNNING, paused:False, 0 tasks
2025-05-17 17:47:26,632 [INFO] scheduler: select yahooworld:_on_get_info data:,_on_get_info
2025-05-17 17:47:26,634 [INFO] scheduler: in 5m: new:0,success:0,retry:0,failed:0
2025-05-17 17:47:26,635 [INFO] scheduler: project mehoo updated, status:RUNNING, paused:False, 0 tasks
2025-05-17 17:47:26,636 [INFO] scheduler: select mehoo:_on_get_info data:,_on_get_info
2025-05-17 17:47:26,637 [INFO] scheduler: project roastmeat updated, status:RUNNING, paused:False, 0 tasks
2025-05-17 17:47:26,637 [INFO] scheduler: select roastmeat:_on_get_info data:,_on_get_info
2025-05-17 17:47:26,639 [INFO] scheduler: project nohoo updated, status:RUNNING, paused:False, 0 tasks
2025-05-17 17:47:26,639 [INFO] scheduler: select nohoo:_on_get_info data:,_on_get_info
2025-05-17 17:47:30,833 [INFO] fetcher: fetcher starting...
2025-05-17 17:47:30,849 [INFO] fetcher: fetcher.xmlrpc listening on 0.0.0.0:24444
2025-05-17 17:47:30,943 [INFO] fetcher: on fetch data:{'taskid': '_on_get_info', 'project': 'yahoo', 'url': 'data:,_on_get_info', 'fetch': {'method': None, 'fetch_type': None}}
2025-05-17 17:47:30,944 [INFO] fetcher: [200] yahoo:_on_get_info data:,_on_get_info 0s
2025-05-17 17:47:30,949 [INFO] fetcher: on fetch data:{'taskid': '_on_get_info', 'project': 'test_project', 'url': 'data:,_on_get_info', 'fetch': {'method': None, 'fetch_type': None}}
2025-05-17 17:47:30,950 [INFO] fetcher: [200] test_project:_on_get_info data:,_on_get_info 0s
2025-05-17 17:47:30,954 [INFO] fetcher: on fetch data:{'taskid': '_on_get_info', 'project': 'yahooworld', 'url': 'data:,_on_get_info', 'fetch': {'method': None, 'fetch_type': None}}
2025-05-17 17:47:30,955 [INFO] fetcher: [200] yahooworld:_on_get_info data:,_on_get_info 0s
2025-05-17 17:47:30,960 [INFO] fetcher: on fetch data:{'taskid': '_on_get_info', 'project': 'mehoo', 'url': 'data:,_on_get_info', 'fetch': {'method': None, 'fetch_type': None}}
2025-05-17 17:47:30,961 [INFO] fetcher: [200] mehoo:_on_get_info data:,_on_get_info 0s
2025-05-17 17:47:30,965 [INFO] fetcher: on fetch data:{'taskid': '_on_get_info', 'project': 'roastmeat', 'url': 'data:,_on_get_info', 'fetch': {'method': None, 'fetch_type': None}}
2025-05-17 17:47:30,965 [INFO] fetcher: [200] roastmeat:_on_get_info data:,_on_get_info 0s
2025-05-17 17:47:30,968 [INFO] fetcher: on fetch data:{'taskid': '_on_get_info', 'project': 'nohoo', 'url': 'data:,_on_get_info', 'fetch': {'method': None, 'fetch_type': None}}
2025-05-17 17:47:30,968 [INFO] fetcher: [200] nohoo:_on_get_info data:,_on_get_info 0s
2025-05-17 17:47:33,004 [INFO] processor: processor starting...
2025-05-17 17:47:33,009 [INFO] processor: process yahoo:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:None
2025-05-17 17:47:33,010 [INFO] processor: process test_project:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:None
2025-05-17 17:47:33,012 [INFO] processor: process yahooworld:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:None
2025-05-17 17:47:33,013 [INFO] processor: process mehoo:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:None
2025-05-17 17:47:33,014 [INFO] processor: process roastmeat:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:None
2025-05-17 17:47:33,015 [INFO] processor: process nohoo:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:None
2025-05-17 17:47:33,021 [INFO] scheduler: yahoo on_get_info {'min_tick': 86400, 'retry_delay': {}, 'crawl_config': {}}
2025-05-17 17:47:33,022 [INFO] scheduler: test_project on_get_info {'min_tick': 0, 'retry_delay': {}, 'crawl_config': {'headers': {'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'}, 'connect_timeout': 20, 'timeout': 90, 'retries': 3}}
2025-05-17 17:47:33,022 [INFO] scheduler: yahooworld on_get_info {'min_tick': 0, 'retry_delay': {}, 'crawl_config': {'headers': {'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'}, 'connect_timeout': 20, 'timeout': 90, 'retries': 3}}
2025-05-17 17:47:33,023 [INFO] scheduler: mehoo on_get_info {'min_tick': 0, 'retry_delay': {}, 'crawl_config': {}}
2025-05-17 17:47:33,023 [INFO] scheduler: roastmeat on_get_info {'min_tick': 0, 'retry_delay': {}, 'crawl_config': {}}
2025-05-17 17:47:33,024 [INFO] scheduler: nohoo on_get_info {'min_tick': 0, 'retry_delay': {}, 'crawl_config': {}}
2025-05-17 17:47:34,755 [INFO] result: result_worker starting...
2025-05-17 17:47:36,857 [INFO] webui: CORS enabled for all routes
2025-05-17 17:47:37,187 [INFO] libs.cache: Using Redis cache
2025-05-17 17:47:37,275 [INFO] webui: WebDAV mode has been removed
2025-05-17 17:47:37,276 [INFO] webui: webui running on 0.0.0.0:5000
2025-05-17 17:48:26,696 [INFO] scheduler: in 5m: new:0,success:0,retry:0,failed:0
2025-05-17 17:49:26,704 [INFO] scheduler: in 5m: new:0,success:0,retry:0,failed:0
