{"timestamp": "2025-05-24T12:51:22.785980", "level": "ERROR", "logger": "processor", "message": "process yahooo2:c38374ecc3e9732c6059c21b00a77057 https://x.com/YahooNewsTopics -> [400] len:2540 -> result:None fol:0 msg:0 err:HTTPError('HTTP 599: HTTP 400: Bad Request')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T12:51:53.393166", "level": "ERROR", "logger": "processor", "message": "process yahooo2:c38374ecc3e9732c6059c21b00a77057 https://x.com/YahooNewsTopics -> [400] len:2540 -> result:None fol:0 msg:0 err:HTTPError('HTTP 599: HTTP 400: Bad Request')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T12:58:48.277673", "level": "ERROR", "logger": "scheduler", "message": "project yahoooo3 not started, please set status to RUNNING or DEBUG", "module": "scheduler", "function": "task_verify", "line": 310, "extra": {"taskName": "Task-268"}}
{"timestamp": "2025-05-24T13:00:16.435833", "level": "ERROR", "logger": "processor", "message": "process yahoooo3:c38374ecc3e9732c6059c21b00a77057 https://x.com/YahooNewsTopics -> [400] len:2540 -> result:None fol:0 msg:0 err:HTTPError('HTTP 599: HTTP 400: Bad Request')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T13:00:46.949945", "level": "ERROR", "logger": "processor", "message": "process yahoooo3:c38374ecc3e9732c6059c21b00a77057 https://x.com/YahooNewsTopics -> [400] len:2540 -> result:None fol:0 msg:0 err:HTTPError('HTTP 599: HTTP 400: Bad Request')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T13:18:48.707974", "level": "ERROR", "logger": "processor", "message": "load project test_auth_success error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T13:18:48.711874", "level": "ERROR", "logger": "processor", "message": "process test_auth_success:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T13:23:48.819891", "level": "ERROR", "logger": "processor", "message": "load project content_type_test_1748060447 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T13:23:48.833025", "level": "ERROR", "logger": "processor", "message": "process content_type_test_1748060447:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T13:23:48.836555", "level": "ERROR", "logger": "processor", "message": "load project form_test_1748060447 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T13:23:48.854510", "level": "ERROR", "logger": "processor", "message": "process form_test_1748060447:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T13:23:48.860395", "level": "ERROR", "logger": "processor", "message": "load project duplicate_test_1748060447 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T13:23:48.885087", "level": "ERROR", "logger": "processor", "message": "process duplicate_test_1748060447:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T13:23:48.889378", "level": "ERROR", "logger": "processor", "message": "load project auth_test_1748060447 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T13:23:48.902730", "level": "ERROR", "logger": "processor", "message": "process auth_test_1748060447:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T13:23:48.905597", "level": "ERROR", "logger": "processor", "message": "load project valid_name_1748060447 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T13:23:48.914232", "level": "ERROR", "logger": "processor", "message": "process valid_name_1748060447:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T13:23:48.916630", "level": "ERROR", "logger": "processor", "message": "load project valid-name_1748060447 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T13:23:48.925023", "level": "ERROR", "logger": "processor", "message": "process valid-name_1748060447:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T13:23:48.935304", "level": "ERROR", "logger": "processor", "message": "load project validname123_1748060447 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T13:23:48.941335", "level": "ERROR", "logger": "processor", "message": "process validname123_1748060447:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T13:23:48.942642", "level": "ERROR", "logger": "processor", "message": "load project VALIDNAME_1748060447 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T13:23:48.947424", "level": "ERROR", "logger": "processor", "message": "process VALIDNAME_1748060447:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T13:23:48.948574", "level": "ERROR", "logger": "processor", "message": "load project Valid_Name_123_1748060447 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T13:23:48.953695", "level": "ERROR", "logger": "processor", "message": "process Valid_Name_123_1748060447:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T13:23:48.955378", "level": "ERROR", "logger": "processor", "message": "load project valid-name-123_1748060447 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T13:23:48.959895", "level": "ERROR", "logger": "processor", "message": "process valid-name-123_1748060447:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T13:23:48.961128", "level": "ERROR", "logger": "processor", "message": "load project a_1748060447 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T13:23:48.965587", "level": "ERROR", "logger": "processor", "message": "process a_1748060447:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T13:23:48.967272", "level": "ERROR", "logger": "processor", "message": "load project content_type_test_1748060569 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T13:23:48.971161", "level": "ERROR", "logger": "processor", "message": "process content_type_test_1748060569:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T13:23:48.972136", "level": "ERROR", "logger": "processor", "message": "load project form_test_1748060569 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T13:23:48.976507", "level": "ERROR", "logger": "processor", "message": "process form_test_1748060569:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T13:23:48.977506", "level": "ERROR", "logger": "processor", "message": "load project duplicate_test_1748060569 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T13:23:48.981547", "level": "ERROR", "logger": "processor", "message": "process duplicate_test_1748060569:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T13:23:48.983103", "level": "ERROR", "logger": "processor", "message": "load project auth_test_1748060569 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T13:23:48.987175", "level": "ERROR", "logger": "processor", "message": "process auth_test_1748060569:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T13:23:48.988332", "level": "ERROR", "logger": "processor", "message": "load project valid_name_1748060569 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T13:23:48.991981", "level": "ERROR", "logger": "processor", "message": "process valid_name_1748060569:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T13:23:48.993051", "level": "ERROR", "logger": "processor", "message": "load project valid-name_1748060569 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T13:23:48.998552", "level": "ERROR", "logger": "processor", "message": "process valid-name_1748060569:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T13:23:48.999844", "level": "ERROR", "logger": "processor", "message": "load project validname123_1748060569 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T13:23:49.005052", "level": "ERROR", "logger": "processor", "message": "process validname123_1748060569:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T13:23:49.006084", "level": "ERROR", "logger": "processor", "message": "load project VALIDNAME_1748060569 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T13:23:49.009694", "level": "ERROR", "logger": "processor", "message": "process VALIDNAME_1748060569:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T13:23:49.010721", "level": "ERROR", "logger": "processor", "message": "load project Valid_Name_123_1748060569 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T13:23:49.014711", "level": "ERROR", "logger": "processor", "message": "process Valid_Name_123_1748060569:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T13:23:49.016254", "level": "ERROR", "logger": "processor", "message": "load project valid-name-123_1748060569 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T13:23:49.019789", "level": "ERROR", "logger": "processor", "message": "process valid-name-123_1748060569:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T13:23:49.020851", "level": "ERROR", "logger": "processor", "message": "load project a_1748060569 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T13:23:49.024557", "level": "ERROR", "logger": "processor", "message": "process a_1748060569:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T13:23:49.025576", "level": "ERROR", "logger": "processor", "message": "load project content_type_test_1748060591 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T13:23:49.030432", "level": "ERROR", "logger": "processor", "message": "process content_type_test_1748060591:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T13:23:49.031726", "level": "ERROR", "logger": "processor", "message": "load project form_test_1748060591 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T13:23:49.036239", "level": "ERROR", "logger": "processor", "message": "process form_test_1748060591:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T13:23:49.037117", "level": "ERROR", "logger": "processor", "message": "load project duplicate_test_1748060591 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T13:23:49.040527", "level": "ERROR", "logger": "processor", "message": "process duplicate_test_1748060591:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T13:23:49.041492", "level": "ERROR", "logger": "processor", "message": "load project auth_test_1748060591 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T13:23:49.044954", "level": "ERROR", "logger": "processor", "message": "process auth_test_1748060591:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T13:23:49.046027", "level": "ERROR", "logger": "processor", "message": "load project valid_name_1748060591 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T13:23:49.050560", "level": "ERROR", "logger": "processor", "message": "process valid_name_1748060591:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T13:23:49.051595", "level": "ERROR", "logger": "processor", "message": "load project valid-name_1748060591 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T13:23:49.055066", "level": "ERROR", "logger": "processor", "message": "process valid-name_1748060591:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T13:23:49.055928", "level": "ERROR", "logger": "processor", "message": "load project validname123_1748060591 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T13:23:49.059514", "level": "ERROR", "logger": "processor", "message": "process validname123_1748060591:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T13:23:49.060428", "level": "ERROR", "logger": "processor", "message": "load project VALIDNAME_1748060591 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T13:23:49.064509", "level": "ERROR", "logger": "processor", "message": "process VALIDNAME_1748060591:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T13:23:49.065606", "level": "ERROR", "logger": "processor", "message": "load project Valid_Name_123_1748060591 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T13:23:49.068985", "level": "ERROR", "logger": "processor", "message": "process Valid_Name_123_1748060591:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T13:23:49.070084", "level": "ERROR", "logger": "processor", "message": "load project valid-name-123_1748060591 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T13:23:49.073728", "level": "ERROR", "logger": "processor", "message": "process valid-name-123_1748060591:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T13:23:49.074723", "level": "ERROR", "logger": "processor", "message": "load project a_1748060591 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T13:23:49.078964", "level": "ERROR", "logger": "processor", "message": "process a_1748060591:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T13:23:49.079871", "level": "ERROR", "logger": "processor", "message": "load project content_type_test_1748060619 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T13:23:49.083187", "level": "ERROR", "logger": "processor", "message": "process content_type_test_1748060619:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T13:23:49.084122", "level": "ERROR", "logger": "processor", "message": "load project form_test_1748060619 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T13:23:49.087620", "level": "ERROR", "logger": "processor", "message": "process form_test_1748060619:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T13:23:49.088513", "level": "ERROR", "logger": "processor", "message": "load project duplicate_test_1748060619 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T13:23:49.092082", "level": "ERROR", "logger": "processor", "message": "process duplicate_test_1748060619:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T13:23:49.093518", "level": "ERROR", "logger": "processor", "message": "load project auth_test_1748060619 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T13:23:49.097384", "level": "ERROR", "logger": "processor", "message": "process auth_test_1748060619:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T13:23:49.098545", "level": "ERROR", "logger": "processor", "message": "load project valid_name_1748060619 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T13:23:49.102401", "level": "ERROR", "logger": "processor", "message": "process valid_name_1748060619:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T13:23:49.103523", "level": "ERROR", "logger": "processor", "message": "load project valid-name_1748060619 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T13:23:49.107381", "level": "ERROR", "logger": "processor", "message": "process valid-name_1748060619:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T13:23:49.108564", "level": "ERROR", "logger": "processor", "message": "load project validname123_1748060619 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T13:23:49.112285", "level": "ERROR", "logger": "processor", "message": "process validname123_1748060619:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T13:23:49.113297", "level": "ERROR", "logger": "processor", "message": "load project VALIDNAME_1748060619 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T13:23:49.117539", "level": "ERROR", "logger": "processor", "message": "process VALIDNAME_1748060619:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T13:23:49.118542", "level": "ERROR", "logger": "processor", "message": "load project Valid_Name_123_1748060619 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T13:23:49.122200", "level": "ERROR", "logger": "processor", "message": "process Valid_Name_123_1748060619:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T13:23:49.123136", "level": "ERROR", "logger": "processor", "message": "load project valid-name-123_1748060619 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T13:23:49.126840", "level": "ERROR", "logger": "processor", "message": "process valid-name-123_1748060619:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T13:23:49.127860", "level": "ERROR", "logger": "processor", "message": "load project a_1748060619 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T13:23:49.131286", "level": "ERROR", "logger": "processor", "message": "process a_1748060619:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T13:23:49.132294", "level": "ERROR", "logger": "processor", "message": "load project aaaaaaaaaaaaaaaaaaaaaaaaaaaaaa_1748060619 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T13:23:49.135790", "level": "ERROR", "logger": "processor", "message": "process aaaaaaaaaaaaaaaaaaaaaaaaaaaaaa_1748060619:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T13:28:48.647235", "level": "ERROR", "logger": "processor", "message": "load project final_test_success error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T13:28:48.651962", "level": "ERROR", "logger": "processor", "message": "process final_test_success:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T13:38:48.975729", "level": "ERROR", "logger": "processor", "message": "load project test_auth_project error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T13:38:48.981853", "level": "ERROR", "logger": "processor", "message": "process test_auth_project:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T13:41:43.701943", "level": "ERROR", "logger": "processor", "message": "process uiuiuuiuiuiuiuiuiu:c38374ecc3e9732c6059c21b00a77057 https://x.com/YahooNewsTopics -> [400] len:2540 -> result:None fol:0 msg:0 err:HTTPError('HTTP 599: HTTP 400: Bad Request')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T13:42:14.220519", "level": "ERROR", "logger": "processor", "message": "process uiuiuuiuiuiuiuiuiu:c38374ecc3e9732c6059c21b00a77057 https://x.com/YahooNewsTopics -> [400] len:2540 -> result:None fol:0 msg:0 err:HTTPError('HTTP 599: HTTP 400: Bad Request')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T13:51:54.033856", "level": "ERROR", "logger": "processor", "message": "process yahooo2:c38374ecc3e9732c6059c21b00a77057 https://x.com/YahooNewsTopics -> [400] len:2540 -> result:None fol:0 msg:0 err:HTTPError('HTTP 599: HTTP 400: Bad Request')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T13:55:51.499904", "level": "ERROR", "logger": "processor", "message": "load project unauthorized_test error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T13:55:51.506297", "level": "ERROR", "logger": "processor", "message": "process unauthorized_test:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T14:00:47.607654", "level": "ERROR", "logger": "processor", "message": "process yahoooo3:c38374ecc3e9732c6059c21b00a77057 https://x.com/YahooNewsTopics -> [400] len:2540 -> result:None fol:0 msg:0 err:HTTPError('HTTP 599: HTTP 400: Bad Request')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T14:00:51.512397", "level": "ERROR", "logger": "processor", "message": "load project auth_test_fixed error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T14:00:51.516288", "level": "ERROR", "logger": "processor", "message": "process auth_test_fixed:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T14:00:51.517546", "level": "ERROR", "logger": "processor", "message": "load project no_auth_test error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T14:00:51.521306", "level": "ERROR", "logger": "processor", "message": "process no_auth_test:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T14:00:51.522318", "level": "ERROR", "logger": "processor", "message": "load project no_auth_should_fail error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T14:00:51.526365", "level": "ERROR", "logger": "processor", "message": "process no_auth_should_fail:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T14:00:51.527419", "level": "ERROR", "logger": "processor", "message": "load project auth_success_test error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T14:00:51.531066", "level": "ERROR", "logger": "processor", "message": "process auth_success_test:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T14:42:14.885571", "level": "ERROR", "logger": "processor", "message": "process uiuiuuiuiuiuiuiuiu:c38374ecc3e9732c6059c21b00a77057 https://x.com/YahooNewsTopics -> [400] len:2540 -> result:None fol:0 msg:0 err:HTTPError('HTTP 599: HTTP 400: Bad Request')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T15:15:52.168366", "level": "ERROR", "logger": "processor", "message": "load project test_new_code error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T15:15:52.178908", "level": "ERROR", "logger": "processor", "message": "process test_new_code:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T16:05:17.920230", "level": "ERROR", "logger": "scheduler", "message": "project aiueo6 not started, please set status to RUNNING or DEBUG", "module": "scheduler", "function": "task_verify", "line": 310, "extra": {"taskName": "Task-1454"}}
{"timestamp": "2025-05-24T16:07:04.081158", "level": "ERROR", "logger": "processor", "message": "process aiueo6:c38374ecc3e9732c6059c21b00a77057 https://x.com/YahooNewsTopics -> [400] len:2540 -> result:None fol:0 msg:0 err:HTTPError('HTTP 599: HTTP 400: Bad Request')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T16:07:34.801192", "level": "ERROR", "logger": "processor", "message": "process aiueo6:c38374ecc3e9732c6059c21b00a77057 https://x.com/YahooNewsTopics -> [400] len:2540 -> result:None fol:0 msg:0 err:HTTPError('HTTP 599: HTTP 400: Bad Request')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T16:08:39.041736", "level": "ERROR", "logger": "processor", "message": "process hemohemohemo:c38374ecc3e9732c6059c21b00a77057 https://x.com/YahooNewsTopics -> [400] len:2540 -> result:None fol:0 msg:0 err:HTTPError('HTTP 599: HTTP 400: Bad Request')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T16:09:09.494849", "level": "ERROR", "logger": "processor", "message": "process hemohemohemo:c38374ecc3e9732c6059c21b00a77057 https://x.com/YahooNewsTopics -> [400] len:2540 -> result:None fol:0 msg:0 err:HTTPError('HTTP 599: HTTP 400: Bad Request')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T17:07:35.287225", "level": "ERROR", "logger": "processor", "message": "process aiueo6:c38374ecc3e9732c6059c21b00a77057 https://x.com/YahooNewsTopics -> [400] len:2540 -> result:None fol:0 msg:0 err:HTTPError('HTTP 599: HTTP 400: Bad Request')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T17:09:10.272873", "level": "ERROR", "logger": "processor", "message": "process hemohemohemo:c38374ecc3e9732c6059c21b00a77057 https://x.com/YahooNewsTopics -> [400] len:2540 -> result:None fol:0 msg:0 err:HTTPError('HTTP 599: HTTP 400: Bad Request')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T18:54:34.777576", "level": "ERROR", "logger": "processor", "message": "load project test_auth_success error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T18:54:34.782270", "level": "ERROR", "logger": "processor", "message": "process test_auth_success:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T18:54:34.783906", "level": "ERROR", "logger": "processor", "message": "load project content_type_test_1748060447 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T18:54:34.787942", "level": "ERROR", "logger": "processor", "message": "process content_type_test_1748060447:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T18:54:34.788880", "level": "ERROR", "logger": "processor", "message": "load project form_test_1748060447 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T18:54:34.793311", "level": "ERROR", "logger": "processor", "message": "process form_test_1748060447:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T18:54:34.794421", "level": "ERROR", "logger": "processor", "message": "load project duplicate_test_1748060447 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T18:54:34.799587", "level": "ERROR", "logger": "processor", "message": "process duplicate_test_1748060447:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T18:54:34.801021", "level": "ERROR", "logger": "processor", "message": "load project auth_test_1748060447 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T18:54:34.805769", "level": "ERROR", "logger": "processor", "message": "process auth_test_1748060447:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T18:54:34.806774", "level": "ERROR", "logger": "processor", "message": "load project valid_name_1748060447 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T18:54:34.810271", "level": "ERROR", "logger": "processor", "message": "process valid_name_1748060447:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T18:54:34.811507", "level": "ERROR", "logger": "processor", "message": "load project valid-name_1748060447 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T18:54:34.814891", "level": "ERROR", "logger": "processor", "message": "process valid-name_1748060447:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T18:54:34.815798", "level": "ERROR", "logger": "processor", "message": "load project validname123_1748060447 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T18:54:34.820924", "level": "ERROR", "logger": "processor", "message": "process validname123_1748060447:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T18:54:34.822228", "level": "ERROR", "logger": "processor", "message": "load project VALIDNAME_1748060447 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T18:54:34.827072", "level": "ERROR", "logger": "processor", "message": "process VALIDNAME_1748060447:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T18:54:34.828547", "level": "ERROR", "logger": "processor", "message": "load project Valid_Name_123_1748060447 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T18:54:34.834101", "level": "ERROR", "logger": "processor", "message": "process Valid_Name_123_1748060447:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T18:54:34.835327", "level": "ERROR", "logger": "processor", "message": "load project valid-name-123_1748060447 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T18:54:34.838631", "level": "ERROR", "logger": "processor", "message": "process valid-name-123_1748060447:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T18:54:34.839768", "level": "ERROR", "logger": "processor", "message": "load project a_1748060447 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T18:54:34.843804", "level": "ERROR", "logger": "processor", "message": "process a_1748060447:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T18:54:34.844699", "level": "ERROR", "logger": "processor", "message": "load project content_type_test_1748060569 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T18:54:34.849330", "level": "ERROR", "logger": "processor", "message": "process content_type_test_1748060569:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T18:54:34.850487", "level": "ERROR", "logger": "processor", "message": "load project form_test_1748060569 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T18:54:34.855569", "level": "ERROR", "logger": "processor", "message": "process form_test_1748060569:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T18:54:34.856750", "level": "ERROR", "logger": "processor", "message": "load project duplicate_test_1748060569 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T18:54:34.860769", "level": "ERROR", "logger": "processor", "message": "process duplicate_test_1748060569:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T18:54:34.861927", "level": "ERROR", "logger": "processor", "message": "load project auth_test_1748060569 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T18:54:34.865869", "level": "ERROR", "logger": "processor", "message": "process auth_test_1748060569:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T18:54:34.867031", "level": "ERROR", "logger": "processor", "message": "load project valid_name_1748060569 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T18:54:34.871573", "level": "ERROR", "logger": "processor", "message": "process valid_name_1748060569:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T18:54:34.873449", "level": "ERROR", "logger": "processor", "message": "load project valid-name_1748060569 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T18:54:34.877358", "level": "ERROR", "logger": "processor", "message": "process valid-name_1748060569:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T18:54:34.878498", "level": "ERROR", "logger": "processor", "message": "load project validname123_1748060569 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T18:54:34.882749", "level": "ERROR", "logger": "processor", "message": "process validname123_1748060569:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T18:54:34.884155", "level": "ERROR", "logger": "processor", "message": "load project VALIDNAME_1748060569 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T18:54:34.887523", "level": "ERROR", "logger": "processor", "message": "process VALIDNAME_1748060569:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T18:54:34.888763", "level": "ERROR", "logger": "processor", "message": "load project Valid_Name_123_1748060569 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T18:54:34.892267", "level": "ERROR", "logger": "processor", "message": "process Valid_Name_123_1748060569:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T18:54:34.893714", "level": "ERROR", "logger": "processor", "message": "load project valid-name-123_1748060569 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T18:54:34.897113", "level": "ERROR", "logger": "processor", "message": "process valid-name-123_1748060569:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T18:54:34.898038", "level": "ERROR", "logger": "processor", "message": "load project a_1748060569 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T18:54:34.901329", "level": "ERROR", "logger": "processor", "message": "process a_1748060569:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T18:54:34.902450", "level": "ERROR", "logger": "processor", "message": "load project content_type_test_1748060591 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T18:54:34.907542", "level": "ERROR", "logger": "processor", "message": "process content_type_test_1748060591:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T18:54:34.909230", "level": "ERROR", "logger": "processor", "message": "load project form_test_1748060591 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T18:54:34.914867", "level": "ERROR", "logger": "processor", "message": "process form_test_1748060591:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T18:54:34.916452", "level": "ERROR", "logger": "processor", "message": "load project duplicate_test_1748060591 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T18:54:34.921729", "level": "ERROR", "logger": "processor", "message": "process duplicate_test_1748060591:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T18:54:34.922940", "level": "ERROR", "logger": "processor", "message": "load project auth_test_1748060591 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T18:54:34.928275", "level": "ERROR", "logger": "processor", "message": "process auth_test_1748060591:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T18:54:34.929637", "level": "ERROR", "logger": "processor", "message": "load project valid_name_1748060591 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T18:54:34.933814", "level": "ERROR", "logger": "processor", "message": "process valid_name_1748060591:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T18:54:34.935698", "level": "ERROR", "logger": "processor", "message": "load project valid-name_1748060591 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T18:54:34.939889", "level": "ERROR", "logger": "processor", "message": "process valid-name_1748060591:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T18:54:34.940923", "level": "ERROR", "logger": "processor", "message": "load project validname123_1748060591 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T18:54:34.946357", "level": "ERROR", "logger": "processor", "message": "process validname123_1748060591:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T18:54:34.947353", "level": "ERROR", "logger": "processor", "message": "load project VALIDNAME_1748060591 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T18:54:34.951842", "level": "ERROR", "logger": "processor", "message": "process VALIDNAME_1748060591:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T18:54:34.953143", "level": "ERROR", "logger": "processor", "message": "load project Valid_Name_123_1748060591 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T18:54:34.956997", "level": "ERROR", "logger": "processor", "message": "process Valid_Name_123_1748060591:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T18:54:34.957860", "level": "ERROR", "logger": "processor", "message": "load project valid-name-123_1748060591 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T18:54:34.960759", "level": "ERROR", "logger": "processor", "message": "process valid-name-123_1748060591:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T18:54:34.961534", "level": "ERROR", "logger": "processor", "message": "load project a_1748060591 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T18:54:34.964912", "level": "ERROR", "logger": "processor", "message": "process a_1748060591:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T18:54:34.965885", "level": "ERROR", "logger": "processor", "message": "load project content_type_test_1748060619 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T18:54:34.968827", "level": "ERROR", "logger": "processor", "message": "process content_type_test_1748060619:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T18:54:34.969865", "level": "ERROR", "logger": "processor", "message": "load project form_test_1748060619 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T18:54:34.972855", "level": "ERROR", "logger": "processor", "message": "process form_test_1748060619:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T18:54:34.974024", "level": "ERROR", "logger": "processor", "message": "load project duplicate_test_1748060619 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T18:54:34.977429", "level": "ERROR", "logger": "processor", "message": "process duplicate_test_1748060619:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T18:54:34.978262", "level": "ERROR", "logger": "processor", "message": "load project auth_test_1748060619 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T18:54:34.981577", "level": "ERROR", "logger": "processor", "message": "process auth_test_1748060619:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T18:54:34.982509", "level": "ERROR", "logger": "processor", "message": "load project valid_name_1748060619 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T18:54:34.986101", "level": "ERROR", "logger": "processor", "message": "process valid_name_1748060619:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T18:54:34.986975", "level": "ERROR", "logger": "processor", "message": "load project valid-name_1748060619 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T18:54:34.989988", "level": "ERROR", "logger": "processor", "message": "process valid-name_1748060619:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T18:54:34.991046", "level": "ERROR", "logger": "processor", "message": "load project validname123_1748060619 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T18:54:34.995024", "level": "ERROR", "logger": "processor", "message": "process validname123_1748060619:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T18:54:34.996430", "level": "ERROR", "logger": "processor", "message": "load project VALIDNAME_1748060619 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T18:54:35.000014", "level": "ERROR", "logger": "processor", "message": "process VALIDNAME_1748060619:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T18:54:35.000859", "level": "ERROR", "logger": "processor", "message": "load project Valid_Name_123_1748060619 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T18:54:35.003788", "level": "ERROR", "logger": "processor", "message": "process Valid_Name_123_1748060619:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T18:54:35.004949", "level": "ERROR", "logger": "processor", "message": "load project valid-name-123_1748060619 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T18:54:35.008088", "level": "ERROR", "logger": "processor", "message": "process valid-name-123_1748060619:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T18:54:35.008927", "level": "ERROR", "logger": "processor", "message": "load project a_1748060619 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T18:54:35.012818", "level": "ERROR", "logger": "processor", "message": "process a_1748060619:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T18:54:35.013821", "level": "ERROR", "logger": "processor", "message": "load project aaaaaaaaaaaaaaaaaaaaaaaaaaaaaa_1748060619 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T18:54:35.016801", "level": "ERROR", "logger": "processor", "message": "process aaaaaaaaaaaaaaaaaaaaaaaaaaaaaa_1748060619:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T18:54:35.017624", "level": "ERROR", "logger": "processor", "message": "load project final_test_success error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T18:54:35.020983", "level": "ERROR", "logger": "processor", "message": "process final_test_success:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T18:54:35.021738", "level": "ERROR", "logger": "processor", "message": "load project test_auth_project error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T18:54:35.024510", "level": "ERROR", "logger": "processor", "message": "process test_auth_project:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T18:54:35.029358", "level": "ERROR", "logger": "processor", "message": "load project unauthorized_test error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T18:54:35.033029", "level": "ERROR", "logger": "processor", "message": "process unauthorized_test:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T18:54:35.033958", "level": "ERROR", "logger": "processor", "message": "load project auth_test_fixed error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T18:54:35.037053", "level": "ERROR", "logger": "processor", "message": "process auth_test_fixed:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T18:54:35.037982", "level": "ERROR", "logger": "processor", "message": "load project no_auth_test error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T18:54:35.041581", "level": "ERROR", "logger": "processor", "message": "process no_auth_test:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T18:54:35.042353", "level": "ERROR", "logger": "processor", "message": "load project no_auth_should_fail error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T18:54:35.047417", "level": "ERROR", "logger": "processor", "message": "process no_auth_should_fail:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T18:54:35.048406", "level": "ERROR", "logger": "processor", "message": "load project auth_success_test error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T18:54:35.051451", "level": "ERROR", "logger": "processor", "message": "process auth_success_test:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T18:54:35.052527", "level": "ERROR", "logger": "processor", "message": "load project test_new_code error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T18:54:35.055506", "level": "ERROR", "logger": "processor", "message": "process test_new_code:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:04:22.633069", "level": "ERROR", "logger": "processor", "message": "load project test_auth_success error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:04:22.637287", "level": "ERROR", "logger": "processor", "message": "process test_auth_success:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:04:22.638949", "level": "ERROR", "logger": "processor", "message": "load project content_type_test_1748060447 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:04:22.642915", "level": "ERROR", "logger": "processor", "message": "process content_type_test_1748060447:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:04:22.644232", "level": "ERROR", "logger": "processor", "message": "load project form_test_1748060447 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:04:22.650260", "level": "ERROR", "logger": "processor", "message": "process form_test_1748060447:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:04:22.651375", "level": "ERROR", "logger": "processor", "message": "load project duplicate_test_1748060447 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:04:22.655877", "level": "ERROR", "logger": "processor", "message": "process duplicate_test_1748060447:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:04:22.657089", "level": "ERROR", "logger": "processor", "message": "load project auth_test_1748060447 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:04:22.661742", "level": "ERROR", "logger": "processor", "message": "process auth_test_1748060447:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:04:22.662761", "level": "ERROR", "logger": "processor", "message": "load project valid_name_1748060447 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:04:22.667201", "level": "ERROR", "logger": "processor", "message": "process valid_name_1748060447:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:04:22.668375", "level": "ERROR", "logger": "processor", "message": "load project valid-name_1748060447 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:04:22.672286", "level": "ERROR", "logger": "processor", "message": "process valid-name_1748060447:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:04:22.673905", "level": "ERROR", "logger": "processor", "message": "load project validname123_1748060447 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:04:22.679160", "level": "ERROR", "logger": "processor", "message": "process validname123_1748060447:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:04:22.680412", "level": "ERROR", "logger": "processor", "message": "load project VALIDNAME_1748060447 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:04:22.683725", "level": "ERROR", "logger": "processor", "message": "process VALIDNAME_1748060447:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:04:22.684562", "level": "ERROR", "logger": "processor", "message": "load project Valid_Name_123_1748060447 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:04:22.688620", "level": "ERROR", "logger": "processor", "message": "process Valid_Name_123_1748060447:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:04:22.689876", "level": "ERROR", "logger": "processor", "message": "load project valid-name-123_1748060447 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:04:22.695088", "level": "ERROR", "logger": "processor", "message": "process valid-name-123_1748060447:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:04:22.696038", "level": "ERROR", "logger": "processor", "message": "load project a_1748060447 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:04:22.699565", "level": "ERROR", "logger": "processor", "message": "process a_1748060447:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:04:22.700484", "level": "ERROR", "logger": "processor", "message": "load project content_type_test_1748060569 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:04:22.703996", "level": "ERROR", "logger": "processor", "message": "process content_type_test_1748060569:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:04:22.704841", "level": "ERROR", "logger": "processor", "message": "load project form_test_1748060569 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:04:22.709128", "level": "ERROR", "logger": "processor", "message": "process form_test_1748060569:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:04:22.710040", "level": "ERROR", "logger": "processor", "message": "load project duplicate_test_1748060569 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:04:22.713572", "level": "ERROR", "logger": "processor", "message": "process duplicate_test_1748060569:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:04:22.714766", "level": "ERROR", "logger": "processor", "message": "load project auth_test_1748060569 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:04:22.719778", "level": "ERROR", "logger": "processor", "message": "process auth_test_1748060569:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:04:22.720800", "level": "ERROR", "logger": "processor", "message": "load project valid_name_1748060569 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:04:22.724056", "level": "ERROR", "logger": "processor", "message": "process valid_name_1748060569:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:04:22.724837", "level": "ERROR", "logger": "processor", "message": "load project valid-name_1748060569 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:04:22.727726", "level": "ERROR", "logger": "processor", "message": "process valid-name_1748060569:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:04:22.728722", "level": "ERROR", "logger": "processor", "message": "load project validname123_1748060569 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:04:22.731941", "level": "ERROR", "logger": "processor", "message": "process validname123_1748060569:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:04:22.732983", "level": "ERROR", "logger": "processor", "message": "load project VALIDNAME_1748060569 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:04:22.736834", "level": "ERROR", "logger": "processor", "message": "process VALIDNAME_1748060569:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:04:22.737772", "level": "ERROR", "logger": "processor", "message": "load project Valid_Name_123_1748060569 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:04:22.740905", "level": "ERROR", "logger": "processor", "message": "process Valid_Name_123_1748060569:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:04:22.741718", "level": "ERROR", "logger": "processor", "message": "load project valid-name-123_1748060569 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:04:22.746500", "level": "ERROR", "logger": "processor", "message": "process valid-name-123_1748060569:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:04:22.747505", "level": "ERROR", "logger": "processor", "message": "load project a_1748060569 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:04:22.751051", "level": "ERROR", "logger": "processor", "message": "process a_1748060569:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:04:22.752126", "level": "ERROR", "logger": "processor", "message": "load project content_type_test_1748060591 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:04:22.756508", "level": "ERROR", "logger": "processor", "message": "process content_type_test_1748060591:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:04:22.757772", "level": "ERROR", "logger": "processor", "message": "load project form_test_1748060591 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:04:22.765424", "level": "ERROR", "logger": "processor", "message": "process form_test_1748060591:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:04:22.766814", "level": "ERROR", "logger": "processor", "message": "load project duplicate_test_1748060591 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:04:22.770717", "level": "ERROR", "logger": "processor", "message": "process duplicate_test_1748060591:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:04:22.771756", "level": "ERROR", "logger": "processor", "message": "load project auth_test_1748060591 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:04:22.776171", "level": "ERROR", "logger": "processor", "message": "process auth_test_1748060591:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:04:22.777442", "level": "ERROR", "logger": "processor", "message": "load project valid_name_1748060591 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:04:22.782713", "level": "ERROR", "logger": "processor", "message": "process valid_name_1748060591:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:04:22.784008", "level": "ERROR", "logger": "processor", "message": "load project valid-name_1748060591 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:04:22.790131", "level": "ERROR", "logger": "processor", "message": "process valid-name_1748060591:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:04:22.791163", "level": "ERROR", "logger": "processor", "message": "load project validname123_1748060591 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:04:22.795994", "level": "ERROR", "logger": "processor", "message": "process validname123_1748060591:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:04:22.797009", "level": "ERROR", "logger": "processor", "message": "load project VALIDNAME_1748060591 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:04:22.800397", "level": "ERROR", "logger": "processor", "message": "process VALIDNAME_1748060591:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:04:22.801230", "level": "ERROR", "logger": "processor", "message": "load project Valid_Name_123_1748060591 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:04:22.804415", "level": "ERROR", "logger": "processor", "message": "process Valid_Name_123_1748060591:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:04:22.805277", "level": "ERROR", "logger": "processor", "message": "load project valid-name-123_1748060591 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:04:22.809615", "level": "ERROR", "logger": "processor", "message": "process valid-name-123_1748060591:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:04:22.810584", "level": "ERROR", "logger": "processor", "message": "load project a_1748060591 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:04:22.814237", "level": "ERROR", "logger": "processor", "message": "process a_1748060591:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:04:22.815177", "level": "ERROR", "logger": "processor", "message": "load project content_type_test_1748060619 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:04:22.818294", "level": "ERROR", "logger": "processor", "message": "process content_type_test_1748060619:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:04:22.819104", "level": "ERROR", "logger": "processor", "message": "load project form_test_1748060619 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:04:22.824359", "level": "ERROR", "logger": "processor", "message": "process form_test_1748060619:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:04:22.825516", "level": "ERROR", "logger": "processor", "message": "load project duplicate_test_1748060619 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:04:22.829129", "level": "ERROR", "logger": "processor", "message": "process duplicate_test_1748060619:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:04:22.830184", "level": "ERROR", "logger": "processor", "message": "load project auth_test_1748060619 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:04:22.833533", "level": "ERROR", "logger": "processor", "message": "process auth_test_1748060619:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:04:22.834971", "level": "ERROR", "logger": "processor", "message": "load project valid_name_1748060619 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:04:22.839658", "level": "ERROR", "logger": "processor", "message": "process valid_name_1748060619:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:04:22.841146", "level": "ERROR", "logger": "processor", "message": "load project valid-name_1748060619 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:04:22.845016", "level": "ERROR", "logger": "processor", "message": "process valid-name_1748060619:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:04:22.846400", "level": "ERROR", "logger": "processor", "message": "load project validname123_1748060619 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:04:22.852634", "level": "ERROR", "logger": "processor", "message": "process validname123_1748060619:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:04:22.853959", "level": "ERROR", "logger": "processor", "message": "load project VALIDNAME_1748060619 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:04:22.858748", "level": "ERROR", "logger": "processor", "message": "process VALIDNAME_1748060619:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:04:22.859961", "level": "ERROR", "logger": "processor", "message": "load project Valid_Name_123_1748060619 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:04:22.864003", "level": "ERROR", "logger": "processor", "message": "process Valid_Name_123_1748060619:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:04:22.865226", "level": "ERROR", "logger": "processor", "message": "load project valid-name-123_1748060619 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:04:22.870608", "level": "ERROR", "logger": "processor", "message": "process valid-name-123_1748060619:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:04:22.871713", "level": "ERROR", "logger": "processor", "message": "load project a_1748060619 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:04:22.874743", "level": "ERROR", "logger": "processor", "message": "process a_1748060619:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:04:22.875826", "level": "ERROR", "logger": "processor", "message": "load project aaaaaaaaaaaaaaaaaaaaaaaaaaaaaa_1748060619 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:04:22.880791", "level": "ERROR", "logger": "processor", "message": "process aaaaaaaaaaaaaaaaaaaaaaaaaaaaaa_1748060619:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:04:22.881769", "level": "ERROR", "logger": "processor", "message": "load project final_test_success error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:04:22.885483", "level": "ERROR", "logger": "processor", "message": "process final_test_success:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:04:22.886878", "level": "ERROR", "logger": "processor", "message": "load project test_auth_project error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:04:22.891307", "level": "ERROR", "logger": "processor", "message": "process test_auth_project:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:04:22.895022", "level": "ERROR", "logger": "processor", "message": "load project unauthorized_test error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:04:22.898748", "level": "ERROR", "logger": "processor", "message": "process unauthorized_test:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:04:22.899738", "level": "ERROR", "logger": "processor", "message": "load project auth_test_fixed error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:04:22.902938", "level": "ERROR", "logger": "processor", "message": "process auth_test_fixed:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:04:22.904252", "level": "ERROR", "logger": "processor", "message": "load project no_auth_test error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:04:22.909726", "level": "ERROR", "logger": "processor", "message": "process no_auth_test:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:04:22.911066", "level": "ERROR", "logger": "processor", "message": "load project no_auth_should_fail error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:04:22.914321", "level": "ERROR", "logger": "processor", "message": "process no_auth_should_fail:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:04:22.915215", "level": "ERROR", "logger": "processor", "message": "load project auth_success_test error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:04:22.918896", "level": "ERROR", "logger": "processor", "message": "process auth_success_test:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:04:22.919913", "level": "ERROR", "logger": "processor", "message": "load project test_new_code error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:04:22.922895", "level": "ERROR", "logger": "processor", "message": "process test_new_code:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:15:12.518348", "level": "ERROR", "logger": "processor", "message": "load project test_auth_success error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:15:12.521974", "level": "ERROR", "logger": "processor", "message": "process test_auth_success:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:15:12.522825", "level": "ERROR", "logger": "processor", "message": "load project content_type_test_1748060447 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:15:12.526215", "level": "ERROR", "logger": "processor", "message": "process content_type_test_1748060447:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:15:12.527136", "level": "ERROR", "logger": "processor", "message": "load project form_test_1748060447 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:15:12.530042", "level": "ERROR", "logger": "processor", "message": "process form_test_1748060447:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:15:12.530925", "level": "ERROR", "logger": "processor", "message": "load project duplicate_test_1748060447 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:15:12.534063", "level": "ERROR", "logger": "processor", "message": "process duplicate_test_1748060447:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:15:12.534872", "level": "ERROR", "logger": "processor", "message": "load project auth_test_1748060447 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:15:12.538219", "level": "ERROR", "logger": "processor", "message": "process auth_test_1748060447:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:15:12.539091", "level": "ERROR", "logger": "processor", "message": "load project valid_name_1748060447 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:15:12.542189", "level": "ERROR", "logger": "processor", "message": "process valid_name_1748060447:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:15:12.542997", "level": "ERROR", "logger": "processor", "message": "load project valid-name_1748060447 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:15:12.546262", "level": "ERROR", "logger": "processor", "message": "process valid-name_1748060447:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:15:12.547170", "level": "ERROR", "logger": "processor", "message": "load project validname123_1748060447 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:15:12.550388", "level": "ERROR", "logger": "processor", "message": "process validname123_1748060447:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:15:12.551419", "level": "ERROR", "logger": "processor", "message": "load project VALIDNAME_1748060447 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:15:12.554685", "level": "ERROR", "logger": "processor", "message": "process VALIDNAME_1748060447:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:15:12.555567", "level": "ERROR", "logger": "processor", "message": "load project Valid_Name_123_1748060447 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:15:12.559026", "level": "ERROR", "logger": "processor", "message": "process Valid_Name_123_1748060447:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:15:12.559920", "level": "ERROR", "logger": "processor", "message": "load project valid-name-123_1748060447 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:15:12.562962", "level": "ERROR", "logger": "processor", "message": "process valid-name-123_1748060447:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:15:12.563818", "level": "ERROR", "logger": "processor", "message": "load project a_1748060447 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:15:12.567160", "level": "ERROR", "logger": "processor", "message": "process a_1748060447:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:15:12.568045", "level": "ERROR", "logger": "processor", "message": "load project content_type_test_1748060569 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:15:12.570981", "level": "ERROR", "logger": "processor", "message": "process content_type_test_1748060569:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:15:12.571810", "level": "ERROR", "logger": "processor", "message": "load project form_test_1748060569 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:15:12.575555", "level": "ERROR", "logger": "processor", "message": "process form_test_1748060569:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:15:12.576411", "level": "ERROR", "logger": "processor", "message": "load project duplicate_test_1748060569 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:15:12.579737", "level": "ERROR", "logger": "processor", "message": "process duplicate_test_1748060569:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:15:12.580761", "level": "ERROR", "logger": "processor", "message": "load project auth_test_1748060569 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:15:12.583797", "level": "ERROR", "logger": "processor", "message": "process auth_test_1748060569:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:15:12.584674", "level": "ERROR", "logger": "processor", "message": "load project valid_name_1748060569 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:15:12.588373", "level": "ERROR", "logger": "processor", "message": "process valid_name_1748060569:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:15:12.589258", "level": "ERROR", "logger": "processor", "message": "load project valid-name_1748060569 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:15:12.592303", "level": "ERROR", "logger": "processor", "message": "process valid-name_1748060569:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:15:12.593298", "level": "ERROR", "logger": "processor", "message": "load project validname123_1748060569 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:15:12.597008", "level": "ERROR", "logger": "processor", "message": "process validname123_1748060569:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:15:12.597946", "level": "ERROR", "logger": "processor", "message": "load project VALIDNAME_1748060569 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:15:12.601483", "level": "ERROR", "logger": "processor", "message": "process VALIDNAME_1748060569:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:15:12.602420", "level": "ERROR", "logger": "processor", "message": "load project Valid_Name_123_1748060569 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:15:12.605635", "level": "ERROR", "logger": "processor", "message": "process Valid_Name_123_1748060569:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:15:12.606646", "level": "ERROR", "logger": "processor", "message": "load project valid-name-123_1748060569 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:15:12.610142", "level": "ERROR", "logger": "processor", "message": "process valid-name-123_1748060569:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:15:12.611137", "level": "ERROR", "logger": "processor", "message": "load project a_1748060569 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:15:12.614554", "level": "ERROR", "logger": "processor", "message": "process a_1748060569:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:15:12.615650", "level": "ERROR", "logger": "processor", "message": "load project content_type_test_1748060591 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:15:12.618824", "level": "ERROR", "logger": "processor", "message": "process content_type_test_1748060591:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:15:12.619664", "level": "ERROR", "logger": "processor", "message": "load project form_test_1748060591 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:15:12.624872", "level": "ERROR", "logger": "processor", "message": "process form_test_1748060591:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:15:12.625840", "level": "ERROR", "logger": "processor", "message": "load project duplicate_test_1748060591 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:15:12.630655", "level": "ERROR", "logger": "processor", "message": "process duplicate_test_1748060591:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:15:12.631670", "level": "ERROR", "logger": "processor", "message": "load project auth_test_1748060591 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:15:12.635968", "level": "ERROR", "logger": "processor", "message": "process auth_test_1748060591:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:15:12.637077", "level": "ERROR", "logger": "processor", "message": "load project valid_name_1748060591 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:15:12.640639", "level": "ERROR", "logger": "processor", "message": "process valid_name_1748060591:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:15:12.641831", "level": "ERROR", "logger": "processor", "message": "load project valid-name_1748060591 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:15:12.646180", "level": "ERROR", "logger": "processor", "message": "process valid-name_1748060591:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:15:12.647299", "level": "ERROR", "logger": "processor", "message": "load project validname123_1748060591 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:15:12.651289", "level": "ERROR", "logger": "processor", "message": "process validname123_1748060591:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:15:12.652321", "level": "ERROR", "logger": "processor", "message": "load project VALIDNAME_1748060591 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:15:12.656144", "level": "ERROR", "logger": "processor", "message": "process VALIDNAME_1748060591:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:15:12.657233", "level": "ERROR", "logger": "processor", "message": "load project Valid_Name_123_1748060591 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:15:12.660571", "level": "ERROR", "logger": "processor", "message": "process Valid_Name_123_1748060591:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:15:12.661404", "level": "ERROR", "logger": "processor", "message": "load project valid-name-123_1748060591 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:15:12.665438", "level": "ERROR", "logger": "processor", "message": "process valid-name-123_1748060591:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:15:12.666442", "level": "ERROR", "logger": "processor", "message": "load project a_1748060591 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:15:12.670092", "level": "ERROR", "logger": "processor", "message": "process a_1748060591:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:15:12.671022", "level": "ERROR", "logger": "processor", "message": "load project content_type_test_1748060619 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:15:12.674645", "level": "ERROR", "logger": "processor", "message": "process content_type_test_1748060619:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:15:12.675617", "level": "ERROR", "logger": "processor", "message": "load project form_test_1748060619 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:15:12.679201", "level": "ERROR", "logger": "processor", "message": "process form_test_1748060619:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:15:12.680338", "level": "ERROR", "logger": "processor", "message": "load project duplicate_test_1748060619 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:15:12.684058", "level": "ERROR", "logger": "processor", "message": "process duplicate_test_1748060619:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:15:12.685065", "level": "ERROR", "logger": "processor", "message": "load project auth_test_1748060619 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:15:12.688234", "level": "ERROR", "logger": "processor", "message": "process auth_test_1748060619:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:15:12.689159", "level": "ERROR", "logger": "processor", "message": "load project valid_name_1748060619 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:15:12.692517", "level": "ERROR", "logger": "processor", "message": "process valid_name_1748060619:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:15:12.693346", "level": "ERROR", "logger": "processor", "message": "load project valid-name_1748060619 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:15:12.696444", "level": "ERROR", "logger": "processor", "message": "process valid-name_1748060619:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:15:12.697543", "level": "ERROR", "logger": "processor", "message": "load project validname123_1748060619 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:15:12.700781", "level": "ERROR", "logger": "processor", "message": "process validname123_1748060619:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:15:12.701688", "level": "ERROR", "logger": "processor", "message": "load project VALIDNAME_1748060619 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:15:12.705998", "level": "ERROR", "logger": "processor", "message": "process VALIDNAME_1748060619:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:15:12.707598", "level": "ERROR", "logger": "processor", "message": "load project Valid_Name_123_1748060619 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:15:12.714695", "level": "ERROR", "logger": "processor", "message": "process Valid_Name_123_1748060619:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:15:12.715699", "level": "ERROR", "logger": "processor", "message": "load project valid-name-123_1748060619 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:15:12.719215", "level": "ERROR", "logger": "processor", "message": "process valid-name-123_1748060619:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:15:12.720379", "level": "ERROR", "logger": "processor", "message": "load project a_1748060619 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:15:12.723776", "level": "ERROR", "logger": "processor", "message": "process a_1748060619:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:15:12.724723", "level": "ERROR", "logger": "processor", "message": "load project aaaaaaaaaaaaaaaaaaaaaaaaaaaaaa_1748060619 error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:15:12.728465", "level": "ERROR", "logger": "processor", "message": "process aaaaaaaaaaaaaaaaaaaaaaaaaaaaaa_1748060619:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:15:12.729374", "level": "ERROR", "logger": "processor", "message": "load project final_test_success error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:15:12.733504", "level": "ERROR", "logger": "processor", "message": "process final_test_success:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:15:12.734530", "level": "ERROR", "logger": "processor", "message": "load project test_auth_project error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:15:12.739273", "level": "ERROR", "logger": "processor", "message": "process test_auth_project:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:15:12.745680", "level": "ERROR", "logger": "processor", "message": "load project unauthorized_test error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:15:12.749957", "level": "ERROR", "logger": "processor", "message": "process unauthorized_test:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:15:12.751485", "level": "ERROR", "logger": "processor", "message": "load project auth_test_fixed error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:15:12.755764", "level": "ERROR", "logger": "processor", "message": "process auth_test_fixed:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:15:12.756911", "level": "ERROR", "logger": "processor", "message": "load project no_auth_test error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:15:12.761256", "level": "ERROR", "logger": "processor", "message": "process no_auth_test:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:15:12.762457", "level": "ERROR", "logger": "processor", "message": "load project no_auth_should_fail error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:15:12.767063", "level": "ERROR", "logger": "processor", "message": "process no_auth_should_fail:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:15:12.768382", "level": "ERROR", "logger": "processor", "message": "load project auth_success_test error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:15:12.772222", "level": "ERROR", "logger": "processor", "message": "process auth_success_test:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:15:12.773261", "level": "ERROR", "logger": "processor", "message": "load project test_new_code error", "module": "project_module", "function": "_load_project", "line": 132, "exception": "Traceback (most recent call last):\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 129, in _load_project\n    ret = self.build_module(project, self.env)\n  File \"/home/<USER>/workplace/python/pyspiderNX2/pyspider/processor/project_module.py\", line 71, in build_module\n    assert _class is not None, \"need BaseHandler in project module\"\n           ^^^^^^^^^^^^^^^^^^\nAssertionError: need BaseHandler in project module", "extra": {"taskName": null}}
{"timestamp": "2025-05-24T19:15:12.777149", "level": "ERROR", "logger": "processor", "message": "process test_new_code:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:AssertionError('need BaseHandler in project module')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-25T02:03:28.525251", "level": "ERROR", "logger": "processor", "message": "process tesr:c38374ecc3e9732c6059c21b00a77057 https://x.com/YahooNewsTopics -> [400] len:2540 -> result:None fol:0 msg:0 err:HTTPError('HTTP 599: HTTP 400: Bad Request')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-25T02:03:58.882842", "level": "ERROR", "logger": "processor", "message": "process tesr:c38374ecc3e9732c6059c21b00a77057 https://x.com/YahooNewsTopics -> [400] len:2540 -> result:None fol:0 msg:0 err:HTTPError('HTTP 599: HTTP 400: Bad Request')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-25T02:04:41.679369", "level": "ERROR", "logger": "processor", "message": "process sportssssssss:c38374ecc3e9732c6059c21b00a77057 https://x.com/YahooNewsTopics -> [400] len:2540 -> result:None fol:0 msg:0 err:HTTPError('HTTP 599: HTTP 400: Bad Request')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-25T02:05:12.175686", "level": "ERROR", "logger": "processor", "message": "process sportssssssss:c38374ecc3e9732c6059c21b00a77057 https://x.com/YahooNewsTopics -> [400] len:2540 -> result:None fol:0 msg:0 err:HTTPError('HTTP 599: HTTP 400: Bad Request')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-25T02:12:51.799330", "level": "ERROR", "logger": "scheduler", "message": "unknown project: siense", "module": "scheduler", "function": "task_verify", "line": 305, "extra": {"taskName": "Task-204"}}
{"timestamp": "2025-05-25T02:14:03.998948", "level": "ERROR", "logger": "processor", "message": "process siense:c38374ecc3e9732c6059c21b00a77057 https://x.com/YahooNewsTopics -> [400] len:2540 -> result:None fol:0 msg:0 err:HTTPError('HTTP 599: HTTP 400: Bad Request')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-25T02:14:34.567641", "level": "ERROR", "logger": "processor", "message": "process siense:c38374ecc3e9732c6059c21b00a77057 https://x.com/YahooNewsTopics -> [400] len:2540 -> result:None fol:0 msg:0 err:HTTPError('HTTP 599: HTTP 400: Bad Request')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-25T02:36:58.180857", "level": "ERROR", "logger": "processor", "message": "process 040404040_1:c38374ecc3e9732c6059c21b00a77057 https://x.com/YahooNewsTopics -> [400] len:2540 -> result:None fol:0 msg:0 err:HTTPError('HTTP 599: HTTP 400: Bad Request')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-25T02:37:28.913784", "level": "ERROR", "logger": "processor", "message": "process 040404040_1:c38374ecc3e9732c6059c21b00a77057 https://x.com/YahooNewsTopics -> [400] len:2540 -> result:None fol:0 msg:0 err:HTTPError('HTTP 599: HTTP 400: Bad Request')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-25T02:37:39.805779", "level": "ERROR", "logger": "processor", "message": "process domestic_1:c38374ecc3e9732c6059c21b00a77057 https://x.com/YahooNewsTopics -> [400] len:2540 -> result:None fol:0 msg:0 err:HTTPError('HTTP 599: HTTP 400: Bad Request')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-25T02:38:10.449834", "level": "ERROR", "logger": "processor", "message": "process domestic_1:c38374ecc3e9732c6059c21b00a77057 https://x.com/YahooNewsTopics -> [400] len:2540 -> result:None fol:0 msg:0 err:HTTPError('HTTP 599: HTTP 400: Bad Request')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-25T02:47:29.778149", "level": "ERROR", "logger": "processor", "message": "process popopop:c38374ecc3e9732c6059c21b00a77057 https://x.com/YahooNewsTopics -> [400] len:2540 -> result:None fol:0 msg:0 err:HTTPError('HTTP 599: HTTP 400: Bad Request')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-25T02:48:00.205050", "level": "ERROR", "logger": "processor", "message": "process popopop:c38374ecc3e9732c6059c21b00a77057 https://x.com/YahooNewsTopics -> [400] len:2540 -> result:None fol:0 msg:0 err:HTTPError('HTTP 599: HTTP 400: Bad Request')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-25T03:00:39.330514", "level": "ERROR", "logger": "fetcher", "message": "[599] popopopo:b59c7da2813b5a9122ac8c781e450cd5 https://news.yahoo.co.jp/categories/business, HTTP 599: Could not resolve host: news.yahoo.co.jp 10.05s", "module": "tornado_fetcher", "function": "handle_error", "line": 523, "extra": {"taskName": null}}
{"timestamp": "2025-05-25T03:00:39.639443", "level": "ERROR", "logger": "processor", "message": "process popopopo:b59c7da2813b5a9122ac8c781e450cd5 https://news.yahoo.co.jp/categories/business -> [599] len:0 -> result:None fol:0 msg:0 err:Exception('HTTP 599: HTTP 599: Could not resolve host: news.yahoo.co.jp')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-25T03:03:59.466765", "level": "ERROR", "logger": "processor", "message": "process tesr:c38374ecc3e9732c6059c21b00a77057 https://x.com/YahooNewsTopics -> [400] len:2540 -> result:None fol:0 msg:0 err:HTTPError('HTTP 599: HTTP 400: Bad Request')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-25T03:05:12.553208", "level": "ERROR", "logger": "processor", "message": "process sportssssssss:c38374ecc3e9732c6059c21b00a77057 https://x.com/YahooNewsTopics -> [400] len:2540 -> result:None fol:0 msg:0 err:HTTPError('HTTP 599: HTTP 400: Bad Request')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-25T03:14:34.913498", "level": "ERROR", "logger": "processor", "message": "process siense:c38374ecc3e9732c6059c21b00a77057 https://x.com/YahooNewsTopics -> [400] len:2540 -> result:None fol:0 msg:0 err:HTTPError('HTTP 599: HTTP 400: Bad Request')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-25T03:37:29.344590", "level": "ERROR", "logger": "processor", "message": "process 040404040_1:c38374ecc3e9732c6059c21b00a77057 https://x.com/YahooNewsTopics -> [400] len:2540 -> result:None fol:0 msg:0 err:HTTPError('HTTP 599: HTTP 400: Bad Request')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-25T03:38:10.997078", "level": "ERROR", "logger": "processor", "message": "process domestic_1:c38374ecc3e9732c6059c21b00a77057 https://x.com/YahooNewsTopics -> [400] len:2540 -> result:None fol:0 msg:0 err:HTTPError('HTTP 599: HTTP 400: Bad Request')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-25T03:48:00.940317", "level": "ERROR", "logger": "processor", "message": "process popopop:c38374ecc3e9732c6059c21b00a77057 https://x.com/YahooNewsTopics -> [400] len:2540 -> result:None fol:0 msg:0 err:HTTPError('HTTP 599: HTTP 400: Bad Request')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-25T05:31:21.781541", "level": "ERROR", "logger": "processor", "message": "process bibibi:c38374ecc3e9732c6059c21b00a77057 https://x.com/YahooNewsTopics -> [400] len:2540 -> result:None fol:0 msg:0 err:HTTPError('HTTP 599: HTTP 400: Bad Request')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-25T05:34:00.859214", "level": "ERROR", "logger": "processor", "message": "process uino:c38374ecc3e9732c6059c21b00a77057 https://x.com/YahooNewsTopics -> [400] len:2540 -> result:None fol:0 msg:0 err:HTTPError('HTTP 599: HTTP 400: Bad Request')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-25T06:24:01.686823", "level": "ERROR", "logger": "processor", "message": "process 01sono2:c38374ecc3e9732c6059c21b00a77057 https://x.com/YahooNewsTopics -> [400] len:2540 -> result:None fol:0 msg:0 err:HTTPError('HTTP 599: HTTP 400: Bad Request')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-25T07:26:22.826007", "level": "ERROR", "logger": "processor", "message": "process testbus:c38374ecc3e9732c6059c21b00a77057 https://x.com/YahooNewsTopics -> [400] len:2540 -> result:None fol:0 msg:0 err:HTTPError('HTTP 599: HTTP 400: Bad Request')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-25T07:26:53.280735", "level": "ERROR", "logger": "processor", "message": "process testbus:c38374ecc3e9732c6059c21b00a77057 https://x.com/YahooNewsTopics -> [400] len:2540 -> result:None fol:0 msg:0 err:HTTPError('HTTP 599: HTTP 400: Bad Request')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-25T08:26:53.850311", "level": "ERROR", "logger": "processor", "message": "process testbus:c38374ecc3e9732c6059c21b00a77057 https://x.com/YahooNewsTopics -> [400] len:2540 -> result:None fol:0 msg:0 err:HTTPError('HTTP 599: HTTP 400: Bad Request')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-25T09:01:28.606145", "level": "ERROR", "logger": "processor", "message": "process popopopo:c38374ecc3e9732c6059c21b00a77057 https://x.com/YahooNewsTopics -> [400] len:2540 -> result:None fol:0 msg:0 err:HTTPError('HTTP 599: HTTP 400: Bad Request')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-25T09:01:59.154762", "level": "ERROR", "logger": "processor", "message": "process popopopo:c38374ecc3e9732c6059c21b00a77057 https://x.com/YahooNewsTopics -> [400] len:2540 -> result:None fol:0 msg:0 err:HTTPError('HTTP 599: HTTP 400: Bad Request')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-25T09:04:00.084688", "level": "ERROR", "logger": "processor", "message": "process tesr:c38374ecc3e9732c6059c21b00a77057 https://x.com/YahooNewsTopics -> [400] len:2540 -> result:None fol:0 msg:0 err:HTTPError('HTTP 599: HTTP 400: Bad Request')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-25T09:05:12.894202", "level": "ERROR", "logger": "processor", "message": "process sportssssssss:c38374ecc3e9732c6059c21b00a77057 https://x.com/YahooNewsTopics -> [400] len:2540 -> result:None fol:0 msg:0 err:HTTPError('HTTP 599: HTTP 400: Bad Request')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-25T09:14:35.377076", "level": "ERROR", "logger": "processor", "message": "process siense:c38374ecc3e9732c6059c21b00a77057 https://x.com/YahooNewsTopics -> [400] len:2540 -> result:None fol:0 msg:0 err:HTTPError('HTTP 599: HTTP 400: Bad Request')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-25T09:37:29.801672", "level": "ERROR", "logger": "processor", "message": "process 040404040_1:c38374ecc3e9732c6059c21b00a77057 https://x.com/YahooNewsTopics -> [400] len:2540 -> result:None fol:0 msg:0 err:HTTPError('HTTP 599: HTTP 400: Bad Request')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-25T09:38:11.255382", "level": "ERROR", "logger": "processor", "message": "process domestic_1:c38374ecc3e9732c6059c21b00a77057 https://x.com/YahooNewsTopics -> [400] len:2540 -> result:None fol:0 msg:0 err:HTTPError('HTTP 599: HTTP 400: Bad Request')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-25T09:48:01.333750", "level": "ERROR", "logger": "processor", "message": "process popopop:c38374ecc3e9732c6059c21b00a77057 https://x.com/YahooNewsTopics -> [400] len:2540 -> result:None fol:0 msg:0 err:HTTPError('HTTP 599: HTTP 400: Bad Request')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-05-25T10:01:59.740813", "level": "ERROR", "logger": "processor", "message": "process popopopo:c38374ecc3e9732c6059c21b00a77057 https://x.com/YahooNewsTopics -> [400] len:2540 -> result:None fol:0 msg:0 err:HTTPError('HTTP 599: HTTP 400: Bad Request')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T05:46:42.546353", "level": "ERROR", "logger": "processor", "message": "process oioio:22667f09f1ff980d63bde1566fd704f1 https://www.amazon.co.jp/Nintendo-Switch-%E6%97%A5%E6%9C%AC%E8%AA%9E%E3%83%BB%E5%9B%BD%E5%86%85%E5%B0%82%E7%94%A8-%E3%83%9E%E3%83%AA%E3%82%AA%E3%82%AB%E3%83%BC%E3%83%88-%E3%83%AF%E3%83%BC%E3%83%AB%E3%83%89/dp/B0DWZJBXNZ/ref=zg_bs_g_videogames_d_sccl_1/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T05:46:42.551558", "level": "ERROR", "logger": "processor", "message": "process oioio:948418e334da5a1990aaf722a3b34ad9 https://www.amazon.co.jp/%E3%83%AC%E3%83%99%E3%83%AB%E3%83%95%E3%82%A1%E3%82%A4%E3%83%96-HAC-P-A8C8A-%E3%83%95%E3%82%A1%E3%83%B3%E3%82%BF%E3%82%B8%E3%83%BC%E3%83%A9%E3%82%A4%E3%83%95%EF%BD%89-%E3%82%B0%E3%83%AB%E3%82%B0%E3%83%AB%E3%81%AE%E7%AB%9C%E3%81%A8%E6%99%82%E3%82%92%E3%81%AC%E3%81%99%E3%82%80%E5%B0%91%E5%A5%B3-Switch/dp/B0DX747PD2/ref=zg_bs_g_videogames_d_sccl_2/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T05:46:42.555273", "level": "ERROR", "logger": "processor", "message": "process oioio:a1418923683dd1aaa093e1be1209d397 https://www.amazon.co.jp/%E3%82%BD%E3%83%8B%E3%83%BC%E3%83%BB%E3%82%A4%E3%83%B3%E3%82%BF%E3%83%A9%E3%82%AF%E3%83%86%E3%82%A3%E3%83%96%E3%82%A8%E3%83%B3%E3%82%BF%E3%83%86%E3%82%A4%E3%83%B3%E3%83%A1%E3%83%B3%E3%83%88-PlayStation-5-CFI-2000A01/dp/B0CKYM15RJ/ref=zg_bs_g_videogames_d_sccl_3/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T05:46:43.462202", "level": "ERROR", "logger": "processor", "message": "process oioio:468fbca6fcb7877f5197018310d31a45 https://www.amazon.co.jp/Agrado-%E3%82%AC%E3%83%A9%E3%82%B9%E3%83%95%E3%82%A3%E3%83%AB%E3%83%A0-Switch-%E3%83%96%E3%83%AB%E3%83%BC%E3%83%A9%E3%82%A4%E3%83%88%E3%82%AB%E3%83%83%E3%83%88-%E4%BF%9D%E8%AD%B7%E3%83%95%E3%82%A3%E3%83%AB%E3%83%A0/dp/B0DY14JF3L/ref=zg_bs_g_videogames_d_sccl_4/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T05:46:45.767866", "level": "ERROR", "logger": "processor", "message": "process oioio:787d1153bb0bc164795dddd10645d9b2 https://www.amazon.co.jp/%E3%82%B9%E3%82%AF%E3%82%A6%E3%82%A7%E3%82%A2%E3%83%BB%E3%82%A8%E3%83%8B%E3%83%83%E3%82%AF%E3%82%B9-%E3%83%89%E3%83%A9%E3%82%B4%E3%83%B3%E3%82%AF%E3%82%A8%E3%82%B9%E3%83%88I%EF%BC%86II-Switch/dp/B0F9PQ43Y5/ref=zg_bs_g_videogames_d_sccl_6/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T05:46:46.772979", "level": "ERROR", "logger": "processor", "message": "process oioio:bcbd57eb25c38caab088f8508b7e65b6 https://www.amazon.co.jp/%E3%82%B9%E3%82%AF%E3%82%A6%E3%82%A7%E3%82%A2%E3%83%BB%E3%82%A8%E3%83%8B%E3%83%83%E3%82%AF%E3%82%B9-%E3%83%89%E3%83%A9%E3%82%B4%E3%83%B3%E3%82%AF%E3%82%A8%E3%82%B9%E3%83%88I%EF%BC%86II-%E3%80%90Amazon-co-jp%E9%99%90%E5%AE%9A%E7%89%B9%E5%85%B8%E3%80%91%E6%97%85%E4%BA%BA%E3%81%AE%E3%81%9F%E3%81%97%E3%81%AA%E3%81%BF%E3%82%BB%E3%83%83%E3%83%88-%E3%82%B3%E3%83%BC%E3%83%89%E9%85%8D%E4%BF%A1-Switch/dp/B0F9PPHDQL/ref=zg_bs_g_videogames_d_sccl_7/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T05:46:47.378748", "level": "ERROR", "logger": "processor", "message": "process oioio:587673006459360f89abe3461e6b0f22 https://www.amazon.co.jp/%E4%BB%BB%E5%A4%A9%E5%A0%82-HAC-P-A7HLA-%E3%82%B9%E3%83%BC%E3%83%91%E3%83%BC-%E3%83%9E%E3%83%AA%E3%82%AA%E3%83%91%E3%83%BC%E3%83%86%E3%82%A3-%E3%82%B8%E3%83%A3%E3%83%B3%E3%83%9C%E3%83%AA%E3%83%BC/dp/B0D7GQRT8R/ref=zg_bs_g_videogames_d_sccl_5/000-0000000-0000000?psc=1 -> [599] len:52 -> result:None fol:0 msg:0 err:HTTPError('HTTP 599: TimeoutError: Navigation timeout of 3000 ms exceeded')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T05:46:48.684726", "level": "ERROR", "logger": "processor", "message": "process oioio:2bbb8e802bd6dad5a63cbdb186fabfde https://www.amazon.co.jp/%E3%83%9E%E3%82%A4%E3%82%AF%E3%83%AD%E3%82%BD%E3%83%95%E3%83%88-Minecraft-%E3%83%9E%E3%82%A4%E3%83%B3%E3%82%AF%E3%83%A9%E3%83%95%E3%83%88-Switch/dp/B07D131MS4/ref=zg_bs_g_videogames_d_sccl_9/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T05:46:49.590659", "level": "ERROR", "logger": "processor", "message": "process oioio:1ab126e2a022911c500d93bad423b484 https://www.amazon.co.jp/Switch2-%E3%82%AC%E3%83%A9%E3%82%B9%E3%83%95%E3%82%A4%E3%83%AB%E3%83%A0-%E3%82%AC%E3%82%A4%E3%83%89%E6%9E%A0%E4%BB%98%E3%81%8D%E3%80%90Seninhi-%E6%97%A5%E6%9C%AC%E6%97%AD%E7%A1%9D%E5%AD%90%E8%A3%BD-%E9%AB%98-SENLX-TMSH2/dp/B0DZ2BFLPH/ref=zg_bs_g_videogames_d_sccl_10/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T05:46:53.025113", "level": "ERROR", "logger": "processor", "message": "process oioio:56e6f9ff1f90391d7cdff4a9d0012380 https://www.amazon.co.jp/%E3%82%BD%E3%83%8B%E3%83%BC%E3%83%BB%E3%82%A4%E3%83%B3%E3%82%BF%E3%83%A9%E3%82%AF%E3%83%86%E3%82%A3%E3%83%96%E3%82%A8%E3%83%B3%E3%82%BF%E3%83%86%E3%82%A4%E3%83%B3%E3%83%A1%E3%83%B3%E3%83%88-%E3%80%90%E7%B4%94%E6%AD%A3%E5%93%81%E3%80%91DualSense-%E3%83%AF%E3%82%A4%E3%83%A4%E3%83%AC%E3%82%B9%E3%82%B3%E3%83%B3%E3%83%88%E3%83%AD%E3%83%BC%E3%83%A9%E3%83%BC-CFI-ZCT1J/dp/B08GG1QSRR/ref=zg_bs_g_videogames_d_sccl_11/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T05:46:53.028234", "level": "ERROR", "logger": "processor", "message": "process oioio:24a60278b2168c32bdf32eaac4b7b509 https://www.amazon.co.jp/%E3%83%90%E3%83%B3%E3%83%80%E3%82%A4%E3%83%8A%E3%83%A0%E3%82%B3%E3%82%A8%E3%83%B3%E3%82%BF%E3%83%BC%E3%83%86%E3%82%A4%E3%83%B3%E3%83%A1%E3%83%B3%E3%83%88-HAC-P-BB2BA-%E3%81%9F%E3%81%BE%E3%81%94%E3%81%A3%E3%81%A1%E3%81%AE%E3%83%97%E3%83%81%E3%83%97%E3%83%81%E3%81%8A%E3%81%BF%E3%81%9B%E3%81%A3%E3%81%A1-%E3%81%8A%E3%81%BE%E3%81%A1%E3%81%A9%EF%BD%9E%E3%81%95%E3%81%BE%EF%BC%81-Switch/dp/B0F5QJ6CCL/ref=zg_bs_g_videogames_d_sccl_12/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T05:46:53.033160", "level": "ERROR", "logger": "processor", "message": "process oioio:65694203f1140e8b8dbf2ec136b7cfcf https://www.amazon.co.jp/%E3%80%90%E7%B4%94%E6%AD%A3%E5%93%81%E3%80%91DualSense-%E3%83%AF%E3%82%A4%E3%83%A4%E3%83%AC%E3%82%B9%E3%82%B3%E3%83%B3%E3%83%88%E3%83%AD%E3%83%BC%E3%83%A9%E3%83%BC-%E3%83%9F%E3%83%83%E3%83%89%E3%83%8A%E3%82%A4%E3%83%88-%E3%83%96%E3%83%A9%E3%83%83%E3%82%AF-CFI-ZCT1J01/dp/B094VJXGGL/ref=zg_bs_g_videogames_d_sccl_13/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T05:46:53.939599", "level": "ERROR", "logger": "processor", "message": "process oioio:b42c19a99ed5847e58b9bf209c913f7e https://www.amazon.co.jp/%E3%82%B9%E3%82%AF%E3%82%A6%E3%82%A7%E3%82%A2%E3%83%BB%E3%82%A8%E3%83%8B%E3%83%83%E3%82%AF%E3%82%B9-%E3%83%89%E3%83%A9%E3%82%B4%E3%83%B3%E3%82%AF%E3%82%A8%E3%82%B9%E3%83%88I%EF%BC%86II-PS5/dp/B0F9PSSMYW/ref=zg_bs_g_videogames_d_sccl_14/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T05:46:55.853122", "level": "ERROR", "logger": "processor", "message": "process oioio:9fcfad5ecb2e2245e7184f877fdd3be2 https://www.amazon.co.jp/%E4%BB%BB%E5%A4%A9%E5%A0%82-Nintendo-Switch-Pro%E3%82%B3%E3%83%B3%E3%83%88%E3%83%AD%E3%83%BC%E3%83%A9%E3%83%BC/dp/B01NCX3W3O/ref=zg_bs_g_videogames_d_sccl_16/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T05:46:56.858675", "level": "ERROR", "logger": "processor", "message": "process oioio:78ffa7d1b22a581a14be815b0b5b0771 https://www.amazon.co.jp/%E3%83%9E%E3%83%BC%E3%83%99%E3%83%A9%E3%82%B9-HAC-P-BEWEA-%E9%BE%8D%E3%81%AE%E5%9B%BD-%E3%83%AB%E3%83%BC%E3%83%B3%E3%83%95%E3%82%A1%E3%82%AF%E3%83%88%E3%83%AA%E3%83%BC-Switch/dp/B0DT3J2176/ref=zg_bs_g_videogames_d_sccl_17/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T05:46:57.467132", "level": "ERROR", "logger": "processor", "message": "process oioio:670be5867fecab0f41fc0faa53c0737f https://www.amazon.co.jp/Nintendo-Switch-%E6%9C%89%E6%A9%9FEL%E3%83%A2%E3%83%87%E3%83%AB-Joy-%E3%83%9B%E3%83%AF%E3%82%A4%E3%83%88/dp/B098B8PFXY/ref=zg_bs_g_videogames_d_sccl_15/000-0000000-0000000?psc=1 -> [599] len:52 -> result:None fol:0 msg:0 err:HTTPError('HTTP 599: TimeoutError: Navigation timeout of 3000 ms exceeded')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T05:46:57.773944", "level": "ERROR", "logger": "processor", "message": "process oioio:3ce04e3ca3234e6684bbe60a3203977e https://www.amazon.co.jp/Clair-Obscur-Expedition-33%EF%BC%88%E3%82%AF%E3%83%AC%E3%83%BC%E3%83%AB%E3%82%AA%E3%83%96%E3%82%B9%E3%82%AD%E3%83%A5%E3%83%BC%E3%83%AB%EF%BC%9A%E3%82%A8%E3%82%AF%E3%82%B9%E3%83%9A%E3%83%87%E3%82%A3%E3%82%B7%E3%83%A7%E3%83%B333%EF%BC%89-PS5/dp/B0DTK98QBQ/ref=zg_bs_g_videogames_d_sccl_18/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T05:46:59.078950", "level": "ERROR", "logger": "processor", "message": "process oioio:c26a000a0c63d2331f65ab1ff4e2a54b https://www.amazon.co.jp/Roblox%E3%82%AE%E3%83%95%E3%83%88%E3%82%AB%E3%83%BC%E3%83%89-%E3%80%90%E9%99%90%E5%AE%9A%E3%83%90%E3%83%BC%E3%83%81%E3%83%A3%E3%83%AB%E3%82%A2%E3%82%A4%E3%83%86%E3%83%A0%E3%82%92%E5%90%AB%E3%82%80%E3%80%91-%E3%80%90%E3%82%AA%E3%83%B3%E3%83%A9%E3%82%A4%E3%83%B3%E3%82%B2%E3%83%BC%E3%83%A0%E3%82%B3%E3%83%BC%E3%83%89%E3%80%91-%E3%83%AD%E3%83%96%E3%83%AD%E3%83%83%E3%82%AF%E3%82%B9-%E3%82%AA%E3%83%B3%E3%83%A9%E3%82%A4%E3%83%B3%E3%82%B3%E3%83%BC%E3%83%89%E7%89%88/dp/B09NQC37C8/ref=zg_bs_g_videogames_d_sccl_19/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T05:46:59.985085", "level": "ERROR", "logger": "processor", "message": "process oioio:a857f277d91ba540ba01fd50400bb4c6 https://www.amazon.co.jp/%E4%BB%BB%E5%A4%A9%E5%A0%82-%E5%A4%A7%E4%B9%B1%E9%97%98%E3%82%B9%E3%83%9E%E3%83%83%E3%82%B7%E3%83%A5%E3%83%96%E3%83%A9%E3%82%B6%E3%83%BC%E3%82%BA-SPECIAL-Switch/dp/B07FDW61HX/ref=zg_bs_g_videogames_d_sccl_20/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T05:52:00.755600", "level": "ERROR", "logger": "processor", "message": "process oioio:610c2f6a9f62f4bc13f04b638682d3a9 https://www.amazon.co.jp/Nintendo-Switch-%E6%9C%89%E6%A9%9FEL%E3%83%A2%E3%83%87%E3%83%AB-%E3%83%8D%E3%82%AA%E3%83%B3%E3%83%96%E3%83%AB%E3%83%BC-%E3%83%8D%E3%82%AA%E3%83%B3%E3%83%AC%E3%83%83%E3%83%89/dp/B098B79SJL/ref=zg_bs_g_videogames_d_sccl_22/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T05:52:00.760145", "level": "ERROR", "logger": "processor", "message": "process oioio:6e6b235664448b6238ebe75be07a182c https://www.amazon.co.jp/Pikmin-4-%E3%83%94%E3%82%AF%E3%83%9F%E3%83%B3-Switch/dp/B0BV983D5M/ref=zg_bs_g_videogames_d_sccl_21/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T05:52:00.767044", "level": "ERROR", "logger": "processor", "message": "process oioio:ef4666d9248144ecdf3accc89b0f160c https://www.amazon.co.jp/%E4%BB%BB%E5%A4%A9%E5%A0%82-BEE-P-AAAAA-%E3%83%9E%E3%83%AA%E3%82%AA%E3%82%AB%E3%83%BC%E3%83%88-%E3%83%AF%E3%83%BC%E3%83%AB%E3%83%89-Switch2/dp/B0F54D861F/ref=zg_bs_g_videogames_d_sccl_23/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T05:57:01.635673", "level": "ERROR", "logger": "processor", "message": "process oioio:86d3a6602bd5fea6689b359787b0fb3d https://www.amazon.co.jp/%E3%82%BD%E3%83%8B%E3%83%BC%E3%83%BB%E3%82%A4%E3%83%B3%E3%82%BF%E3%83%A9%E3%82%AF%E3%83%86%E3%82%A3%E3%83%96%E3%82%A8%E3%83%B3%E3%82%BF%E3%83%86%E3%82%A4%E3%83%B3%E3%83%A1%E3%83%B3%E3%83%88-PlayStation-5-%E3%83%87%E3%82%B8%E3%82%BF%E3%83%AB%E3%83%BB%E3%82%A8%E3%83%87%E3%82%A3%E3%82%B7%E3%83%A7%E3%83%B3-CFI-2000B01/dp/B0CKYL9MCP/ref=zg_bs_g_videogames_d_sccl_25/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T05:57:01.639403", "level": "ERROR", "logger": "processor", "message": "process oioio:48e7f18127604f12d1fa7045807ff21c https://www.amazon.co.jp/%E3%82%B9%E3%82%AF%E3%82%A6%E3%82%A7%E3%82%A2%E3%83%BB%E3%82%A8%E3%83%8B%E3%83%83%E3%82%AF%E3%82%B9-%E3%83%89%E3%83%A9%E3%82%B4%E3%83%B3%E3%82%AF%E3%82%A8%E3%82%B9%E3%83%88I%EF%BC%86II-%E3%80%90Amazon-co-jp%E9%99%90%E5%AE%9A%E7%89%B9%E5%85%B8%E3%80%91%E6%97%85%E4%BA%BA%E3%81%AE%E3%81%9F%E3%81%97%E3%81%AA%E3%81%BF%E3%82%BB%E3%83%83%E3%83%88-%E3%82%B3%E3%83%BC%E3%83%89%E9%85%8D%E4%BF%A1-PS5/dp/B0F9PPH5L8/ref=zg_bs_g_videogames_d_sccl_24/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T05:57:01.645321", "level": "ERROR", "logger": "processor", "message": "process oioio:0e9661ab00bde369b237613db1017447 https://www.amazon.co.jp/%E3%80%90%E7%B4%94%E6%AD%A3%E5%93%81%E3%80%91DualSense-%E3%83%AF%E3%82%A4%E3%83%A4%E3%83%AC%E3%82%B9%E3%82%B3%E3%83%B3%E3%83%88%E3%83%AD%E3%83%BC%E3%83%A9%E3%83%BC-%E3%83%B4%E3%82%A9%E3%83%AB%E3%82%AB%E3%83%8B%E3%83%83%E3%82%AF-%E3%83%AC%E3%83%83%E3%83%89-CFI-ZCT1J07/dp/B0CK86ZXHJ/ref=zg_bs_g_videogames_d_sccl_26/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:02:02.497645", "level": "ERROR", "logger": "processor", "message": "process oioio:9452b57b923df4edcc50577e77ecfd33 https://www.amazon.co.jp/%E4%BB%BB%E5%A4%A9%E5%A0%82-%E4%B8%96%E7%95%8C%E3%81%AE%E3%82%A2%E3%82%BD%E3%83%93%E5%A4%A7%E5%85%A851-Switch/dp/B086GRKKR9/ref=zg_bs_g_videogames_d_sccl_29/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:02:02.500096", "level": "ERROR", "logger": "processor", "message": "process oioio:4e193a242da5093d0feccca9e09a4aff https://www.amazon.co.jp/%E3%83%95%E3%83%AD%E3%83%A0%E3%82%BD%E3%83%95%E3%83%88%E3%82%A6%E3%82%A7%E3%82%A2-%E3%80%90PS5%E3%80%91ELDEN-RING-NIGHTREIGN/dp/B0DWZVYNM3/ref=zg_bs_g_videogames_d_sccl_27/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:02:02.503854", "level": "ERROR", "logger": "processor", "message": "process oioio:52e4eafa1135124497bf5c9d9fde1019 https://www.amazon.co.jp/Spigen-%E8%B2%BC%E3%82%8A%E4%BB%98%E3%81%91%E3%82%AD%E3%83%83%E3%83%88%E4%BB%98%E3%81%8D-9H%E7%A1%AC%E5%BA%A6%E5%BC%B7%E5%8C%96%E3%82%AC%E3%83%A9%E3%82%B9-%E3%83%8A%E3%83%8E%E3%82%B3%E3%83%BC%E3%83%86%E3%82%A3%E3%83%B3%E3%82%B0-AGL09111/dp/B0DNH9C9FD/ref=zg_bs_g_videogames_d_sccl_28/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:06:04.041517", "level": "ERROR", "logger": "processor", "message": "process oioio:22667f09f1ff980d63bde1566fd704f1 https://www.amazon.co.jp/Nintendo-Switch-%E6%97%A5%E6%9C%AC%E8%AA%9E%E3%83%BB%E5%9B%BD%E5%86%85%E5%B0%82%E7%94%A8-%E3%83%9E%E3%83%AA%E3%82%AA%E3%82%AB%E3%83%BC%E3%83%88-%E3%83%AF%E3%83%BC%E3%83%AB%E3%83%89/dp/B0DWZJBXNZ/ref=zg_bs_g_videogames_d_sccl_1/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:06:04.051659", "level": "ERROR", "logger": "processor", "message": "process oioio:948418e334da5a1990aaf722a3b34ad9 https://www.amazon.co.jp/%E3%83%AC%E3%83%99%E3%83%AB%E3%83%95%E3%82%A1%E3%82%A4%E3%83%96-HAC-P-A8C8A-%E3%83%95%E3%82%A1%E3%83%B3%E3%82%BF%E3%82%B8%E3%83%BC%E3%83%A9%E3%82%A4%E3%83%95%EF%BD%89-%E3%82%B0%E3%83%AB%E3%82%B0%E3%83%AB%E3%81%AE%E7%AB%9C%E3%81%A8%E6%99%82%E3%82%92%E3%81%AC%E3%81%99%E3%82%80%E5%B0%91%E5%A5%B3-Switch/dp/B0DX747PD2/ref=zg_bs_g_videogames_d_sccl_2/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:06:04.060061", "level": "ERROR", "logger": "processor", "message": "process oioio:a1418923683dd1aaa093e1be1209d397 https://www.amazon.co.jp/%E3%82%BD%E3%83%8B%E3%83%BC%E3%83%BB%E3%82%A4%E3%83%B3%E3%82%BF%E3%83%A9%E3%82%AF%E3%83%86%E3%82%A3%E3%83%96%E3%82%A8%E3%83%B3%E3%82%BF%E3%83%86%E3%82%A4%E3%83%B3%E3%83%A1%E3%83%B3%E3%83%88-PlayStation-5-CFI-2000A01/dp/B0CKYM15RJ/ref=zg_bs_g_videogames_d_sccl_3/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:06:04.965735", "level": "ERROR", "logger": "processor", "message": "process oioio:468fbca6fcb7877f5197018310d31a45 https://www.amazon.co.jp/Agrado-%E3%82%AC%E3%83%A9%E3%82%B9%E3%83%95%E3%82%A3%E3%83%AB%E3%83%A0-Switch-%E3%83%96%E3%83%AB%E3%83%BC%E3%83%A9%E3%82%A4%E3%83%88%E3%82%AB%E3%83%83%E3%83%88-%E4%BF%9D%E8%AD%B7%E3%83%95%E3%82%A3%E3%83%AB%E3%83%A0/dp/B0DY14JF3L/ref=zg_bs_g_videogames_d_sccl_4/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:06:07.273342", "level": "ERROR", "logger": "processor", "message": "process oioio:787d1153bb0bc164795dddd10645d9b2 https://www.amazon.co.jp/%E3%82%B9%E3%82%AF%E3%82%A6%E3%82%A7%E3%82%A2%E3%83%BB%E3%82%A8%E3%83%8B%E3%83%83%E3%82%AF%E3%82%B9-%E3%83%89%E3%83%A9%E3%82%B4%E3%83%B3%E3%82%AF%E3%82%A8%E3%82%B9%E3%83%88I%EF%BC%86II-Switch/dp/B0F9PQ43Y5/ref=zg_bs_g_videogames_d_sccl_6/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:06:08.182245", "level": "ERROR", "logger": "processor", "message": "process oioio:bcbd57eb25c38caab088f8508b7e65b6 https://www.amazon.co.jp/%E3%82%B9%E3%82%AF%E3%82%A6%E3%82%A7%E3%82%A2%E3%83%BB%E3%82%A8%E3%83%8B%E3%83%83%E3%82%AF%E3%82%B9-%E3%83%89%E3%83%A9%E3%82%B4%E3%83%B3%E3%82%AF%E3%82%A8%E3%82%B9%E3%83%88I%EF%BC%86II-%E3%80%90Amazon-co-jp%E9%99%90%E5%AE%9A%E7%89%B9%E5%85%B8%E3%80%91%E6%97%85%E4%BA%BA%E3%81%AE%E3%81%9F%E3%81%97%E3%81%AA%E3%81%BF%E3%82%BB%E3%83%83%E3%83%88-%E3%82%B3%E3%83%BC%E3%83%89%E9%85%8D%E4%BF%A1-Switch/dp/B0F9PPHDQL/ref=zg_bs_g_videogames_d_sccl_7/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:06:08.803116", "level": "ERROR", "logger": "processor", "message": "process oioio:587673006459360f89abe3461e6b0f22 https://www.amazon.co.jp/%E4%BB%BB%E5%A4%A9%E5%A0%82-HAC-P-A7HLA-%E3%82%B9%E3%83%BC%E3%83%91%E3%83%BC-%E3%83%9E%E3%83%AA%E3%82%AA%E3%83%91%E3%83%BC%E3%83%86%E3%82%A3-%E3%82%B8%E3%83%A3%E3%83%B3%E3%83%9C%E3%83%AA%E3%83%BC/dp/B0D7GQRT8R/ref=zg_bs_g_videogames_d_sccl_5/000-0000000-0000000?psc=1 -> [599] len:52 -> result:None fol:0 msg:0 err:HTTPError('HTTP 599: TimeoutError: Navigation timeout of 3000 ms exceeded')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:06:09.416627", "level": "ERROR", "logger": "processor", "message": "process oioio:2bbb8e802bd6dad5a63cbdb186fabfde https://www.amazon.co.jp/%E3%83%9E%E3%82%A4%E3%82%AF%E3%83%AD%E3%82%BD%E3%83%95%E3%83%88-Minecraft-%E3%83%9E%E3%82%A4%E3%83%B3%E3%82%AF%E3%83%A9%E3%83%95%E3%83%88-Switch/dp/B07D131MS4/ref=zg_bs_g_videogames_d_sccl_9/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:06:10.339759", "level": "ERROR", "logger": "processor", "message": "process oioio:1ab126e2a022911c500d93bad423b484 https://www.amazon.co.jp/Switch2-%E3%82%AC%E3%83%A9%E3%82%B9%E3%83%95%E3%82%A4%E3%83%AB%E3%83%A0-%E3%82%AC%E3%82%A4%E3%83%89%E6%9E%A0%E4%BB%98%E3%81%8D%E3%80%90Seninhi-%E6%97%A5%E6%9C%AC%E6%97%AD%E7%A1%9D%E5%AD%90%E8%A3%BD-%E9%AB%98-SENLX-TMSH2/dp/B0DZ2BFLPH/ref=zg_bs_g_videogames_d_sccl_10/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:06:11.345891", "level": "ERROR", "logger": "processor", "message": "process oioio:56e6f9ff1f90391d7cdff4a9d0012380 https://www.amazon.co.jp/%E3%82%BD%E3%83%8B%E3%83%BC%E3%83%BB%E3%82%A4%E3%83%B3%E3%82%BF%E3%83%A9%E3%82%AF%E3%83%86%E3%82%A3%E3%83%96%E3%82%A8%E3%83%B3%E3%82%BF%E3%83%86%E3%82%A4%E3%83%B3%E3%83%A1%E3%83%B3%E3%83%88-%E3%80%90%E7%B4%94%E6%AD%A3%E5%93%81%E3%80%91DualSense-%E3%83%AF%E3%82%A4%E3%83%A4%E3%83%AC%E3%82%B9%E3%82%B3%E3%83%B3%E3%83%88%E3%83%AD%E3%83%BC%E3%83%A9%E3%83%BC-CFI-ZCT1J/dp/B08GG1QSRR/ref=zg_bs_g_videogames_d_sccl_11/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:08:25.181041", "level": "ERROR", "logger": "processor", "message": "process oioio:65694203f1140e8b8dbf2ec136b7cfcf https://www.amazon.co.jp/%E3%80%90%E7%B4%94%E6%AD%A3%E5%93%81%E3%80%91DualSense-%E3%83%AF%E3%82%A4%E3%83%A4%E3%83%AC%E3%82%B9%E3%82%B3%E3%83%B3%E3%83%88%E3%83%AD%E3%83%BC%E3%83%A9%E3%83%BC-%E3%83%9F%E3%83%83%E3%83%89%E3%83%8A%E3%82%A4%E3%83%88-%E3%83%96%E3%83%A9%E3%83%83%E3%82%AF-CFI-ZCT1J01/dp/B094VJXGGL/ref=zg_bs_g_videogames_d_sccl_13/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:08:25.186559", "level": "ERROR", "logger": "processor", "message": "process oioio:24a60278b2168c32bdf32eaac4b7b509 https://www.amazon.co.jp/%E3%83%90%E3%83%B3%E3%83%80%E3%82%A4%E3%83%8A%E3%83%A0%E3%82%B3%E3%82%A8%E3%83%B3%E3%82%BF%E3%83%BC%E3%83%86%E3%82%A4%E3%83%B3%E3%83%A1%E3%83%B3%E3%83%88-HAC-P-BB2BA-%E3%81%9F%E3%81%BE%E3%81%94%E3%81%A3%E3%81%A1%E3%81%AE%E3%83%97%E3%83%81%E3%83%97%E3%83%81%E3%81%8A%E3%81%BF%E3%81%9B%E3%81%A3%E3%81%A1-%E3%81%8A%E3%81%BE%E3%81%A1%E3%81%A9%EF%BD%9E%E3%81%95%E3%81%BE%EF%BC%81-Switch/dp/B0F5QJ6CCL/ref=zg_bs_g_videogames_d_sccl_12/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:08:25.490725", "level": "ERROR", "logger": "processor", "message": "process oioio:b42c19a99ed5847e58b9bf209c913f7e https://www.amazon.co.jp/%E3%82%B9%E3%82%AF%E3%82%A6%E3%82%A7%E3%82%A2%E3%83%BB%E3%82%A8%E3%83%8B%E3%83%83%E3%82%AF%E3%82%B9-%E3%83%89%E3%83%A9%E3%82%B4%E3%83%B3%E3%82%AF%E3%82%A8%E3%82%B9%E3%83%88I%EF%BC%86II-PS5/dp/B0F9PSSMYW/ref=zg_bs_g_videogames_d_sccl_14/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:08:27.500040", "level": "ERROR", "logger": "processor", "message": "process oioio:9fcfad5ecb2e2245e7184f877fdd3be2 https://www.amazon.co.jp/%E4%BB%BB%E5%A4%A9%E5%A0%82-Nintendo-Switch-Pro%E3%82%B3%E3%83%B3%E3%83%88%E3%83%AD%E3%83%BC%E3%83%A9%E3%83%BC/dp/B01NCX3W3O/ref=zg_bs_g_videogames_d_sccl_16/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:08:28.406272", "level": "ERROR", "logger": "processor", "message": "process oioio:78ffa7d1b22a581a14be815b0b5b0771 https://www.amazon.co.jp/%E3%83%9E%E3%83%BC%E3%83%99%E3%83%A9%E3%82%B9-HAC-P-BEWEA-%E9%BE%8D%E3%81%AE%E5%9B%BD-%E3%83%AB%E3%83%BC%E3%83%B3%E3%83%95%E3%82%A1%E3%82%AF%E3%83%88%E3%83%AA%E3%83%BC-Switch/dp/B0DT3J2176/ref=zg_bs_g_videogames_d_sccl_17/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:08:29.011546", "level": "ERROR", "logger": "processor", "message": "process oioio:670be5867fecab0f41fc0faa53c0737f https://www.amazon.co.jp/Nintendo-Switch-%E6%9C%89%E6%A9%9FEL%E3%83%A2%E3%83%87%E3%83%AB-Joy-%E3%83%9B%E3%83%AF%E3%82%A4%E3%83%88/dp/B098B8PFXY/ref=zg_bs_g_videogames_d_sccl_15/000-0000000-0000000?psc=1 -> [599] len:52 -> result:None fol:0 msg:0 err:HTTPError('HTTP 599: TimeoutError: Navigation timeout of 3000 ms exceeded')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:08:29.315962", "level": "ERROR", "logger": "processor", "message": "process oioio:3ce04e3ca3234e6684bbe60a3203977e https://www.amazon.co.jp/Clair-Obscur-Expedition-33%EF%BC%88%E3%82%AF%E3%83%AC%E3%83%BC%E3%83%AB%E3%82%AA%E3%83%96%E3%82%B9%E3%82%AD%E3%83%A5%E3%83%BC%E3%83%AB%EF%BC%9A%E3%82%A8%E3%82%AF%E3%82%B9%E3%83%9A%E3%83%87%E3%82%A3%E3%82%B7%E3%83%A7%E3%83%B333%EF%BC%89-PS5/dp/B0DTK98QBQ/ref=zg_bs_g_videogames_d_sccl_18/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:08:30.225419", "level": "ERROR", "logger": "processor", "message": "process oioio:c26a000a0c63d2331f65ab1ff4e2a54b https://www.amazon.co.jp/Roblox%E3%82%AE%E3%83%95%E3%83%88%E3%82%AB%E3%83%BC%E3%83%89-%E3%80%90%E9%99%90%E5%AE%9A%E3%83%90%E3%83%BC%E3%83%81%E3%83%A3%E3%83%AB%E3%82%A2%E3%82%A4%E3%83%86%E3%83%A0%E3%82%92%E5%90%AB%E3%82%80%E3%80%91-%E3%80%90%E3%82%AA%E3%83%B3%E3%83%A9%E3%82%A4%E3%83%B3%E3%82%B2%E3%83%BC%E3%83%A0%E3%82%B3%E3%83%BC%E3%83%89%E3%80%91-%E3%83%AD%E3%83%96%E3%83%AD%E3%83%83%E3%82%AF%E3%82%B9-%E3%82%AA%E3%83%B3%E3%83%A9%E3%82%A4%E3%83%B3%E3%82%B3%E3%83%BC%E3%83%89%E7%89%88/dp/B09NQC37C8/ref=zg_bs_g_videogames_d_sccl_19/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:08:31.228918", "level": "ERROR", "logger": "processor", "message": "process oioio:a857f277d91ba540ba01fd50400bb4c6 https://www.amazon.co.jp/%E4%BB%BB%E5%A4%A9%E5%A0%82-%E5%A4%A7%E4%B9%B1%E9%97%98%E3%82%B9%E3%83%9E%E3%83%83%E3%82%B7%E3%83%A5%E3%83%96%E3%83%A9%E3%82%B6%E3%83%BC%E3%82%BA-SPECIAL-Switch/dp/B07FDW61HX/ref=zg_bs_g_videogames_d_sccl_20/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:08:32.549826", "level": "ERROR", "logger": "processor", "message": "process oioio:6e6b235664448b6238ebe75be07a182c https://www.amazon.co.jp/Pikmin-4-%E3%83%94%E3%82%AF%E3%83%9F%E3%83%B3-Switch/dp/B0BV983D5M/ref=zg_bs_g_videogames_d_sccl_21/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:12:50.083977", "level": "ERROR", "logger": "processor", "message": "process oioio:610c2f6a9f62f4bc13f04b638682d3a9 https://www.amazon.co.jp/Nintendo-Switch-%E6%9C%89%E6%A9%9FEL%E3%83%A2%E3%83%87%E3%83%AB-%E3%83%8D%E3%82%AA%E3%83%B3%E3%83%96%E3%83%AB%E3%83%BC-%E3%83%8D%E3%82%AA%E3%83%B3%E3%83%AC%E3%83%83%E3%83%89/dp/B098B79SJL/ref=zg_bs_g_videogames_d_sccl_22/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:12:50.086190", "level": "ERROR", "logger": "processor", "message": "process oioio:ef4666d9248144ecdf3accc89b0f160c https://www.amazon.co.jp/%E4%BB%BB%E5%A4%A9%E5%A0%82-BEE-P-AAAAA-%E3%83%9E%E3%83%AA%E3%82%AA%E3%82%AB%E3%83%BC%E3%83%88-%E3%83%AF%E3%83%BC%E3%83%AB%E3%83%89-Switch2/dp/B0F54D861F/ref=zg_bs_g_videogames_d_sccl_23/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:12:50.089351", "level": "ERROR", "logger": "processor", "message": "process oioio:48e7f18127604f12d1fa7045807ff21c https://www.amazon.co.jp/%E3%82%B9%E3%82%AF%E3%82%A6%E3%82%A7%E3%82%A2%E3%83%BB%E3%82%A8%E3%83%8B%E3%83%83%E3%82%AF%E3%82%B9-%E3%83%89%E3%83%A9%E3%82%B4%E3%83%B3%E3%82%AF%E3%82%A8%E3%82%B9%E3%83%88I%EF%BC%86II-%E3%80%90Amazon-co-jp%E9%99%90%E5%AE%9A%E7%89%B9%E5%85%B8%E3%80%91%E6%97%85%E4%BA%BA%E3%81%AE%E3%81%9F%E3%81%97%E3%81%AA%E3%81%BF%E3%82%BB%E3%83%83%E3%83%88-%E3%82%B3%E3%83%BC%E3%83%89%E9%85%8D%E4%BF%A1-PS5/dp/B0F9PPH5L8/ref=zg_bs_g_videogames_d_sccl_24/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:12:50.995639", "level": "ERROR", "logger": "processor", "message": "process oioio:86d3a6602bd5fea6689b359787b0fb3d https://www.amazon.co.jp/%E3%82%BD%E3%83%8B%E3%83%BC%E3%83%BB%E3%82%A4%E3%83%B3%E3%82%BF%E3%83%A9%E3%82%AF%E3%83%86%E3%82%A3%E3%83%96%E3%82%A8%E3%83%B3%E3%82%BF%E3%83%86%E3%82%A4%E3%83%B3%E3%83%A1%E3%83%B3%E3%83%88-PlayStation-5-%E3%83%87%E3%82%B8%E3%82%BF%E3%83%AB%E3%83%BB%E3%82%A8%E3%83%87%E3%82%A3%E3%82%B7%E3%83%A7%E3%83%B3-CFI-2000B01/dp/B0CKYL9MCP/ref=zg_bs_g_videogames_d_sccl_25/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:12:52.000317", "level": "ERROR", "logger": "processor", "message": "process oioio:0e9661ab00bde369b237613db1017447 https://www.amazon.co.jp/%E3%80%90%E7%B4%94%E6%AD%A3%E5%93%81%E3%80%91DualSense-%E3%83%AF%E3%82%A4%E3%83%A4%E3%83%AC%E3%82%B9%E3%82%B3%E3%83%B3%E3%83%88%E3%83%AD%E3%83%BC%E3%83%A9%E3%83%BC-%E3%83%B4%E3%82%A9%E3%83%AB%E3%82%AB%E3%83%8B%E3%83%83%E3%82%AF-%E3%83%AC%E3%83%83%E3%83%89-CFI-ZCT1J07/dp/B0CK86ZXHJ/ref=zg_bs_g_videogames_d_sccl_26/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:12:53.003595", "level": "ERROR", "logger": "processor", "message": "process oioio:4e193a242da5093d0feccca9e09a4aff https://www.amazon.co.jp/%E3%83%95%E3%83%AD%E3%83%A0%E3%82%BD%E3%83%95%E3%83%88%E3%82%A6%E3%82%A7%E3%82%A2-%E3%80%90PS5%E3%80%91ELDEN-RING-NIGHTREIGN/dp/B0DWZVYNM3/ref=zg_bs_g_videogames_d_sccl_27/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:12:54.308074", "level": "ERROR", "logger": "processor", "message": "process oioio:52e4eafa1135124497bf5c9d9fde1019 https://www.amazon.co.jp/Spigen-%E8%B2%BC%E3%82%8A%E4%BB%98%E3%81%91%E3%82%AD%E3%83%83%E3%83%88%E4%BB%98%E3%81%8D-9H%E7%A1%AC%E5%BA%A6%E5%BC%B7%E5%8C%96%E3%82%AC%E3%83%A9%E3%82%B9-%E3%83%8A%E3%83%8E%E3%82%B3%E3%83%BC%E3%83%86%E3%82%A3%E3%83%B3%E3%82%B0-AGL09111/dp/B0DNH9C9FD/ref=zg_bs_g_videogames_d_sccl_28/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:12:55.214772", "level": "ERROR", "logger": "processor", "message": "process oioio:9452b57b923df4edcc50577e77ecfd33 https://www.amazon.co.jp/%E4%BB%BB%E5%A4%A9%E5%A0%82-%E4%B8%96%E7%95%8C%E3%81%AE%E3%82%A2%E3%82%BD%E3%83%93%E5%A4%A7%E5%85%A851-Switch/dp/B086GRKKR9/ref=zg_bs_g_videogames_d_sccl_29/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:13:03.558053", "level": "ERROR", "logger": "processor", "message": "process oioio:4a2223a7d5f9685bdee461a8dfcd319f https://www.amazon.co.jp/%E4%BB%BB%E5%A4%A9%E5%A0%82-%E3%83%8B%E3%83%B3%E3%83%86%E3%83%B3%E3%83%89%E3%83%BC%E3%83%97%E3%83%AA%E3%83%9A%E3%82%A4%E3%83%89%E7%95%AA%E5%8F%B7-500%E5%86%86-%E3%82%AA%E3%83%B3%E3%83%A9%E3%82%A4%E3%83%B3%E3%82%B3%E3%83%BC%E3%83%89%E7%89%88/dp/B09997WD23/ref=zg_bs_g_videogames_d_sccl_3/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:13:09.953744", "level": "ERROR", "logger": "processor", "message": "process oioio:bcba466826228846c02f985063786612 https://www.amazon.co.jp/%E4%BB%BB%E5%A4%A9%E5%A0%82-%E3%83%8B%E3%83%B3%E3%83%86%E3%83%B3%E3%83%89%E3%83%BC%E3%83%97%E3%83%AA%E3%83%9A%E3%82%A4%E3%83%89%E7%95%AA%E5%8F%B7-1000%E5%86%86-%E3%82%AA%E3%83%B3%E3%83%A9%E3%82%A4%E3%83%B3%E3%82%B3%E3%83%BC%E3%83%89%E7%89%88/dp/B09999M8HV/ref=zg_bs_g_videogames_d_sccl_7/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:13:09.956078", "level": "ERROR", "logger": "processor", "message": "process oioio:a8da308eef574b8a45325adbcbca80cd https://www.amazon.co.jp/%E4%BB%BB%E5%A4%A9%E5%A0%82-%E3%83%9E%E3%83%AA%E3%82%AA%E3%82%AB%E3%83%BC%E3%83%888-%E3%83%87%E3%83%A9%E3%83%83%E3%82%AF%E3%82%B9-Switch/dp/B01N12G06K/ref=zg_bs_g_videogames_d_sccl_5/000-0000000-0000000?psc=1 -> [599] len:52 -> result:None fol:0 msg:0 err:HTTPError('HTTP 599: TimeoutError: Navigation timeout of 3000 ms exceeded')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:13:09.960303", "level": "ERROR", "logger": "processor", "message": "process oioio:7c289fa4972d4b3c1398855de3d3d189 https://www.amazon.co.jp/%E3%80%90%E4%BB%BB%E5%A4%A9%E5%A0%82%E7%B4%94%E6%AD%A3%E5%93%81%E3%80%91Joy-2%E5%80%8B%E3%82%BB%E3%83%83%E3%83%88-%E3%80%90Amazon-co-jp%E9%99%90%E5%AE%9A%E7%89%B9%E5%85%B8%E3%80%91Nintendo-Switch-%E3%83%AD%E3%82%B4%E3%83%87%E3%82%B6%E3%82%A4%E3%83%B3%E3%82%B9%E3%83%86%E3%83%83%E3%82%AB%E3%83%BC/dp/B0F54LNT4M/ref=zg_bs_g_videogames_d_sccl_8/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:13:11.982557", "level": "ERROR", "logger": "processor", "message": "process oioio:3af94f3638ce16ca3d2d3bf1a9fc5514 https://www.amazon.co.jp/%E3%82%BD%E3%83%8B%E3%83%BC%E3%83%BB%E3%82%A4%E3%83%B3%E3%82%BF%E3%83%A9%E3%82%AF%E3%83%86%E3%82%A3%E3%83%96%E3%82%A8%E3%83%B3%E3%82%BF%E3%83%86%E3%82%A4%E3%83%B3%E3%83%A1%E3%83%B3%E3%83%88-CFI-7000B01-PlayStation-5-Pro/dp/B0DGT79B1T/ref=zg_bs_g_videogames_d_sccl_9/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:13:11.985985", "level": "ERROR", "logger": "processor", "message": "process oioio:b63f9bc9ea6771ced1450ab9d89dba1a https://www.amazon.co.jp/%E4%BB%BB%E5%A4%A9%E5%A0%82-%E3%83%8B%E3%83%B3%E3%83%86%E3%83%B3%E3%83%89%E3%83%BC%E3%83%97%E3%83%AA%E3%83%9A%E3%82%A4%E3%83%89%E7%95%AA%E5%8F%B7-9000%E5%86%86-%E3%82%AA%E3%83%B3%E3%83%A9%E3%82%A4%E3%83%B3%E3%82%B3%E3%83%BC%E3%83%89%E7%89%88/dp/B09998HHSY/ref=zg_bs_g_videogames_d_sccl_10/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:13:11.989471", "level": "ERROR", "logger": "processor", "message": "process oioio:7c832e474063760c59eb608a6a77b63c https://www.amazon.co.jp/%E4%BB%BB%E5%A4%A9%E5%A0%82-%E3%83%8B%E3%83%B3%E3%83%86%E3%83%B3%E3%83%89%E3%83%BC%E3%83%97%E3%83%AA%E3%83%9A%E3%82%A4%E3%83%89%E7%95%AA%E5%8F%B7-5000%E5%86%86-%E3%82%AA%E3%83%B3%E3%83%A9%E3%82%A4%E3%83%B3%E3%82%B3%E3%83%BC%E3%83%89%E7%89%88/dp/B09998HHSG/ref=zg_bs_g_videogames_d_sccl_11/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:13:11.993375", "level": "ERROR", "logger": "processor", "message": "process oioio:ecfe582a9e2bd3cc103675ee27f76b3a https://www.amazon.co.jp/%E4%BB%BB%E5%A4%A9%E5%A0%82-%E3%81%82%E3%81%A4%E3%81%BE%E3%82%8C-%E3%81%A9%E3%81%86%E3%81%B6%E3%81%A4%E3%81%AE%E6%A3%AE-Switch/dp/B084HPGQ9W/ref=zg_bs_g_videogames_d_sccl_13/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:13:14.084456", "level": "ERROR", "logger": "processor", "message": "process oioio:ccc726e6af8d56fdeccfe00ceeb92e83 https://www.amazon.co.jp/%E3%82%AB%E3%83%97%E3%82%B3%E3%83%B3-CAPCOM-ELJM-30582-%E3%80%90PS5%E3%80%91%E3%83%A2%E3%83%B3%E3%82%B9%E3%82%BF%E3%83%BC%E3%83%8F%E3%83%B3%E3%82%BF%E3%83%BC%E3%83%AF%E3%82%A4%E3%83%AB%E3%82%BA/dp/B0DHCTRH16/ref=zg_bs_g_videogames_d_sccl_15/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:13:14.089299", "level": "ERROR", "logger": "processor", "message": "process oioio:6f4cf3c41984cccf6e48875222d56481 https://www.amazon.co.jp/%E3%83%99%E3%83%AB%E3%83%A2%E3%83%B3%E3%83%89-%E3%80%902%E6%9E%9A%E3%82%BB%E3%83%83%E3%83%88%E3%80%91-%E3%82%AC%E3%83%A9%E3%82%B9%E3%83%95%E3%82%A3%E3%83%AB%E3%83%A0-%E7%94%BB%E9%9D%A2%E4%BF%9D%E8%AD%B7%E3%83%95%E3%82%A3%E3%83%AB%E3%83%A0-%E3%83%A2%E3%83%B3%E3%82%B9%E3%82%BF%E3%83%BC%E3%83%95%E3%82%A3%E3%83%AB%E3%83%A0/dp/B0F1CJB2Q5/ref=zg_bs_g_videogames_d_sccl_16/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:13:16.136578", "level": "ERROR", "logger": "processor", "message": "process oioio:ebd3bb2e498825f66ee0563204501bc7 https://www.amazon.co.jp/%E3%80%90%E4%BB%BB%E5%A4%A9%E5%A0%82%E3%83%A9%E3%82%A4%E3%82%BB%E3%83%B3%E3%82%B9%E5%95%86%E5%93%81%E3%80%91SCREEN-GUARD-Nintendo-Switch-%E6%8C%87%E7%B4%8B%E9%98%B2%E6%AD%A2%E3%82%BF%E3%82%A4%E3%83%97/dp/B0F5WYM7RJ/ref=zg_bs_g_videogames_d_sccl_18/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:13:20.636190", "level": "ERROR", "logger": "processor", "message": "process oioio:2474f2329d721afde0ffa7b5d76c5b88 https://www.amazon.co.jp/%E4%BB%BB%E5%A4%A9%E5%A0%82-%E6%98%9F%E3%81%AE%E3%82%AB%E3%83%BC%E3%83%93%E3%82%A3-%E3%83%87%E3%82%A3%E3%82%B9%E3%82%AB%E3%83%90%E3%83%AA%E3%83%BC-Switch/dp/B09Q5GM5PQ/ref=zg_bs_g_videogames_d_sccl_20/000-0000000-0000000?psc=1 -> [599] len:52 -> result:None fol:0 msg:0 err:HTTPError('HTTP 599: TimeoutError: Navigation timeout of 3000 ms exceeded')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:13:20.641852", "level": "ERROR", "logger": "processor", "message": "process oioio:c81792f942c90564ca29b8fdcc76b35e https://www.amazon.co.jp/%E3%82%BD%E3%83%8B%E3%83%BC%E3%83%BB%E3%82%A4%E3%83%B3%E3%82%BF%E3%83%A9%E3%82%AF%E3%83%86%E3%82%A3%E3%83%96%E3%82%A8%E3%83%B3%E3%82%BF%E3%83%86%E3%82%A4%E3%83%B3%E3%83%A1%E3%83%B3%E3%83%88-%E3%80%90PS5%E3%80%91%E3%82%B0%E3%83%A9%E3%83%B3%E3%83%84%E3%83%BC%E3%83%AA%E3%82%B9%E3%83%A27/dp/B09H2X6R6C/ref=zg_bs_g_videogames_d_sccl_1/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:13:21.249058", "level": "ERROR", "logger": "processor", "message": "process oioio:558fcd3a023ec03dcf2797819589d46a https://www.amazon.co.jp/%E4%BB%BB%E5%A4%A9%E5%A0%82-Nintendo-Switch-Lite-%E3%82%B0%E3%83%AC%E3%83%BC/dp/B07X47QTN3/ref=zg_bs_g_videogames_d_sccl_3/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:13:22.254061", "level": "ERROR", "logger": "processor", "message": "process oioio:0387a17765f14d9c5a145a1ca11ed588 https://www.amazon.co.jp/%E4%BB%BB%E5%A4%A9%E5%A0%82-%E3%83%8B%E3%83%B3%E3%83%86%E3%83%B3%E3%83%89%E3%83%BC%E3%83%97%E3%83%AA%E3%83%9A%E3%82%A4%E3%83%89%E7%95%AA%E5%8F%B7-3000%E5%86%86-%E3%82%AA%E3%83%B3%E3%83%A9%E3%82%A4%E3%83%B3%E3%82%B3%E3%83%BC%E3%83%89%E7%89%88/dp/B09996CH6H/ref=zg_bs_g_videogames_d_sccl_5/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:13:24.974208", "level": "ERROR", "logger": "processor", "message": "process oioio:ba596a2016e51fdcb4526f12208b189d https://www.amazon.co.jp/%E3%82%BD%E3%83%8B%E3%83%BC%E3%83%BB%E3%82%A4%E3%83%B3%E3%82%BF%E3%83%A9%E3%82%AF%E3%83%86%E3%82%A3%E3%83%96%E3%82%A8%E3%83%B3%E3%82%BF%E3%83%86%E3%82%A4%E3%83%B3%E3%83%A1%E3%83%B3%E3%83%88-%E3%80%90PS5%E3%80%91%E3%82%A2%E3%82%B9%E3%83%88%E3%83%AD%E3%83%9C%E3%83%83%E3%83%88-ASTRO-BOT/dp/B0D66L4TMB/ref=zg_bs_g_videogames_d_sccl_6/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:13:27.075862", "level": "ERROR", "logger": "processor", "message": "process oioio:6a8b4a6790db0819a13aa9abfcee0358 https://www.amazon.co.jp/%E3%80%901%E6%9E%9A%E3%82%BB%E3%83%83%E3%83%88%E3%80%91-Switch-%E8%B2%BC%E3%82%8A%E4%BB%98%E3%81%91%E3%82%AD%E3%83%83%E3%83%88%E4%BB%98%E3%81%8D-%E3%82%AC%E3%83%A9%E3%82%B9%E3%83%95%E3%82%A3%E3%83%AB%E3%83%A0-%E9%AB%98%E6%84%9F%E5%BA%A6%E3%82%BF%E3%83%83%E3%83%81/dp/B0F1NFBW9P/ref=zg_bs_g_videogames_d_sccl_8/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:13:29.298454", "level": "ERROR", "logger": "processor", "message": "process oioio:620c0b82784fa94badde4d8865cb7f2a https://www.amazon.co.jp/%E3%83%97%E3%83%AC%E3%82%A4%E3%82%B9%E3%83%86%E3%83%BC%E3%82%B7%E3%83%A7%E3%83%B3-%E3%82%B9%E3%83%88%E3%82%A2%E3%83%81%E3%82%B1%E3%83%83%E3%83%88-3-000%E5%86%86-%E3%82%AA%E3%83%B3%E3%83%A9%E3%82%A4%E3%83%B3%E3%82%B3%E3%83%BC%E3%83%89%E7%89%88/dp/B08M6DTZ38/ref=zg_bs_g_videogames_d_sccl_9/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:13:29.301481", "level": "ERROR", "logger": "processor", "message": "process oioio:dc73e474504cf2f2b5344cfec29e4a7f https://www.amazon.co.jp/%E3%80%90PS5%E3%80%91Ghost-Y%C5%8Dtei-%E3%82%B4%E3%83%BC%E3%82%B9%E3%83%88%E3%83%BB%E3%82%AA%E3%83%96%E3%83%BB%E3%83%A8%E3%82%A6%E3%83%86%E3%82%A4-%E3%80%90%E6%97%A9%E6%9C%9F%E8%B3%BC%E5%85%A5%E7%89%B9%E5%85%B8%E3%80%91%E3%83%BB%E3%82%B2%E3%83%BC%E3%83%A0%E5%86%85%E3%82%A2%E3%82%A4%E3%83%86%E3%83%A0-%E3%83%BB%E7%AF%A4%E3%81%A8%E7%BE%8A%E8%B9%84%E5%85%AD%E4%BA%BA%E8%A1%86%E3%81%AE%E3%82%A2%E3%83%90%E3%82%BF%E3%83%BC%E3%82%BB%E3%83%83%E3%83%88%EF%BC%88%E5%85%A8%EF%BC%97%E7%A8%AE%EF%BC%89%EF%BC%88%E5%B0%81%E5%85%A5%EF%BC%89/dp/B0F6CZ4RNN/ref=zg_bs_g_videogames_d_sccl_10/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:13:29.303821", "level": "ERROR", "logger": "processor", "message": "process oioio:a28e3e6b5c1617b0605adb50fbd3d866 https://www.amazon.co.jp/%E3%83%97%E3%83%AC%E3%82%A4%E3%82%B9%E3%83%86%E3%83%BC%E3%82%B7%E3%83%A7%E3%83%B3-%E3%82%B9%E3%83%88%E3%82%A2%E3%83%81%E3%82%B1%E3%83%83%E3%83%88-5-000%E5%86%86-%E3%82%AA%E3%83%B3%E3%83%A9%E3%82%A4%E3%83%B3%E3%82%B3%E3%83%BC%E3%83%89%E7%89%88/dp/B08M69WPHZ/ref=zg_bs_g_videogames_d_sccl_11/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:13:29.307399", "level": "ERROR", "logger": "processor", "message": "process oioio:222955742b64ef18e5d8f02beb9019d6 https://www.amazon.co.jp/TURBO%E9%80%A3%E5%B0%84%E6%A9%9F%E8%83%BD-1000mAh%E5%A4%A7%E5%AE%B9%E9%87%8F-Bluetooth-%E3%82%B8%E3%83%A3%E3%82%A4%E3%83%AD%E3%82%BB%E3%83%B3%E3%82%B5%E3%83%BC%E6%90%AD%E8%BC%89-%E6%97%A5%E6%9C%AC%E8%AA%9E%E5%8F%96%E6%89%B1%E8%AA%AC%E6%98%8E%E6%9B%B8%E4%BB%98%E3%81%8D/dp/B0D8KWTL2C/ref=zg_bs_g_videogames_d_sccl_12/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:13:30.212551", "level": "ERROR", "logger": "processor", "message": "process oioio:75cc752db3655061e3cb78274755f21a https://www.amazon.co.jp/%E3%82%A2%E3%82%B5%E3%82%B7%E3%83%B3-%E3%82%AF%E3%83%AA%E3%83%BC%E3%83%89-%E3%82%B7%E3%83%A3%E3%83%89%E3%82%A6%E3%82%BA-%E3%82%B9%E3%82%BF%E3%83%B3%E3%83%80%E3%83%BC%E3%83%89%E3%82%A8%E3%83%87%E3%82%A3%E3%82%B7%E3%83%A7%E3%83%B3-PS5/dp/B0D49J3CHB/ref=zg_bs_g_videogames_d_sccl_13/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:13:31.519548", "level": "ERROR", "logger": "processor", "message": "process oioio:f30a722677881a072de1dfd4e233c506 https://www.amazon.co.jp/%E3%80%90%E7%B4%94%E6%AD%A3%E5%93%81%E3%80%91%E3%82%B9%E3%83%86%E3%82%A3%E3%83%83%E3%82%AF%E3%83%A2%E3%82%B8%E3%83%A5%E3%83%BC%E3%83%AB-DualSense-Edge-%E3%83%AF%E3%82%A4%E3%83%A4%E3%83%AC%E3%82%B9%E3%82%B3%E3%83%B3%E3%83%88%E3%83%AD%E3%83%BC%E3%83%A9%E3%83%BC%E7%94%A8-CFI-ZSM1G/dp/B0BJTSHHK8/ref=zg_bs_g_videogames_d_sccl_14/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:13:33.427432", "level": "ERROR", "logger": "processor", "message": "process oioio:1ea87cf2808630f3594ef373a156dcc1 https://www.amazon.co.jp/%E3%80%90%E7%B4%94%E6%AD%A3%E5%93%81%E3%80%91DualSense-Edge-%E3%83%AF%E3%82%A4%E3%83%A4%E3%83%AC%E3%82%B9%E3%82%B3%E3%83%B3%E3%83%88%E3%83%AD%E3%83%BC%E3%83%A9%E3%83%BC-%E3%83%9F%E3%83%83%E3%83%89%E3%83%8A%E3%82%A4%E3%83%88-CFI-ZCP1J01/dp/B0DT2PGBYZ/ref=zg_bs_g_videogames_d_sccl_16/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:13:35.036543", "level": "ERROR", "logger": "processor", "message": "process oioio:71eaff9f1a08adf5ccc164081c1298dc https://www.amazon.co.jp/%E3%80%90Amazon-co-jp%E9%99%90%E5%AE%9A%E3%80%91Logicool-%E3%83%AD%E3%82%B8%E3%82%AF%E3%83%BC%E3%83%AB-G-PHS-003d-%E3%80%90Amazon-co-jp-%E9%99%90%E5%AE%9A%E5%A3%81%E7%B4%99%E3%83%80%E3%82%A6%E3%83%B3%E3%83%AD%E3%83%BC%E3%83%89%E4%BB%98%E3%81%8D%E3%80%91/dp/B0BC142H1L/ref=zg_bs_g_videogames_d_sccl_15/000-0000000-0000000?psc=1 -> [599] len:52 -> result:None fol:0 msg:0 err:HTTPError('HTTP 599: TimeoutError: Navigation timeout of 3000 ms exceeded')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:13:38.762732", "level": "ERROR", "logger": "processor", "message": "process oioio:4da68110aeb62c6b6802c1972fdc7aa3 https://www.amazon.co.jp/%E3%80%90PS5%E3%80%91Ghost-of-Tsushima-Directors-Cut/dp/B098HVGHFK/ref=zg_bs_g_videogames_d_sccl_19/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:13:38.768025", "level": "ERROR", "logger": "processor", "message": "process oioio:c2b027b3a8e370645ebfbe79e1084b3f https://www.amazon.co.jp/%E3%83%AF%E3%82%A4%E3%83%A4%E3%83%AC%E3%82%B9%E3%82%B3%E3%83%B3%E3%83%88%E3%83%AD%E3%83%BC%E3%83%A9%E3%83%BC-Turbo%E9%80%A3%E5%B0%84%E6%A9%9F%E8%83%BD-Bluetooth%E6%8E%A5%E7%B6%9A-%E3%82%B8%E3%83%A3%E3%82%A4%E3%83%AD%E3%82%BB%E3%83%B3%E3%82%B5%E3%83%BC%E6%A9%9F%E8%83%BD-%E3%83%98%E3%83%83%E3%83%89%E3%83%95%E3%82%A9%E3%83%B3%E3%82%B8%E3%83%A3%E3%83%83%E3%82%AF%E4%BB%98%E3%81%8D/dp/B0DHCRV5FG/ref=zg_bs_g_videogames_d_sccl_17/000-0000000-0000000?psc=1 -> [599] len:52 -> result:None fol:0 msg:0 err:HTTPError('HTTP 599: TimeoutError: Navigation timeout of 3000 ms exceeded')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:13:39.374340", "level": "ERROR", "logger": "processor", "message": "process oioio:b436482f846230e5c6bdef9a60007b0a https://www.amazon.co.jp/TURBO%E9%80%A3%E5%B0%84%E6%A9%9F%E8%83%BD-1000mAh%E5%A4%A7%E5%AE%B9%E9%87%8F-Bluetooth-%E3%82%B8%E3%83%A3%E3%82%A4%E3%83%AD%E3%82%BB%E3%83%B3%E3%82%B5%E3%83%BC%E6%90%AD%E8%BC%89-%E6%97%A5%E6%9C%AC%E8%AA%9E%E5%8F%96%E6%89%B1%E8%AA%AC%E6%98%8E%E6%9B%B8%E4%BB%98%E3%81%8D/dp/B0DDK6R2KZ/ref=zg_bs_g_videogames_d_sccl_18/000-0000000-0000000?psc=1 -> [599] len:52 -> result:None fol:0 msg:0 err:HTTPError('HTTP 599: TimeoutError: Navigation timeout of 3000 ms exceeded')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:13:39.680109", "level": "ERROR", "logger": "processor", "message": "process oioio:83015ccad4fc79cece2b0e93f2566398 https://www.amazon.co.jp/%E3%80%90%E4%BB%BB%E5%A4%A9%E5%A0%82%E7%B4%94%E6%AD%A3%E5%93%81%E3%80%91Joy-%E3%83%A9%E3%82%A4%E3%83%88%E3%83%96%E3%83%AB%E3%83%BC-%E3%83%A9%E3%82%A4%E3%83%88%E3%83%AC%E3%83%83%E3%83%89-%E3%80%90Amazon-co-jp%E9%99%90%E5%AE%9A%E7%89%B9%E5%85%B8%E3%80%91Nintendo-%E3%83%AD%E3%82%B4%E3%83%87%E3%82%B6%E3%82%A4%E3%83%B3%E3%82%B9%E3%83%86%E3%83%83%E3%82%AB%E3%83%BC/dp/B0F54H3C6T/ref=zg_bs_g_videogames_d_sccl_21/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:13:40.588292", "level": "ERROR", "logger": "processor", "message": "process oioio:bcba466826228846c02f985063786612 https://www.amazon.co.jp/%E4%BB%BB%E5%A4%A9%E5%A0%82-%E3%83%8B%E3%83%B3%E3%83%86%E3%83%B3%E3%83%89%E3%83%BC%E3%83%97%E3%83%AA%E3%83%9A%E3%82%A4%E3%83%89%E7%95%AA%E5%8F%B7-1000%E5%86%86-%E3%82%AA%E3%83%B3%E3%83%A9%E3%82%A4%E3%83%B3%E3%82%B3%E3%83%BC%E3%83%89%E7%89%88/dp/B09999M8HV/ref=zg_bs_g_videogames_d_sccl_7/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:13:43.598983", "level": "ERROR", "logger": "processor", "message": "process oioio:3af94f3638ce16ca3d2d3bf1a9fc5514 https://www.amazon.co.jp/%E3%82%BD%E3%83%8B%E3%83%BC%E3%83%BB%E3%82%A4%E3%83%B3%E3%82%BF%E3%83%A9%E3%82%AF%E3%83%86%E3%82%A3%E3%83%96%E3%82%A8%E3%83%B3%E3%82%BF%E3%83%86%E3%82%A4%E3%83%B3%E3%83%A1%E3%83%B3%E3%83%88-CFI-7000B01-PlayStation-5-Pro/dp/B0DGT79B1T/ref=zg_bs_g_videogames_d_sccl_9/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:13:44.508515", "level": "ERROR", "logger": "processor", "message": "process oioio:a8da308eef574b8a45325adbcbca80cd https://www.amazon.co.jp/%E4%BB%BB%E5%A4%A9%E5%A0%82-%E3%83%9E%E3%83%AA%E3%82%AA%E3%82%AB%E3%83%BC%E3%83%888-%E3%83%87%E3%83%A9%E3%83%83%E3%82%AF%E3%82%B9-Switch/dp/B01N12G06K/ref=zg_bs_g_videogames_d_sccl_5/000-0000000-0000000?psc=1 -> [599] len:52 -> result:None fol:0 msg:0 err:HTTPError('HTTP 599: TimeoutError: Navigation timeout of 3000 ms exceeded')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:13:46.785875", "level": "ERROR", "logger": "processor", "message": "process oioio:7c832e474063760c59eb608a6a77b63c https://www.amazon.co.jp/%E4%BB%BB%E5%A4%A9%E5%A0%82-%E3%83%8B%E3%83%B3%E3%83%86%E3%83%B3%E3%83%89%E3%83%BC%E3%83%97%E3%83%AA%E3%83%9A%E3%82%A4%E3%83%89%E7%95%AA%E5%8F%B7-5000%E5%86%86-%E3%82%AA%E3%83%B3%E3%83%A9%E3%82%A4%E3%83%B3%E3%82%B3%E3%83%BC%E3%83%89%E7%89%88/dp/B09998HHSG/ref=zg_bs_g_videogames_d_sccl_11/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:13:49.175392", "level": "ERROR", "logger": "processor", "message": "process oioio:ccc726e6af8d56fdeccfe00ceeb92e83 https://www.amazon.co.jp/%E3%82%AB%E3%83%97%E3%82%B3%E3%83%B3-CAPCOM-ELJM-30582-%E3%80%90PS5%E3%80%91%E3%83%A2%E3%83%B3%E3%82%B9%E3%82%BF%E3%83%BC%E3%83%8F%E3%83%B3%E3%82%BF%E3%83%BC%E3%83%AF%E3%82%A4%E3%83%AB%E3%82%BA/dp/B0DHCTRH16/ref=zg_bs_g_videogames_d_sccl_15/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:13:49.180052", "level": "ERROR", "logger": "processor", "message": "process oioio:6f4cf3c41984cccf6e48875222d56481 https://www.amazon.co.jp/%E3%83%99%E3%83%AB%E3%83%A2%E3%83%B3%E3%83%89-%E3%80%902%E6%9E%9A%E3%82%BB%E3%83%83%E3%83%88%E3%80%91-%E3%82%AC%E3%83%A9%E3%82%B9%E3%83%95%E3%82%A3%E3%83%AB%E3%83%A0-%E7%94%BB%E9%9D%A2%E4%BF%9D%E8%AD%B7%E3%83%95%E3%82%A3%E3%83%AB%E3%83%A0-%E3%83%A2%E3%83%B3%E3%82%B9%E3%82%BF%E3%83%BC%E3%83%95%E3%82%A3%E3%83%AB%E3%83%A0/dp/B0F1CJB2Q5/ref=zg_bs_g_videogames_d_sccl_16/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:13:49.485820", "level": "ERROR", "logger": "processor", "message": "process oioio:ecfe582a9e2bd3cc103675ee27f76b3a https://www.amazon.co.jp/%E4%BB%BB%E5%A4%A9%E5%A0%82-%E3%81%82%E3%81%A4%E3%81%BE%E3%82%8C-%E3%81%A9%E3%81%86%E3%81%B6%E3%81%A4%E3%81%AE%E6%A3%AE-Switch/dp/B084HPGQ9W/ref=zg_bs_g_videogames_d_sccl_13/000-0000000-0000000?psc=1 -> [599] len:52 -> result:None fol:0 msg:0 err:HTTPError('HTTP 599: TimeoutError: Navigation timeout of 3000 ms exceeded')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:13:50.091810", "level": "ERROR", "logger": "processor", "message": "process oioio:ebd3bb2e498825f66ee0563204501bc7 https://www.amazon.co.jp/%E3%80%90%E4%BB%BB%E5%A4%A9%E5%A0%82%E3%83%A9%E3%82%A4%E3%82%BB%E3%83%B3%E3%82%B9%E5%95%86%E5%93%81%E3%80%91SCREEN-GUARD-Nintendo-Switch-%E6%8C%87%E7%B4%8B%E9%98%B2%E6%AD%A2%E3%82%BF%E3%82%A4%E3%83%97/dp/B0F5WYM7RJ/ref=zg_bs_g_videogames_d_sccl_18/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:13:50.998593", "level": "ERROR", "logger": "processor", "message": "process oioio:24fb57bc98ce3a4f7e6d0e87031a3904 https://www.amazon.co.jp/Nintendo-Switch2-NintendoSwitch2-%E3%83%A9%E3%82%A6%E3%83%B3%E3%83%89%E3%82%A8%E3%83%83%E3%82%B8%E5%8A%A0%E5%B7%A5-%E3%82%AC%E3%82%A4%E3%83%89%E6%9E%A0%E3%80%91SENTM-2SWGD2-1/dp/B0DZVVQCBF/ref=zg_bs_g_videogames_d_sccl_22/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:13:53.307254", "level": "ERROR", "logger": "processor", "message": "process oioio:e990c28e6edce93ad5df8ddc759c0d04 https://www.amazon.co.jp/%E6%A9%9F%E5%8B%95%E6%88%A6%E5%A3%AB%E3%82%AC%E3%83%B3%E3%83%80%E3%83%A0SEED-BATTLE-DESTINY-REMASTERED-Switch/dp/B0DX65ZFDZ/ref=zg_bs_g_videogames_d_sccl_25/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:13:54.213883", "level": "ERROR", "logger": "processor", "message": "process oioio:81e809593e9f4525587f863f6149eeed https://www.amazon.co.jp/%E3%82%BD%E3%83%8B%E3%83%BC%E3%83%BB%E3%82%A4%E3%83%B3%E3%82%BF%E3%83%A9%E3%82%AF%E3%83%86%E3%82%A3%E3%83%96%E3%82%A8%E3%83%B3%E3%82%BF%E3%83%86%E3%82%A4%E3%83%B3%E3%83%A1%E3%83%B3%E3%83%88-%E3%80%90PS5%E3%80%91DEATH-STRANDING-DIRECTORS-CUT/dp/B09923SLTK/ref=zg_bs_g_videogames_d_sccl_26/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:14:00.833249", "level": "ERROR", "logger": "processor", "message": "process oioio:bbdbd3d611ef340d7a32cfcf7bb86567 https://www.amazon.co.jp/%E9%BE%8D%E3%81%AE%E5%9B%BD-%E3%83%AB%E3%83%BC%E3%83%B3%E3%83%95%E3%82%A1%E3%82%AF%E3%83%88%E3%83%AA%E3%83%BC-Nintendo-Switch-Switch2/dp/B0F5WKRKXD/ref=zg_bs_g_videogames_d_sccl_30/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:14:00.838304", "level": "ERROR", "logger": "processor", "message": "process oioio:971c616bfaac5a5793cc99c122fbbcb7 https://www.amazon.co.jp/Nintendo-Switch-Joy-%E3%83%8D%E3%82%AA%E3%83%B3%E3%83%96%E3%83%AB%E3%83%BC-%E3%83%8D%E3%82%AA%E3%83%B3%E3%83%AC%E3%83%83%E3%83%89/dp/B0BM46DFH1/ref=zg_bs_g_videogames_d_sccl_28/000-0000000-0000000?psc=1 -> [599] len:52 -> result:None fol:0 msg:0 err:HTTPError('HTTP 599: TimeoutError: Navigation timeout of 3000 ms exceeded')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:14:03.051137", "level": "ERROR", "logger": "processor", "message": "process oioio:2a97f7968ac12c7ab39dd20af81c75b8 https://www.amazon.co.jp/%E3%83%9D%E3%82%B1%E3%83%83%E3%83%88%E3%83%A2%E3%83%B3%E3%82%B9%E3%82%BF%E3%83%BC-%E3%83%90%E3%82%A4%E3%82%AA%E3%83%AC%E3%83%83%E3%83%88-Switch-%E3%80%90%E6%97%A9%E6%9C%9F%E8%B3%BC%E5%85%A5%E7%89%B9%E5%85%B8%E3%80%91%E3%83%97%E3%83%AD%E3%83%A2%E3%82%AB%E3%83%BC%E3%83%89%E3%80%8C%E3%83%94%E3%82%AB%E3%83%81%E3%83%A5%E3%82%A6%E3%80%8D-%C3%971/dp/B09X17GBLT/ref=zg_bs_g_videogames_d_sccl_1/000-0000000-0000000?psc=1 -> [599] len:52 -> result:None fol:0 msg:0 err:HTTPError('HTTP 599: TimeoutError: Navigation timeout of 3000 ms exceeded')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:14:03.057719", "level": "ERROR", "logger": "processor", "message": "process oioio:e22b05b6fd9355f9f221a278d985323b https://www.amazon.co.jp/Spigen-Switch2%E5%AF%BE%E5%BF%9C%E3%82%AF%E3%83%AA%E3%82%A2%E3%82%B1%E3%83%BC%E3%82%B9-%E3%83%89%E3%83%83%E3%82%AF%E5%AF%BE%E5%BF%9C-%E3%82%A8%E3%82%A2%E3%83%BB%E3%83%95%E3%82%A3%E3%83%83%E3%83%88-ACS09311%EF%BC%88%E3%82%AF%E3%83%AA%E3%82%B9%E3%82%BF%E3%83%AB%E3%83%BB%E3%82%AF%E3%83%AA%E3%82%A2%EF%BC%89/dp/B0DQ6WJ25Y/ref=zg_bs_g_videogames_d_sccl_2/000-0000000-0000000?psc=1 -> [599] len:52 -> result:None fol:0 msg:0 err:HTTPError('HTTP 599: TimeoutError: Navigation timeout of 3000 ms exceeded')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:14:03.970984", "level": "ERROR", "logger": "processor", "message": "process oioio:a50e37612337c25c9aafc6f04c562da9 https://www.amazon.co.jp/Nintendo-Switch-Sports-%E3%83%8B%E3%83%B3%E3%83%86%E3%83%B3%E3%83%89%E3%83%BC%E3%82%B9%E3%82%A4%E3%83%83%E3%83%81%E3%82%B9%E3%83%9D%E3%83%BC%E3%83%84-Switch/dp/B09S422VPV/ref=zg_bs_g_videogames_d_sccl_3/000-0000000-0000000?psc=1 -> [599] len:52 -> result:None fol:0 msg:0 err:HTTPError('HTTP 599: TimeoutError: Navigation timeout of 3000 ms exceeded')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:14:05.275981", "level": "ERROR", "logger": "processor", "message": "process oioio:b3bf30fdeda2e565a23736ab6410c380 https://www.amazon.co.jp/%E4%BB%BB%E5%A4%A9%E5%A0%82-%E3%83%8B%E3%83%B3%E3%83%86%E3%83%B3%E3%83%89%E3%83%BC%E3%83%97%E3%83%AA%E3%83%9A%E3%82%A4%E3%83%89%E7%95%AA%E5%8F%B7-2000%E5%86%86-%E3%82%AA%E3%83%B3%E3%83%A9%E3%82%A4%E3%83%B3%E3%82%B3%E3%83%BC%E3%83%89%E7%89%88/dp/B09998GN7Z/ref=zg_bs_g_videogames_d_sccl_4/000-0000000-0000000?psc=1 -> [599] len:52 -> result:None fol:0 msg:0 err:HTTPError('HTTP 599: TimeoutError: Navigation timeout of 3000 ms exceeded')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:14:06.185763", "level": "ERROR", "logger": "processor", "message": "process oioio:459ff73b6d5d85f800a25c97b58df933 https://www.amazon.co.jp/%E3%80%902025%E9%9D%A9%E6%96%B0%E3%83%A2%E3%83%87%E3%83%AB%E3%80%91-1000mAh%E5%A4%A7%E5%AE%B9%E9%87%8F-%E3%82%B8%E3%83%A3%E3%82%A4%E3%83%AD%E3%82%BB%E3%83%B3%E3%82%B5%E3%83%BC%E6%90%AD%E8%BC%89-Switch%E5%85%A8%E4%B8%96%E4%BB%A3%E3%81%AB%E5%AF%BE%E5%BF%9C-%E6%97%A5%E6%9C%AC%E8%AA%9E%E5%8F%96%E6%89%B1%E8%AA%AC%E6%98%8E%E6%9B%B8%E4%BB%98%E3%81%8D/dp/B0DZCWJGBH/ref=zg_bs_g_videogames_d_sccl_5/000-0000000-0000000?psc=1 -> [599] len:52 -> result:None fol:0 msg:0 err:HTTPError('HTTP 599: TimeoutError: Navigation timeout of 3000 ms exceeded')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:14:07.097843", "level": "ERROR", "logger": "processor", "message": "process oioio:d73c3dc2a2c549c30948773f7c4096b3 https://www.amazon.co.jp/%E3%82%BC%E3%83%AB%E3%83%80%E3%81%AE%E4%BC%9D%E8%AA%AC-%E3%83%96%E3%83%AC%E3%82%B9-%E3%82%AA%E3%83%96-%E3%83%AF%E3%82%A4%E3%83%AB%E3%83%89-Switch/dp/B01N12HJHQ/ref=zg_bs_g_videogames_d_sccl_6/000-0000000-0000000?psc=1 -> [599] len:52 -> result:None fol:0 msg:0 err:HTTPError('HTTP 599: TimeoutError: Navigation timeout of 3000 ms exceeded')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:14:08.109731", "level": "ERROR", "logger": "processor", "message": "process oioio:a38e24416ebc85d906fb0d3e7c5ca40a https://www.amazon.co.jp/Lunriwis-%E3%82%BC%E3%83%AB%E3%83%80%E7%84%A1%E5%8F%8C-%E5%8E%84%E7%81%BD%E3%81%AE%E9%BB%99%E7%A4%BA%E9%8C%B2-Switch/dp/B08HSPBMTY/ref=zg_bs_g_videogames_d_sccl_7/000-0000000-0000000?psc=1 -> [599] len:52 -> result:None fol:0 msg:0 err:HTTPError('HTTP 599: TimeoutError: Navigation timeout of 3000 ms exceeded')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:14:09.417251", "level": "ERROR", "logger": "processor", "message": "process oioio:deb9a0321d72ac67ce3c7f00f733afb0 https://www.amazon.co.jp/%E3%82%BD%E3%83%8B%E3%83%BC%E3%83%BB%E3%82%A4%E3%83%B3%E3%82%BF%E3%83%A9%E3%82%AF%E3%83%86%E3%82%A3%E3%83%96%E3%82%A8%E3%83%B3%E3%82%BF%E3%83%86%E3%82%A4%E3%83%B3%E3%83%A1%E3%83%B3%E3%83%88-%E3%80%90PS4%E3%80%91%E3%82%B0%E3%83%A9%E3%83%B3%E3%83%84%E3%83%BC%E3%83%AA%E3%82%B9%E3%83%A27/dp/B09TWQ6H74/ref=zg_bs_g_videogames_d_sccl_8/000-0000000-0000000?psc=1 -> [599] len:52 -> result:None fol:0 msg:0 err:HTTPError('HTTP 599: TimeoutError: Navigation timeout of 3000 ms exceeded')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:14:10.335836", "level": "ERROR", "logger": "processor", "message": "process oioio:46a6af0206459afc249a5fcdf78a4949 https://www.amazon.co.jp/%E3%83%AF%E3%82%A4%E3%83%A4%E3%83%AC%E3%82%B9%E3%82%B3%E3%83%B3%E3%83%88%E3%83%AD%E3%83%BC%E3%83%A9%E3%83%BC-DUALSHOCK-%E3%82%B8%E3%82%A7%E3%83%83%E3%83%88%E3%83%BB%E3%83%96%E3%83%A9%E3%83%83%E3%82%AF-%E3%80%90Amazon-co-jp%E7%89%B9%E5%85%B8%E3%80%91CYBER-PS4%E7%94%A8%E3%82%B3%E3%83%B3%E3%83%88%E3%83%AD%E3%83%BC%E3%83%A9%E3%83%BC%E5%85%85%E9%9B%BB%E3%82%B1%E3%83%BC%E3%83%96%E3%83%AB3m/dp/B07GYMT9GF/ref=zg_bs_g_videogames_d_sccl_9/000-0000000-0000000?psc=1 -> [599] len:52 -> result:None fol:0 msg:0 err:HTTPError('HTTP 599: TimeoutError: Navigation timeout of 3000 ms exceeded')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:14:11.350797", "level": "ERROR", "logger": "processor", "message": "process oioio:386699f79cbedb9e04b535d98491571c https://www.amazon.co.jp/Pok%C3%A9mon-%EF%BC%88%E3%83%9D%E3%82%B1%E3%83%A2%E3%83%B3-%E3%83%97%E3%83%A9%E3%82%B9%E3%83%97%E3%83%A9%E3%82%B9%EF%BC%89-%E3%80%90Amazon-co-jp%E9%99%90%E5%AE%9A%E3%80%91%E3%82%AA%E3%83%AA%E3%82%B8%E3%83%8A%E3%83%AB%E3%83%87%E3%82%B6%E3%82%A4%E3%83%B3-%E3%83%9E%E3%82%A4%E3%82%AF%E3%83%AD%E3%83%95%E3%82%A1%E3%82%A4%E3%83%90%E3%83%BC%E3%82%AF%E3%83%AD%E3%82%B9/dp/B0CLYCCG1N/ref=zg_bs_g_videogames_d_sccl_10/000-0000000-0000000?psc=1 -> [599] len:52 -> result:None fol:0 msg:0 err:HTTPError('HTTP 599: TimeoutError: Navigation timeout of 3000 ms exceeded')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:14:12.371461", "level": "ERROR", "logger": "processor", "message": "process oioio:c58db4d01bfb9c6d4db8a6101b33c21e https://www.amazon.co.jp/%E4%BB%BB%E5%A4%A9%E5%A0%82-%E3%80%90Switch%E7%94%A8%E8%BF%BD%E5%8A%A0%E3%82%B3%E3%83%B3%E3%83%86%E3%83%B3%E3%83%84%E3%80%91%E3%83%9D%E3%82%B1%E3%83%83%E3%83%88%E3%83%A2%E3%83%B3%E3%82%B9%E3%82%BF%E3%83%BC-%E3%82%B9%E3%82%AB%E3%83%BC%E3%83%AC%E3%83%83%E3%83%88%E3%83%BB%E3%83%90%E3%82%A4%E3%82%AA%E3%83%AC%E3%83%83%E3%83%88-%E3%82%BC%E3%83%AD%E3%81%AE%E7%A7%98%E5%AE%9D-%E3%82%AA%E3%83%B3%E3%83%A9%E3%82%A4%E3%83%B3%E3%82%B3%E3%83%BC%E3%83%89%E7%89%88/dp/B0BWQQ1GW3/ref=zg_bs_g_videogames_d_sccl_11/000-0000000-0000000?psc=1 -> [599] len:52 -> result:None fol:0 msg:0 err:HTTPError('HTTP 599: TimeoutError: Navigation timeout of 3000 ms exceeded')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:14:13.678183", "level": "ERROR", "logger": "processor", "message": "process oioio:b5821af3824dc7d5b8ff7e009a695375 https://www.amazon.co.jp/%E3%82%BB%E3%82%AC-%E3%81%B7%E3%82%88%E3%81%B7%E3%82%88%E3%83%86%E3%83%88%E3%83%AA%E3%82%B92-%E3%82%B9%E3%83%9A%E3%82%B7%E3%83%A3%E3%83%AB%E3%83%97%E3%83%A9%E3%82%A4%E3%82%B9-Switch/dp/B0BBFKZWZL/ref=zg_bs_g_videogames_d_sccl_12/000-0000000-0000000?psc=1 -> [599] len:52 -> result:None fol:0 msg:0 err:HTTPError('HTTP 599: TimeoutError: Navigation timeout of 3000 ms exceeded')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:14:14.600211", "level": "ERROR", "logger": "processor", "message": "process oioio:e0d28106efbcdbcf6c2cf0db2ec7c2fd https://www.amazon.co.jp/%E3%82%BD%E3%83%8B%E3%83%BC-%E3%82%B2%E3%83%BC%E3%83%9F%E3%83%B3%E3%82%B0%E3%82%A4%E3%83%A4%E3%83%9B%E3%83%B3-INZONE-Buds-USBType-C%E3%83%88%E3%83%A9%E3%83%B3%E3%82%B7%E3%83%BC%E3%83%90%E3%83%BC%E5%90%8C%E6%A2%B1/dp/B0CKFHB9P3/ref=zg_bs_g_videogames_d_sccl_13/000-0000000-0000000?psc=1 -> [599] len:52 -> result:None fol:0 msg:0 err:HTTPError('HTTP 599: TimeoutError: Navigation timeout of 3000 ms exceeded')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:16:28.410406", "level": "ERROR", "logger": "processor", "message": "process oioio:c81792f942c90564ca29b8fdcc76b35e https://www.amazon.co.jp/%E3%82%BD%E3%83%8B%E3%83%BC%E3%83%BB%E3%82%A4%E3%83%B3%E3%82%BF%E3%83%A9%E3%82%AF%E3%83%86%E3%82%A3%E3%83%96%E3%82%A8%E3%83%B3%E3%82%BF%E3%83%86%E3%82%A4%E3%83%B3%E3%83%A1%E3%83%B3%E3%83%88-%E3%80%90PS5%E3%80%91%E3%82%B0%E3%83%A9%E3%83%B3%E3%83%84%E3%83%BC%E3%83%AA%E3%82%B9%E3%83%A27/dp/B09H2X6R6C/ref=zg_bs_g_videogames_d_sccl_1/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:16:28.415425", "level": "ERROR", "logger": "processor", "message": "process oioio:558fcd3a023ec03dcf2797819589d46a https://www.amazon.co.jp/%E4%BB%BB%E5%A4%A9%E5%A0%82-Nintendo-Switch-Lite-%E3%82%B0%E3%83%AC%E3%83%BC/dp/B07X47QTN3/ref=zg_bs_g_videogames_d_sccl_3/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:16:28.720845", "level": "ERROR", "logger": "processor", "message": "process oioio:2474f2329d721afde0ffa7b5d76c5b88 https://www.amazon.co.jp/%E4%BB%BB%E5%A4%A9%E5%A0%82-%E6%98%9F%E3%81%AE%E3%82%AB%E3%83%BC%E3%83%93%E3%82%A3-%E3%83%87%E3%82%A3%E3%82%B9%E3%82%AB%E3%83%90%E3%83%AA%E3%83%BC-Switch/dp/B09Q5GM5PQ/ref=zg_bs_g_videogames_d_sccl_20/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:16:29.326526", "level": "ERROR", "logger": "processor", "message": "process oioio:0387a17765f14d9c5a145a1ca11ed588 https://www.amazon.co.jp/%E4%BB%BB%E5%A4%A9%E5%A0%82-%E3%83%8B%E3%83%B3%E3%83%86%E3%83%B3%E3%83%89%E3%83%BC%E3%83%97%E3%83%AA%E3%83%9A%E3%82%A4%E3%83%89%E7%95%AA%E5%8F%B7-3000%E5%86%86-%E3%82%AA%E3%83%B3%E3%83%A9%E3%82%A4%E3%83%B3%E3%82%B3%E3%83%BC%E3%83%89%E7%89%88/dp/B09996CH6H/ref=zg_bs_g_videogames_d_sccl_5/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:16:30.537258", "level": "ERROR", "logger": "processor", "message": "process oioio:ba596a2016e51fdcb4526f12208b189d https://www.amazon.co.jp/%E3%82%BD%E3%83%8B%E3%83%BC%E3%83%BB%E3%82%A4%E3%83%B3%E3%82%BF%E3%83%A9%E3%82%AF%E3%83%86%E3%82%A3%E3%83%96%E3%82%A8%E3%83%B3%E3%82%BF%E3%83%86%E3%82%A4%E3%83%B3%E3%83%A1%E3%83%B3%E3%83%88-%E3%80%90PS5%E3%80%91%E3%82%A2%E3%82%B9%E3%83%88%E3%83%AD%E3%83%9C%E3%83%83%E3%83%88-ASTRO-BOT/dp/B0D66L4TMB/ref=zg_bs_g_videogames_d_sccl_6/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:16:31.442633", "level": "ERROR", "logger": "processor", "message": "process oioio:6a8b4a6790db0819a13aa9abfcee0358 https://www.amazon.co.jp/%E3%80%901%E6%9E%9A%E3%82%BB%E3%83%83%E3%83%88%E3%80%91-Switch-%E8%B2%BC%E3%82%8A%E4%BB%98%E3%81%91%E3%82%AD%E3%83%83%E3%83%88%E4%BB%98%E3%81%8D-%E3%82%AC%E3%83%A9%E3%82%B9%E3%83%95%E3%82%A3%E3%83%AB%E3%83%A0-%E9%AB%98%E6%84%9F%E5%BA%A6%E3%82%BF%E3%83%83%E3%83%81/dp/B0F1NFBW9P/ref=zg_bs_g_videogames_d_sccl_8/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:16:32.349855", "level": "ERROR", "logger": "processor", "message": "process oioio:620c0b82784fa94badde4d8865cb7f2a https://www.amazon.co.jp/%E3%83%97%E3%83%AC%E3%82%A4%E3%82%B9%E3%83%86%E3%83%BC%E3%82%B7%E3%83%A7%E3%83%B3-%E3%82%B9%E3%83%88%E3%82%A2%E3%83%81%E3%82%B1%E3%83%83%E3%83%88-3-000%E5%86%86-%E3%82%AA%E3%83%B3%E3%83%A9%E3%82%A4%E3%83%B3%E3%82%B3%E3%83%BC%E3%83%89%E7%89%88/dp/B08M6DTZ38/ref=zg_bs_g_videogames_d_sccl_9/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:16:33.655803", "level": "ERROR", "logger": "processor", "message": "process oioio:dc73e474504cf2f2b5344cfec29e4a7f https://www.amazon.co.jp/%E3%80%90PS5%E3%80%91Ghost-Y%C5%8Dtei-%E3%82%B4%E3%83%BC%E3%82%B9%E3%83%88%E3%83%BB%E3%82%AA%E3%83%96%E3%83%BB%E3%83%A8%E3%82%A6%E3%83%86%E3%82%A4-%E3%80%90%E6%97%A9%E6%9C%9F%E8%B3%BC%E5%85%A5%E7%89%B9%E5%85%B8%E3%80%91%E3%83%BB%E3%82%B2%E3%83%BC%E3%83%A0%E5%86%85%E3%82%A2%E3%82%A4%E3%83%86%E3%83%A0-%E3%83%BB%E7%AF%A4%E3%81%A8%E7%BE%8A%E8%B9%84%E5%85%AD%E4%BA%BA%E8%A1%86%E3%81%AE%E3%82%A2%E3%83%90%E3%82%BF%E3%83%BC%E3%82%BB%E3%83%83%E3%83%88%EF%BC%88%E5%85%A8%EF%BC%97%E7%A8%AE%EF%BC%89%EF%BC%88%E5%B0%81%E5%85%A5%EF%BC%89/dp/B0F6CZ4RNN/ref=zg_bs_g_videogames_d_sccl_10/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:16:34.660012", "level": "ERROR", "logger": "processor", "message": "process oioio:a28e3e6b5c1617b0605adb50fbd3d866 https://www.amazon.co.jp/%E3%83%97%E3%83%AC%E3%82%A4%E3%82%B9%E3%83%86%E3%83%BC%E3%82%B7%E3%83%A7%E3%83%B3-%E3%82%B9%E3%83%88%E3%82%A2%E3%83%81%E3%82%B1%E3%83%83%E3%83%88-5-000%E5%86%86-%E3%82%AA%E3%83%B3%E3%83%A9%E3%82%A4%E3%83%B3%E3%82%B3%E3%83%BC%E3%83%89%E7%89%88/dp/B08M69WPHZ/ref=zg_bs_g_videogames_d_sccl_11/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:16:35.568089", "level": "ERROR", "logger": "processor", "message": "process oioio:222955742b64ef18e5d8f02beb9019d6 https://www.amazon.co.jp/TURBO%E9%80%A3%E5%B0%84%E6%A9%9F%E8%83%BD-1000mAh%E5%A4%A7%E5%AE%B9%E9%87%8F-Bluetooth-%E3%82%B8%E3%83%A3%E3%82%A4%E3%83%AD%E3%82%BB%E3%83%B3%E3%82%B5%E3%83%BC%E6%90%AD%E8%BC%89-%E6%97%A5%E6%9C%AC%E8%AA%9E%E5%8F%96%E6%89%B1%E8%AA%AC%E6%98%8E%E6%9B%B8%E4%BB%98%E3%81%8D/dp/B0D8KWTL2C/ref=zg_bs_g_videogames_d_sccl_12/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:18:15.436228", "level": "ERROR", "logger": "processor", "message": "process amazon:76fbc53be1fc6627d0ceaeaf87d6752c https://www.amazon.co.jp/%E3%83%91%E3%82%A4%E3%82%AA%E3%83%8B%E3%82%A2-G400-00010-%E3%81%AE%E3%81%B3%E3%81%AE%E3%81%B3%E3%82%B9%E3%83%9D%E3%83%BC%E3%83%84%E3%82%BC%E3%83%83%E3%82%B1%E3%83%B3-%E4%B8%AD/dp/B00197JLY4/ref=zg_bs_g_15218661_d_sccl_4/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:18:16.745969", "level": "ERROR", "logger": "processor", "message": "process amazon:c6e5be98435222feabeb591d1d9b3da2 https://www.amazon.co.jp/%E3%83%9F%E3%82%BA%E3%83%8E-%E3%83%88%E3%83%AC%E3%83%BC%E3%83%8B%E3%83%B3%E3%82%B0%E3%82%A6%E3%82%A7%E3%82%A2-%E3%83%8A%E3%83%93%E3%83%89%E3%83%A9%E3%82%A4-%E3%80%90Amazon%E9%99%90%E5%AE%9A%E3%83%A2%E3%83%87%E3%83%AB%E3%81%82%E3%82%8A%E3%80%91-32MAC190/dp/B0D6GGGD96/ref=zg_bs_g_15218661_d_sccl_2/000-0000000-0000000?psc=1 -> [599] len:52 -> result:None fol:0 msg:0 err:HTTPError('HTTP 599: TimeoutError: Navigation timeout of 3000 ms exceeded')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:18:16.759933", "level": "ERROR", "logger": "processor", "message": "process amazon:5b478df09beb1b2adfdb8636c6000f38 https://www.amazon.co.jp/%E3%82%A2%E3%83%B3%E3%83%80%E3%83%BC%E3%82%A2%E3%83%BC%E3%83%9E%E3%83%BC-Tech-2-0-1358553-%E6%97%A5%E6%9C%AC%E3%82%B5%E3%82%A4%E3%82%BAL%E7%9B%B8%E5%BD%93/dp/B07T1ZBS4K/ref=zg_bs_g_15218661_d_sccl_1/000-0000000-0000000?psc=1 -> [599] len:52 -> result:None fol:0 msg:0 err:HTTPError('HTTP 599: TimeoutError: Navigation timeout of 3000 ms exceeded')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:18:16.769966", "level": "ERROR", "logger": "processor", "message": "process amazon:8b4160b9a9470f3eba5ce50c256b0e87 https://www.amazon.co.jp/%E3%82%A2%E3%83%B3%E3%83%80%E3%83%BC%E3%82%A2%E3%83%BC%E3%83%9E%E3%83%BC-%E3%82%B7%E3%83%A3%E3%83%84-Tech-%E3%83%A1%E3%83%B3%E3%82%BA-%E6%97%A5%E6%9C%AC%E3%82%B5%E3%82%A4%E3%82%BAL%E7%9B%B8%E5%BD%93/dp/B08BX61C8J/ref=zg_bs_g_15218661_d_sccl_3/000-0000000-0000000?psc=1 -> [599] len:52 -> result:None fol:0 msg:0 err:HTTPError('HTTP 599: TimeoutError: Navigation timeout of 3000 ms exceeded')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:18:17.375741", "level": "ERROR", "logger": "processor", "message": "process amazon:5a3f8a1456571569a419e5480db93d16 https://www.amazon.co.jp/%E3%83%86%E3%82%B9%E3%83%A9-%E3%82%B3%E3%83%B3%E3%83%97%E3%83%AC%E3%83%83%E3%82%B7%E3%83%A7%E3%83%B3%E3%82%A6%E3%82%A7%E3%82%A2-UV%E3%82%AB%E3%83%83%E3%83%88%E3%83%BB%E5%90%B8%E6%B1%97%E9%80%9F%E4%B9%BE-%E3%82%B3%E3%83%B3%E3%83%97%E3%83%AC%E3%83%83%E3%82%B7%E3%83%A7%E3%83%B3%E3%82%B7%E3%83%A3%E3%83%84-MUV01-JPA_L/dp/B0BF9NLCTL/ref=zg_bs_g_15218661_d_sccl_6/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:18:21.003650", "level": "ERROR", "logger": "processor", "message": "process amazon:1d3e5bf2f454b7801d9ae1a619178b2b https://www.amazon.co.jp/%E3%83%A0%E3%83%BC%E3%83%96%E3%82%B9%E3%83%9D%E3%83%BC%E3%83%84-%E3%83%88%E3%83%AC%E3%83%BC%E3%83%8B%E3%83%B3%E3%82%B0-%E3%83%A9%E3%83%B3%E3%83%8B%E3%83%B3%E3%82%B0-%E3%82%A6%E3%82%A9%E3%83%BC%E3%82%AD%E3%83%B3%E3%82%B0-ST5SHTT0M/dp/B0DJQSS2KR/ref=zg_bs_g_15218661_d_sccl_9/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:18:26.183598", "level": "ERROR", "logger": "processor", "message": "process amazon:4c2326cea99bb51216c8caa4494c44ed https://www.amazon.co.jp/%E3%82%B6%E3%83%8E%E3%83%BC%E3%82%B9%E3%83%95%E3%82%A7%E3%82%A4%E3%82%B9-%E3%82%AB%E3%83%83%E3%83%88%E3%82%BD%E3%83%BC-T%E3%82%B7%E3%83%A3%E3%83%84-Square-Traverse/dp/B0D6N3KP1B/ref=zg_bs_g_15218661_d_sccl_11/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:18:28.679616", "level": "ERROR", "logger": "processor", "message": "process amazon:cf7b3a35721a45f47c66eac4bef9f1f8 https://www.amazon.co.jp/%E3%82%A2%E3%83%B3%E3%83%80%E3%83%BC%E3%82%A2%E3%83%BC%E3%83%9E%E3%83%BC-TECH-LOGO-Mens-%E6%97%A5%E6%9C%AC%E3%82%B5%E3%82%A4%E3%82%BAL%E7%9B%B8%E5%BD%93/dp/B07SYGTP6X/ref=zg_bs_g_15218661_d_sccl_13/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:18:31.159853", "level": "ERROR", "logger": "processor", "message": "process amazon:da52edfbc45e39829e0e96edfcfa8485 https://www.amazon.co.jp/%E3%83%9F%E3%82%BA%E3%83%8E-%E3%83%88%E3%83%AC%E3%83%BC%E3%83%8B%E3%83%B3%E3%82%B0%E3%82%A6%E3%82%A7%E3%82%A2-%E3%80%90Amazon%E9%99%90%E5%AE%9A%E3%83%A2%E3%83%87%E3%83%AB%E3%81%82%E3%82%8A%E3%80%91-32MAC195-%E3%83%89%E3%83%AC%E3%82%B9%E3%83%8D%E3%82%A4%E3%83%93%E3%83%BC/dp/B0D7VGLPCM/ref=zg_bs_g_15218661_d_sccl_16/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:18:33.682254", "level": "ERROR", "logger": "processor", "message": "process amazon:d30372cf4d24840d6e1fa7a02e1e5c5f https://www.amazon.co.jp/%E3%83%97%E3%83%BC%E3%83%9E-%E3%82%AB%E3%82%B8%E3%83%A5%E3%82%A2%E3%83%AB-%E3%82%B9%E3%83%A2%E3%83%BC%E3%83%AB-%E3%83%97%E3%83%BC%E3%83%9E%E3%83%AD%E3%82%B4-687561/dp/B0D5CCPQLW/ref=zg_bs_g_15218661_d_sccl_17/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:18:33.687302", "level": "ERROR", "logger": "processor", "message": "process amazon:ebea0373d6e3b63420f976d112c67043 https://www.amazon.co.jp/%E3%82%A2%E3%83%B3%E3%83%80%E3%83%BC%E3%82%A2%E3%83%BC%E3%83%9E%E3%83%BC-%E3%82%B3%E3%83%B3%E3%83%97%E3%83%AC%E3%83%83%E3%82%B7%E3%83%A7%E3%83%B3%E3%82%A6%E3%82%A7%E3%82%A2-Armour-Black-White/dp/B0874X72WP/ref=zg_bs_g_15218661_d_sccl_15/000-0000000-0000000?psc=1 -> [599] len:52 -> result:None fol:0 msg:0 err:HTTPError('HTTP 599: TimeoutError: Navigation timeout of 3000 ms exceeded')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:18:33.695934", "level": "ERROR", "logger": "processor", "message": "process amazon:0253a29e0eeda50fa25b69d195ad6895 https://www.amazon.co.jp/%E3%83%9F%E3%82%BA%E3%83%8E-%E3%83%88%E3%83%AC%E3%83%BC%E3%83%8B%E3%83%B3%E3%82%B0%E3%82%A6%E3%82%A7%E3%82%A2-%E5%8D%8A%E8%A2%96T%E3%82%B7%E3%83%A3%E3%83%84-%E5%90%B8%E6%B1%97%E9%80%9F%E4%B9%BE-32MAA156/dp/B0B3RNS3F8/ref=zg_bs_g_15218661_d_sccl_18/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:18:33.713791", "level": "ERROR", "logger": "processor", "message": "process amazon:99fde82e7e915d5fbe44209c9167f888 https://www.amazon.co.jp/%E3%83%9E%E3%83%BC%E3%82%AB%E3%83%BC%E3%82%B3%E3%83%BC%E3%83%B3-%E3%83%88%E3%83%AC%E3%83%BC%E3%83%8B%E3%83%B3%E3%82%B0%E3%82%B3%E3%83%BC%E3%83%B3-%E3%83%89%E3%83%AA%E3%83%96%E3%83%AB%E3%83%9E%E3%83%BC%E3%82%AB%E3%83%BC-%E3%83%86%E3%83%8B%E3%82%B9%E7%B7%B4%E7%BF%92%E3%82%B3%E3%83%BC%E3%83%B3-%E5%8F%8E%E7%B4%8D%E8%A2%8B%E3%83%BB%E5%B0%82%E7%94%A8%E3%82%B9%E3%82%BF%E3%83%B3%E3%83%89%E4%BB%98%E3%81%8D/dp/B0BXXFZ8GL/ref=zg_bs_g_15218661_d_sccl_19/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:18:33.719830", "level": "ERROR", "logger": "processor", "message": "process amazon:f689fdd47104e5a0d63d2055c09882b9 https://www.amazon.co.jp/phiten-%E3%83%95%E3%82%A1%E3%82%A4%E3%83%86%E3%83%B3-RAKU%E3%82%B7%E3%83%A3%E3%83%84-%E3%82%B0%E3%83%A9%E3%83%87%E3%83%BC%E3%82%B7%E3%83%A7%E3%83%B3-%E8%B2%A9%E8%B7%AF%E9%99%90%E5%AE%9A%E3%83%87%E3%82%B6%E3%82%A4%E3%83%B3/dp/B0F18QVVJ3/ref=zg_bs_g_15218661_d_sccl_22/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:18:36.187681", "level": "ERROR", "logger": "processor", "message": "process amazon:bce1890f85f5ba969c5a84562e757b39 https://www.amazon.co.jp/%E3%82%A2%E3%83%B3%E3%83%80%E3%83%BC%E3%82%A2%E3%83%BC%E3%83%9E%E3%83%BC-Compression-%E3%83%AC%E3%83%87%E3%82%A3%E3%83%BC%E3%82%B9-1358604-%E6%97%A5%E6%9C%AC%E3%82%B5%E3%82%A4%E3%82%BAM%E7%9B%B8%E5%BD%93/dp/B07SZYHDJZ/ref=zg_bs_g_15218661_d_sccl_23/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:18:38.719285", "level": "ERROR", "logger": "processor", "message": "process amazon:340f1dd511880a75afc97e1c315704a5 https://www.amazon.co.jp/%E3%83%AB%E3%82%B3%E3%83%83%E3%82%AF%E3%82%B9%E3%83%9D%E3%83%AB%E3%83%86%E3%82%A3%E3%83%95-%E3%83%8F%E3%83%BC%E3%83%95%E3%82%B8%E3%83%83%E3%83%97%E5%8D%8A%E8%A2%96%E3%82%B7%E3%83%A3%E3%83%84-%E5%8D%8A%E8%A2%96%E3%82%B7%E3%83%A3%E3%83%84-%E3%83%AF%E3%83%B3%E3%83%9D%E3%82%A4%E3%83%B3%E3%83%88-%E3%83%88%E3%83%AC%E3%83%BC%E3%83%8B%E3%83%B3%E3%82%B0/dp/B096VSMFRH/ref=zg_bs_g_15218661_d_sccl_25/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:18:41.205459", "level": "ERROR", "logger": "processor", "message": "process amazon:53de1d3e3d19ace218080e027e8d5d33 https://www.amazon.co.jp/RACURIA-%E3%82%B9%E3%83%9D%E3%83%BC%E3%83%84%E7%94%A8%E3%83%9E%E3%82%A6%E3%82%B9%E3%83%94%E3%83%BC%E3%82%B9-%E3%80%90%E9%A3%9F%E5%93%81%E8%A1%9B%E7%94%9F%E8%A9%A6%E9%A8%93%E9%81%A9%E5%90%88-%E6%AD%AF%E7%A7%91%E8%A1%9B%E7%94%9F%E5%A3%AB%E7%9B%A3%E4%BF%AE%E3%80%91%E5%8F%8E%E7%B4%8D%E3%82%B1%E3%83%BC%E3%82%B9-%E5%8F%96%E6%89%B1%E8%AA%AC%E6%98%8E%E6%9B%B8%E4%BB%98%E3%81%8D/dp/B0DMJ9972F/ref=zg_bs_g_15218661_d_sccl_26/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:18:41.210176", "level": "ERROR", "logger": "processor", "message": "process amazon:ca8d9a662fdda6a73165d40f7e0a5f49 https://www.amazon.co.jp/EVANE-4WAY-%E3%82%B9%E3%83%88%E3%83%AC%E3%83%83%E3%83%81-%E3%82%BB%E3%83%83%E3%83%88%E3%82%A4%E3%83%B3-%E3%83%88%E3%83%AC%E3%83%BC%E3%83%8B%E3%83%B3%E3%82%B0T%E3%82%B7%E3%83%A3%E3%83%84/dp/B0D83PTQ8L/ref=zg_bs_g_15218661_d_sccl_27/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:18:41.213855", "level": "ERROR", "logger": "processor", "message": "process amazon:7fcd528091073a7de4bd1ed406d77a6f https://www.amazon.co.jp/%E3%80%90%E5%8C%BB%E5%B8%AB%E7%9B%A3%E4%BF%AE%E3%80%91%E3%82%B9%E3%83%9D%E3%83%BC%E3%83%84%E7%94%A8%E3%83%9E%E3%82%A6%E3%82%B9%E3%83%94%E3%83%BC%E3%82%B9%E3%80%9090%E7%A7%923%E3%82%B9%E3%83%86%E3%83%83%E3%83%97%E3%81%AE%E7%B0%A1%E5%8D%98%E5%9E%8B%E5%8F%96%E3%82%8A%E3%80%91%E6%AD%AF%E7%A7%91%E6%9D%90%E6%96%99EVA%E4%BD%BF%E7%94%A8-%E5%B0%82%E7%94%A8%E3%82%B1%E3%83%BC%E3%82%B9%E4%BB%98-%E3%83%8A%E3%82%A4%E3%83%88%E3%82%AC%E3%83%BC%E3%83%89-%E3%83%9E%E3%82%A6%E3%82%B9%E3%82%AC%E3%83%BC%E3%83%89-BULQREA/dp/B0DZBPBQHZ/ref=zg_bs_g_15218661_d_sccl_28/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:18:41.217664", "level": "ERROR", "logger": "processor", "message": "process amazon:e155c24fcdfc0c3d629c7e2cd1317d0c https://www.amazon.co.jp/%E3%82%A2%E3%83%B3%E3%83%80%E3%83%BC%E3%82%A2%E3%83%BC%E3%83%9E%E3%83%BC-%E3%82%B3%E3%83%B3%E3%83%97%E3%83%AC%E3%83%83%E3%82%B7%E3%83%A7%E3%83%B3%E3%82%A6%E3%82%A7%E3%82%A2-HEATGEAR-FITTED-SLEEVE/dp/B095YK8ZJF/ref=zg_bs_g_15218661_d_sccl_29/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:18:41.823391", "level": "ERROR", "logger": "processor", "message": "process amazon:d90c47d224ad3cc68e915f7e03dbb57a https://www.amazon.co.jp/DAYCLOSET-%E3%83%A8%E3%82%AC%E3%82%A6%E3%82%A7%E3%82%A2%E3%83%88%E3%83%83%E3%83%97%E3%82%B9-%E3%83%A8%E3%82%ACt%E3%82%B7%E3%83%A3%E3%83%84-yoga125-%E3%82%A6%E3%82%A9%E3%83%BC%E3%83%A0%E3%82%B0%E3%83%AC%E3%83%BC/dp/B0CLCHXQ6K/ref=zg_bs_g_15218661_d_sccl_30/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:18:44.131840", "level": "ERROR", "logger": "processor", "message": "process amazon:2a9437f3c15ffc5a867e90430feac89d https://www.amazon.co.jp/%E3%82%A2%E3%83%B3%E3%83%80%E3%83%BC%E3%82%A2%E3%83%BC%E3%83%9E%E3%83%BC-UA-TECH-LOGO-Black/dp/B0D3TZ5PJK/ref=zg_bs_g_15218661_d_sccl_3/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:18:49.989932", "level": "ERROR", "logger": "processor", "message": "process amazon:e3164ad208d167aa65bb0697328dfa6c https://www.amazon.co.jp/%E3%83%9E%E3%82%A6%E3%82%B9%E3%83%94%E3%83%BC%E3%82%B9-%E3%83%9E%E3%82%A6%E3%82%B9%E3%82%AC%E3%83%BC%E3%83%89-%E3%82%B1%E3%83%BC%E3%82%B9%E4%BB%98%E3%81%8D-%E3%83%9C%E3%82%AF%E3%82%B7%E3%83%B3%E3%82%B0-%E3%82%AF%E3%83%AA%E3%82%A2%E3%83%96%E3%83%A9%E3%83%83%E3%82%AF/dp/B07KGQW8S2/ref=zg_bs_g_15218661_d_sccl_5/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:18:52.675855", "level": "ERROR", "logger": "processor", "message": "process amazon:74157678c179a3c6322d0923b373f421 https://www.amazon.co.jp/%E3%82%B6%E3%83%8E%E3%83%BC%E3%82%B9%E3%83%95%E3%82%A7%E3%82%A4%E3%82%B9-%E5%8D%8A%E8%A2%96%E3%82%B7%E3%83%A3%E3%83%84-Nuptse-Shirt-%E3%82%A2%E3%82%B9%E3%83%95%E3%82%A1%E3%83%AB%E3%83%88%E3%82%B0%E3%83%AC%E3%83%BC/dp/B0D6N6DS2W/ref=zg_bs_g_15218661_d_sccl_8/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:18:57.634522", "level": "ERROR", "logger": "processor", "message": "process amazon:b813fd6e6bf601179e55ee7d9b2b23fb https://www.amazon.co.jp/%E3%83%A8%E3%83%8D%E3%83%83%E3%82%AF%E3%82%B9-%E3%83%86%E3%83%8B%E3%82%B9%E3%82%A6%E3%82%A7%E3%82%A2-%E3%83%90%E3%83%89%E3%83%9F%E3%83%B3%E3%83%88%E3%83%B3%E3%82%A6%E3%82%A7%E3%82%A2-%E3%82%B9%E3%82%BF%E3%83%B3%E3%83%80%E3%83%BC%E3%83%89%E3%82%B5%E3%82%A4%E3%82%BA-RWHI1301/dp/B01M1R98QP/ref=zg_bs_g_15218661_d_sccl_10/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:18:57.640296", "level": "ERROR", "logger": "processor", "message": "process amazon:02368774ba5c4be06248469d44a89b0b https://www.amazon.co.jp/%E3%83%9F%E3%82%BA%E3%83%8E-%E3%83%88%E3%83%AC%E3%83%BC%E3%83%8B%E3%83%B3%E3%82%B0%E3%82%A6%E3%82%A7%E3%82%A2-%E3%82%BF%E3%83%B3%E3%82%AF%E3%83%88%E3%83%83%E3%83%97-%E3%83%8E%E3%83%BC%E3%82%B9%E3%83%AA%E3%83%BC%E3%83%96%E3%82%B7%E3%83%A3%E3%83%84-32MAC199/dp/B0D6G9SSBJ/ref=zg_bs_g_15218661_d_sccl_11/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:19:00.408224", "level": "ERROR", "logger": "processor", "message": "process amazon:1402584190b1bc019ecb8d7b20295346 https://www.amazon.co.jp/%E3%82%B6%E3%83%8E%E3%83%BC%E3%82%B9%E3%83%95%E3%82%A7%E3%82%A4%E3%82%B9-%E3%82%AB%E3%83%83%E3%83%88%E3%82%BD%E3%83%BC-%E3%82%B7%E3%83%A7%E3%83%BC%E3%83%88%E3%82%B9%E3%83%AA%E3%83%BC%E3%83%96%E3%83%90%E3%83%B3%E3%83%80%E3%83%8A%E3%82%B9%E3%82%AF%E3%82%A8%E3%82%A2%E3%83%AD%E3%82%B4%E3%83%86%E3%82%A3%E3%83%BC-NT32446-%E3%83%96%E3%83%A9%E3%83%83%E3%82%AF%EF%BC%92/dp/B0D6QFX8DF/ref=zg_bs_g_15218661_d_sccl_13/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:19:00.413361", "level": "ERROR", "logger": "processor", "message": "process amazon:3c95dfe555f80e12046d693b46428ed2 https://www.amazon.co.jp/%E3%83%AB%E3%82%B3%E3%83%83%E3%82%AF%E3%82%B9%E3%83%9D%E3%83%AB%E3%83%86%E3%82%A3%E3%83%95-%E9%95%B7%E8%A2%96%E3%82%B7%E3%83%A3%E3%83%84-%E3%82%B9%E3%83%88%E3%83%AC%E3%83%83%E3%83%81-%E3%83%AF%E3%83%B3%E3%83%9D%E3%82%A4%E3%83%B3%E3%83%88-%E3%83%88%E3%83%AC%E3%83%BC%E3%83%8B%E3%83%B3%E3%82%B0/dp/B096VW2GP6/ref=zg_bs_g_15218661_d_sccl_14/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:19:07.416155", "level": "ERROR", "logger": "processor", "message": "process amazon:2bb218148a68495b82df531a7ab075d3 https://www.amazon.co.jp/KEFITEVD-%E9%95%B7%E8%A2%96t%E3%82%B7%E3%83%A3%E3%83%84-%E9%80%9F%E4%B9%BEt%E3%82%B7%E3%83%A3%E3%83%84-%E3%82%B9%E3%83%9D%E3%83%BC%E3%83%84%E3%82%A6%E3%82%A7%E3%82%A2-%E3%83%A9%E3%83%B3%E3%83%8B%E3%83%B3%E3%82%B0t%E3%82%B7%E3%83%A3%E3%83%84/dp/B099RWJ1W8/ref=zg_bs_g_15218661_d_sccl_18/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:19:09.962193", "level": "ERROR", "logger": "processor", "message": "process amazon:cbba98565acf8a7107f627d33d38102e https://www.amazon.co.jp/EVANE-%E3%82%AA%E3%83%BC%E3%83%90%E3%83%BC%E3%82%B5%E3%82%A4%E3%82%BA-%E3%82%B3%E3%83%83%E3%83%88%E3%83%B3-%E3%82%B9%E3%83%88%E3%83%AC%E3%83%83%E3%83%81-T%E3%82%B7%E3%83%A3%E3%83%84/dp/B0CTMR98CV/ref=zg_bs_g_15218661_d_sccl_17/000-0000000-0000000?psc=1 -> [599] len:52 -> result:None fol:0 msg:0 err:HTTPError('HTTP 599: TimeoutError: Navigation timeout of 3000 ms exceeded')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:19:10.052302", "level": "ERROR", "logger": "processor", "message": "process amazon:33949b71b62b7c1e023def2cbd52ef96 https://www.amazon.co.jp/happybeans-304-21-%E3%81%AC%E3%81%84%E3%81%A4%E3%81%91%E3%82%BC%E3%83%83%E3%82%B1%E3%83%B3-%E4%BD%93%E6%93%8D%E6%9C%8D-%E3%83%8B%E3%82%B8%E3%83%9F%E9%98%B2%E6%AD%A2%E5%8A%A0%E5%B7%A5/dp/B0BJDR39M5/ref=zg_bs_g_15218661_d_sccl_19/000-0000000-0000000?psc=1 -> [599] len:52 -> result:None fol:0 msg:0 err:HTTPError('HTTP 599: TimeoutError: Navigation timeout of 3000 ms exceeded')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:19:10.055160", "level": "ERROR", "logger": "processor", "message": "process amazon:76fbc53be1fc6627d0ceaeaf87d6752c https://www.amazon.co.jp/%E3%83%91%E3%82%A4%E3%82%AA%E3%83%8B%E3%82%A2-G400-00010-%E3%81%AE%E3%81%B3%E3%81%AE%E3%81%B3%E3%82%B9%E3%83%9D%E3%83%BC%E3%83%84%E3%82%BC%E3%83%83%E3%82%B1%E3%83%B3-%E4%B8%AD/dp/B00197JLY4/ref=zg_bs_g_15218661_d_sccl_4/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:19:15.145901", "level": "ERROR", "logger": "processor", "message": "process amazon:5a3f8a1456571569a419e5480db93d16 https://www.amazon.co.jp/%E3%83%86%E3%82%B9%E3%83%A9-%E3%82%B3%E3%83%B3%E3%83%97%E3%83%AC%E3%83%83%E3%82%B7%E3%83%A7%E3%83%B3%E3%82%A6%E3%82%A7%E3%82%A2-UV%E3%82%AB%E3%83%83%E3%83%88%E3%83%BB%E5%90%B8%E6%B1%97%E9%80%9F%E4%B9%BE-%E3%82%B3%E3%83%B3%E3%83%97%E3%83%AC%E3%83%83%E3%82%B7%E3%83%A7%E3%83%B3%E3%82%B7%E3%83%A3%E3%83%84-MUV01-JPA_L/dp/B0BF9NLCTL/ref=zg_bs_g_15218661_d_sccl_6/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:19:17.761239", "level": "ERROR", "logger": "processor", "message": "process amazon:1d3e5bf2f454b7801d9ae1a619178b2b https://www.amazon.co.jp/%E3%83%A0%E3%83%BC%E3%83%96%E3%82%B9%E3%83%9D%E3%83%BC%E3%83%84-%E3%83%88%E3%83%AC%E3%83%BC%E3%83%8B%E3%83%B3%E3%82%B0-%E3%83%A9%E3%83%B3%E3%83%8B%E3%83%B3%E3%82%B0-%E3%82%A6%E3%82%A9%E3%83%BC%E3%82%AD%E3%83%B3%E3%82%B0-ST5SHTT0M/dp/B0DJQSS2KR/ref=zg_bs_g_15218661_d_sccl_9/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:19:20.271495", "level": "ERROR", "logger": "processor", "message": "process amazon:4c2326cea99bb51216c8caa4494c44ed https://www.amazon.co.jp/%E3%82%B6%E3%83%8E%E3%83%BC%E3%82%B9%E3%83%95%E3%82%A7%E3%82%A4%E3%82%B9-%E3%82%AB%E3%83%83%E3%83%88%E3%82%BD%E3%83%BC-T%E3%82%B7%E3%83%A3%E3%83%84-Square-Traverse/dp/B0D6N3KP1B/ref=zg_bs_g_15218661_d_sccl_11/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:19:20.275027", "level": "ERROR", "logger": "processor", "message": "process amazon:cf7b3a35721a45f47c66eac4bef9f1f8 https://www.amazon.co.jp/%E3%82%A2%E3%83%B3%E3%83%80%E3%83%BC%E3%82%A2%E3%83%BC%E3%83%9E%E3%83%BC-TECH-LOGO-Mens-%E6%97%A5%E6%9C%AC%E3%82%B5%E3%82%A4%E3%82%BAL%E7%9B%B8%E5%BD%93/dp/B07SYGTP6X/ref=zg_bs_g_15218661_d_sccl_13/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:19:20.278573", "level": "ERROR", "logger": "processor", "message": "process amazon:da52edfbc45e39829e0e96edfcfa8485 https://www.amazon.co.jp/%E3%83%9F%E3%82%BA%E3%83%8E-%E3%83%88%E3%83%AC%E3%83%BC%E3%83%8B%E3%83%B3%E3%82%B0%E3%82%A6%E3%82%A7%E3%82%A2-%E3%80%90Amazon%E9%99%90%E5%AE%9A%E3%83%A2%E3%83%87%E3%83%AB%E3%81%82%E3%82%8A%E3%80%91-32MAC195-%E3%83%89%E3%83%AC%E3%82%B9%E3%83%8D%E3%82%A4%E3%83%93%E3%83%BC/dp/B0D7VGLPCM/ref=zg_bs_g_15218661_d_sccl_16/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:19:20.281387", "level": "ERROR", "logger": "processor", "message": "process amazon:d30372cf4d24840d6e1fa7a02e1e5c5f https://www.amazon.co.jp/%E3%83%97%E3%83%BC%E3%83%9E-%E3%82%AB%E3%82%B8%E3%83%A5%E3%82%A2%E3%83%AB-%E3%82%B9%E3%83%A2%E3%83%BC%E3%83%AB-%E3%83%97%E3%83%BC%E3%83%9E%E3%83%AD%E3%82%B4-687561/dp/B0D5CCPQLW/ref=zg_bs_g_15218661_d_sccl_17/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:19:20.284948", "level": "ERROR", "logger": "processor", "message": "process amazon:0253a29e0eeda50fa25b69d195ad6895 https://www.amazon.co.jp/%E3%83%9F%E3%82%BA%E3%83%8E-%E3%83%88%E3%83%AC%E3%83%BC%E3%83%8B%E3%83%B3%E3%82%B0%E3%82%A6%E3%82%A7%E3%82%A2-%E5%8D%8A%E8%A2%96T%E3%82%B7%E3%83%A3%E3%83%84-%E5%90%B8%E6%B1%97%E9%80%9F%E4%B9%BE-32MAA156/dp/B0B3RNS3F8/ref=zg_bs_g_15218661_d_sccl_18/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:19:20.288395", "level": "ERROR", "logger": "processor", "message": "process amazon:99fde82e7e915d5fbe44209c9167f888 https://www.amazon.co.jp/%E3%83%9E%E3%83%BC%E3%82%AB%E3%83%BC%E3%82%B3%E3%83%BC%E3%83%B3-%E3%83%88%E3%83%AC%E3%83%BC%E3%83%8B%E3%83%B3%E3%82%B0%E3%82%B3%E3%83%BC%E3%83%B3-%E3%83%89%E3%83%AA%E3%83%96%E3%83%AB%E3%83%9E%E3%83%BC%E3%82%AB%E3%83%BC-%E3%83%86%E3%83%8B%E3%82%B9%E7%B7%B4%E7%BF%92%E3%82%B3%E3%83%BC%E3%83%B3-%E5%8F%8E%E7%B4%8D%E8%A2%8B%E3%83%BB%E5%B0%82%E7%94%A8%E3%82%B9%E3%82%BF%E3%83%B3%E3%83%89%E4%BB%98%E3%81%8D/dp/B0BXXFZ8GL/ref=zg_bs_g_15218661_d_sccl_19/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:19:22.997767", "level": "ERROR", "logger": "processor", "message": "process amazon:f689fdd47104e5a0d63d2055c09882b9 https://www.amazon.co.jp/phiten-%E3%83%95%E3%82%A1%E3%82%A4%E3%83%86%E3%83%B3-RAKU%E3%82%B7%E3%83%A3%E3%83%84-%E3%82%B0%E3%83%A9%E3%83%87%E3%83%BC%E3%82%B7%E3%83%A7%E3%83%B3-%E8%B2%A9%E8%B7%AF%E9%99%90%E5%AE%9A%E3%83%87%E3%82%B6%E3%82%A4%E3%83%B3/dp/B0F18QVVJ3/ref=zg_bs_g_15218661_d_sccl_22/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:19:23.001045", "level": "ERROR", "logger": "processor", "message": "process amazon:bce1890f85f5ba969c5a84562e757b39 https://www.amazon.co.jp/%E3%82%A2%E3%83%B3%E3%83%80%E3%83%BC%E3%82%A2%E3%83%BC%E3%83%9E%E3%83%BC-Compression-%E3%83%AC%E3%83%87%E3%82%A3%E3%83%BC%E3%82%B9-1358604-%E6%97%A5%E6%9C%AC%E3%82%B5%E3%82%A4%E3%82%BAM%E7%9B%B8%E5%BD%93/dp/B07SZYHDJZ/ref=zg_bs_g_15218661_d_sccl_23/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:19:23.003811", "level": "ERROR", "logger": "processor", "message": "process amazon:340f1dd511880a75afc97e1c315704a5 https://www.amazon.co.jp/%E3%83%AB%E3%82%B3%E3%83%83%E3%82%AF%E3%82%B9%E3%83%9D%E3%83%AB%E3%83%86%E3%82%A3%E3%83%95-%E3%83%8F%E3%83%BC%E3%83%95%E3%82%B8%E3%83%83%E3%83%97%E5%8D%8A%E8%A2%96%E3%82%B7%E3%83%A3%E3%83%84-%E5%8D%8A%E8%A2%96%E3%82%B7%E3%83%A3%E3%83%84-%E3%83%AF%E3%83%B3%E3%83%9D%E3%82%A4%E3%83%B3%E3%83%88-%E3%83%88%E3%83%AC%E3%83%BC%E3%83%8B%E3%83%B3%E3%82%B0/dp/B096VSMFRH/ref=zg_bs_g_15218661_d_sccl_25/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:19:23.006327", "level": "ERROR", "logger": "processor", "message": "process amazon:53de1d3e3d19ace218080e027e8d5d33 https://www.amazon.co.jp/RACURIA-%E3%82%B9%E3%83%9D%E3%83%BC%E3%83%84%E7%94%A8%E3%83%9E%E3%82%A6%E3%82%B9%E3%83%94%E3%83%BC%E3%82%B9-%E3%80%90%E9%A3%9F%E5%93%81%E8%A1%9B%E7%94%9F%E8%A9%A6%E9%A8%93%E9%81%A9%E5%90%88-%E6%AD%AF%E7%A7%91%E8%A1%9B%E7%94%9F%E5%A3%AB%E7%9B%A3%E4%BF%AE%E3%80%91%E5%8F%8E%E7%B4%8D%E3%82%B1%E3%83%BC%E3%82%B9-%E5%8F%96%E6%89%B1%E8%AA%AC%E6%98%8E%E6%9B%B8%E4%BB%98%E3%81%8D/dp/B0DMJ9972F/ref=zg_bs_g_15218661_d_sccl_26/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:19:23.009541", "level": "ERROR", "logger": "processor", "message": "process amazon:ca8d9a662fdda6a73165d40f7e0a5f49 https://www.amazon.co.jp/EVANE-4WAY-%E3%82%B9%E3%83%88%E3%83%AC%E3%83%83%E3%83%81-%E3%82%BB%E3%83%83%E3%83%88%E3%82%A4%E3%83%B3-%E3%83%88%E3%83%AC%E3%83%BC%E3%83%8B%E3%83%B3%E3%82%B0T%E3%82%B7%E3%83%A3%E3%83%84/dp/B0D83PTQ8L/ref=zg_bs_g_15218661_d_sccl_27/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:19:23.012413", "level": "ERROR", "logger": "processor", "message": "process amazon:7fcd528091073a7de4bd1ed406d77a6f https://www.amazon.co.jp/%E3%80%90%E5%8C%BB%E5%B8%AB%E7%9B%A3%E4%BF%AE%E3%80%91%E3%82%B9%E3%83%9D%E3%83%BC%E3%83%84%E7%94%A8%E3%83%9E%E3%82%A6%E3%82%B9%E3%83%94%E3%83%BC%E3%82%B9%E3%80%9090%E7%A7%923%E3%82%B9%E3%83%86%E3%83%83%E3%83%97%E3%81%AE%E7%B0%A1%E5%8D%98%E5%9E%8B%E5%8F%96%E3%82%8A%E3%80%91%E6%AD%AF%E7%A7%91%E6%9D%90%E6%96%99EVA%E4%BD%BF%E7%94%A8-%E5%B0%82%E7%94%A8%E3%82%B1%E3%83%BC%E3%82%B9%E4%BB%98-%E3%83%8A%E3%82%A4%E3%83%88%E3%82%AC%E3%83%BC%E3%83%89-%E3%83%9E%E3%82%A6%E3%82%B9%E3%82%AC%E3%83%BC%E3%83%89-BULQREA/dp/B0DZBPBQHZ/ref=zg_bs_g_15218661_d_sccl_28/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:19:23.016474", "level": "ERROR", "logger": "processor", "message": "process amazon:e155c24fcdfc0c3d629c7e2cd1317d0c https://www.amazon.co.jp/%E3%82%A2%E3%83%B3%E3%83%80%E3%83%BC%E3%82%A2%E3%83%BC%E3%83%9E%E3%83%BC-%E3%82%B3%E3%83%B3%E3%83%97%E3%83%AC%E3%83%83%E3%82%B7%E3%83%A7%E3%83%B3%E3%82%A6%E3%82%A7%E3%82%A2-HEATGEAR-FITTED-SLEEVE/dp/B095YK8ZJF/ref=zg_bs_g_15218661_d_sccl_29/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:19:23.921302", "level": "ERROR", "logger": "processor", "message": "process amazon:d90c47d224ad3cc68e915f7e03dbb57a https://www.amazon.co.jp/DAYCLOSET-%E3%83%A8%E3%82%AC%E3%82%A6%E3%82%A7%E3%82%A2%E3%83%88%E3%83%83%E3%83%97%E3%82%B9-%E3%83%A8%E3%82%ACt%E3%82%B7%E3%83%A3%E3%83%84-yoga125-%E3%82%A6%E3%82%A9%E3%83%BC%E3%83%A0%E3%82%B0%E3%83%AC%E3%83%BC/dp/B0CLCHXQ6K/ref=zg_bs_g_15218661_d_sccl_30/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:19:24.832826", "level": "ERROR", "logger": "processor", "message": "process amazon:2a9437f3c15ffc5a867e90430feac89d https://www.amazon.co.jp/%E3%82%A2%E3%83%B3%E3%83%80%E3%83%BC%E3%82%A2%E3%83%BC%E3%83%9E%E3%83%BC-UA-TECH-LOGO-Black/dp/B0D3TZ5PJK/ref=zg_bs_g_15218661_d_sccl_3/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:19:25.838075", "level": "ERROR", "logger": "processor", "message": "process amazon:e3164ad208d167aa65bb0697328dfa6c https://www.amazon.co.jp/%E3%83%9E%E3%82%A6%E3%82%B9%E3%83%94%E3%83%BC%E3%82%B9-%E3%83%9E%E3%82%A6%E3%82%B9%E3%82%AC%E3%83%BC%E3%83%89-%E3%82%B1%E3%83%BC%E3%82%B9%E4%BB%98%E3%81%8D-%E3%83%9C%E3%82%AF%E3%82%B7%E3%83%B3%E3%82%B0-%E3%82%AF%E3%83%AA%E3%82%A2%E3%83%96%E3%83%A9%E3%83%83%E3%82%AF/dp/B07KGQW8S2/ref=zg_bs_g_15218661_d_sccl_5/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:21:36.567156", "level": "ERROR", "logger": "processor", "message": "process oioio:75cc752db3655061e3cb78274755f21a https://www.amazon.co.jp/%E3%82%A2%E3%82%B5%E3%82%B7%E3%83%B3-%E3%82%AF%E3%83%AA%E3%83%BC%E3%83%89-%E3%82%B7%E3%83%A3%E3%83%89%E3%82%A6%E3%82%BA-%E3%82%B9%E3%82%BF%E3%83%B3%E3%83%80%E3%83%BC%E3%83%89%E3%82%A8%E3%83%87%E3%82%A3%E3%82%B7%E3%83%A7%E3%83%B3-PS5/dp/B0D49J3CHB/ref=zg_bs_g_videogames_d_sccl_13/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:21:36.572507", "level": "ERROR", "logger": "processor", "message": "process oioio:f30a722677881a072de1dfd4e233c506 https://www.amazon.co.jp/%E3%80%90%E7%B4%94%E6%AD%A3%E5%93%81%E3%80%91%E3%82%B9%E3%83%86%E3%82%A3%E3%83%83%E3%82%AF%E3%83%A2%E3%82%B8%E3%83%A5%E3%83%BC%E3%83%AB-DualSense-Edge-%E3%83%AF%E3%82%A4%E3%83%A4%E3%83%AC%E3%82%B9%E3%82%B3%E3%83%B3%E3%83%88%E3%83%AD%E3%83%BC%E3%83%A9%E3%83%BC%E7%94%A8-CFI-ZSM1G/dp/B0BJTSHHK8/ref=zg_bs_g_videogames_d_sccl_14/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:21:37.479741", "level": "ERROR", "logger": "processor", "message": "process oioio:1ea87cf2808630f3594ef373a156dcc1 https://www.amazon.co.jp/%E3%80%90%E7%B4%94%E6%AD%A3%E5%93%81%E3%80%91DualSense-Edge-%E3%83%AF%E3%82%A4%E3%83%A4%E3%83%AC%E3%82%B9%E3%82%B3%E3%83%B3%E3%83%88%E3%83%AD%E3%83%BC%E3%83%A9%E3%83%BC-%E3%83%9F%E3%83%83%E3%83%89%E3%83%8A%E3%82%A4%E3%83%88-CFI-ZCP1J01/dp/B0DT2PGBYZ/ref=zg_bs_g_videogames_d_sccl_16/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:21:39.387348", "level": "ERROR", "logger": "processor", "message": "process oioio:71eaff9f1a08adf5ccc164081c1298dc https://www.amazon.co.jp/%E3%80%90Amazon-co-jp%E9%99%90%E5%AE%9A%E3%80%91Logicool-%E3%83%AD%E3%82%B8%E3%82%AF%E3%83%BC%E3%83%AB-G-PHS-003d-%E3%80%90Amazon-co-jp-%E9%99%90%E5%AE%9A%E5%A3%81%E7%B4%99%E3%83%80%E3%82%A6%E3%83%B3%E3%83%AD%E3%83%BC%E3%83%89%E4%BB%98%E3%81%8D%E3%80%91/dp/B0BC142H1L/ref=zg_bs_g_videogames_d_sccl_15/000-0000000-0000000?psc=1 -> [599] len:52 -> result:None fol:0 msg:0 err:HTTPError('HTTP 599: TimeoutError: Navigation timeout of 3000 ms exceeded')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:24:26.743665", "level": "ERROR", "logger": "processor", "message": "process amazon:02368774ba5c4be06248469d44a89b0b https://www.amazon.co.jp/%E3%83%9F%E3%82%BA%E3%83%8E-%E3%83%88%E3%83%AC%E3%83%BC%E3%83%8B%E3%83%B3%E3%82%B0%E3%82%A6%E3%82%A7%E3%82%A2-%E3%82%BF%E3%83%B3%E3%82%AF%E3%83%88%E3%83%83%E3%83%97-%E3%83%8E%E3%83%BC%E3%82%B9%E3%83%AA%E3%83%BC%E3%83%96%E3%82%B7%E3%83%A3%E3%83%84-32MAC199/dp/B0D6G9SSBJ/ref=zg_bs_g_15218661_d_sccl_11/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:24:26.746202", "level": "ERROR", "logger": "processor", "message": "process amazon:b813fd6e6bf601179e55ee7d9b2b23fb https://www.amazon.co.jp/%E3%83%A8%E3%83%8D%E3%83%83%E3%82%AF%E3%82%B9-%E3%83%86%E3%83%8B%E3%82%B9%E3%82%A6%E3%82%A7%E3%82%A2-%E3%83%90%E3%83%89%E3%83%9F%E3%83%B3%E3%83%88%E3%83%B3%E3%82%A6%E3%82%A7%E3%82%A2-%E3%82%B9%E3%82%BF%E3%83%B3%E3%83%80%E3%83%BC%E3%83%89%E3%82%B5%E3%82%A4%E3%82%BA-RWHI1301/dp/B01M1R98QP/ref=zg_bs_g_15218661_d_sccl_10/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
{"timestamp": "2025-06-05T06:24:26.749901", "level": "ERROR", "logger": "processor", "message": "process amazon:74157678c179a3c6322d0923b373f421 https://www.amazon.co.jp/%E3%82%B6%E3%83%8E%E3%83%BC%E3%82%B9%E3%83%95%E3%82%A7%E3%82%A4%E3%82%B9-%E5%8D%8A%E8%A2%96%E3%82%B7%E3%83%A3%E3%83%84-Nuptse-Shirt-%E3%82%A2%E3%82%B9%E3%83%95%E3%82%A1%E3%83%AB%E3%83%88%E3%82%B0%E3%83%AC%E3%83%BC/dp/B0D6N6DS2W/ref=zg_bs_g_15218661_d_sccl_8/000-0000000-0000000?psc=1 -> [500] len:1993 -> result:None fol:0 msg:0 err:HTTPError('HTTP 500: Server Error')", "module": "processor", "function": "on_task", "line": 199, "extra": {"taskName": null}}
