
> pyspider-webui@0.1.0 dev
> next dev -p 3000

   ▲ Next.js 15.3.2
   - Local:        http://localhost:3000
   - Network:      http://**************:3000
   - Environments: .env.local
   - Experiments (use with caution):
     ✓ reactCompiler

 ✓ Starting...
 ✓ Ready in 2.2s
 ✓ Compiled /middleware in 469ms (108 modules)
 ○ Compiling / ...
 ✓ Compiled / in 4.8s (1204 modules)
 GET / 200 in 5713ms
 ✓ Compiled in 1026ms (524 modules)
 ○ Compiling /api/v2/projects ...
 ✓ Compiled /api/v2/projects in 1270ms (1209 modules)
Fetching projects list
Fetching projects from pyspider API
Returning 0 projects from pyspider standard API (result field)
 GET /api/v2/projects 200 in 1450ms
Fetching projects list
Fetching projects from pyspider API
(node:186758) [DEP0060] DeprecationWarning: The `util._extend` API is deprecated. Please use Object.assign() instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
Returning 0 projects from pyspider standard API (result field)
 GET /api/v2/projects 200 in 49ms
Fetching projects list
Fetching projects from pyspider API
Returning 0 projects from pyspider standard API (result field)
 GET /api/v2/projects 200 in 47ms
 ✓ Compiled /api/v2/tasks in 431ms (1211 modules)
Fetching tasks with status=active, limit=20, offset=0, project=null
Fetching tasks from http://localhost:5000/api/v2/tasks?status=active&limit=20&offset=0
Received 0 tasks from pyspider API v2
 GET /api/v2/tasks?status=active&limit=20 200 in 564ms
Fetching tasks with status=active, limit=20, offset=0, project=null
Fetching tasks from http://localhost:5000/api/v2/tasks?status=active&limit=20&offset=0
Received 0 tasks from pyspider API v2
 GET /api/v2/tasks?status=active&limit=20 200 in 36ms
 ○ Compiling /api/v2/metrics ...
 ✓ Compiled /api/v2/metrics in 800ms (1213 modules)
Fetching system metrics
Fetching metrics from APIv2: http://localhost:5000/api/v2/metrics
Fetching tasks with status=active, limit=20, offset=0, project=null
Fetching tasks from http://localhost:5000/api/v2/tasks?status=active&limit=20&offset=0
Received metrics from pyspider APIv2
 GET /api/v2/metrics 200 in 1120ms
Received 0 tasks from pyspider API v2
 GET /api/v2/tasks?status=active&limit=20 200 in 1078ms
Fetching system metrics
Fetching metrics from APIv2: http://localhost:5000/api/v2/metrics
Received metrics from pyspider APIv2
 GET /api/v2/metrics 200 in 320ms
Fetching system metrics
Fetching metrics from APIv2: http://localhost:5000/api/v2/metrics
Received metrics from pyspider APIv2
 GET /api/v2/metrics 200 in 301ms
 ○ Compiling /results ...
 ✓ Compiled /results in 871ms (1230 modules)
 GET /results 200 in 1047ms
Fetching projects list
Fetching projects from pyspider API
Returning 0 projects from pyspider standard API (result field)
 GET /api/v2/projects 200 in 63ms
 ○ Compiling /api/v2/results ...
 ✓ Compiled /api/v2/results in 672ms (1232 modules)
Fetching all results with offset 0 and limit 20
Fetching results from http://localhost:5000/api/v2/results?offset=0&limit=20
Fetching projects list
Fetching projects from pyspider API
Received 0 results
 GET /api/v2/results?offset=0&limit=20 200 in 789ms
Returning 0 projects from pyspider standard API (result field)
 GET /api/v2/projects 200 in 739ms
 ○ Compiling /file-output ...
 ✓ Compiled /file-output in 1141ms (1289 modules)
Fetching all results with offset 0 and limit 20
Fetching results from http://localhost:5000/api/v2/results?offset=0&limit=20
Received 0 results
 GET /api/v2/results?offset=0&limit=20 200 in 1204ms
 GET /file-output 200 in 1316ms
 ✓ Compiled /tasks in 411ms (1300 modules)
 GET /tasks 200 in 509ms
 ⚠ Fast Refresh had to perform a full reload due to a runtime error.
 ✓ Compiled in 4.2s (1316 modules)
 GET /tasks 200 in 2026ms
Fetching projects list
Fetching projects from pyspider API
Returning 0 projects from pyspider standard API (result field)
 GET /api/v2/projects 200 in 86ms
Fetching projects list
Fetching projects from pyspider API
Returning 0 projects from pyspider standard API (result field)
 GET /api/v2/projects 200 in 35ms
Fetching projects list
Fetching projects from pyspider API
Returning 0 projects from pyspider standard API (result field)
 GET /api/v2/projects 200 in 45ms
Fetching projects list
Fetching projects from pyspider API
Returning 0 projects from pyspider standard API (result field)
 GET /api/v2/projects 200 in 38ms
 ⨯ ./src/app/tasks/page.jsx
Error:   [31mx[0m Return statement is not allowed here
     ,-[[36;1;4m/home/<USER>/workplace/python/pyspiderNX2/pyspider/webui-next/src/app/tasks/page.jsx[0m:170:1]
 [2m167[0m |         }
 [2m168[0m |       };
 [2m169[0m |     
 [2m170[0m | [35;1m,[0m[35;1m-[0m[35;1m>[0m   return (
 [2m171[0m | [35;1m|[0m       <Suspense fallback={<Loading size="lg" text="タスク情報を読み込み中..." />}>
 [2m172[0m | [35;1m|[0m         <TasksClient />
 [2m173[0m | [35;1m|[0m       </Suspense>
 [2m174[0m | [35;1m`[0m[35;1m-[0m[35;1m>[0m   );
 [2m175[0m |     }
 [2m176[0m |     
 [2m177[0m |     // メインのページコンポーネント
     `----
  [31mx[0m Expression expected
     ,-[[36;1;4m/home/<USER>/workplace/python/pyspiderNX2/pyspider/webui-next/src/app/tasks/page.jsx[0m:175:1]
 [2m172[0m |       <TasksClient />
 [2m173[0m |     </Suspense>
 [2m174[0m |   );
 [2m175[0m | }
     : [35;1m^[0m
 [2m176[0m | 
 [2m177[0m | // メインのページコンポーネント
 [2m178[0m | export default function TasksPage() {
     `----

Caused by:
    Syntax Error

Import trace for requested module:
./src/app/tasks/page.jsx
 ○ Compiling /_error ...
 ⨯ ./src/app/tasks/page.jsx
Error:   [31mx[0m Return statement is not allowed here
     ,-[[36;1;4m/home/<USER>/workplace/python/pyspiderNX2/pyspider/webui-next/src/app/tasks/page.jsx[0m:170:1]
 [2m167[0m |         }
 [2m168[0m |       };
 [2m169[0m |     
 [2m170[0m | [35;1m,[0m[35;1m-[0m[35;1m>[0m   return (
 [2m171[0m | [35;1m|[0m       <Suspense fallback={<Loading size="lg" text="タスク情報を読み込み中..." />}>
 [2m172[0m | [35;1m|[0m         <TasksClient />
 [2m173[0m | [35;1m|[0m       </Suspense>
 [2m174[0m | [35;1m`[0m[35;1m-[0m[35;1m>[0m   );
 [2m175[0m |     }
 [2m176[0m |     
 [2m177[0m |     // メインのページコンポーネント
     `----
  [31mx[0m Expression expected
     ,-[[36;1;4m/home/<USER>/workplace/python/pyspiderNX2/pyspider/webui-next/src/app/tasks/page.jsx[0m:175:1]
 [2m172[0m |       <TasksClient />
 [2m173[0m |     </Suspense>
 [2m174[0m |   );
 [2m175[0m | }
     : [35;1m^[0m
 [2m176[0m | 
 [2m177[0m | // メインのページコンポーネント
 [2m178[0m | export default function TasksPage() {
     `----

Caused by:
    Syntax Error

Import trace for requested module:
./src/app/tasks/page.jsx
 GET /tasks 500 in 6438ms
 GET /tasks 500 in 29ms
 GET /tasks 500 in 41ms
 GET /tasks 500 in 54ms
 ⨯ ./src/app/tasks/page.jsx
Error:   [31mx[0m Return statement is not allowed here
    ,-[[36;1;4m/home/<USER>/workplace/python/pyspiderNX2/pyspider/webui-next/src/app/tasks/page.jsx[0m:65:1]
 [2m62[0m |         }
 [2m63[0m |       };
 [2m64[0m |     
 [2m65[0m | [35;1m,[0m[35;1m-[0m[35;1m>[0m   return (
 [2m66[0m | [35;1m|[0m       <Suspense fallback={<Loading size="lg" text="タスク情報を読み込み中..." />}>
 [2m67[0m | [35;1m|[0m         <TasksClient />
 [2m68[0m | [35;1m|[0m       </Suspense>
 [2m69[0m | [35;1m`[0m[35;1m-[0m[35;1m>[0m   );
 [2m70[0m |     }
 [2m71[0m |     
 [2m72[0m |     // メインのページコンポーネント
    `----
  [31mx[0m Expression expected
    ,-[[36;1;4m/home/<USER>/workplace/python/pyspiderNX2/pyspider/webui-next/src/app/tasks/page.jsx[0m:70:1]
 [2m67[0m |       <TasksClient />
 [2m68[0m |     </Suspense>
 [2m69[0m |   );
 [2m70[0m | }
    : [35;1m^[0m
 [2m71[0m | 
 [2m72[0m | // メインのページコンポーネント
 [2m73[0m | export default function TasksPage() {
    `----

Caused by:
    Syntax Error

Import trace for requested module:
./src/app/tasks/page.jsx
 GET /_next/static/webpack/1c25c0ef4531b1f3.webpack.hot-update.json 500 in 1664ms
 ⚠ Fast Refresh had to perform a full reload due to a runtime error.
 GET /tasks 200 in 26ms
 GET /tasks 500 in 38ms
 ✓ Compiled /_not-found in 5.1s (1613 modules)
 ⚠ Fast Refresh had to perform a full reload due to a runtime error.
 GET /_next/static/webpack/1c25c0ef4531b1f3.webpack.hot-update.json 404 in 6284ms
 ⨯ ReferenceError: TasksContent is not defined
    at TasksPage (src/app/tasks/page.jsx:21:7)
  19 |   return (
  20 |     <Suspense fallback={<Loading size="lg" text="ページを読み込み中..." />}>
> 21 |       <TasksContent />
     |       ^
  22 |     </Suspense>
  23 |   );
  24 | } {
  digest: '2686307302'
}
 GET /tasks 500 in 551ms
 ⚠ Fast Refresh had to perform a full reload due to a runtime error.
 ✓ Compiled in 3.9s (1627 modules)
 GET /tasks 200 in 3506ms
 ✓ Compiled /api/v2/projects in 408ms (825 modules)
Fetching projects list
Fetching projects from pyspider API
Returning 0 projects from pyspider standard API (result field)
 GET /api/v2/projects 200 in 613ms
Fetching projects list
Fetching projects from pyspider API
Returning 0 projects from pyspider standard API (result field)
 GET /api/v2/projects 200 in 30ms
 ○ Compiling /projects/[name] ...
 ✓ Compiled /projects/[name] in 5s (1727 modules)
 GET /projects/tinti 200 in 7590ms
 ✓ Compiled in 2.6s (1741 modules)
 GET /projects/tinti 200 in 304ms
 GET /projects/tinti 200 in 1029ms
 GET /tasks 200 in 176ms
Fetching projects list
Fetching projects from pyspider API
Returning 0 projects from pyspider standard API (result field)
 GET /api/v2/projects 200 in 75ms
Fetching projects list
Fetching projects from pyspider API
Returning 0 projects from pyspider standard API (result field)
 GET /api/v2/projects 200 in 34ms
 GET /projects/tinti 200 in 71ms
 ✓ Compiled in 1595ms (1705 modules)
 ○ Compiling /projects ...
 ✓ Compiled /projects in 4.6s (1718 modules)
 GET /projects 200 in 5532ms
 ○ Compiling /api/v2/projects ...
 ✓ Compiled /api/v2/projects in 586ms (883 modules)
Fetching projects list
Fetching projects from pyspider API
Returning 0 projects from pyspider standard API (result field)
 GET /api/v2/projects 200 in 857ms
Fetching projects list
Fetching projects from pyspider API
Returning 0 projects from pyspider standard API (result field)
 GET /api/v2/projects 200 in 48ms
Fetching projects list
Fetching projects from pyspider API
Returning 0 projects from pyspider standard API (result field)
 GET /api/v2/projects 200 in 40ms
 GET / 200 in 178ms
Fetching projects list
Fetching projects from pyspider API
Returning 0 projects from pyspider standard API (result field)
 GET /api/v2/projects 200 in 45ms
Fetching projects list
Fetching projects from pyspider API
Returning 0 projects from pyspider standard API (result field)
 GET /api/v2/projects 200 in 64ms
 ○ Compiling /api/v2/tasks ...
 ✓ Compiled /api/v2/tasks in 1994ms (885 modules)
Fetching tasks with status=active, limit=20, offset=0, project=null
Fetching tasks from http://localhost:5000/api/v2/tasks?status=active&limit=20&offset=0
Received 0 tasks from pyspider API v2
 GET /api/v2/tasks?status=active&limit=20 200 in 2190ms
Fetching tasks with status=active, limit=20, offset=0, project=null
Fetching tasks from http://localhost:5000/api/v2/tasks?status=active&limit=20&offset=0
Received 0 tasks from pyspider API v2
 GET /api/v2/tasks?status=active&limit=20 200 in 71ms
 ○ Compiling /api/v2/metrics ...
 ✓ Compiled /api/v2/metrics in 1321ms (887 modules)
Fetching system metrics
Fetching metrics from APIv2: http://localhost:5000/api/v2/metrics
Received metrics from pyspider APIv2
 GET /api/v2/metrics 200 in 1753ms
Fetching system metrics
Fetching metrics from APIv2: http://localhost:5000/api/v2/metrics
Received metrics from pyspider APIv2
 GET /api/v2/metrics 200 in 330ms
 GET /tasks 200 in 100ms
Fetching projects list
Fetching projects from pyspider API
Returning 0 projects from pyspider standard API (result field)
 GET /api/v2/projects 200 in 36ms
Fetching projects list
Fetching projects from pyspider API
Returning 0 projects from pyspider standard API (result field)
 GET /api/v2/projects 200 in 23ms
 GET /projects/tinti 200 in 88ms
Fetching projects list
Fetching projects from pyspider API
Returning 0 projects from pyspider standard API (result field)
 GET /api/v2/projects 200 in 49ms
 GET /projects/tinti 200 in 762ms
 GET /projects/test_project 200 in 250ms
 GET /projects/tinti 200 in 188ms
 ✓ Compiled in 3.1s (1718 modules)
 GET /projects/test_project 200 in 672ms
[?25h
