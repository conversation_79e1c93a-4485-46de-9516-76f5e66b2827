
> pyspider-webui@0.1.0 dev
> next dev -p 3000

   ▲ Next.js 15.3.2
   - Local:        http://localhost:3000
   - Network:      http://**************:3000
   - Environments: .env.local
   - Experiments (use with caution):
     ✓ reactCompiler

 ✓ Starting...
 ✓ Ready in 2.2s
 ✓ Compiled /middleware in 469ms (108 modules)
 ○ Compiling / ...
 ✓ Compiled / in 4.8s (1204 modules)
 GET / 200 in 5713ms
 ✓ Compiled in 1026ms (524 modules)
 ○ Compiling /api/v2/projects ...
 ✓ Compiled /api/v2/projects in 1270ms (1209 modules)
Fetching projects list
Fetching projects from pyspider API
Returning 0 projects from pyspider standard API (result field)
 GET /api/v2/projects 200 in 1450ms
Fetching projects list
Fetching projects from pyspider API
(node:186758) [DEP0060] DeprecationWarning: The `util._extend` API is deprecated. Please use Object.assign() instead.
(Use `node --trace-deprecation ...` to show where the warning was created)
Returning 0 projects from pyspider standard API (result field)
 GET /api/v2/projects 200 in 49ms
Fetching projects list
Fetching projects from pyspider API
Returning 0 projects from pyspider standard API (result field)
 GET /api/v2/projects 200 in 47ms
 ✓ Compiled /api/v2/tasks in 431ms (1211 modules)
Fetching tasks with status=active, limit=20, offset=0, project=null
Fetching tasks from http://localhost:5000/api/v2/tasks?status=active&limit=20&offset=0
Received 0 tasks from pyspider API v2
 GET /api/v2/tasks?status=active&limit=20 200 in 564ms
Fetching tasks with status=active, limit=20, offset=0, project=null
Fetching tasks from http://localhost:5000/api/v2/tasks?status=active&limit=20&offset=0
Received 0 tasks from pyspider API v2
 GET /api/v2/tasks?status=active&limit=20 200 in 36ms
 ○ Compiling /api/v2/metrics ...
 ✓ Compiled /api/v2/metrics in 800ms (1213 modules)
Fetching system metrics
Fetching metrics from APIv2: http://localhost:5000/api/v2/metrics
Fetching tasks with status=active, limit=20, offset=0, project=null
Fetching tasks from http://localhost:5000/api/v2/tasks?status=active&limit=20&offset=0
Received metrics from pyspider APIv2
 GET /api/v2/metrics 200 in 1120ms
Received 0 tasks from pyspider API v2
 GET /api/v2/tasks?status=active&limit=20 200 in 1078ms
Fetching system metrics
Fetching metrics from APIv2: http://localhost:5000/api/v2/metrics
Received metrics from pyspider APIv2
 GET /api/v2/metrics 200 in 320ms
Fetching system metrics
Fetching metrics from APIv2: http://localhost:5000/api/v2/metrics
Received metrics from pyspider APIv2
 GET /api/v2/metrics 200 in 301ms
 ○ Compiling /results ...
 ✓ Compiled /results in 871ms (1230 modules)
 GET /results 200 in 1047ms
Fetching projects list
Fetching projects from pyspider API
Returning 0 projects from pyspider standard API (result field)
 GET /api/v2/projects 200 in 63ms
 ○ Compiling /api/v2/results ...
 ✓ Compiled /api/v2/results in 672ms (1232 modules)
Fetching all results with offset 0 and limit 20
Fetching results from http://localhost:5000/api/v2/results?offset=0&limit=20
Fetching projects list
Fetching projects from pyspider API
Received 0 results
 GET /api/v2/results?offset=0&limit=20 200 in 789ms
Returning 0 projects from pyspider standard API (result field)
 GET /api/v2/projects 200 in 739ms
 ○ Compiling /file-output ...
 ✓ Compiled /file-output in 1141ms (1289 modules)
Fetching all results with offset 0 and limit 20
Fetching results from http://localhost:5000/api/v2/results?offset=0&limit=20
Received 0 results
 GET /api/v2/results?offset=0&limit=20 200 in 1204ms
 GET /file-output 200 in 1316ms
 ✓ Compiled /tasks in 411ms (1300 modules)
 GET /tasks 200 in 509ms
