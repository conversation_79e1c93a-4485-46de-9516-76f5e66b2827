#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
pyspider プロセッサ診断スクリプト
起動エラーの原因を特定し、修正案を提供
"""

import os
import sys
import json
import traceback
import subprocess
from datetime import datetime

def check_dependencies():
    """依存関係チェック"""
    print("🔍 依存関係チェック")
    print("-" * 30)
    
    required_modules = [
        'pyspider',
        'tornado',
        'redis',
        'sqlalchemy',
        'requests',
        'lxml',
        'pyquery'
    ]
    
    missing_modules = []
    
    for module in required_modules:
        try:
            __import__(module)
            print(f"✅ {module}: インストール済み")
        except ImportError as e:
            print(f"❌ {module}: 未インストール ({e})")
            missing_modules.append(module)
    
    return missing_modules

def check_database_connection():
    """データベース接続チェック"""
    print("\n🗄️ データベース接続チェック")
    print("-" * 30)
    
    try:
        from pyspider.database.sqlalchemy import taskdb, projectdb, resultdb
        from pyspider.config import config
        
        # TaskDB接続テスト
        try:
            task_db = taskdb.TaskDB(config.get('taskdb', {}))
            print("✅ TaskDB: 接続成功")
        except Exception as e:
            print(f"❌ TaskDB: 接続失敗 ({e})")
        
        # ProjectDB接続テスト
        try:
            project_db = projectdb.ProjectDB(config.get('projectdb', {}))
            print("✅ ProjectDB: 接続成功")
        except Exception as e:
            print(f"❌ ProjectDB: 接続失敗 ({e})")
        
        # ResultDB接続テスト
        try:
            result_db = resultdb.ResultDB(config.get('resultdb', {}))
            print("✅ ResultDB: 接続成功")
        except Exception as e:
            print(f"❌ ResultDB: 接続失敗 ({e})")
            
    except Exception as e:
        print(f"❌ データベースモジュール読み込み失敗: {e}")
        return False
    
    return True

def check_message_queue():
    """メッセージキュー接続チェック"""
    print("\n📨 メッセージキュー接続チェック")
    print("-" * 30)
    
    try:
        import redis
        from pyspider.config import config
        
        # Redis接続テスト
        redis_config = config.get('message_queue', {})
        redis_url = redis_config.get('url', 'redis://localhost:6379/db')
        
        r = redis.from_url(redis_url)
        r.ping()
        print(f"✅ Redis: 接続成功 ({redis_url})")
        return True
        
    except Exception as e:
        print(f"❌ Redis: 接続失敗 ({e})")
        return False

def check_processor_config():
    """プロセッサ設定チェック"""
    print("\n⚙️ プロセッサ設定チェック")
    print("-" * 30)
    
    try:
        from pyspider.processor.processor import Processor
        from pyspider.config import config
        
        processor_config = config.get('processor', {})
        print(f"📋 プロセッサ設定: {json.dumps(processor_config, indent=2, ensure_ascii=False)}")
        
        # プロセッサインスタンス作成テスト
        processor = Processor(
            taskdb=None,
            projectdb=None,
            newtask_queue=None,
            status_queue=None,
            out_queue=None,
            data_path='./data',
            resultdb=None
        )
        print("✅ プロセッサインスタンス: 作成成功")
        return True
        
    except Exception as e:
        print(f"❌ プロセッサ設定: エラー ({e})")
        traceback.print_exc()
        return False

def check_log_files():
    """ログファイルチェック"""
    print("\n📝 ログファイルチェック")
    print("-" * 30)
    
    log_files = [
        'logs/pyspider.log',
        'logs/processor.log',
        'logs/fetcher.log',
        'logs/scheduler.log'
    ]
    
    for log_file in log_files:
        if os.path.exists(log_file):
            size = os.path.getsize(log_file)
            print(f"✅ {log_file}: 存在 ({size} bytes)")
            
            # 最新のエラーをチェック
            try:
                with open(log_file, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                    error_lines = [line for line in lines[-50:] if 'ERROR' in line or 'Exception' in line]
                    
                    if error_lines:
                        print(f"⚠️ 最新のエラー ({len(error_lines)}件):")
                        for error_line in error_lines[-3:]:  # 最新3件
                            print(f"   {error_line.strip()}")
            except Exception as e:
                print(f"   ログ読み込みエラー: {e}")
        else:
            print(f"❌ {log_file}: 存在しない")

def check_process_status():
    """プロセス状況チェック"""
    print("\n🔄 プロセス状況チェック")
    print("-" * 30)
    
    try:
        # pyspiderプロセスをチェック
        result = subprocess.run(['ps', 'aux'], capture_output=True, text=True)
        processes = result.stdout
        
        pyspider_processes = [line for line in processes.split('\n') if 'pyspider' in line and 'grep' not in line]
        
        if pyspider_processes:
            print(f"✅ pyspiderプロセス: {len(pyspider_processes)}個実行中")
            for process in pyspider_processes:
                print(f"   {process}")
        else:
            print("❌ pyspiderプロセス: 実行されていません")
            
    except Exception as e:
        print(f"❌ プロセスチェック失敗: {e}")

def generate_fix_suggestions(missing_modules, db_ok, mq_ok, processor_ok):
    """修正提案を生成"""
    print("\n🔧 修正提案")
    print("=" * 50)
    
    if missing_modules:
        print("📦 依存関係の修正:")
        print(f"   pip install {' '.join(missing_modules)}")
    
    if not db_ok:
        print("\n🗄️ データベースの修正:")
        print("   1. PostgreSQL/SQLiteが起動しているか確認")
        print("   2. データベース接続設定を確認")
        print("   3. データベースの権限を確認")
    
    if not mq_ok:
        print("\n📨 メッセージキューの修正:")
        print("   1. Redisサーバーを起動:")
        print("      sudo systemctl start redis")
        print("   2. Redis設定を確認:")
        print("      redis-cli ping")
    
    if not processor_ok:
        print("\n⚙️ プロセッサの修正:")
        print("   1. 設定ファイルを確認")
        print("   2. データディレクトリの権限を確認")
        print("   3. プロセッサを再起動:")
        print("      python -m pyspider.run processor")
    
    print("\n🚀 推奨起動手順:")
    print("   1. Redis起動: sudo systemctl start redis")
    print("   2. データベース起動")
    print("   3. スケジューラ起動: python -m pyspider.run scheduler")
    print("   4. フェッチャー起動: python -m pyspider.run fetcher")
    print("   5. プロセッサ起動: python -m pyspider.run processor")
    print("   6. WebUI起動: python -m pyspider.run webui")

def main():
    """メイン診断関数"""
    print("🔍 pyspider プロセッサ診断開始")
    print("=" * 50)
    print(f"実行時刻: {datetime.now().isoformat()}")
    print(f"Python: {sys.version}")
    print(f"作業ディレクトリ: {os.getcwd()}")
    
    # 各種チェック実行
    missing_modules = check_dependencies()
    db_ok = check_database_connection()
    mq_ok = check_message_queue()
    processor_ok = check_processor_config()
    
    check_log_files()
    check_process_status()
    
    # 修正提案
    generate_fix_suggestions(missing_modules, db_ok, mq_ok, processor_ok)
    
    # 総合評価
    print("\n📊 診断結果サマリー")
    print("=" * 50)
    
    issues = []
    if missing_modules:
        issues.append(f"依存関係不足: {len(missing_modules)}個")
    if not db_ok:
        issues.append("データベース接続問題")
    if not mq_ok:
        issues.append("メッセージキュー接続問題")
    if not processor_ok:
        issues.append("プロセッサ設定問題")
    
    if issues:
        print(f"❌ 問題発見: {', '.join(issues)}")
        print("🔧 上記の修正提案を参照してください")
        return 1
    else:
        print("✅ 全ての診断項目が正常です")
        print("🎯 プロセッサは正常に動作するはずです")
        return 0

if __name__ == '__main__':
    sys.exit(main())
