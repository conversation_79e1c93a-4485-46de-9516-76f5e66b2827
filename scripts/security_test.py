#!/usr/bin/env python
# -*- encoding: utf-8 -*-
# vim: set et sw=4 ts=4 sts=4 ff=unix fenc=utf8:
# Author: PySpiderNX2 Team
# Created on 2025-01-XX

"""
pyspiderNX2 セキュリティテストスクリプト
"""

import os
import sys
import json
import logging
from urllib.parse import urljoin

# pyspiderNX2のパスを追加
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

try:
    import requests
except ImportError:
    print("requests library not found. Installing...")
    import subprocess
    subprocess.check_call([sys.executable, '-m', 'pip', 'install', 'requests'])
    import requests

try:
    from pyspider.config.unified_config import get_config
    from pyspider.webui.input_validation import validate_input, validate_url
    from pyspider.webui.security import is_ip_allowed
except ImportError as e:
    print(f"Import error: {e}")
    print("Some modules may not be available. Continuing with available tests...")

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SecurityTester:
    """セキュリティテストクラス"""

    def __init__(self, base_url='http://localhost:5000'):
        self.base_url = base_url
        self.session = requests.Session()
        try:
            self.config = get_config()
        except:
            self.config = None
            logger.warning("Could not load unified config. Using fallback tests.")

    def test_authentication(self):
        """認証テスト"""
        logger.info("Testing authentication...")

        # 認証なしでのアクセステスト
        response = self.session.get(urljoin(self.base_url, '/'))
        if response.status_code == 401:
            logger.info("✓ Authentication is properly enforced")
        else:
            logger.warning("✗ Authentication may not be properly configured")

        # 不正な認証情報でのテスト
        auth_data = {
            'username': 'invalid_user',
            'password': 'invalid_password'
        }

        response = self.session.post(
            urljoin(self.base_url, '/api/v2/auth/login'),
            json=auth_data
        )

        if response.status_code == 401:
            logger.info("✓ Invalid credentials are properly rejected")
        else:
            logger.warning("✗ Invalid credentials may be accepted")

    def test_input_validation(self):
        """入力検証テスト"""
        logger.info("Testing input validation...")

        # プロジェクト名の検証テスト
        test_cases = [
            ('valid_project', True),
            ('invalid-project!', False),
            ('<script>alert("xss")</script>', False),
            ('', False),
            ('a' * 100, False)  # 長すぎる名前
        ]

        for project_name, expected in test_cases:
            result = validate_input('project_name', project_name)
            if result == expected:
                logger.info(f"✓ Project name validation: '{project_name}' -> {result}")
            else:
                logger.warning(f"✗ Project name validation failed: '{project_name}' -> {result}, expected {expected}")

        # URL検証テスト
        url_test_cases = [
            ('https://example.com', True),
            ('http://example.com', True),
            ('ftp://example.com', False),
            ('javascript:alert("xss")', False),
            ('http://example.com/<script>', False)
        ]

        for url, expected in url_test_cases:
            result = validate_url(url)
            if result == expected:
                logger.info(f"✓ URL validation: '{url}' -> {result}")
            else:
                logger.warning(f"✗ URL validation failed: '{url}' -> {result}, expected {expected}")

    def test_ip_access_control(self):
        """IPアクセス制御テスト"""
        logger.info("Testing IP access control...")

        # 許可されたIPアドレスのテスト
        allowed_ips = [
            '127.0.0.1',
            '*************',
            '********'
        ]

        for ip in allowed_ips:
            result = is_ip_allowed(ip)
            if result:
                logger.info(f"✓ IP access control: '{ip}' is allowed")
            else:
                logger.warning(f"✗ IP access control: '{ip}' should be allowed but was denied")

        # 拒否されるべきIPアドレスのテスト
        denied_ips = [
            '*******',
            '*******',
            '***********'
        ]

        for ip in denied_ips:
            result = is_ip_allowed(ip)
            if not result:
                logger.info(f"✓ IP access control: '{ip}' is properly denied")
            else:
                logger.warning(f"✗ IP access control: '{ip}' should be denied but was allowed")

    def test_csrf_protection(self):
        """CSRF保護テスト"""
        logger.info("Testing CSRF protection...")

        # CSRFトークンなしでのPOSTリクエストテスト
        response = self.session.post(
            urljoin(self.base_url, '/api/v2/projects'),
            json={'name': 'test_project'}
        )

        if response.status_code == 403:
            logger.info("✓ CSRF protection is working - POST without token rejected")
        else:
            logger.warning("✗ CSRF protection may not be working - POST without token accepted")

    def test_cors_configuration(self):
        """CORS設定テスト"""
        logger.info("Testing CORS configuration...")

        # 許可されたオリジンからのリクエスト
        allowed_origins = [
            'http://localhost:3000',
            'http://localhost:3001'
        ]

        for origin in allowed_origins:
            headers = {'Origin': origin}
            response = self.session.options(
                urljoin(self.base_url, '/api/v2/projects'),
                headers=headers
            )

            if 'Access-Control-Allow-Origin' in response.headers:
                logger.info(f"✓ CORS: Origin '{origin}' is properly allowed")
            else:
                logger.warning(f"✗ CORS: Origin '{origin}' may not be properly configured")

        # 拒否されるべきオリジンからのリクエスト
        denied_origin = 'http://malicious-site.com'
        headers = {'Origin': denied_origin}
        response = self.session.options(
            urljoin(self.base_url, '/api/v2/projects'),
            headers=headers
        )

        if 'Access-Control-Allow-Origin' not in response.headers or \
           response.headers.get('Access-Control-Allow-Origin') != denied_origin:
            logger.info(f"✓ CORS: Origin '{denied_origin}' is properly denied")
        else:
            logger.warning(f"✗ CORS: Origin '{denied_origin}' should be denied")

    def test_configuration_security(self):
        """設定セキュリティテスト"""
        logger.info("Testing configuration security...")

        if not self.config:
            logger.warning("Configuration not available - skipping config tests")
            return

        # デフォルトパスワードのチェック
        webui_config = self.config.get_component_config('webui')
        password = webui_config.get('password', '')

        if password == 'change_this_password_immediately':
            logger.warning("✗ Default password is still in use! Please change it immediately.")
        elif len(password) < 8:
            logger.warning("✗ Password is too short. Use at least 8 characters.")
        else:
            logger.info("✓ Password appears to be properly configured")

        # 認証設定のチェック
        need_auth = webui_config.get('need-auth', False)
        if need_auth:
            logger.info("✓ Authentication is enabled")
        else:
            logger.warning("✗ Authentication is disabled - this is not recommended for production")

        # CSRF保護設定のチェック
        csrf_protection = webui_config.get('csrf_protection', False)
        if csrf_protection:
            logger.info("✓ CSRF protection is enabled")
        else:
            logger.warning("✗ CSRF protection is disabled")

    def test_logging_security(self):
        """ログセキュリティテスト"""
        logger.info("Testing logging security...")

        if not self.config:
            logger.warning("Configuration not available - skipping logging tests")
            return

        log_config = self.config.get_component_config('logging')

        # セキュリティ監査ログの設定チェック
        security_audit = log_config.get('enable_security_audit', False)
        if security_audit:
            logger.info("✓ Security audit logging is enabled")
        else:
            logger.warning("✗ Security audit logging is disabled")

        # ログディレクトリの権限チェック
        log_dir = log_config.get('dir', 'logs')
        if os.path.exists(log_dir):
            stat_info = os.stat(log_dir)
            permissions = oct(stat_info.st_mode)[-3:]

            if permissions in ['755', '750', '700']:
                logger.info(f"✓ Log directory permissions are secure: {permissions}")
            else:
                logger.warning(f"✗ Log directory permissions may be too permissive: {permissions}")
        else:
            logger.warning(f"✗ Log directory does not exist: {log_dir}")

    def run_all_tests(self):
        """すべてのセキュリティテストを実行"""
        logger.info("Starting comprehensive security tests...")

        try:
            self.test_input_validation()
            self.test_ip_access_control()
            self.test_configuration_security()
            self.test_logging_security()

            # ネットワークテスト（サーバーが起動している場合のみ）
            try:
                _ = self.session.get(self.base_url, timeout=5)
                self.test_authentication()
                self.test_csrf_protection()
                self.test_cors_configuration()
            except requests.exceptions.ConnectionError:
                logger.warning("Server is not running - skipping network tests")
            except Exception as e:
                logger.warning(f"Network tests failed: {e}")

        except Exception as e:
            logger.error(f"Error during security tests: {e}")

        logger.info("Security tests completed")

def main():
    """メイン関数"""
    import argparse

    parser = argparse.ArgumentParser(description='pyspiderNX2 Security Tester')
    parser.add_argument('--url', default='http://localhost:5000',
                       help='Base URL for testing (default: http://localhost:5000)')
    parser.add_argument('--test', choices=['auth', 'input', 'ip', 'csrf', 'cors', 'config', 'logging', 'all'],
                       default='all', help='Specific test to run (default: all)')

    args = parser.parse_args()

    tester = SecurityTester(args.url)

    if args.test == 'all':
        tester.run_all_tests()
    elif args.test == 'auth':
        tester.test_authentication()
    elif args.test == 'input':
        tester.test_input_validation()
    elif args.test == 'ip':
        tester.test_ip_access_control()
    elif args.test == 'csrf':
        tester.test_csrf_protection()
    elif args.test == 'cors':
        tester.test_cors_configuration()
    elif args.test == 'config':
        tester.test_configuration_security()
    elif args.test == 'logging':
        tester.test_logging_security()

if __name__ == '__main__':
    main()
