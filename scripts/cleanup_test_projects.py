#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
テストプロジェクト削除スクリプト
BaseHandlerエラーを起こすテストプロジェクトを削除
"""

import sqlite3
import os
import sys
from datetime import datetime

def cleanup_test_projects():
    """問題のあるテストプロジェクトを削除"""
    
    db_path = 'data/project.db'
    
    if not os.path.exists(db_path):
        print(f"❌ データベースファイルが見つかりません: {db_path}")
        return False
    
    print("🧹 テストプロジェクト削除開始")
    print("=" * 50)
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 問題のあるプロジェクトパターンを定義
        problematic_patterns = [
            '%test%',
            '%auth%',
            '%valid%',
            '%content_type%',
            '%form_test%',
            '%duplicate%',
            '%final_test%',
            '%unauthorized%',
            '%1748060%'  # タイムスタンプ付きテストプロジェクト
        ]
        
        total_deleted = 0
        
        for pattern in problematic_patterns:
            # 削除対象プロジェクトを検索
            cursor.execute(
                'SELECT name, status, updatetime FROM projectdb WHERE name LIKE ?',
                (pattern,)
            )
            projects = cursor.fetchall()
            
            if projects:
                print(f"\n🔍 パターン '{pattern}' に一致するプロジェクト: {len(projects)}個")
                
                for name, status, updatetime in projects:
                    print(f"   削除: {name} (status: {status})")
                
                # プロジェクトを削除
                cursor.execute('DELETE FROM projectdb WHERE name LIKE ?', (pattern,))
                deleted_count = cursor.rowcount
                total_deleted += deleted_count
                
                print(f"   ✅ {deleted_count}個のプロジェクトを削除")
        
        # 変更をコミット
        conn.commit()
        
        print(f"\n📊 削除サマリー")
        print("-" * 30)
        print(f"総削除数: {total_deleted}個")
        
        # 残りのプロジェクト数を確認
        cursor.execute('SELECT COUNT(*) FROM projectdb')
        remaining_count = cursor.fetchone()[0]
        print(f"残りのプロジェクト数: {remaining_count}個")
        
        # 残りのプロジェクト一覧を表示
        if remaining_count > 0:
            print(f"\n📋 残りのプロジェクト:")
            cursor.execute('SELECT name, status FROM projectdb ORDER BY name')
            remaining_projects = cursor.fetchall()
            
            for name, status in remaining_projects:
                print(f"   {name}: {status}")
        
        conn.close()
        
        print(f"\n🎉 テストプロジェクト削除完了")
        return True
        
    except Exception as e:
        print(f"❌ エラー: {e}")
        return False

def backup_database():
    """データベースをバックアップ"""
    
    db_path = 'data/project.db'
    backup_path = f'data/project_backup_{datetime.now().strftime("%Y%m%d_%H%M%S")}.db'
    
    try:
        import shutil
        shutil.copy2(db_path, backup_path)
        print(f"✅ データベースバックアップ作成: {backup_path}")
        return True
    except Exception as e:
        print(f"❌ バックアップ失敗: {e}")
        return False

def main():
    """メイン関数"""
    
    print("🗄️ pyspider テストプロジェクト削除ツール")
    print("=" * 50)
    print(f"実行時刻: {datetime.now().isoformat()}")
    
    # データベースバックアップ
    print("\n📦 データベースバックアップ中...")
    if not backup_database():
        print("⚠️ バックアップに失敗しましたが、続行します")
    
    # テストプロジェクト削除
    if cleanup_test_projects():
        print("\n🎯 推奨次ステップ:")
        print("1. pyspiderを再起動してください")
        print("2. WebUIでプロジェクト一覧を確認してください")
        print("3. 正常なプロジェクトが残っていることを確認してください")
        return 0
    else:
        print("\n❌ 削除処理に失敗しました")
        return 1

if __name__ == '__main__':
    sys.exit(main())
