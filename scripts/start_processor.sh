#!/bin/bash
# pyspider プロセッサ起動スクリプト（エラー対応版）

echo "🚀 pyspider プロセッサ起動"
echo "========================="

# プロジェクトルート（相対パス対応）
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"
cd "$PROJECT_ROOT"

# 環境設定読み込み
if [ -f ".env" ]; then
    source .env
    echo "✅ 環境変数読み込み完了"
else
    echo "⚠️ 環境変数ファイルが見つかりません - 設定を実行します"
    source scripts/setup_env.sh
fi

# 既存プロセッサプロセスを確認・停止
echo "🔍 既存プロセッサプロセスをチェック中..."
EXISTING_PID=$(ps aux | grep "pyspider.run processor" | grep -v grep | awk '{print $2}')

if [ ! -z "$EXISTING_PID" ]; then
    echo "⚠️ 既存プロセッサプロセス発見 (PID: $EXISTING_PID) - 停止します"
    kill -TERM $EXISTING_PID 2>/dev/null
    sleep 2
    
    # 強制終了が必要な場合
    if ps -p $EXISTING_PID > /dev/null 2>&1; then
        echo "🔨 強制終了します"
        kill -KILL $EXISTING_PID 2>/dev/null
    fi
fi

# PIDファイルクリーンアップ
if [ -f "/tmp/pyspider_processor.pid" ]; then
    rm -f /tmp/pyspider_processor.pid
    echo "✅ PIDファイルクリーンアップ完了"
fi

# Redis接続確認
echo "📡 Redis接続確認..."
if redis-cli ping > /dev/null 2>&1; then
    echo "✅ Redis: 接続成功"
else
    echo "❌ Redis: 接続失敗 - Redisを起動してください"
    echo "   sudo systemctl start redis"
    exit 1
fi

# データベース接続確認
echo "🗄️ データベース接続確認..."
python -c "
import sys
sys.path.insert(0, '$PROJECT_ROOT')
try:
    from pyspider.database.sqlalchemy.taskdb import TaskDB
    print('✅ データベース: 接続成功')
except Exception as e:
    print(f'❌ データベース: 接続失敗 ({e})')
    sys.exit(1)
" || exit 1

# プロセッサ起動
echo "🔄 プロセッサを起動中..."
echo "ログ: tail -f logs/processor.log"
echo "停止: Ctrl+C または kill \$(cat /tmp/pyspider_processor.pid)"
echo ""

# エラー継続モードでプロセッサ起動
PYTHONPATH="$PROJECT_ROOT:$PYTHONPATH" python -m pyspider.run processor \
    --process-time-limit 30 \
    2>&1 | tee -a logs/processor_startup.log

# 起動結果確認
if [ $? -eq 0 ]; then
    echo "✅ プロセッサ起動成功"
else
    echo "❌ プロセッサ起動失敗"
    echo "📝 ログを確認してください:"
    echo "   tail -20 logs/processor.log"
    echo "   tail -20 logs/processor_startup.log"
    exit 1
fi
