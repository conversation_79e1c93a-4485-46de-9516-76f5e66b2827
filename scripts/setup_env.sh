#!/bin/bash
# pyspiderNX2 環境設定スクリプト

echo "🔧 pyspiderNX2 環境設定"
echo "======================"

# プロジェクトルートディレクトリ（相対パス対応）
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"

# Pythonパス設定
export PYTHONPATH="$PROJECT_ROOT:$PYTHONPATH"
echo "✅ PYTHONPATH設定: $PYTHONPATH"

# 作業ディレクトリ移動
cd "$PROJECT_ROOT"
echo "✅ 作業ディレクトリ: $(pwd)"

# ログディレクトリ作成
mkdir -p logs data
echo "✅ ログディレクトリ作成完了"

# Redis起動確認
if command -v redis-cli &> /dev/null; then
    if redis-cli ping &> /dev/null; then
        echo "✅ Redis: 起動中"
    else
        echo "⚠️ Redis: 停止中 - 起動を試行します"
        if command -v systemctl &> /dev/null; then
            sudo systemctl start redis 2>/dev/null || echo "❌ Redis起動失敗 - 手動で起動してください"
        else
            redis-server --daemonize yes 2>/dev/null || echo "❌ Redis起動失敗 - 手動で起動してください"
        fi
    fi
else
    echo "❌ Redis: インストールされていません"
fi

# 環境変数をファイルに保存（相対パス使用）
cat > .env << EOF
# pyspiderNX2 環境変数
# 相対パス設定（pyspiderNX2ルートディレクトリから）
export PYTHONPATH="\$(pwd):\$PYTHONPATH"
export PYSPIDER_CONFIG_FILE="\$(pwd)/config.json"
export PYSPIDER_DATA_DIR="\$(pwd)/data"
export PYSPIDER_LOG_DIR="\$(pwd)/logs"
EOF

echo "✅ 環境変数ファイル作成: .env"

echo ""
echo "🚀 使用方法:"
echo "source scripts/setup_env.sh"
echo "または"
echo "source .env"

echo ""
echo "🎯 環境設定完了!"
