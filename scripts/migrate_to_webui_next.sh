#!/bin/bash
# pyspider webui/static から webui-next への完全移行スクリプト

echo "🚀 pyspider WebUI 移行スクリプト"
echo "=================================="

# 現在のディレクトリを保存
ORIGINAL_DIR=$(pwd)
# 相対パス対応
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"

cd "$PROJECT_ROOT"

echo "📁 現在の構成:"
echo "- webui/static: Vue 3 + Vuetify (Vite削除済み)"
echo "- webui-next: React 19 + Next.js 15.x"
echo ""

echo "🎯 移行計画:"
echo "1. webui/static を非推奨に設定"
echo "2. webui-next をメインWebUIに設定"
echo "3. リダイレクト設定を追加"
echo "4. 設定ファイルを更新"
echo ""

# 1. webui/static を非推奨に設定
echo "📝 1. webui/static を非推奨に設定中..."

cat > pyspider/webui/static/DEPRECATED.md << 'EOF'
# ⚠️ このWebUIは非推奨です

## 移行のお知らせ

このVue.js版WebUI (`pyspider/webui/static`) は非推奨となりました。

### 新しいWebUI
- **場所**: `pyspider/webui-next`
- **技術**: React 19 + Next.js 15.x
- **URL**: http://localhost:3000
- **機能**: より高性能で現代的なUI

### 移行理由
1. **Vite削除**: ビルドシステムが削除されたため
2. **技術的負債**: Vue 3 + Vuetifyの保守コスト
3. **パフォーマンス**: React 19の優れた性能
4. **開発効率**: Next.js 15.xの開発体験

### 移行手順
```bash
# 新しいWebUIを起動
cd pyspider/webui-next
npm run dev

# ブラウザでアクセス
open http://localhost:3000
```

### サポート終了予定
- **2024年12月**: 新機能開発停止
- **2025年3月**: 完全サポート終了

新しいWebUIをご利用ください。
EOF

# 2. index.htmlにリダイレクトを追加
echo "🔄 2. リダイレクト設定を追加中..."

cat > pyspider/webui/static/index.html << 'EOF'
<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>pyspider WebUI - 移行のお知らせ</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 40px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            text-align: center;
            max-width: 600px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        h1 {
            font-size: 2.5em;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }
        .warning {
            background: rgba(255, 193, 7, 0.2);
            border: 2px solid #ffc107;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        .new-ui {
            background: rgba(40, 167, 69, 0.2);
            border: 2px solid #28a745;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        .btn {
            display: inline-block;
            padding: 15px 30px;
            background: #28a745;
            color: white;
            text-decoration: none;
            border-radius: 50px;
            font-weight: bold;
            margin: 10px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
        }
        .btn:hover {
            background: #218838;
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
        }
        .btn-secondary {
            background: #6c757d;
            box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3);
        }
        .btn-secondary:hover {
            background: #5a6268;
            box-shadow: 0 6px 20px rgba(108, 117, 125, 0.4);
        }
        .countdown {
            font-size: 1.2em;
            margin: 20px 0;
            font-weight: bold;
        }
        .features {
            text-align: left;
            margin: 20px 0;
        }
        .features li {
            margin: 10px 0;
            padding-left: 20px;
            position: relative;
        }
        .features li::before {
            content: "✨";
            position: absolute;
            left: 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 pyspider WebUI</h1>
        
        <div class="warning">
            <h2>⚠️ 移行のお知らせ</h2>
            <p>このWebUIは新しいバージョンに移行しました。</p>
        </div>
        
        <div class="new-ui">
            <h3>🎉 新しいWebUI</h3>
            <ul class="features">
                <li>React 19 + Next.js 15.x</li>
                <li>高性能・高速レンダリング</li>
                <li>モダンなUI/UX</li>
                <li>リアルタイム更新</li>
                <li>レスポンシブデザイン</li>
            </ul>
        </div>
        
        <div class="countdown">
            <span id="countdown">5</span>秒後に自動的にリダイレクトします...
        </div>
        
        <div>
            <a href="http://localhost:3000" class="btn">
                🚀 新しいWebUIを開く
            </a>
            <a href="index-cdn.html" class="btn btn-secondary">
                📱 CDN版を使用
            </a>
        </div>
        
        <p style="margin-top: 30px; opacity: 0.8;">
            <small>
                問題がある場合は、<br>
                <code>cd pyspider/webui-next && npm run dev</code><br>
                でWebUIを起動してください。
            </small>
        </p>
    </div>

    <script>
        let countdown = 5;
        const countdownElement = document.getElementById('countdown');
        
        const timer = setInterval(() => {
            countdown--;
            countdownElement.textContent = countdown;
            
            if (countdown <= 0) {
                clearInterval(timer);
                window.location.href = 'http://localhost:3000';
            }
        }, 1000);
        
        // 新しいWebUIが起動しているかチェック
        fetch('http://localhost:3000')
            .then(response => {
                if (response.ok) {
                    console.log('新しいWebUIが利用可能です');
                }
            })
            .catch(() => {
                console.log('新しいWebUIが起動していません');
                clearInterval(timer);
                countdownElement.parentElement.innerHTML = 
                    '<p style="color: #ffc107;">新しいWebUIが起動していません。<br>' +
                    '<code>cd pyspider/webui-next && npm run dev</code><br>' +
                    'で起動してください。</p>';
            });
    </script>
</body>
</html>
EOF

# 3. pyspider設定ファイルを更新
echo "⚙️ 3. pyspider設定を更新中..."

# webui設定を更新（webui-nextを優先）
if [ -f "pyspider/config.py" ]; then
    echo "# WebUI設定更新" >> pyspider/config.py
    echo "WEBUI_PORT = 3000  # Next.js webui-next" >> pyspider/config.py
    echo "WEBUI_HOST = '0.0.0.0'" >> pyspider/config.py
    echo "WEBUI_USERNAME = 'admin'" >> pyspider/config.py
    echo "WEBUI_PASSWORD = 'PySpider2024!SecurePass#'" >> pyspider/config.py
fi

# 4. 起動スクリプトを更新
echo "🔧 4. 起動スクリプトを更新中..."

cat > scripts/start_webui_next.sh << 'EOF'
#!/bin/bash
# pyspider WebUI (Next.js) 起動スクリプト

echo "🚀 pyspider WebUI (React 19 + Next.js 15.x) を起動します"
echo "=============================================="

# 相対パス対応（2回目）
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"
cd "$PROJECT_ROOT/pyspider/webui-next"

# 依存関係チェック
if [ ! -d "node_modules" ]; then
    echo "📦 依存関係をインストール中..."
    npm install
fi

# 開発サーバー起動
echo "🌐 開発サーバーを起動中..."
echo "URL: http://localhost:3000"
echo "Ctrl+C で停止"

npm run dev
EOF

chmod +x scripts/start_webui_next.sh

# 5. README更新
echo "📚 5. ドキュメントを更新中..."

cat > pyspider/webui/README.md << 'EOF'
# pyspider WebUI

## 🚀 新しいWebUI (推奨)

**場所**: `pyspider/webui-next`  
**技術**: React 19 + Next.js 15.x  
**URL**: http://localhost:3000

### 起動方法
```bash
cd pyspider/webui-next
npm run dev
```

### 特徴
- ⚡ React 19の高性能レンダリング
- 🎨 モダンなUI/UX
- 📱 レスポンシブデザイン
- 🔄 リアルタイム更新
- 🛡️ セキュリティ強化

## ⚠️ 旧WebUI (非推奨)

**場所**: `pyspider/webui/static`  
**技術**: Vue 3 + Vuetify  
**状態**: 非推奨 (Vite削除済み)

### CDN版 (緊急時のみ)
```bash
# 静的ファイルサーバーで起動
python -m http.server 8080 -d pyspider/webui/static
# http://localhost:8080/index-cdn.html
```

## 移行スケジュール

- **2024年12月**: 新機能開発停止
- **2025年3月**: 完全サポート終了

新しいWebUIへの移行をお願いします。
EOF

echo ""
echo "✅ 移行設定完了!"
echo "=================================="
echo ""
echo "📋 次のステップ:"
echo "1. 新しいWebUIを起動:"
echo "   cd pyspider/webui-next && npm run dev"
echo ""
echo "2. ブラウザでアクセス:"
echo "   http://localhost:3000"
echo ""
echo "3. 旧WebUIアクセス時:"
echo "   http://localhost:5000 → 自動リダイレクト"
echo ""
echo "🎯 移行完了!"

cd "$ORIGINAL_DIR"
