#!/usr/bin/env node
/**
 * Express サーバー
 * pyspiderNX2用の補助サーバー
 */

const express = require('express');
const path = require('path');
const fs = require('fs');

const app = express();
const PORT = process.env.PORT || 9000;

// ミドルウェア設定
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// CORS設定
app.use((req, res, next) => {
  res.header('Access-Control-Allow-Origin', '*');
  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization');
  
  if (req.method === 'OPTIONS') {
    res.sendStatus(200);
  } else {
    next();
  }
});

// ログミドルウェア
app.use((req, res, next) => {
  const timestamp = new Date().toISOString();
  console.log(`[${timestamp}] ${req.method} ${req.url} - ${req.ip}`);
  next();
});

// ルート定義

// ヘルスチェック
app.get('/health', (req, res) => {
  res.json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    memory: process.memoryUsage(),
    version: process.version
  });
});

// pyspider統計情報（モック）
app.get('/api/stats', (req, res) => {
  res.json({
    projects: {
      total: 5,
      running: 2,
      stopped: 2,
      debug: 1
    },
    tasks: {
      pending: 150,
      active: 25,
      success: 1250,
      failed: 15
    },
    performance: {
      avg_response_time: 245,
      requests_per_minute: 120,
      success_rate: 98.8
    },
    timestamp: new Date().toISOString()
  });
});

// プロジェクト一覧（モック）
app.get('/api/projects', (req, res) => {
  const projects = [
    {
      name: 'example_project',
      group: 'default',
      status: 'RUNNING',
      rate: '1/s',
      burst: 3,
      pending: 10,
      active: 2,
      success: 150,
      failed: 1,
      last_run: new Date().toISOString()
    },
    {
      name: 'news_crawler',
      group: 'media',
      status: 'STOPPED',
      rate: '2/s',
      burst: 5,
      pending: 0,
      active: 0,
      success: 500,
      failed: 5,
      last_run: new Date(Date.now() - 3600000).toISOString()
    },
    {
      name: 'ecommerce_monitor',
      group: 'business',
      status: 'DEBUG',
      rate: '0.5/s',
      burst: 2,
      pending: 5,
      active: 1,
      success: 75,
      failed: 2,
      last_run: new Date(Date.now() - 1800000).toISOString()
    }
  ];
  
  res.json({
    projects,
    total: projects.length,
    timestamp: new Date().toISOString()
  });
});

// プロジェクト詳細
app.get('/api/projects/:name', (req, res) => {
  const { name } = req.params;
  
  // モックデータ
  const project = {
    name,
    group: 'default',
    status: 'RUNNING',
    rate: '1/s',
    burst: 3,
    script: `#!/usr/bin/env python
# -*- encoding: utf-8 -*-

from pyspider.libs.base_handler import *

class Handler(BaseHandler):
    crawl_config = {}

    @every(minutes=24 * 60)
    def on_start(self):
        self.crawl('http://example.com/', callback=self.index_page)

    @config(age=10 * 24 * 60 * 60)
    def index_page(self, response):
        for each in response.doc('a[href^="http"]').items():
            self.crawl(each.attr.href, callback=self.detail_page)

    @config(priority=2)
    def detail_page(self, response):
        return {
            'url': response.url,
            'title': response.doc('title').text(),
        }`,
    tasks: {
      pending: 10,
      active: 2,
      success: 150,
      failed: 1
    },
    created_at: new Date(Date.now() - 86400000).toISOString(),
    updated_at: new Date().toISOString()
  };
  
  res.json(project);
});

// プロジェクト作成
app.post('/api/projects', (req, res) => {
  const { name, group, script } = req.body;
  
  if (!name) {
    return res.status(400).json({
      error: 'プロジェクト名は必須です'
    });
  }
  
  // バリデーション
  if (!/^[a-zA-Z0-9_]+$/.test(name)) {
    return res.status(400).json({
      error: 'プロジェクト名は英数字とアンダースコアのみ使用可能です'
    });
  }
  
  const project = {
    name,
    group: group || 'default',
    status: 'TODO',
    script: script || '',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  };
  
  console.log(`📝 新規プロジェクト作成: ${name}`);
  
  res.status(201).json({
    message: 'プロジェクトが作成されました',
    project
  });
});

// プロジェクト操作（開始/停止）
app.post('/api/projects/:name/:action', (req, res) => {
  const { name, action } = req.params;
  
  if (!['start', 'stop', 'debug'].includes(action)) {
    return res.status(400).json({
      error: '無効なアクションです'
    });
  }
  
  const statusMap = {
    start: 'RUNNING',
    stop: 'STOPPED',
    debug: 'DEBUG'
  };
  
  console.log(`🎮 プロジェクト操作: ${name} -> ${action}`);
  
  res.json({
    message: `プロジェクト ${name} を ${action} しました`,
    project: {
      name,
      status: statusMap[action],
      updated_at: new Date().toISOString()
    }
  });
});

// 静的ファイル配信
app.use('/static', express.static(path.join(__dirname, '../public')));

// 404ハンドラー
app.use((req, res) => {
  res.status(404).json({
    error: 'エンドポイントが見つかりません',
    path: req.path,
    method: req.method
  });
});

// エラーハンドラー
app.use((err, req, res, next) => {
  console.error('サーバーエラー:', err);
  res.status(500).json({
    error: 'サーバー内部エラー',
    message: err.message
  });
});

// サーバー起動
app.listen(PORT, () => {
  console.log('🚀 Express サーバー起動');
  console.log('=' * 40);
  console.log(`📡 ポート: ${PORT}`);
  console.log(`🌐 URL: http://localhost:${PORT}`);
  console.log('');
  console.log('📋 利用可能なエンドポイント:');
  console.log('  GET  /health              - ヘルスチェック');
  console.log('  GET  /api/stats           - 統計情報');
  console.log('  GET  /api/projects        - プロジェクト一覧');
  console.log('  GET  /api/projects/:name  - プロジェクト詳細');
  console.log('  POST /api/projects        - プロジェクト作成');
  console.log('  POST /api/projects/:name/:action - プロジェクト操作');
  console.log('');
  console.log('🛑 停止: Ctrl+C');
});

// グレースフルシャットダウン
process.on('SIGTERM', () => {
  console.log('\n🛑 SIGTERM受信 - サーバーを停止します');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('\n🛑 SIGINT受信 - サーバーを停止します');
  process.exit(0);
});

module.exports = app;
