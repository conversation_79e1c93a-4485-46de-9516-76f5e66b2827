#!/usr/bin/env node
/**
 * Puppeteer動作テストスクリプト
 * pyspiderNX2でのPuppeteer機能確認
 */

const puppeteer = require('puppeteer');
const path = require('path');

async function testPuppeteer() {
  console.log('🚀 Puppeteer動作テスト開始');
  console.log('=' * 50);
  
  let browser;
  
  try {
    // ブラウザ起動
    console.log('📱 ブラウザを起動中...');
    browser = await puppeteer.launch({
      headless: 'new', // 新しいheadlessモード
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-accelerated-2d-canvas',
        '--no-first-run',
        '--no-zygote',
        '--disable-gpu'
      ]
    });
    
    console.log('✅ ブラウザ起動成功');
    
    // 新しいページを作成
    const page = await browser.newPage();
    
    // ビューポート設定
    await page.setViewport({ width: 1280, height: 720 });
    
    // User-Agent設定
    await page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36');
    
    console.log('📄 新しいページを作成');
    
    // テストサイトにアクセス
    console.log('🌐 テストサイトにアクセス中...');
    const response = await page.goto('https://example.com', {
      waitUntil: 'networkidle2',
      timeout: 30000
    });
    
    console.log(`✅ ページ読み込み完了 (ステータス: ${response.status()})`);
    
    // ページタイトル取得
    const title = await page.title();
    console.log(`📝 ページタイトル: ${title}`);
    
    // ページ内容取得
    const content = await page.content();
    console.log(`📊 ページサイズ: ${content.length} 文字`);
    
    // 要素の存在確認
    const h1Element = await page.$('h1');
    if (h1Element) {
      const h1Text = await page.evaluate(el => el.textContent, h1Element);
      console.log(`🎯 H1要素: ${h1Text}`);
    }
    
    // スクリーンショット撮影
    console.log('📸 スクリーンショット撮影中...');
    await page.screenshot({
      path: 'scripts/test-screenshot.png',
      fullPage: true
    });
    console.log('✅ スクリーンショット保存完了: scripts/test-screenshot.png');
    
    // JavaScript実行テスト
    console.log('⚡ JavaScript実行テスト...');
    const result = await page.evaluate(() => {
      return {
        url: window.location.href,
        userAgent: navigator.userAgent,
        timestamp: new Date().toISOString(),
        viewport: {
          width: window.innerWidth,
          height: window.innerHeight
        }
      };
    });
    
    console.log('✅ JavaScript実行結果:');
    console.log(`   URL: ${result.url}`);
    console.log(`   ビューポート: ${result.viewport.width}x${result.viewport.height}`);
    console.log(`   タイムスタンプ: ${result.timestamp}`);
    
    // フォーム操作テスト（もしフォームがあれば）
    const forms = await page.$$('form');
    console.log(`📋 フォーム数: ${forms.length}`);
    
    // リンク数カウント
    const links = await page.$$('a');
    console.log(`🔗 リンク数: ${links.length}`);
    
    // パフォーマンス測定
    const metrics = await page.metrics();
    console.log('📈 パフォーマンス指標:');
    console.log(`   JSヒープ使用量: ${Math.round(metrics.JSHeapUsedSize / 1024 / 1024)}MB`);
    console.log(`   DOM要素数: ${metrics.Nodes}`);
    console.log(`   イベントリスナー数: ${metrics.JSEventListeners}`);
    
    console.log('\n🎉 Puppeteerテスト完了');
    console.log('=' * 50);
    console.log('✅ 全ての機能が正常に動作しています');
    
  } catch (error) {
    console.error('❌ Puppeteerテストエラー:', error.message);
    console.error('スタックトレース:', error.stack);
    process.exit(1);
    
  } finally {
    if (browser) {
      console.log('🔚 ブラウザを終了中...');
      await browser.close();
      console.log('✅ ブラウザ終了完了');
    }
  }
}

// pyspider用のスクレイピング関数例
async function pyspiderExample() {
  console.log('\n🕷️ pyspider用スクレイピング例');
  console.log('-' * 30);
  
  const browser = await puppeteer.launch({ headless: 'new' });
  const page = await browser.newPage();
  
  try {
    // SPAサイトの例
    await page.goto('https://quotes.toscrape.com/js/', {
      waitUntil: 'networkidle2'
    });
    
    // JavaScript実行後の要素を待機
    await page.waitForSelector('.quote', { timeout: 10000 });
    
    // データ抽出
    const quotes = await page.evaluate(() => {
      const quoteElements = document.querySelectorAll('.quote');
      return Array.from(quoteElements).map(quote => ({
        text: quote.querySelector('.text')?.textContent?.trim(),
        author: quote.querySelector('.author')?.textContent?.trim(),
        tags: Array.from(quote.querySelectorAll('.tag')).map(tag => tag.textContent.trim())
      }));
    });
    
    console.log(`📚 抽出した名言数: ${quotes.length}`);
    if (quotes.length > 0) {
      console.log('📝 最初の名言:');
      console.log(`   テキスト: ${quotes[0].text}`);
      console.log(`   著者: ${quotes[0].author}`);
      console.log(`   タグ: ${quotes[0].tags.join(', ')}`);
    }
    
  } catch (error) {
    console.error('❌ スクレイピング例エラー:', error.message);
  } finally {
    await browser.close();
  }
}

// メイン実行
async function main() {
  await testPuppeteer();
  await pyspiderExample();
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = { testPuppeteer, pyspiderExample };
