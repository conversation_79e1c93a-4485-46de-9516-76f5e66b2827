#!/usr/bin/env node
/**
 * React 19 互換性チェックスクリプト
 * 現在のコードベースがReact 19に対応可能かを確認
 */

const fs = require('fs');
const path = require('path');

class React19CompatibilityChecker {
    constructor() {
        this.issues = [];
        this.warnings = [];
        this.srcDir = 'pyspider/webui-next/src';
        
        // React 19で非推奨・変更される機能
        this.deprecatedPatterns = [
            {
                pattern: /React\.FC/g,
                message: 'React.FC は React 19 で型推論が改善されるため、明示的な使用は不要になります',
                severity: 'warning'
            },
            {
                pattern: /useEffect\(\s*\(\)\s*=>\s*\{[^}]*\},\s*\[\]\s*\)/g,
                message: 'useEffect の依存配列が空の場合、React 19 では use() フックの使用を検討してください',
                severity: 'info'
            },
            {
                pattern: /React\.memo\(/g,
                message: 'React.memo は React 19 でも使用可能ですが、新しい最適化機能を確認してください',
                severity: 'info'
            },
            {
                pattern: /forwardRef/g,
                message: 'forwardRef は React 19 で簡素化される可能性があります',
                severity: 'info'
            },
            {
                pattern: /useCallback\(/g,
                message: 'useCallback は React 19 で自動最適化される可能性があります',
                severity: 'info'
            },
            {
                pattern: /useMemo\(/g,
                message: 'useMemo は React 19 で自動最適化される可能性があります',
                severity: 'info'
            }
        ];
        
        // React 19 の新機能で置き換え可能なパターン
        this.improvementPatterns = [
            {
                pattern: /useState\(\s*null\s*\)/g,
                message: 'React 19 では use() フックでより効率的な状態管理が可能です',
                severity: 'improvement'
            },
            {
                pattern: /fetch\(/g,
                message: 'React 19 では use() フックでより効率的なデータフェッチが可能です',
                severity: 'improvement'
            }
        ];
    }
    
    scanFile(filePath) {
        try {
            const content = fs.readFileSync(filePath, 'utf8');
            const relativePath = path.relative(process.cwd(), filePath);
            
            // 非推奨パターンをチェック
            this.deprecatedPatterns.forEach(({ pattern, message, severity }) => {
                const matches = content.match(pattern);
                if (matches) {
                    this.addIssue(relativePath, message, severity, matches.length);
                }
            });
            
            // 改善可能パターンをチェック
            this.improvementPatterns.forEach(({ pattern, message, severity }) => {
                const matches = content.match(pattern);
                if (matches) {
                    this.addIssue(relativePath, message, severity, matches.length);
                }
            });
            
        } catch (error) {
            console.error(`ファイル読み込みエラー: ${filePath}`, error.message);
        }
    }
    
    addIssue(file, message, severity, count) {
        const issue = {
            file,
            message,
            severity,
            count
        };
        
        if (severity === 'warning' || severity === 'error') {
            this.issues.push(issue);
        } else {
            this.warnings.push(issue);
        }
    }
    
    scanDirectory(dir) {
        if (!fs.existsSync(dir)) {
            console.log(`ディレクトリが見つかりません: ${dir}`);
            return;
        }
        
        const files = fs.readdirSync(dir);
        
        files.forEach(file => {
            const filePath = path.join(dir, file);
            const stat = fs.statSync(filePath);
            
            if (stat.isDirectory()) {
                this.scanDirectory(filePath);
            } else if (file.endsWith('.js') || file.endsWith('.jsx')) {
                this.scanFile(filePath);
            }
        });
    }
    
    checkPackageJson() {
        const packageJsonPath = 'pyspider/webui-next/package.json';
        
        if (!fs.existsSync(packageJsonPath)) {
            this.addIssue(packageJsonPath, 'package.json が見つかりません', 'error', 1);
            return;
        }
        
        try {
            const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
            const dependencies = { ...packageJson.dependencies, ...packageJson.devDependencies };
            
            // React バージョンチェック
            if (dependencies.react) {
                const reactVersion = dependencies.react.replace(/[^0-9.]/g, '');
                const majorVersion = parseInt(reactVersion.split('.')[0]);
                
                if (majorVersion < 18) {
                    this.addIssue(packageJsonPath, `React ${reactVersion} は React 19 と互換性がない可能性があります`, 'warning', 1);
                } else if (majorVersion === 18) {
                    this.addIssue(packageJsonPath, `React ${reactVersion} から React 19 への移行が可能です`, 'info', 1);
                }
            }
            
            // Next.js バージョンチェック
            if (dependencies.next) {
                const nextVersion = dependencies.next.replace(/[^0-9.]/g, '');
                const majorVersion = parseInt(nextVersion.split('.')[0]);
                
                if (majorVersion < 14) {
                    this.addIssue(packageJsonPath, `Next.js ${nextVersion} は React 19 をサポートしていない可能性があります`, 'warning', 1);
                } else {
                    this.addIssue(packageJsonPath, `Next.js ${nextVersion} は React 19 と互換性があります`, 'info', 1);
                }
            }
            
        } catch (error) {
            this.addIssue(packageJsonPath, `package.json の解析エラー: ${error.message}`, 'error', 1);
        }
    }
    
    generateReport() {
        console.log('🔍 React 19 互換性チェック結果');
        console.log('=' * 60);
        
        // package.json チェック
        this.checkPackageJson();
        
        // ソースコードスキャン
        this.scanDirectory(this.srcDir);
        
        // 結果表示
        console.log(`\\n📊 スキャン結果サマリー`);
        console.log(`重要な問題: ${this.issues.length}`);
        console.log(`情報・改善提案: ${this.warnings.length}`);
        
        if (this.issues.length > 0) {
            console.log(`\\n⚠️ 重要な問題 (${this.issues.length}件)`);
            console.log('-'.repeat(50));
            this.issues.forEach((issue, index) => {
                console.log(`${index + 1}. ${issue.file}`);
                console.log(`   ${issue.message}`);
                console.log(`   発生回数: ${issue.count}`);
                console.log('');
            });
        }
        
        if (this.warnings.length > 0) {
            console.log(`\\n💡 情報・改善提案 (${this.warnings.length}件)`);
            console.log('-'.repeat(50));
            this.warnings.slice(0, 10).forEach((warning, index) => {
                console.log(`${index + 1}. ${warning.file}`);
                console.log(`   ${warning.message}`);
                console.log(`   発生回数: ${warning.count}`);
                console.log('');
            });
            
            if (this.warnings.length > 10) {
                console.log(`   ... 他 ${this.warnings.length - 10} 件`);
            }
        }
        
        // 移行推奨度の判定
        console.log(`\\n🎯 React 19 移行推奨度`);
        console.log('-'.repeat(50));
        
        const criticalIssues = this.issues.filter(i => i.severity === 'error').length;
        const warningIssues = this.issues.filter(i => i.severity === 'warning').length;
        
        if (criticalIssues === 0 && warningIssues === 0) {
            console.log('🟢 推奨: React 19 への移行を推奨します');
            console.log('   互換性の問題は検出されませんでした');
        } else if (criticalIssues === 0 && warningIssues <= 3) {
            console.log('🟡 条件付き推奨: 軽微な修正後にReact 19への移行が可能です');
            console.log(`   警告: ${warningIssues}件の確認が必要です`);
        } else if (criticalIssues <= 2) {
            console.log('🟠 要検討: いくつかの問題を解決してからの移行を推奨します');
            console.log(`   重要な問題: ${criticalIssues}件, 警告: ${warningIssues}件`);
        } else {
            console.log('🔴 非推奨: 現時点でのReact 19移行は推奨しません');
            console.log(`   重要な問題: ${criticalIssues}件の解決が必要です`);
        }
        
        // 移行手順の提案
        if (criticalIssues <= 2 && warningIssues <= 5) {
            console.log(`\\n📋 React 19 移行手順（推奨）`);
            console.log('-'.repeat(50));
            console.log('1. 現在のコードベースのバックアップを作成');
            console.log('2. テストスイートの実行（移行前の動作確認）');
            console.log('3. React と React-DOM を 19.x に更新');
            console.log('4. Next.js を最新版に更新（React 19 サポート版）');
            console.log('5. 検出された問題の修正');
            console.log('6. テストスイートの再実行');
            console.log('7. 段階的なデプロイメント');
        }
        
        return {
            criticalIssues,
            warningIssues,
            totalIssues: this.issues.length + this.warnings.length,
            recommendation: criticalIssues === 0 && warningIssues <= 3 ? 'recommended' : 
                           criticalIssues <= 2 ? 'conditional' : 'not_recommended'
        };
    }
}

// スクリプト実行
if (require.main === module) {
    const checker = new React19CompatibilityChecker();
    const result = checker.generateReport();
    
    // 終了コード設定
    process.exit(result.criticalIssues > 0 ? 1 : 0);
}

module.exports = React19CompatibilityChecker;
