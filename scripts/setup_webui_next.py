#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
webui-nextセットアップスクリプト
pip install後にwebui-nextの依存関係をインストールし、ビルドを実行
"""

import os
import sys
import subprocess
import shutil
import json
from pathlib import Path


def run_command(cmd, cwd=None, check=True):
    """コマンドを実行"""
    print(f"実行中: {' '.join(cmd) if isinstance(cmd, list) else cmd}")
    try:
        result = subprocess.run(
            cmd, 
            cwd=cwd, 
            check=check, 
            capture_output=True, 
            text=True,
            shell=isinstance(cmd, str)
        )
        if result.stdout:
            print(result.stdout)
        return result
    except subprocess.CalledProcessError as e:
        print(f"エラー: {e}")
        if e.stderr:
            print(f"stderr: {e.stderr}")
        if check:
            raise
        return e


def check_node_npm():
    """Node.jsとnpmの存在確認"""
    try:
        node_result = run_command(['node', '--version'], check=False)
        npm_result = run_command(['npm', '--version'], check=False)
        
        if node_result.returncode == 0 and npm_result.returncode == 0:
            print(f"✅ Node.js: {node_result.stdout.strip()}")
            print(f"✅ npm: {npm_result.stdout.strip()}")
            return True
        else:
            print("❌ Node.jsまたはnpmが見つかりません")
            return False
    except Exception as e:
        print(f"❌ Node.js/npm確認エラー: {e}")
        return False


def find_pyspider_installation():
    """pyspiderのインストール場所を特定"""
    try:
        import pyspider
        pyspider_path = Path(pyspider.__file__).parent
        webui_next_path = pyspider_path / "webui-next"
        
        if webui_next_path.exists():
            print(f"✅ pyspider webui-next found: {webui_next_path}")
            return webui_next_path
        else:
            print(f"❌ webui-nextディレクトリが見つかりません: {webui_next_path}")
            return None
    except ImportError:
        print("❌ pyspiderがインストールされていません")
        return None


def setup_webui_next(webui_next_path):
    """webui-nextのセットアップ"""
    print("=== webui-nextセットアップ開始 ===")
    
    # package.jsonの存在確認
    package_json = webui_next_path / "package.json"
    if not package_json.exists():
        print(f"❌ package.jsonが見つかりません: {package_json}")
        return False
    
    # npm install
    print("📦 npm install実行中...")
    result = run_command(['npm', 'install'], cwd=webui_next_path, check=False)
    if result.returncode != 0:
        print("❌ npm installに失敗しました")
        return False
    
    # Next.js build
    print("🔨 Next.js build実行中...")
    result = run_command(['npm', 'run', 'build'], cwd=webui_next_path, check=False)
    if result.returncode != 0:
        print("⚠️  Next.js buildに失敗しました（開発時は無視可能）")
        # buildの失敗は致命的ではない（開発時）
    
    print("✅ webui-nextセットアップ完了")
    return True


def create_webui_next_launcher():
    """webui-next起動スクリプトの作成"""
    webui_next_path = find_pyspider_installation()
    if not webui_next_path:
        return False
    
    launcher_script = f"""#!/usr/bin/env python3
# -*- coding: utf-8 -*-
'''
pyspiderNX2 webui-next launcher
'''

import os
import sys
import subprocess
from pathlib import Path

def main():
    webui_next_path = Path(__file__).parent / "webui-next"
    
    if not webui_next_path.exists():
        print("❌ webui-nextディレクトリが見つかりません")
        sys.exit(1)
    
    # 開発サーバー起動
    try:
        print("🚀 webui-next開発サーバーを起動中...")
        subprocess.run(['npm', 'run', 'dev'], cwd=webui_next_path)
    except KeyboardInterrupt:
        print("\\n✅ webui-nextサーバーを停止しました")
    except Exception as e:
        print(f"❌ エラー: {{e}}")
        sys.exit(1)

if __name__ == "__main__":
    main()
"""
    
    # pyspiderディレクトリにランチャーを作成
    try:
        import pyspider
        pyspider_path = Path(pyspider.__file__).parent
        launcher_path = pyspider_path / "webui_next_launcher.py"
        
        with open(launcher_path, 'w', encoding='utf-8') as f:
            f.write(launcher_script)
        
        # 実行権限を付与
        os.chmod(launcher_path, 0o755)
        print(f"✅ webui-nextランチャーを作成: {launcher_path}")
        return True
    except Exception as e:
        print(f"❌ ランチャー作成エラー: {e}")
        return False


def main():
    """メイン処理"""
    print("🚀 pyspiderNX2 webui-nextセットアップ")
    print("=" * 50)
    
    # Node.js/npm確認
    if not check_node_npm():
        print("\n❌ Node.js/npmをインストールしてください")
        print("参考: https://nodejs.org/")
        sys.exit(1)
    
    # pyspiderインストール場所確認
    webui_next_path = find_pyspider_installation()
    if not webui_next_path:
        sys.exit(1)
    
    # webui-nextセットアップ
    if not setup_webui_next(webui_next_path):
        sys.exit(1)
    
    # ランチャー作成
    create_webui_next_launcher()
    
    print("\n🎉 セットアップ完了！")
    print("\nwebui-nextの起動方法:")
    print("  python -m pyspider.webui_next_launcher")
    print("  または")
    print(f"  cd {webui_next_path} && npm run dev")


if __name__ == "__main__":
    main()
