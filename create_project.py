#!/usr/bin/env python
# -*- coding: utf-8 -*-

import requests
import json
import time

# プロジェクト作成用のスクリプト
def create_project(name, group="default", status="RUNNING"):
    # プロジェクトのスクリプトテンプレート
    script = """
#!/usr/bin/env python
# -*- coding: utf-8 -*-
import re
from pyspider.libs.base_handler import *

class Handler(BaseHandler):
    crawl_config = {
    }

    @every(minutes=24 * 60)
    def on_start(self):
        self.crawl('https://example.com/', callback=self.index_page)

    @config(age=10 * 24 * 60 * 60)
    def index_page(self, response):
        for each in response.doc('a[href^="http"]').items():
            self.crawl(each.attr.href, callback=self.detail_page)

    @config(priority=2)
    def detail_page(self, response):
        return {
            "url": response.url,
            "title": response.doc('title').text(),
        }
"""

    # プロジェクトデータの作成
    project_data = {
        "name": name,
        "group": group,
        "status": status,
        "script": script,
        "rate": 1,
        "burst": 10,
        "updatetime": time.time()
    }

    # プロジェクトの作成
    try:
        # プロジェクトの作成
        response = requests.post(
            "http://localhost:5000/debug/new",
            data={
                "project": name,
                "script": script
            }
        )
        print(f"Project creation response: {response.status_code}")
        print(response.text)

        # プロジェクトの更新（ステータスの設定）
        update_response = requests.post(
            f"http://localhost:5000/update",
            data={
                "pk": name,
                "name": "status",
                "value": status
            }
        )
        print(f"Project update response: {update_response.status_code}")
        print(update_response.text)

        return True
    except Exception as e:
        print(f"Error creating project: {e}")
        return False

# サンプルプロジェクトの作成
if __name__ == "__main__":
    projects = [
        {"name": "example_project", "group": "examples", "status": "RUNNING"},
        {"name": "news_crawler", "group": "crawlers", "status": "PAUSED"},
        {"name": "product_scraper", "group": "scrapers", "status": "RUNNING"}
    ]

    for project in projects:
        print(f"Creating project: {project['name']}")
        create_project(project['name'], project['group'], project['status'])
        print("---")
