#!/bin/bash

# PySpider起動スクリプト - SQLite + Redisモード
# Redis + SQLiteデータベースでpyspiderを起動します

# OpenSSL設定を環境変数に設定
export OPENSSL_CONF=/dev/null

# Python 3.13 optimizations (conditional)
# Note: Free-threaded mode requires special Python build
# export PYTHON_GIL=0  # Disable GIL for Python 3.13 free-threaded mode (requires free-threaded build)

# Python 3.13 JIT optimization (experimental)
# export PYTHON_JIT=1  # Enable JIT compiler if available (experimental feature)

# 色の定義
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# ログファイル
LOG_FILE="pyspider_start_sqlite_redis.log"

# ログ関数
log_message() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

echo -e "${BLUE}PySpider起動スクリプト - SQLite + Redisモード${NC}"
echo "=================================="
log_message "PySpider SQLite + Redisモード起動開始"

# logsディレクトリの作成
if [ ! -d "logs" ]; then
    echo -e "${YELLOW}logsディレクトリを作成しています...${NC}"
    mkdir -p logs
    echo -e "${GREEN}logsディレクトリを作成しました${NC}"
fi

# MySQL + Redis設定ファイルの作成
create_mysql_redis_config() {
    echo -e "${YELLOW}MySQL + Redis設定ファイルを作成中...${NC}"
    log_message "MySQL + Redis設定ファイル作成開始"

    # config.jsonをMySQL設定に更新
    echo -e "${BLUE}config.jsonをMySQL設定に更新中...${NC}"
    python3 -c "
import json
import sys
sys.path.insert(0, '.')

try:
    with open('config.json', 'r') as f:
        config = json.load(f)

    config['usedatabase'] = 'mysql'

    with open('config.json', 'w') as f:
        json.dump(config, f, indent=4)

    print('✅ config.jsonをMySQL設定に更新しました')
except Exception as e:
    print(f'❌ config.jsonの更新に失敗しました: {e}')
    sys.exit(1)
"
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ config.jsonをMySQL設定に更新しました${NC}"
        log_message "config.json MySQL設定更新成功"
    else
        echo -e "${RED}❌ config.jsonの更新に失敗しました${NC}"
        log_message "config.json MySQL設定更新失敗"
        exit 1
    fi

    # MySQL接続情報の設定（環境変数から取得、デフォルト値を設定）
    MYSQL_HOST="${MYSQL_HOST:-localhost}"
    MYSQL_PORT="${MYSQL_PORT:-3306}"
    MYSQL_USER="${MYSQL_USER:-pyspider}"
    MYSQL_PASSWORD="${MYSQL_PASSWORD:-PySpider2024!SecurePass#}"
    MYSQL_DATABASE="${MYSQL_DATABASE:-pyspider}"

    cat > mysql_redis_config.json << EOF
{
    "taskdb": "sqlalchemy+mysql+pymysql+taskdb://${MYSQL_USER}:${MYSQL_PASSWORD}@${MYSQL_HOST}:${MYSQL_PORT}/${MYSQL_DATABASE}_taskdb",
    "projectdb": "sqlalchemy+mysql+pymysql+projectdb://${MYSQL_USER}:${MYSQL_PASSWORD}@${MYSQL_HOST}:${MYSQL_PORT}/${MYSQL_DATABASE}_projectdb",
    "resultdb": "sqlalchemy+mysql+pymysql+resultdb://${MYSQL_USER}:${MYSQL_PASSWORD}@${MYSQL_HOST}:${MYSQL_PORT}/${MYSQL_DATABASE}_resultdb",
    "message_queue": "redis://localhost:6379/0",
    "usedatabase": "mysql",
    "redis_fallback": {
        "enabled": true,
        "auto_fallback": true,
        "check_interval": 30,
        "max_connection_attempts": 3,
        "fallback_mode": "xmlrpc",
        "comment": "Redis接続失敗時にXMLRPCモードに自動フォールバック"
    },
    "webui": {
        "username": "",
        "password": "",
        "need_auth": false,
        "host": "0.0.0.0",
        "port": 5000
    },
    "fetcher": {
        "xmlrpc_host": "0.0.0.0",
        "xmlrpc_port": 24444
    },
    "scheduler": {
        "xmlrpc_host": "0.0.0.0", 
        "xmlrpc_port": 23333
    },
    "puppeteer_endpoint": "http://localhost:22223"
}
EOF

    echo -e "${GREEN}✅ MySQL + Redis設定ファイルを作成しました: mysql_redis_config.json${NC}"
    echo -e "${BLUE}MySQL接続情報:${NC}"
    echo -e "${GREEN}  • ホスト: ${MYSQL_HOST}:${MYSQL_PORT}${NC}"
    echo -e "${GREEN}  • ユーザー: ${MYSQL_USER}${NC}"
    echo -e "${GREEN}  • データベース: ${MYSQL_DATABASE}_*${NC}"
    log_message "MySQL + Redis設定ファイル作成完了"
}

# 設定ファイル作成実行
create_mysql_redis_config

# 改良されたプロセスクリーンアップ機能
cleanup_existing_processes() {
    echo -e "${YELLOW}既存のPySpiderプロセスのクリーンアップを実行中...${NC}"
    log_message "既存プロセスクリーンアップ開始"

    # 1. PySpiderプロセスの終了
    echo -e "${YELLOW}PySpiderプロセスを終了中...${NC}"
    PYSPIDER_PIDS=$(ps aux | grep -E "python.*run.py|python.*pyspider.run" | grep -v grep | awk '{print $2}')
    if [ ! -z "$PYSPIDER_PIDS" ]; then
        log_message "PySpiderプロセスを終了: $PYSPIDER_PIDS"
        kill -TERM $PYSPIDER_PIDS 2>/dev/null
        sleep 2
        # 強制終了が必要な場合
        REMAINING_PIDS=$(ps aux | grep -E "python.*run.py|python.*pyspider.run" | grep -v grep | awk '{print $2}')
        if [ ! -z "$REMAINING_PIDS" ]; then
            log_message "PySpiderプロセス強制終了: $REMAINING_PIDS"
            kill -9 $REMAINING_PIDS 2>/dev/null
        fi
        echo -e "${GREEN}PySpiderプロセスを終了しました${NC}"
    else
        echo -e "${GREEN}PySpiderプロセスは実行されていません${NC}"
    fi

    # 2. Puppeteer Fetcherプロセスの終了
    echo -e "${YELLOW}Puppeteer Fetcherプロセスを終了中...${NC}"
    PUPPETEER_PIDS=$(ps aux | grep "node.*puppeteer_fetcher.js" | grep -v grep | awk '{print $2}')
    if [ ! -z "$PUPPETEER_PIDS" ]; then
        log_message "Puppeteer Fetcherプロセスを終了: $PUPPETEER_PIDS"
        kill -TERM $PUPPETEER_PIDS 2>/dev/null
        sleep 1
        # 強制終了が必要な場合
        REMAINING_PIDS=$(ps aux | grep "node.*puppeteer_fetcher.js" | grep -v grep | awk '{print $2}')
        if [ ! -z "$REMAINING_PIDS" ]; then
            log_message "Puppeteer Fetcherプロセス強制終了: $REMAINING_PIDS"
            kill -9 $REMAINING_PIDS 2>/dev/null
        fi
        echo -e "${GREEN}Puppeteer Fetcherプロセスを終了しました${NC}"
    else
        echo -e "${GREEN}Puppeteer Fetcherプロセスは実行されていません${NC}"
    fi

    # 3. ポートクリーンアップ
    echo -e "${YELLOW}PySpider関連ポートのクリーンアップを実行中...${NC}"
    local PYSPIDER_PORTS=(5000 23333 24444 22223 22224 22225)

    for PORT in "${PYSPIDER_PORTS[@]}"; do
        PIDS=$(lsof -i:$PORT -t 2>/dev/null)
        if [ ! -z "$PIDS" ]; then
            log_message "ポート$PORTを使用中のプロセスを終了: $PIDS"
            echo -e "${YELLOW}ポート$PORTを使用中のプロセスを終了: $PIDS${NC}"
            kill -TERM $PIDS 2>/dev/null
            sleep 1
            # 強制終了が必要な場合
            REMAINING_PIDS=$(lsof -i:$PORT -t 2>/dev/null)
            if [ ! -z "$REMAINING_PIDS" ]; then
                kill -9 $REMAINING_PIDS 2>/dev/null
            fi
        fi
    done

    log_message "既存プロセスクリーンアップ完了"
    echo -e "${GREEN}既存のプロセスクリーンアップが完了しました${NC}"
}

# プロセスクリーンアップ実行
cleanup_existing_processes

# MySQLサーバー接続確認
check_mysql_connection() {
    echo -e "${YELLOW}MySQLサーバー接続を確認中...${NC}"
    log_message "MySQL接続確認開始"

    # MySQL接続情報
    MYSQL_HOST="${MYSQL_HOST:-localhost}"
    MYSQL_PORT="${MYSQL_PORT:-3306}"
    MYSQL_USER="${MYSQL_USER:-pyspider}"
    MYSQL_PASSWORD="${MYSQL_PASSWORD:-PySpider2024!SecurePass#}"
    MYSQL_DATABASE="${MYSQL_DATABASE:-pyspider}"

    # MySQL接続テスト
    if command -v mysql >/dev/null 2>&1; then
        echo -e "${YELLOW}MySQL接続テストを実行中...${NC}"
        
        # 接続テスト（タイムアウト5秒）
        if timeout 5 mysql -h"$MYSQL_HOST" -P"$MYSQL_PORT" -u"$MYSQL_USER" -p"$MYSQL_PASSWORD" -e "SELECT 1;" >/dev/null 2>&1; then
            echo -e "${GREEN}✅ MySQLサーバーへの接続が確認できました${NC}"
            log_message "MySQL接続テスト成功"
            
            # データベース存在確認と作成
            echo -e "${YELLOW}PySpiderデータベースを確認中...${NC}"
            
            # 各データベースを作成（存在しない場合）
            local DATABASES=(
                "${MYSQL_DATABASE}_taskdb"
                "${MYSQL_DATABASE}_projectdb"
                "${MYSQL_DATABASE}_resultdb"
            )
            
            for DB in "${DATABASES[@]}"; do
                mysql -h"$MYSQL_HOST" -P"$MYSQL_PORT" -u"$MYSQL_USER" -p"$MYSQL_PASSWORD" -e "CREATE DATABASE IF NOT EXISTS \`$DB\` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;" 2>/dev/null
                if [ $? -eq 0 ]; then
                    echo -e "${GREEN}✅ データベース確認/作成: $DB${NC}"
                    log_message "データベース確認/作成: $DB"
                else
                    echo -e "${YELLOW}⚠️  データベース作成スキップ: $DB${NC}"
                    log_message "警告: データベース作成スキップ: $DB"
                fi
            done
            
        else
            echo -e "${RED}❌ MySQLサーバーに接続できません${NC}"
            echo -e "${RED}接続情報を確認してください:${NC}"
            echo -e "${RED}  • ホスト: ${MYSQL_HOST}:${MYSQL_PORT}${NC}"
            echo -e "${RED}  • ユーザー: ${MYSQL_USER}${NC}"
            echo -e "${RED}  • パスワード: [設定済み]${NC}"
            echo -e "${YELLOW}環境変数で設定を変更できます:${NC}"
            echo -e "${YELLOW}  export MYSQL_HOST=localhost${NC}"
            echo -e "${YELLOW}  export MYSQL_PORT=3306${NC}"
            echo -e "${YELLOW}  export MYSQL_USER=pyspider${NC}"
            echo -e "${YELLOW}  export MYSQL_PASSWORD=your_password${NC}"
            echo -e "${YELLOW}  export MYSQL_DATABASE=pyspider${NC}"
            log_message "エラー: MySQL接続失敗"
            exit 1
        fi
    else
        echo -e "${YELLOW}⚠️  MySQLクライアントが見つかりません${NC}"
        echo -e "${YELLOW}MySQL接続確認をスキップします${NC}"
        echo -e "${YELLOW}MySQLサーバーが起動していることを確認してください${NC}"
        log_message "警告: MySQLクライアント未発見、接続確認スキップ"
    fi

    echo -e "${BLUE}MySQL情報:${NC}"
    echo -e "${GREEN}  • ホスト: ${MYSQL_HOST}:${MYSQL_PORT}${NC}"
    echo -e "${GREEN}  • TaskDB: ${MYSQL_DATABASE}_taskdb${NC}"
    echo -e "${GREEN}  • ProjectDB: ${MYSQL_DATABASE}_projectdb${NC}"
    echo -e "${GREEN}  • ResultDB: ${MYSQL_DATABASE}_resultdb${NC}"

    log_message "MySQL接続確認完了"
}

# MySQL接続確認実行（スキップして直接pyspiderに任せる）
echo -e "${YELLOW}⚠️  MySQL接続確認をスキップします${NC}"
echo -e "${YELLOW}pyspiderが直接MySQL接続を試行します${NC}"
echo -e "${BLUE}MySQL情報:${NC}"
echo -e "${GREEN}  • ホスト: ${MYSQL_HOST}:${MYSQL_PORT}${NC}"
echo -e "${GREEN}  • TaskDB: ${MYSQL_DATABASE}_taskdb${NC}"
echo -e "${GREEN}  • ProjectDB: ${MYSQL_DATABASE}_projectdb${NC}"
echo -e "${GREEN}  • ResultDB: ${MYSQL_DATABASE}_resultdb${NC}"
log_message "MySQL接続確認スキップ、pyspiderに委任"

# 改良されたRedis管理機能
manage_redis_server() {
    echo -e "${YELLOW}Redis管理機能を実行中...${NC}"
    log_message "Redis管理開始"

    # Redisポート（6379）確認
    echo -e "${YELLOW}Redisポート（6379）を確認中...${NC}"
    REDIS_PIDS=$(lsof -i :6379 -t 2>/dev/null)

    if [ -n "$REDIS_PIDS" ]; then
        echo -e "${GREEN}Redisサーバーが既に実行中です (PID: $REDIS_PIDS)${NC}"
        log_message "Redis既存プロセス発見: $REDIS_PIDS"

        # Redis接続テスト
        echo -e "${YELLOW}Redis接続テストを実行中...${NC}"
        if timeout 5 redis-cli ping >/dev/null 2>&1; then
            echo -e "${GREEN}✅ Redisサーバーへの接続が確認できました${NC}"
            log_message "Redis接続テスト成功"
        else
            echo -e "${RED}❌ Redisサーバーが応答しません。再起動を試みます...${NC}"
            log_message "Redis接続テスト失敗、再起動実行"

            # 段階的終了
            kill -TERM $REDIS_PIDS 2>/dev/null
            sleep 2
            # 強制終了が必要な場合
            REMAINING_PIDS=$(lsof -i :6379 -t 2>/dev/null)
            if [ ! -z "$REMAINING_PIDS" ]; then
                kill -9 $REMAINING_PIDS 2>/dev/null
            fi

            # Redis再起動
            echo -e "${YELLOW}Redisサーバーを再起動中...${NC}"
            redis-server --daemonize yes --port 6379 --bind 127.0.0.1
            sleep 3

            # 再起動確認
            if timeout 5 redis-cli ping >/dev/null 2>&1; then
                echo -e "${GREEN}✅ Redisサーバーを再起動しました${NC}"
                log_message "Redis再起動成功"
            else
                echo -e "${RED}❌ Redisサーバーの再起動に失敗しました${NC}"
                log_message "エラー: Redis再起動失敗"
                exit 1
            fi
        fi
    else
        echo -e "${YELLOW}Redisサーバーを起動中...${NC}"
        log_message "Redis新規起動開始"

        # Redisサーバー起動
        redis-server --daemonize yes --port 6379 --bind 127.0.0.1
        sleep 3

        # 起動確認
        local MAX_ATTEMPTS=5
        local ATTEMPT=1

        while [ $ATTEMPT -le $MAX_ATTEMPTS ]; do
            echo -e "${YELLOW}Redis起動確認 $ATTEMPT/$MAX_ATTEMPTS...${NC}"

            if timeout 5 redis-cli ping >/dev/null 2>&1; then
                REDIS_PID=$(lsof -i :6379 -t 2>/dev/null | head -1)
                echo -e "${GREEN}✅ Redisサーバーを起動しました (PID: $REDIS_PID)${NC}"
                log_message "Redis新規起動成功 (PID: $REDIS_PID)"
                break
            fi

            sleep 2
            ATTEMPT=$((ATTEMPT + 1))
        done

        if [ $ATTEMPT -gt $MAX_ATTEMPTS ]; then
            echo -e "${RED}❌ Redisサーバーの起動に失敗しました${NC}"
            log_message "エラー: Redis新規起動失敗"
            exit 1
        fi
    fi

    # Redis情報表示
    echo -e "${BLUE}Redis情報:${NC}"
    echo -e "${GREEN}  • ポート: 6379${NC}"
    echo -e "${GREEN}  • 接続: redis://localhost:6379/0${NC}"

    log_message "Redis管理完了"
}

# Redis管理実行
manage_redis_server

# 少し待機してポートが解放されるのを確認
echo -e "${YELLOW}ポートが解放されるのを待機中...${NC}"
sleep 2

# Puppeteer Fetcherを起動
start_puppeteer_fetcher() {
    echo -e "${YELLOW}Puppeteer Fetcherを起動しています...${NC}"
    log_message "Puppeteer Fetcher起動開始"

    # Puppeteer Fetcherを起動（同期実行）
    ./start_puppeteer_fetcher.sh
    local EXIT_CODE=$?

    if [ $EXIT_CODE -ne 0 ]; then
        echo -e "${RED}❌ Puppeteer Fetcherの起動スクリプトが失敗しました${NC}"
        log_message "エラー: Puppeteer Fetcher起動スクリプト失敗"
        return 1
    fi

    # Puppeteer Fetcherの起動確認
    local MAX_ATTEMPTS=5
    local ATTEMPT=1

    while [ $ATTEMPT -le $MAX_ATTEMPTS ]; do
        echo -e "${YELLOW}Puppeteer Fetcher起動確認 $ATTEMPT/$MAX_ATTEMPTS...${NC}"

        # ポートがリッスンしているかチェック
        if lsof -i:22223 -t &>/dev/null; then
            # 実際のPIDを取得
            PUPPETEER_PID=$(ps aux | grep "node.*puppeteer_fetcher.js" | grep -v grep | awk '{print $2}' | head -1)
            echo -e "${GREEN}✅ Puppeteer Fetcherが正常に起動しました (ポート: 22223, PID: $PUPPETEER_PID)${NC}"
            log_message "Puppeteer Fetcher起動成功 (ポート: 22223, PID: $PUPPETEER_PID)"
            return 0
        fi

        sleep 2
        ATTEMPT=$((ATTEMPT + 1))
    done

    echo -e "${RED}❌ Puppeteer Fetcherの起動確認に失敗しました${NC}"
    log_message "エラー: Puppeteer Fetcher起動確認失敗"
    return 1
}

# PySpiderコンポーネント起動確認関数
verify_component_startup() {
    local COMPONENT_NAME="$1"
    local LOG_FILE="$2"
    local PID="$3"
    local PORT="$4"
    local MAX_ATTEMPTS=10
    local ATTEMPT=1

    echo -e "${YELLOW}${COMPONENT_NAME}の起動確認中...${NC}"

    while [ $ATTEMPT -le $MAX_ATTEMPTS ]; do
        echo -e "${YELLOW}${COMPONENT_NAME}起動確認 $ATTEMPT/$MAX_ATTEMPTS...${NC}"

        # プロセスが生きているかチェック
        if ! kill -0 $PID 2>/dev/null; then
            echo -e "${RED}❌ ${COMPONENT_NAME}プロセスが終了しました${NC}"
            log_message "エラー: ${COMPONENT_NAME}プロセス終了"
            if [ -f "$LOG_FILE" ]; then
                echo -e "${RED}エラーログ:${NC}"
                tail -10 "$LOG_FILE"
            fi
            return 1
        fi

        # ポートがリッスンしているかチェック（ポートが指定されている場合）
        if [ ! -z "$PORT" ]; then
            if lsof -i:$PORT -t &>/dev/null; then
                echo -e "${GREEN}✅ ${COMPONENT_NAME}が正常に起動しました (ポート: $PORT)${NC}"
                log_message "${COMPONENT_NAME}起動成功 (ポート: $PORT)"
                return 0
            fi
        else
            # ポートチェックなしの場合、ログファイルでエラーチェック
            if [ -f "$LOG_FILE" ]; then
                # プロセッサとリザルトワーカーの場合は特別な処理
                if [ "$COMPONENT_NAME" = "プロセッサ" ]; then
                    # プロセッサの致命的エラーのみチェック（HTTPErrorは除外）
                    if grep -q "Fatal\|ImportError\|ModuleNotFoundError\|SyntaxError\|processor starting" "$LOG_FILE"; then
                        if grep -q "processor starting" "$LOG_FILE"; then
                            echo -e "${GREEN}✅ ${COMPONENT_NAME}が正常に起動しました${NC}"
                            log_message "${COMPONENT_NAME}起動成功"
                            return 0
                        elif grep -q "Fatal\|ImportError\|ModuleNotFoundError\|SyntaxError" "$LOG_FILE"; then
                            echo -e "${RED}❌ ${COMPONENT_NAME}でエラーが発生しました${NC}"
                            log_message "エラー: ${COMPONENT_NAME}でエラー発生"
                            tail -10 "$LOG_FILE"
                            return 1
                        fi
                    fi
                elif [ "$COMPONENT_NAME" = "リザルトワーカー" ]; then
                    # リザルトワーカーの起動確認
                    if grep -q "Fatal\|ImportError\|ModuleNotFoundError\|SyntaxError\|result_worker starting" "$LOG_FILE"; then
                        if grep -q "result_worker starting" "$LOG_FILE"; then
                            echo -e "${GREEN}✅ ${COMPONENT_NAME}が正常に起動しました${NC}"
                            log_message "${COMPONENT_NAME}起動成功"
                            return 0
                        elif grep -q "Fatal\|ImportError\|ModuleNotFoundError\|SyntaxError" "$LOG_FILE"; then
                            echo -e "${RED}❌ ${COMPONENT_NAME}でエラーが発生しました${NC}"
                            log_message "エラー: ${COMPONENT_NAME}でエラー発生"
                            tail -10 "$LOG_FILE"
                            return 1
                        fi
                    fi
                else
                    # その他のコンポーネントは従来通り
                    if grep -q "Error\|Fatal\|Exception" "$LOG_FILE"; then
                        echo -e "${RED}❌ ${COMPONENT_NAME}でエラーが発生しました${NC}"
                        log_message "エラー: ${COMPONENT_NAME}でエラー発生"
                        tail -10 "$LOG_FILE"
                        return 1
                    elif grep -q "listening\|started\|running" "$LOG_FILE"; then
                        echo -e "${GREEN}✅ ${COMPONENT_NAME}が正常に起動しました${NC}"
                        log_message "${COMPONENT_NAME}起動成功"
                        return 0
                    fi
                fi
            fi
        fi

        sleep 2
        ATTEMPT=$((ATTEMPT + 1))
    done

    echo -e "${YELLOW}⚠️  ${COMPONENT_NAME}の起動確認がタイムアウトしました${NC}"
    log_message "警告: ${COMPONENT_NAME}起動確認タイムアウト"
    return 0  # タイムアウトでも続行
}

# Puppeteer Fetcher起動実行
if ! start_puppeteer_fetcher; then
    echo -e "${RED}Puppeteer Fetcherの起動に失敗しました。終了します。${NC}"
    exit 1
fi

# MySQL + Redisモード用の設定
echo -e "${YELLOW}MySQL + Redisモードで起動します（DB: MySQL, MQ: Redis）${NC}"

# スケジューラを起動
echo -e "${YELLOW}スケジューラを起動しています...${NC}"
log_message "スケジューラ起動開始"
python -m pyspider.run -c mysql_redis_config.json --log-level=info --log-dir=logs scheduler > scheduler.log 2>&1 &
SCHEDULER_PID=$!

# スケジューラの起動確認
if ! verify_component_startup "スケジューラ" "scheduler.log" "$SCHEDULER_PID" "23333"; then
    echo -e "${RED}スケジューラの起動に失敗しました。終了します。${NC}"
    exit 1
fi

# フェッチャーを起動
echo -e "${YELLOW}フェッチャーを起動しています...${NC}"
log_message "フェッチャー起動開始"
python -m pyspider.run -c mysql_redis_config.json --log-level=info --log-dir=logs fetcher > fetcher.log 2>&1 &
FETCHER_PID=$!

# フェッチャーの起動確認
if ! verify_component_startup "フェッチャー" "fetcher.log" "$FETCHER_PID" "24444"; then
    echo -e "${RED}フェッチャーの起動に失敗しました。終了します。${NC}"
    exit 1
fi

# プロセッサを起動
echo -e "${YELLOW}プロセッサを起動しています...${NC}"
log_message "プロセッサ起動開始"
python -m pyspider.run -c mysql_redis_config.json --log-level=info --log-dir=logs processor > processor.log 2>&1 &
PROCESSOR_PID=$!

# プロセッサの起動確認
if ! verify_component_startup "プロセッサ" "processor.log" "$PROCESSOR_PID" ""; then
    echo -e "${RED}プロセッサの起動に失敗しました。終了します。${NC}"
    exit 1
fi

# リザルトワーカーを起動
echo -e "${YELLOW}リザルトワーカーを起動しています...${NC}"
log_message "リザルトワーカー起動開始"
python -m pyspider.run -c mysql_redis_config.json --log-level=info --log-dir=logs result-worker > result_worker.log 2>&1 &
RESULT_WORKER_PID=$!

# リザルトワーカーの起動確認
if ! verify_component_startup "リザルトワーカー" "result_worker.log" "$RESULT_WORKER_PID" ""; then
    echo -e "${RED}リザルトワーカーの起動に失敗しました。終了します。${NC}"
    exit 1
fi

# WebUIを起動（スケジューラRPCを明示的に指定）
echo -e "${YELLOW}WebUIを起動しています...${NC}"
log_message "WebUI起動開始"
python -m pyspider.run -c sqlite_redis_config.json --log-level=info --log-dir=logs webui --scheduler-rpc=http://localhost:23333/ > webui.log 2>&1 &
WEBUI_PID=$!

# WebUIの起動確認
if ! verify_component_startup "WebUI" "webui.log" "$WEBUI_PID" "5000"; then
    echo -e "${RED}WebUIの起動に失敗しました。終了します。${NC}"
    exit 1
fi

# WebUI-Nextを起動
echo -e "${YELLOW}WebUI-Next (Next.js)を起動しています...${NC}"
log_message "WebUI-Next起動開始"
cd pyspider/webui-next
npm run dev > ../../webui-next.log 2>&1 &
WEBUI_NEXT_PID=$!
cd ../..

# WebUI-Nextの起動確認
echo -e "${YELLOW}WebUI-Nextの起動を確認中...${NC}"
for i in {1..10}; do
    echo -e "${YELLOW}WebUI-Next起動確認 $i/10...${NC}"
    if netstat -tlnp 2>/dev/null | grep -q ":3000.*LISTEN"; then
        echo -e "${GREEN}✅ WebUI-Nextが正常に起動しました (ポート: 3000)${NC}"
        log_message "WebUI-Next起動成功 (ポート: 3000)"
        break
    fi
    if [ $i -eq 10 ]; then
        echo -e "${YELLOW}⚠️  WebUI-Nextの起動確認がタイムアウトしました${NC}"
        echo -e "${YELLOW}WebUI-Nextは起動中の可能性があります。ログを確認してください: webui-next.log${NC}"
        log_message "警告: WebUI-Next起動確認タイムアウト"
    fi
    sleep 1
done

# 起動完了メッセージ
echo "=================================="
echo -e "${BLUE}🎉 PySpider SQLite + Redisモード起動完了！${NC}"
echo "=================================="
log_message "PySpider SQLite + Redisモード起動完了"

# 起動状況サマリー
echo -e "${GREEN}✅ 起動済みコンポーネント:${NC}"
echo -e "${GREEN}   • SQLite TaskDB: data/pyspider_taskdb.db${NC}"
echo -e "${GREEN}   • SQLite ProjectDB: data/pyspider_projectdb.db${NC}"
echo -e "${GREEN}   • SQLite ResultDB: data/pyspider_resultdb.db${NC}"
echo -e "${GREEN}   • Redis Server: redis://localhost:6379/0${NC}"
echo -e "${GREEN}   • Puppeteer Fetcher: http://localhost:22223${NC}"
echo -e "${GREEN}   • スケジューラ: http://localhost:23333${NC}"
echo -e "${GREEN}   • フェッチャー: http://localhost:24444${NC}"
echo -e "${GREEN}   • プロセッサ: 起動中${NC}"
echo -e "${GREEN}   • リザルトワーカー: 起動中${NC}"
echo -e "${GREEN}   • WebUI: http://localhost:5000${NC}"
echo -e "${GREEN}   • WebUI-Next: http://localhost:3000${NC}"
echo ""
echo -e "${BLUE}📝 ログファイル: $LOG_FILE${NC}"
echo -e "${BLUE}📁 コンポーネントログ: scheduler.log, fetcher.log, processor.log, result_worker.log, webui.log, webui-next.log${NC}"
echo -e "${BLUE}🔧 設定ファイル: sqlite_redis_config.json${NC}"
echo ""
echo -e "${YELLOW}🔧 管理コマンド:${NC}"
echo -e "${YELLOW}   • 停止: ./stop_pyspider.sh${NC}"
echo -e "${YELLOW}   • 監視: ./monitor_puppeteer_ports.sh${NC}"
echo -e "${YELLOW}   • Redis監視: redis-cli monitor${NC}"
echo -e "${YELLOW}   • 終了: Ctrl+C${NC}"
echo "=================================="

# 改良されたシグナルハンドラの設定
cleanup_on_exit() {
    echo ""
    echo -e "${YELLOW}🛑 PySpiderを終了しています...${NC}"
    log_message "PySpider終了処理開始"

    # 各プロセスを段階的に終了
    echo -e "${YELLOW}プロセスを終了中...${NC}"

    # WebUIから順番に終了
    [ ! -z "$WEBUI_NEXT_PID" ] && kill -TERM $WEBUI_NEXT_PID 2>/dev/null
    [ ! -z "$WEBUI_PID" ] && kill -TERM $WEBUI_PID 2>/dev/null
    [ ! -z "$RESULT_WORKER_PID" ] && kill -TERM $RESULT_WORKER_PID 2>/dev/null
    [ ! -z "$PROCESSOR_PID" ] && kill -TERM $PROCESSOR_PID 2>/dev/null
    [ ! -z "$FETCHER_PID" ] && kill -TERM $FETCHER_PID 2>/dev/null
    [ ! -z "$SCHEDULER_PID" ] && kill -TERM $SCHEDULER_PID 2>/dev/null
    [ ! -z "$PUPPETEER_PID" ] && kill -TERM $PUPPETEER_PID 2>/dev/null

    # 少し待機
    sleep 2

    # 強制終了が必要な場合
    [ ! -z "$WEBUI_NEXT_PID" ] && kill -9 $WEBUI_NEXT_PID 2>/dev/null
    [ ! -z "$WEBUI_PID" ] && kill -9 $WEBUI_PID 2>/dev/null
    [ ! -z "$RESULT_WORKER_PID" ] && kill -9 $RESULT_WORKER_PID 2>/dev/null
    [ ! -z "$PROCESSOR_PID" ] && kill -9 $PROCESSOR_PID 2>/dev/null
    [ ! -z "$FETCHER_PID" ] && kill -9 $FETCHER_PID 2>/dev/null
    [ ! -z "$SCHEDULER_PID" ] && kill -9 $SCHEDULER_PID 2>/dev/null
    [ ! -z "$PUPPETEER_PID" ] && kill -9 $PUPPETEER_PID 2>/dev/null

    echo -e "${GREEN}✅ PySpiderを正常に終了しました${NC}"
    echo -e "${BLUE}💡 Redisサーバーは継続して実行中です${NC}"
    echo -e "${BLUE}💡 SQLiteデータベースファイルは保持されます${NC}"
    log_message "PySpider終了処理完了"
    exit 0
}

trap cleanup_on_exit INT TERM

# メインプロセスを維持（WebUIプロセスを監視）
echo -e "${GREEN}🚀 PySpiderが正常に動作中です...${NC}"
wait $WEBUI_PID
