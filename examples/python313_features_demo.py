#!/usr/bin/env python3
# -*- encoding: utf-8 -*-
"""
Python 3.13 Features Demo for pyspiderNX2

This script demonstrates the enhanced type system and error handling
features implemented for Python 3.13 compatibility.
"""

import sys
import time
import asyncio
from typing import List, Dict, Any

# Import pyspiderNX2 enhanced components
from pyspider.libs.type_system import (
    TaskDict, ResponseDict, ProjectDict, 
    create_task, is_task_dict, is_valid_status,
    HandlerFactory, TypedConfig, BaseHandler
)
from pyspider.libs.enhanced_errors import (
    with_error_context, error_context, Py<PERSON>piderError,
    error_reporter, EnhancedTracebackFormatter
)
from pyspider.libs.python313_optimizations import (
    optimizer, create_optimized_thread_pool, 
    is_free_threaded, get_optimal_worker_count
)

def demo_type_system():
    """Demonstrate enhanced type system features"""
    print("=" * 60)
    print("Python 3.13 Type System Demo")
    print("=" * 60)
    
    # 1. TypedDict usage
    print("\n1. Creating typed task dictionary:")
    task = create_task(
        taskid="demo_001",
        project="demo_project", 
        url="https://example.com",
        priority=8,
        retries=2
    )
    print(f"Task: {task}")
    print(f"Is valid task dict: {is_task_dict(task)}")
    print(f"Task status is valid: {is_valid_status(task['status'])}")
    
    # 2. Type guards in action
    print("\n2. Type guard validation:")
    invalid_task = {"invalid": "data"}
    print(f"Invalid task dict check: {is_task_dict(invalid_task)}")
    
    # 3. Generic factory pattern
    print("\n3. Generic factory pattern:")
    handler_factory = HandlerFactory(BaseHandler)
    handler1 = handler_factory.create("handler1", {"config": "value1"})
    handler2 = handler_factory.create("handler2", {"config": "value2"})
    print(f"Handler1 type: {type(handler1)}")
    print(f"Handler2 type: {type(handler2)}")
    print(f"Same instance check: {handler1 is handler_factory.get('handler1')}")
    
    # 4. Type-safe configuration
    print("\n4. Type-safe configuration:")
    config = TypedConfig("default_value")
    print(f"Initial config: {config.value}")
    
    try:
        config.value = "new_string_value"
        print(f"Updated config: {config.value}")
        
        # This should raise a TypeError
        config.value = 123  # Wrong type
    except TypeError as e:
        print(f"Type error caught: {e}")

@with_error_context
def demo_error_handling():
    """Demonstrate enhanced error handling"""
    print("\n" + "=" * 60)
    print("Python 3.13 Error Handling Demo")
    print("=" * 60)
    
    # 1. Basic error context
    print("\n1. Error context manager:")
    try:
        with error_context("demo_operation"):
            # Simulate an error
            raise ValueError("This is a demo error")
    except Exception as e:
        error_id = error_reporter.report_error(e, "demo_context")
        print(f"Error reported with ID: {error_id}")
    
    # 2. Enhanced traceback formatting
    print("\n2. Enhanced traceback formatting:")
    try:
        def nested_function():
            local_var = "important_data"
            another_var = [1, 2, 3, 4, 5]
            raise RuntimeError("Nested error with local context")
        
        nested_function()
    except Exception as e:
        enhanced_tb = EnhancedTracebackFormatter.format_exception_with_context(e)
        print("Enhanced traceback:")
        print(enhanced_tb[:500] + "..." if len(enhanced_tb) > 500 else enhanced_tb)
    
    # 3. Error summary
    print("\n3. Error summary:")
    summary = error_reporter.get_error_summary()
    print(f"Total errors: {summary['total_errors']}")
    print(f"Error types: {summary['error_types']}")

def demo_python313_optimizations():
    """Demonstrate Python 3.13 optimizations"""
    print("\n" + "=" * 60)
    print("Python 3.13 Optimizations Demo")
    print("=" * 60)
    
    # 1. Performance information
    print("\n1. Performance information:")
    perf_info = optimizer.get_performance_info()
    for key, value in perf_info.items():
        print(f"  {key}: {value}")
    
    # 2. Free-threaded mode check
    print(f"\n2. Free-threaded mode: {is_free_threaded()}")
    print(f"   Optimal worker count: {get_optimal_worker_count()}")
    
    # 3. Optimized thread pool
    print("\n3. Optimized thread pool demo:")
    with create_optimized_thread_pool(max_workers=4) as executor:
        def sample_task(n):
            time.sleep(0.1)
            return f"Task {n} completed"
        
        # Submit tasks
        futures = [executor.submit(sample_task, i) for i in range(5)]
        
        # Collect results
        results = [future.result() for future in futures]
        print(f"   Completed tasks: {len(results)}")
        for result in results:
            print(f"   - {result}")

@with_error_context
def demo_protocol_usage():
    """Demonstrate Protocol usage"""
    print("\n" + "=" * 60)
    print("Protocol Usage Demo")
    print("=" * 60)
    
    from pyspider.libs.type_system import Fetchable, Processable, Storable
    
    # Example implementation of protocols
    class MockFetcher:
        async def fetch(self, task: TaskDict) -> ResponseDict:
            return {
                'status_code': 200,
                'url': task['url'],
                'orig_url': task['url'],
                'headers': {},
                'content': b'Mock content',
                'text': 'Mock content',
                'encoding': 'utf-8',
                'cookies': {},
                'time': 0.1,
                'save': task.get('save', {})
            }
    
    class MockProcessor:
        def process(self, task: TaskDict, response: ResponseDict) -> Dict[str, Any]:
            return {
                'url': response['url'],
                'title': 'Mock Title',
                'content_length': len(response['content'])
            }
    
    # Check protocol compliance
    fetcher = MockFetcher()
    processor = MockProcessor()
    
    print(f"1. MockFetcher implements Fetchable: {isinstance(fetcher, Fetchable)}")
    print(f"2. MockProcessor implements Processable: {isinstance(processor, Processable)}")
    
    # Demonstrate usage
    print("\n3. Protocol usage example:")
    task = create_task("proto_001", "proto_project", "https://example.com")
    
    async def demo_async():
        response = await fetcher.fetch(task)
        result = processor.process(task, response)
        print(f"   Processed result: {result}")
    
    # Run async demo
    try:
        asyncio.run(demo_async())
    except Exception as e:
        print(f"   Async demo error: {e}")

def main():
    """Main demo function"""
    print("Python 3.13 Enhanced Features Demo for pyspiderNX2")
    print(f"Python version: {sys.version}")
    print(f"Running on Python 3.13+: {sys.version_info >= (3, 13)}")
    
    try:
        # Run all demos
        demo_type_system()
        demo_error_handling()
        demo_python313_optimizations()
        demo_protocol_usage()
        
        print("\n" + "=" * 60)
        print("Demo completed successfully!")
        print("=" * 60)
        
    except Exception as e:
        error_id = error_reporter.report_error(e, "main_demo")
        print(f"\nDemo failed with error ID: {error_id}")
        
        # Show enhanced error information
        enhanced_tb = EnhancedTracebackFormatter.format_exception_with_context(e)
        print("Enhanced error details:")
        print(enhanced_tb)

if __name__ == "__main__":
    main()
