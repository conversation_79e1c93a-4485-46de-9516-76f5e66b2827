#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
import json
# 相対パス対応
script_dir = os.path.dirname(os.path.abspath(__file__))
project_root = script_dir  # このファイルはプロジェクトルートにある
sys.path.insert(0, project_root)
os.chdir(project_root)

def test_database_mode(mode):
    """指定されたデータベースモードをテストする"""
    print(f'\n=== {mode.upper()} モード - 完全テスト ===')
    
    # config.jsonを更新
    with open('config.json', 'r') as f:
        config_data = json.load(f)
    
    config_data['usedatabase'] = mode
    
    with open('config.json', 'w') as f:
        json.dump(config_data, f, indent=4)
    
    try:
        # モジュールのキャッシュをクリア
        if 'pyspider.config.unified_config' in sys.modules:
            del sys.modules['pyspider.config.unified_config']
        
        from pyspider.config.unified_config import get_config
        from pyspider.database import connect_database
        
        config = get_config()
        print(f'✅ 設定読み込み成功: usedatabase = {config.get("usedatabase")}')
        print(f'   projectdb URL: {config.get("projectdb")}')
        
        projectdb = connect_database(config.get('projectdb'))
        print(f'✅ データベース接続成功: {type(projectdb)}')
        
        projects = list(projectdb.get_all())
        print(f'✅ プロジェクト取得成功: {len(projects)}件')
        
        if projects:
            print('   既存プロジェクト:')
            for project in projects[:5]:  # 最初の5件のみ表示
                print(f'     - {project.get("name")} ({project.get("status")}) - Group: {project.get("group")}')
            if len(projects) > 5:
                print(f'     ... 他 {len(projects) - 5} 件')
        else:
            print('   プロジェクトが存在しません')
            
        # 新しいテストプロジェクトを作成
        test_project = {
            'name': f'{mode}_final_test',
            'group': f'{mode}_test_group',
            'status': 'RUNNING',
            'script': f'print("{mode} final test")',
            'comments': f'{mode} mode final test project',
            'rate': 1,
            'burst': 10,
            'updatetime': 1234567890
        }
        
        try:
            projectdb.insert(test_project['name'], test_project)
            print(f'✅ 新しいテストプロジェクトを作成: {test_project["name"]}')
            
            # 作成したプロジェクトを取得
            created_project = projectdb.get(test_project['name'])
            if created_project:
                print(f'✅ 作成したプロジェクトの取得成功: {created_project["name"]}')
            else:
                print('❌ 作成したプロジェクトの取得失敗')
                
        except Exception as e:
            print(f'⚠️ プロジェクト作成エラー（既存の可能性）: {e}')
        
        return True
        
    except Exception as e:
        print(f'❌ エラー: {e}')
        import traceback
        traceback.print_exc()
        return False

def main():
    """全データベースモードのテストを実行"""
    print('=== 全データベースモード完全テスト ===')
    
    modes = ['sqlite', 'mysql', 'postgresql']
    results = {}
    
    for mode in modes:
        results[mode] = test_database_mode(mode)
    
    print('\n=== テスト結果サマリー ===')
    for mode, success in results.items():
        status = '✅ 成功' if success else '❌ 失敗'
        print(f'{mode.upper()}: {status}')
    
    # 成功したモード数
    success_count = sum(results.values())
    print(f'\n成功: {success_count}/{len(modes)} モード')
    
    if success_count == len(modes):
        print('🎉 全データベースモードのテストが成功しました！')
    else:
        print('⚠️ 一部のデータベースモードでエラーが発生しました。')

if __name__ == '__main__':
    main()
