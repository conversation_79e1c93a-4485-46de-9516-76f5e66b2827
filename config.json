{"taskdb": "sqlite+taskdb:///data/pyspider_taskdb.db", "projectdb": "sqlite+projectdb:///data/pyspider_projectdb.db", "resultdb": "sqlite+resultdb:///data/pyspider_resultdb.db", "message_queue": "redis://localhost:6379/0", "redis_fallback": {"enabled": true, "auto_fallback": true, "check_interval": 30, "max_connection_attempts": 3, "fallback_mode": "xmlrpc", "comment": "Redis接続失敗時にXMLRPCモードに自動フォールバック"}, "webui": {"port": 5000, "username": "admin", "password": "PySpider2024!SecurePass#", "need-auth": true, "scheduler-rpc": "http://localhost:23333/", "csrf_protection": false, "session_timeout": 3600}, "fetcher": {"xmlrpc-port": 24444, "puppeteer-endpoint": "http://localhost:22223", "timeout": 30, "poolsize": 100}, "scheduler": {"xmlrpc-port": 23333, "inqueue_limit": 5000, "delete_time": 86400, "active_tasks": 100, "loop_limit": 1000, "fail_pause_num": 5}, "processor": {"process-time-limit": 30}, "result_worker": {"type": "file_output", "file_output": {"enabled": true, "output_dir": "results", "max_file_size": 104857600, "enable_rotation": true, "rotation_count": 10}}, "rate_limit": {"global": {"requests_per_minute": 60}, "per_domain": {"example.com": {"requests_per_minute": 10}, "httpbin.org": {"requests_per_minute": 5}, "gnavi.co.jp": {"requests_per_minute": 5}, "amazon.co.jp": {"requests_per_minute": 2}}}, "logging": {"level": "INFO", "dir": "logs", "max_size": "100MB", "backup_count": 5, "enable_json": true, "enable_security_audit": true, "enable_performance_monitoring": true, "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s"}, "security": {"allowed_ips": ["127.0.0.1", "::1", "***********/16", "10.0.0.0/8", "**********/12"], "csrf_secret_key": null, "session_secret_key": "a42ee5c505b5906140a290d832bdf57daa5280e48c93041ead33cb2da6414609"}, "performance": {"memory_optimization": true, "connection_pool_optimization": true, "database_optimization": true, "query_cache": true}, "usedatabase": "mysql"}