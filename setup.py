#!/usr/bin/env python
from setuptools import setup, find_packages
from setuptools.command.install import install
from setuptools.command.build_py import build_py
import os
import sys
import subprocess
import shutil
from pathlib import Path

# 現在のディレクトリをPYTHONPATHに追加
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))


class CustomBuildPy(build_py):
    """カスタムビルドコマンド - ルートディレクトリのnpmファイルをコピー"""
    def run(self):
        build_py.run(self)

        # ルートディレクトリのnpmファイルをpyspiderディレクトリにコピー
        root_files = ['package.json', 'package-lock.json', 'next.config.js', 'jsconfig.json', 'yarn.lock']
        source_dir = Path(os.path.dirname(__file__))
        target_dir = Path(self.build_lib) / 'pyspider' / 'root_npm'

        # ターゲットディレクトリを作成
        target_dir.mkdir(parents=True, exist_ok=True)

        for file_name in root_files:
            source_file = source_dir / file_name
            if source_file.exists():
                target_file = target_dir / file_name
                shutil.copy2(source_file, target_file)
                print(f"📦 Copied {file_name} to package")


class PostInstallCommand(install):
    """Post-installation for installation mode."""
    def run(self):
        install.run(self)
        # webui-nextセットアップスクリプトを実行
        try:
            print("\n🚀 webui-nextセットアップを開始...")
            script_path = os.path.join(os.path.dirname(__file__), 'scripts', 'setup_webui_next.py')
            if os.path.exists(script_path):
                subprocess.run([sys.executable, script_path], check=False)
            else:
                print("⚠️  webui-nextセットアップスクリプトが見つかりません")
                print("手動でセットアップしてください: python -m pyspider.tools.setup_webui_next")
        except Exception as e:
            print(f"⚠️  webui-nextセットアップでエラーが発生しました: {e}")
            print("手動でセットアップしてください: python -m pyspider.tools.setup_webui_next")


if __name__ == "__main__":
    setup(
        # pyproject.tomlで定義されている設定を使用
        # 追加の設定が必要な場合はここに記述
        include_package_data=True,
        zip_safe=False,
        cmdclass={
            'build_py': CustomBuildPy,
            'install': PostInstallCommand,
        },
    )
