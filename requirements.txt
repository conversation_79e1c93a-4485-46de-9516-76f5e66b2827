# Core dependencies
Flask>=3.0.2
Jinja2>=3.1.3
Werkzeug>=3.0.0
tornado>=6.4.0
# six removed for Python 3.13 compatibility
requests>=2.31.0
# urllib3 version compatible with requests 2.31.0
urllib3>=1.26.0,<3.0.0
certifi>=2024.2.2
charset-normalizer>=3.0.0
idna>=3.4
aiohttp>=3.9.0
# aiohttp dependencies
yarl>=1.9.0
multidict>=6.0.0
frozenlist>=1.4.0
aiosignal>=1.3.0
lxml>=5.1.0
pycurl>=7.45.3
PyYAML>=6.0.0
psutil>=7.0.0
# SQLAlchemy 2.0.x をサポート
SQLAlchemy>=2.0.0
pymongo>=4.6.1
# Redis 6.x をサポート
redis>=6.0.0
mysql-connector-python>=8.3.0
pika>=1.3.2
amqp>=5.2.0
kombu>=5.3.5
celery>=5.3.0
elasticsearch>=8.12.1
pillow>=10.0.0
beautifulsoup4>=4.12.0
pyquery>=2.0.0
cssselect>=1.2.0
cryptography>=41.0.0
pyOpenSSL>=23.0.0
tblib>=3.0.0
click>=8.1.7
tldextract>=3.4.0
python-dateutil>=2.8.0
u-msgpack-python>=2.8.0
attrs>=23.0.0
jsonpath-ng>=1.5.0
propcache>=0.3.0
pydantic>=2.0.0
pydantic-settings>=2.0.0
typing-extensions>=4.0.0
pandas>=2.0.0
chardet>=5.2.0
psycopg2-binary>=2.9.9
wsgidav>=4.3.0
dicttoxml>=1.7.0
python-multipart>=0.0.6
# ログ管理のための追加依存関係
python-json-logger>=2.0.0

# メトリクスとモニタリングのための依存関係
prometheus_client>=0.17.0

# Web UI dependencies
Flask-Login>=0.6.3
flask-cors>=4.0.0

# Playwright support
playwright>=1.40.0
pyee>=11.0.0

# Puppeteer support
pyppeteer>=1.0.0
websockets>=10.0.0

# Type stubs
types-requests>=2.0.0,<2.32.0.20250000
lxml-stubs>=0.4.0

# Note: greenlet is required by SQLAlchemy but we don't specify it directly
# as it has compatibility issues with Python 3.13

