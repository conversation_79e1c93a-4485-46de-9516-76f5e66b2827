#!/usr/bin/env python
# -*- encoding: utf-8 -*-
# Created on 2024-05-06 17:10:00
# Project: yahoo_keizai

from pyspider.libs.base_handler import *


class Handler(BaseHandler):
    crawl_config = {
    }

    @every(minutes=1)
    def on_start(self):
        self.crawl("https://news.yahoo.co.jp/categories/business", callback=self.index_page)

    @config(age=10 * 24 * 60 * 60)
    def index_page(self, response):
        for each in response.doc("a[href^=\"https://news.yahoo.co.jp/articles/\"]")\
                .items():
            self.crawl(each.attr.href, callback=self.detail_page)

    @config(priority=2)
    def detail_page(self, response):
        return {
            "url": response.url,
            "title": response.doc("title").text(),
            "content": response.doc(".article_body").text(),
            "time": response.doc(".articleInfo__time").text(),
        }
