pyspiderNX2
==========

An Enhanced and Modernized Version of PySpider - A Powerful Spider(Web Crawler) System in Python.

This is a fork of the original [pyspider](https://github.com/binux/pyspider) project with additional features and improvements.

- Write script in Python
- Powerful WebUI with script editor, task monitor, project manager and result viewer
- [MySQL](https://www.mysql.com/), [MongoDB](https://www.mongodb.org/), [Redis](http://redis.io/), [SQLite](https://www.sqlite.org/), [Elasticsearch](https://www.elastic.co/products/elasticsearch); [PostgreSQL](http://www.postgresql.org/) with [SQLAlchemy](http://www.sqlalchemy.org/) as database backend
- [RabbitMQ](http://www.rabbitmq.com/), [Redis](http://redis.io/) and [<PERSON>mb<PERSON>](http://kombu.readthedocs.org/) as message queue
- Task priority, retry, periodical, recrawl by age, etc...
- Distributed architecture, Crawl Javascript pages, Python 2.{6,7}, 3.{3,4,5,6} support, etc...

Tutorial: [http://docs.pyspider.org/en/latest/tutorial/](http://docs.pyspider.org/en/latest/tutorial/)
Documentation: [http://docs.pyspider.org/](http://docs.pyspider.org/)
Release notes: [https://github.com/binux/pyspider/releases](https://github.com/binux/pyspider/releases)

Sample Code
-----------

```python
from pyspider.libs.base_handler import *


class Handler(BaseHandler):
    crawl_config = {
    }

    @every(minutes=24 * 60)
    def on_start(self):
        self.crawl('http://scrapy.org/', callback=self.index_page)

    @config(age=10 * 24 * 60 * 60)
    def index_page(self, response):
        for each in response.doc('a[href^="http"]').items():
            self.crawl(each.attr.href, callback=self.detail_page)

    def detail_page(self, response):
        return {
            "url": response.url,
            "title": response.doc('title').text(),
        }
```


Installation
------------

### 📦 Quick Install (Recommended)

```bash
# Install pyspiderNX2
pip install pyspiderNX2

# Setup npm packages (requires Node.js 22+)
pyspider-setup-npm
```

### 🚀 Running

```bash
# Start pyspider
pyspider

# Start webui-next (modern React UI)
pyspider-webui-next
```

Then visit:
- **Classic WebUI**: [http://localhost:5000/](http://localhost:5000/)
- **Modern WebUI**: [http://localhost:3000/](http://localhost:3000/)

### 🔧 Advanced Setup

```bash
# MySQL + Redis mode
pyspider --config mysql_redis_config.json

# PostgreSQL mode
pyspider --config postgresql_config.json
```

For detailed installation instructions, see [INSTALL.md](INSTALL.md).

**WARNING:** WebUI is open to the public by default, it can be used to execute any command which may harm your system. Please use it in an internal network or enable `need-auth` for webui in the config.json file.

Quickstart: [http://docs.pyspider.org/en/latest/Quickstart/](http://docs.pyspider.org/en/latest/Quickstart/)

Contribute
----------

* Use It
* Open [Issue], send PR
* [User Group]
* [中文问答](http://segmentfault.com/t/pyspider)


Features Added in PySpiderNX2
------------------------

- [x] Modern Python 3.8+ support
- [x] Puppeteer integration for JavaScript rendering
- [x] Improved WebUI with metrics dashboard
- [x] Better error handling and logging
- [x] Simplified startup scripts
- [x] Configuration via JSON files
- [x] Redis mode support out of the box
- [x] Improved documentation

TODO
----

- [ ] A visual scraping interface like [portia](https://github.com/scrapinghub/portia)
- [ ] Better documentation website
- [ ] More examples and tutorials


License
-------
Licensed under the Apache License, Version 2.0


[Issue]:                https://github.com/yourusername/pyspiderNX2/issues
[User Group]:           https://groups.google.com/group/pyspider-users
