{"taskdb": "sqlalchemy+mysql+pymysql+taskdb://pyspider:PySpider2024!SecurePass#@localhost:3306/pyspider_taskdb", "projectdb": "sqlalchemy+mysql+pymysql+projectdb://pyspider:PySpider2024!SecurePass#@localhost:3306/pyspider_projectdb", "resultdb": "sqlalchemy+mysql+pymysql+resultdb://pyspider:PySpider2024!SecurePass#@localhost:3306/pyspider_resultdb", "message_queue": "redis://localhost:6379/0", "usedatabase": "mysql", "redis_fallback": {"enabled": true, "auto_fallback": true, "check_interval": 30, "max_connection_attempts": 3, "fallback_mode": "xmlrpc", "comment": "Redis接続失敗時にXMLRPCモードに自動フォールバック"}, "webui": {"username": "", "password": "", "need_auth": false, "host": "0.0.0.0", "port": 5000}, "fetcher": {"xmlrpc_host": "0.0.0.0", "xmlrpc_port": 24444}, "scheduler": {"xmlrpc_host": "0.0.0.0", "xmlrpc_port": 23333}, "puppeteer_endpoint": "http://localhost:22223"}