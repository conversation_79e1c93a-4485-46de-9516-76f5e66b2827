# pyspiderNX2 セキュリティガイド

## 概要

pyspiderNX2は、Webクローリングとスクレイピングのためのフレームワークですが、セキュリティを重視した設計になっています。このガイドでは、pyspiderNX2を安全に運用するためのセキュリティ設定と推奨事項について説明します。

## セキュリティ機能

### 1. 認証・認可

#### 1.1 基本認証
- デフォルトで認証が有効化されています
- 管理者アカウント（username: admin）が設定されています
- **重要**: デフォルトパスワードを必ず変更してください

```json
{
  "webui": {
    "username": "admin",
    "password": "your_strong_password_here",
    "need-auth": true
  }
}
```

#### 1.2 セッション管理
- セッションタイムアウトが設定されています（デフォルト: 3600秒）
- セッションキーは自動生成されます
- セッション情報は安全に暗号化されます

### 2. CSRF保護

#### 2.1 CSRF トークン
- すべてのPOST/PUT/DELETE/PATCHリクエストでCSRFトークンが必要です
- トークンは自動生成され、1時間で期限切れになります
- HTMLフォームでは `{{ csrf_token() }}` を使用してトークンを埋め込みます

#### 2.2 API保護
- APIエンドポイントは自動的にCSRF保護が適用されます
- `X-CSRF-Token` ヘッダーでトークンを送信できます

### 3. IPアクセス制限

#### 3.1 許可IPアドレス
デフォルトで以下のIPアドレスからのアクセスのみ許可されています：

- 127.0.0.1 (localhost)
- ::1 (localhost IPv6)
- ***********/16 (プライベートネットワーク)
- 10.0.0.0/8 (プライベートネットワーク)
- **********/12 (プライベートネットワーク)

#### 3.2 プロキシ対応
- X-Forwarded-For ヘッダーを考慮した実IPアドレス取得
- X-Real-IP、CF-Connecting-IP ヘッダーにも対応

### 4. 入力検証

#### 4.1 検証パターン
以下の入力に対して検証が実行されます：

- プロジェクト名: 英数字、アンダースコア、ハイフンのみ
- タスクID: 英数字、アンダースコア、ハイフン、コロンのみ
- URL: 有効なHTTP/HTTPSスキームのみ
- その他の文字列: HTMLエスケープ処理

#### 4.2 SQLインジェクション対策
- パラメータ化クエリの使用
- LIKE句での特殊文字エスケープ
- 入力値の型チェック

### 5. CORS設定

#### 5.1 制限されたオリジン
CORSは以下のオリジンのみ許可されています：

- http://localhost:3000
- http://localhost:3001
- http://127.0.0.1:3000
- http://127.0.0.1:3001

#### 5.2 許可されたメソッドとヘッダー
- メソッド: GET, POST, PUT, DELETE, OPTIONS
- ヘッダー: Content-Type, Authorization, X-CSRF-Token

## セキュリティ設定

### 1. 推奨設定

#### 1.1 強力なパスワード
```json
{
  "webui": {
    "password": "複雑で長いパスワードを設定してください"
  }
}
```

#### 1.2 セッションタイムアウト
```json
{
  "webui": {
    "session_timeout": 1800  // 30分
  }
}
```

#### 1.3 IPアクセス制限のカスタマイズ
```json
{
  "security": {
    "allowed_ips": [
      "***********/24",  // 特定のサブネットのみ
      "**********"       // 特定のIPアドレスのみ
    ]
  }
}
```

### 2. 本番環境での設定

#### 2.1 HTTPS の使用
本番環境では必ずHTTPSを使用してください：

```bash
# リバースプロキシ（Nginx）の設定例
server {
    listen 443 ssl;
    server_name your-domain.com;
    
    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;
    
    location / {
        proxy_pass http://localhost:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

#### 2.2 ファイアウォール設定
```bash
# UFWの設定例
sudo ufw allow ssh
sudo ufw allow 443/tcp
sudo ufw deny 5000/tcp  # 直接アクセスを禁止
sudo ufw enable
```

## セキュリティ監査

### 1. ログ監視

#### 1.1 セキュリティ監査ログ
セキュリティ関連のイベントは `logs/security_audit.log` に記録されます：

- 認証失敗
- 不正なアクセス試行
- CSRF攻撃の検出
- 入力検証エラー

#### 1.2 ログの確認
```bash
# セキュリティイベントの確認
tail -f logs/security_audit.log

# 不正アクセスの検索
grep "unauthorized" logs/security_audit.log
```

### 2. 定期的なセキュリティチェック

#### 2.1 パスワード強度の確認
- 定期的にパスワードを変更する
- 複雑なパスワードを使用する
- パスワード管理ツールの使用を推奨

#### 2.2 アクセスログの確認
- 異常なアクセスパターンの監視
- 未知のIPアドレスからのアクセス確認
- 大量のリクエストの検出

## トラブルシューティング

### 1. 認証問題

#### 1.1 ログインできない場合
```bash
# 設定ファイルの確認
cat config.json | grep -A 5 "webui"

# ログの確認
grep "authentication" logs/pyspider.log
```

#### 1.2 CSRF エラーが発生する場合
- ブラウザのキャッシュをクリア
- セッションを再開始
- CSRFトークンの有効期限を確認

### 2. アクセス制限問題

#### 2.1 IPアクセス拒否される場合
```bash
# 現在のIPアドレスを確認
curl ifconfig.me

# 設定ファイルでIPアドレスを追加
```

## セキュリティベストプラクティス

### 1. 運用時の注意事項

1. **定期的な更新**: pyspiderNX2を最新バージョンに保つ
2. **最小権限の原則**: 必要最小限の権限のみ付与
3. **ネットワーク分離**: 本番環境では適切なネットワーク分離を実施
4. **バックアップ**: 設定ファイルとデータベースの定期バックアップ
5. **監視**: セキュリティイベントの継続的な監視

### 2. 開発時の注意事項

1. **テスト環境の分離**: 本番データを開発環境で使用しない
2. **認証情報の管理**: 認証情報をコードに埋め込まない
3. **入力検証**: すべてのユーザー入力を検証する
4. **エラーハンドリング**: 適切なエラーメッセージの表示

## 緊急時の対応

### 1. セキュリティインシデント発生時

1. **即座の対応**: 疑わしいアクセスを即座に遮断
2. **ログの保全**: 関連するログファイルを保存
3. **影響範囲の調査**: 被害の範囲を特定
4. **復旧作業**: 安全な状態への復旧
5. **再発防止**: セキュリティ設定の見直し

### 2. 緊急連絡先

セキュリティインシデントが発生した場合は、システム管理者に即座に連絡してください。
