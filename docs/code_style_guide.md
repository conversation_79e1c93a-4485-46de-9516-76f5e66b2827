# pyspiderNX2 コードスタイルガイド

このドキュメントは、pyspiderNX2プロジェクトのコードスタイルガイドラインを定義します。一貫性のあるコードベースを維持し、技術的負債を減らすために、すべての貢献者はこのガイドラインに従うことが推奨されます。

## 1. 一般的なガイドライン

### 1.1 PEP 8に従う

基本的に、[PEP 8](https://www.python.org/dev/peps/pep-0008/)のPythonコードスタイルガイドに従ってください。

### 1.2 行の長さ

- 最大行長は100文字とします。
- 長い行は適切に折り返してください。

### 1.3 インデント

- インデントには4つのスペースを使用します。
- タブは使用しないでください。

### 1.4 空行

- トップレベルの関数とクラスの定義は2行の空行で区切ります。
- クラス内のメソッド定義は1行の空行で区切ります。
- 関連する機能のグループを区切るために、追加の空行を使用することができます。

### 1.5 インポート

- インポートは以下の順序でグループ化します：
  1. 標準ライブラリのインポート
  2. 関連するサードパーティのインポート
  3. ローカルアプリケーション/ライブラリ固有のインポート
- 各グループ内では、アルファベット順にインポートします。
- 各グループの間には空行を入れます。

```python
# 標準ライブラリ
import os
import sys
import time

# サードパーティライブラリ
import flask
import requests

# ローカルアプリケーション
from pyspider.libs.base_handler import BaseHandler
```

## 2. 命名規則

### 2.1 変数名

- 変数名は小文字のスネークケース（`snake_case`）を使用します。
- 意味のある名前を使用し、単一文字の変数名は避けてください（ループカウンタなどの一般的な用途を除く）。

```python
# 良い例
user_name = "John"
item_count = 10

# 悪い例
u = "John"
ic = 10
```

### 2.2 関数名とメソッド名

- 関数名とメソッド名は小文字のスネークケース（`snake_case`）を使用します。

```python
def calculate_total_price(items):
    pass

def get_user_by_id(user_id):
    pass
```

### 2.3 クラス名

- クラス名はキャメルケース（`CamelCase`）を使用します。

```python
class UserAccount:
    pass

class TaskManager:
    pass
```

### 2.4 定数

- 定数は大文字のスネークケース（`SNAKE_CASE`）を使用します。

```python
MAX_RETRY_COUNT = 5
DEFAULT_TIMEOUT = 30
```

## 3. コメントとドキュメンテーション

### 3.1 ドキュメント文字列（Docstrings）

- すべてのモジュール、クラス、関数、メソッドにドキュメント文字列を記述してください。
- Google形式のドキュメント文字列を使用してください。

```python
def get_task_by_id(task_id):
    """タスクIDに基づいてタスクを取得します。
    
    Args:
        task_id (str): 取得するタスクのID
        
    Returns:
        dict: タスク情報を含む辞書
        
    Raises:
        ValueError: タスクIDが無効な場合
        TaskNotFoundError: タスクが見つからない場合
    """
    pass
```

### 3.2 インラインコメント

- 複雑なロジックや非自明なコードにはインラインコメントを追加してください。
- コメントは完全な文で記述し、最初の単語は大文字で始めてください。

```python
# ユーザーの権限を確認し、アクセスを許可するかどうかを決定する
if user.has_permission('admin') or (user.has_permission('editor') and user.is_owner(resource)):
    allow_access = True
```

## 4. エラーハンドリング

### 4.1 例外処理

- 具体的な例外をキャッチし、裸の`except:`ステートメントは避けてください。
- 例外をキャッチする場合は、適切なエラーメッセージをログに記録してください。
- ユーザーに表示するエラーメッセージは、技術的な詳細を含まない、理解しやすいものにしてください。

```python
try:
    result = process_data(data)
except ValueError as e:
    logger.error("データ処理中にValueErrorが発生しました: %s", e)
    return json_response({'error': 'データ形式が無効です'}, 400)
except IOError as e:
    logger.error("データ処理中にIOErrorが発生しました: %s", e)
    return json_response({'error': 'サーバー内部エラーが発生しました'}, 500)
```

### 4.2 共通のエラーハンドリング関数

- 共通のエラーハンドリングパターンには、ヘルパー関数を使用してください。
- エラー情報は構造化された形式で返してください。

```python
def handle_api_error(e, context="API", status_code=500):
    """共通のエラーハンドリング関数
    
    Args:
        e: 例外オブジェクト
        context: エラーが発生したコンテキスト（API名など）
        status_code: HTTPステータスコード
        
    Returns:
        JSON形式のエラーレスポンス
    """
    error_info = {
        'error': str(e),
        'type': e.__class__.__name__,
        'context': context,
        'timestamp': time.time()
    }
    
    logger.error('%s error: %s', context, e)
    logger.error(traceback.format_exc())
    
    return json_response(error_info, status_code)
```

## 5. パフォーマンスとリソース管理

### 5.1 リソースの解放

- ファイルやデータベース接続などのリソースを使用する場合は、`with`ステートメントを使用するか、明示的に解放してください。

```python
# 良い例
with open('file.txt', 'r') as f:
    data = f.read()

# または
try:
    f = open('file.txt', 'r')
    data = f.read()
finally:
    f.close()
```

### 5.2 大きなデータセットの処理

- 大きなデータセットを処理する場合は、ジェネレータやイテレータを使用してメモリ使用量を最小限に抑えてください。

```python
# 良い例
def process_large_file(filename):
    with open(filename, 'r') as f:
        for line in f:
            yield process_line(line)

# 悪い例
def process_large_file_bad(filename):
    with open(filename, 'r') as f:
        lines = f.readlines()  # すべての行をメモリに読み込む
    return [process_line(line) for line in lines]
```

## 6. テスト

### 6.1 テストの命名

- テスト関数名は`test_`で始め、テスト対象の機能と期待される動作を明確に示す名前にしてください。

```python
def test_get_user_returns_correct_user_for_valid_id():
    pass

def test_get_user_raises_error_for_invalid_id():
    pass
```

### 6.2 テストの構造

- 各テストは、準備（Arrange）、実行（Act）、検証（Assert）の3つのフェーズに分けてください。

```python
def test_calculate_total_price_with_discount():
    # 準備
    items = [{'price': 100, 'quantity': 2}, {'price': 50, 'quantity': 1}]
    discount = 0.1
    
    # 実行
    total = calculate_total_price(items, discount)
    
    # 検証
    assert total == 225  # (100 * 2 + 50) * 0.9
```

## 7. コード品質ツール

### 7.1 Flake8

- コードをコミットする前に、Flake8を実行してPEP 8違反がないことを確認してください。

```bash
flake8 pyspider
```

### 7.2 Pylint

- 定期的にPylintを実行して、コードの品質を評価してください。

```bash
pylint pyspider
```

### 7.3 Black

- コードの書式を自動的に整えるために、Blackを使用することを検討してください。

```bash
black pyspider
```

## 8. バージョン管理

### 8.1 コミットメッセージ

- コミットメッセージは明確で簡潔にし、変更内容を適切に説明してください。
- 以下の形式を使用してください：
  ```
  [コンポーネント] 変更の簡潔な説明
  
  変更の詳細な説明（必要な場合）
  ```

例：
```
[API] タスク詳細エンドポイントのエラーハンドリングを改善

- 共通のエラーハンドリング関数を使用するように変更
- 特定の例外タイプに応じた処理を追加
- エラーメッセージの形式を統一
```

### 8.2 ブランチ戦略

- 機能開発には`feature/`プレフィックスを持つブランチを使用してください。
- バグ修正には`bugfix/`プレフィックスを持つブランチを使用してください。
- リリース準備には`release/`プレフィックスを持つブランチを使用してください。

例：
```
feature/add-project-group-api
bugfix/fix-task-rerun-error
release/v1.2.0
```

## 9. セキュリティ

### 9.1 入力検証

- すべてのユーザー入力を検証し、安全でない入力を拒否してください。
- SQLインジェクションやXSSなどの一般的な攻撃ベクトルに対する防御を実装してください。

### 9.2 機密情報

- パスワード、APIキー、その他の機密情報をコードにハードコーディングしないでください。
- 環境変数や設定ファイルを使用して、機密情報を管理してください。

## 10. ドキュメント

### 10.1 README

- プロジェクトのREADMEファイルには、以下の情報を含めてください：
  - プロジェクトの概要
  - インストール手順
  - 基本的な使用方法
  - 貢献方法
  - ライセンス情報

### 10.2 変更履歴

- すべての重要な変更を`CHANGELOG.md`ファイルに記録してください。
- セマンティックバージョニングに従って、バージョン番号を付けてください。
