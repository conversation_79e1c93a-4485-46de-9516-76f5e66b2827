# pyspiderNX2 ユーザーマニュアル

## 1. はじめに

pyspiderNX2は、Webクローリングとスクレイピングのためのオープンソースフレームワークです。このマニュアルでは、pyspiderNX2の基本的な使い方を説明します。

### 1.1 pyspiderNX2とは

pyspiderNX2は、元のpyspiderプロジェクトを拡張し、最新のライブラリとの互換性を確保し、新しい機能を追加したものです。主な特徴は以下の通りです：

- 分散アーキテクチャ（スケジューラ、フェッチャー、プロセッサ、リザルトワーカー）
- Webベースの管理インターフェース
- 強力なスクリプトエディタとデバッガ
- 柔軟なスケジューリング
- 結果の保存と表示
- RESTful API
- モダンなWebUI（Next.js製）

### 1.2 システム要件

- Python 3.7以上
- Redis 6.0.0以上
- Node.js 14.0.0以上（webui-next用）
- 十分なディスク容量（結果の保存用）
- メモリ：最低2GB（推奨4GB以上）

## 2. インストール

### 2.1 前提条件

以下のソフトウェアがインストールされていることを確認してください：

- Python 3.7以上
- pip（Pythonパッケージマネージャ）
- Redis 6.0.0以上
- Node.js 14.0.0以上とnpm

### 2.2 インストール手順

```bash
# リポジトリのクローン
git clone https://github.com/your-username/pyspiderNX2.git
cd pyspiderNX2

# 依存関係のインストール
pip install -r requirements.txt

# webui-nextのインストール
cd pyspider/webui-next
npm install
```

## 3. 基本的な使い方

### 3.1 起動方法

pyspiderNX2は、Redisモードで起動することをお勧めします。

```bash
# Redisモードで起動
./start_redis_mode.sh

# webui-nextを起動
cd pyspider/webui-next
npm run dev
```

これにより、以下のサービスが起動します：

- スケジューラ（ポート23333）
- フェッチャー（ポート24444）
- プロセッサ
- リザルトワーカー
- WebUI（ポート5000）
- WebUI-Next（ポート3000）

### 3.2 WebUIへのアクセス

ブラウザで以下のURLにアクセスします：

- 従来のWebUI: http://localhost:5000
- 新しいWebUI-Next: http://localhost:3000

WebUI-Nextは、より使いやすく、モダンなインターフェースを提供します。

### 3.3 プロジェクトの作成

1. WebUIにアクセス: http://localhost:3000
2. 「新規プロジェクト」ボタンをクリック
3. プロジェクト名と設定を入力
   - プロジェクト名は英数字とアンダースコアのみ使用可能
   - グループ名を指定（オプション）
   - ステータスを選択（RUNNING, PAUSED, STOPPED, CHECKING, TODO, DEBUG）
   - レート制限を設定（1.0がデフォルト）
   - バースト値を設定（10がデフォルト）
4. 「作成」ボタンをクリック

### 3.4 スクリプトの編集

プロジェクトを作成すると、デフォルトのスクリプトテンプレートが生成されます。このスクリプトを編集して、クローリングとスクレイピングのロジックを実装します。

```python
#!/usr/bin/env python
# -*- encoding: utf-8 -*-
# Created on {date}
# Project: {project_name}

from pyspider.libs.base_handler import *

class Handler(BaseHandler):
    crawl_config = {
    }

    @every(minutes=24 * 60)
    def on_start(self):
        self.crawl('http://example.com/', callback=self.index_page)

    @config(age=10 * 24 * 60 * 60)
    def index_page(self, response):
        for each in response.doc('a[href^="http"]').items():
            self.crawl(each.attr.href, callback=self.detail_page)

    @config(priority=2)
    def detail_page(self, response):
        return {
            "url": response.url,
            "title": response.doc('title').text(),
        }
```

スクリプトの主要なコンポーネント：

- `on_start`: プロジェクトが開始されたときに呼び出されるメソッド
- `crawl`: 新しいURLをクロールするためのメソッド
- `callback`: クロール結果を処理するメソッド
- `@every`: 定期的にメソッドを実行するデコレータ
- `@config`: メソッドの設定を指定するデコレータ

### 3.5 プロジェクトの実行

1. プロジェクト一覧ページで、実行したいプロジェクトの「実行」ボタンをクリック
2. または、プロジェクト詳細ページで「実行」ボタンをクリック

プロジェクトのステータスが「RUNNING」または「DEBUG」の場合のみ実行できます。

### 3.6 タスクの管理

タスクページでは、以下の操作が可能です：

1. タスクの一覧表示
2. タスクのステータス確認（ACTIVE, SUCCESS, FAILED）
3. タスクの再実行
4. タスクの詳細表示

### 3.7 結果の表示とエクスポート

結果ページでは、以下の操作が可能です：

1. スクレイピング結果の表示
2. 結果のフィルタリング
3. 結果のエクスポート（JSON, CSV, XML形式）

## 4. 高度な使い方

### 4.1 プロジェクトグループの管理

プロジェクトをグループ化して管理することができます：

1. プロジェクト一覧ページで、グループ列を確認
2. 新規プロジェクト作成時にグループを指定
3. プロジェクト編集でグループを変更

### 4.2 スケジューリングの設定

プロジェクトのスケジューリングを細かく設定できます：

1. レート制限：1秒あたりのリクエスト数（デフォルト: 1.0）
2. バースト値：一度に処理できるリクエスト数（デフォルト: 10）
3. `@every`デコレータ：定期的な実行間隔を指定
4. `age`パラメータ：URLの有効期限を指定

### 4.3 デバッグモード

デバッグモードでは、スクリプトの動作を詳細に確認できます：

1. プロジェクトのステータスを「DEBUG」に設定
2. デバッグページで「実行」ボタンをクリック
3. リアルタイムでクロール結果を確認
4. スクリプトを編集して再実行

### 4.4 APIの利用

pyspiderNX2は、RESTful APIを提供しています：

1. APIドキュメント: `/docs/api_v2.md`
2. APIエンドポイント: `/api/v2/`
3. APIを使用して、外部アプリケーションからpyspiderNX2を制御可能

## 5. トラブルシューティング

### 5.1 よくある問題

1. **接続エラー**
   - Redisサーバーが起動しているか確認
   - ポートが他のアプリケーションで使用されていないか確認

2. **タスクが実行されない**
   - プロジェクトのステータスが「RUNNING」または「DEBUG」になっているか確認
   - スケジューラが正常に動作しているか確認

3. **メモリ不足エラー**
   - 大量のタスクを処理する場合は、メモリ制限を増やす
   - 不要なプロジェクトやタスクを削除

### 5.2 ログの確認

問題が発生した場合は、ログファイルを確認してください：

- スケジューラログ: `scheduler.log`
- WebUIログ: `webui.log`
- フェッチャーログ: `fetcher.log`
- プロセッサログ: `processor.log`
- リザルトワーカーログ: `result_worker.log`

### 5.3 データベースの最適化

長期間使用すると、データベースのパフォーマンスが低下する場合があります。定期的に最適化を行ってください：

```bash
# データベースの最適化
./optimize_database.sh
```

## 6. 参考資料

- [APIドキュメント](/docs/api_v2.md)
- [開発者ガイド](/docs/developer_guide.md)
- [GitHub リポジトリ](https://github.com/your-username/pyspiderNX2)

## 7. よくある質問（FAQ）

**Q: pyspiderNX2は元のpyspiderと互換性がありますか？**

A: はい、基本的な機能は互換性がありますが、一部のAPIやデータベース構造が変更されています。

**Q: どのブラウザがサポートされていますか？**

A: Chrome, Firefox, Edgeなどの最新のブラウザがサポートされています。

**Q: プロジェクトの最大数に制限はありますか？**

A: 理論上の制限はありませんが、サーバーのリソース（メモリ、CPU、ディスク容量）によって実質的な制限があります。

**Q: タスク結果はどのくらいの期間保存されますか？**

A: デフォルトでは無期限に保存されますが、`resultdb`の設定で保存期間を指定できます。
