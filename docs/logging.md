# pyspiderNX2 ログ管理ガイド

このドキュメントでは、pyspiderNX2のログ管理システムについて説明します。ログ管理は、問題のデバッグや監視に重要な役割を果たします。

## 1. ログ管理の概要

pyspiderNX2のログ管理システムは、以下の機能を提供します：

1. **ログレベルの設定**：
   - DEBUG, INFO, WARNING, ERROR, CRITICALの各レベルでログを記録できます。
   - コマンドラインオプションでログレベルを指定できます。

2. **ログローテーション**：
   - ログファイルが大きくなりすぎないように、自動的にローテーションします。
   - 設定可能なサイズ制限とバックアップ数を持っています。

3. **コンポーネント別のログ**：
   - 各コンポーネント（スケジューラ、フェッチャー、プロセッサなど）ごとに個別のログファイルを生成します。
   - 問題の特定と分離が容易になります。

4. **構造化ログ**：
   - タイムスタンプ、ログレベル、コンポーネント名などの情報を含む構造化されたログを生成します。
   - ログの解析と検索が容易になります。

## 2. ログ設定

### 2.1 コマンドラインオプション

pyspiderNX2は、以下のコマンドラインオプションでログ設定を制御できます：

- `--logging-config`: ログ設定ファイルのパス（デフォルト: `pyspider/logging.conf`）
- `--log-level`: ログレベル（debug, info, warning, error, critical）（デフォルト: info）
- `--log-dir`: ログファイルを保存するディレクトリ（デフォルト: logs）
- `--debug`: デバッグモードを有効にする（ログレベルをDEBUGに設定）

例：

```bash
python -m pyspider.run --log-level=debug --log-dir=/var/log/pyspider all
```

### 2.2 設定ファイル

ログ設定ファイルは、JSON形式で記述します。以下は設定ファイルの例です：

```json
{
  "version": 1,
  "disable_existing_loggers": false,
  "formatters": {
    "standard": {
      "format": "%(asctime)s [%(levelname)s] %(name)s: %(message)s"
    }
  },
  "handlers": {
    "console": {
      "class": "logging.StreamHandler",
      "level": "INFO",
      "formatter": "standard",
      "stream": "ext://sys.stdout"
    },
    "file": {
      "class": "logging.handlers.RotatingFileHandler",
      "level": "INFO",
      "formatter": "standard",
      "filename": "logs/pyspider.log",
      "maxBytes": 10485760,
      "backupCount": 5,
      "encoding": "utf8"
    }
  },
  "loggers": {
    "": {
      "handlers": ["console", "file"],
      "level": "INFO",
      "propagate": true
    },
    "scheduler": {
      "level": "INFO",
      "handlers": ["console", "file"],
      "propagate": false
    },
    "fetcher": {
      "level": "INFO",
      "handlers": ["console", "file"],
      "propagate": false
    },
    "processor": {
      "level": "INFO",
      "handlers": ["console", "file"],
      "propagate": false
    },
    "result_worker": {
      "level": "INFO",
      "handlers": ["console", "file"],
      "propagate": false
    },
    "webui": {
      "level": "INFO",
      "handlers": ["console", "file"],
      "propagate": false
    }
  }
}
```

### 2.3 プログラムによる設定

プログラムからログを設定するには、`pyspider.libs.log`モジュールを使用します：

```python
from pyspider.libs.log import configure_logging, get_logger

# ログ設定
configure_logging(
    config_file='logging.json',
    log_level='debug',
    log_dir='logs'
)

# ロガーの取得
logger = get_logger('my_component')
logger.info('情報メッセージ')
logger.error('エラーメッセージ')
```

## 3. ログファイルの構成

pyspiderNX2は、以下のログファイルを生成します：

1. **メインログファイル**：
   - `logs/pyspider_YYYYMMDD.log`: 全体のログ

2. **コンポーネント別ログファイル**：
   - `logs/scheduler.log`: スケジューラのログ
   - `logs/fetcher.log`: フェッチャーのログ
   - `logs/processor.log`: プロセッサのログ
   - `logs/result_worker.log`: リザルトワーカーのログ
   - `logs/webui.log`: WebUIのログ
   - `logs/database.log`: データベース操作のログ

各ログファイルは、設定に基づいてローテーションされます。デフォルトでは、ファイルサイズが10MBを超えると、最大5つのバックアップファイルが保持されます。

## 4. ログレベル

pyspiderNX2は、以下のログレベルをサポートしています：

1. **DEBUG**：
   - 詳細なデバッグ情報
   - 開発とトラブルシューティングに役立ちます

2. **INFO**：
   - 一般的な情報メッセージ
   - 通常の操作を記録します

3. **WARNING**：
   - 潜在的な問題や注意が必要な状況
   - エラーではないが、注意が必要な状況を示します

4. **ERROR**：
   - エラーや例外
   - 操作が失敗したことを示します

5. **CRITICAL**：
   - 重大なエラー
   - システムが正常に動作できない状況を示します

ログレベルは階層的であり、上位のレベルには下位のレベルも含まれます。例えば、`INFO`レベルを設定すると、`INFO`、`WARNING`、`ERROR`、`CRITICAL`のログが記録されますが、`DEBUG`ログは記録されません。

## 5. ログの解析

### 5.1 ログの検索

特定のパターンを含むログを検索するには、以下のコマンドを使用します：

```bash
grep "ERROR" logs/pyspider_*.log
```

### 5.2 ログの集計

エラーの発生回数を集計するには、以下のコマンドを使用します：

```bash
grep -c "ERROR" logs/pyspider_*.log
```

### 5.3 ログの監視

リアルタイムでログを監視するには、以下のコマンドを使用します：

```bash
tail -f logs/pyspider_*.log
```

## 6. 大規模環境でのログ管理

大規模な環境でpyspiderNX2を運用する場合は、以下の方法でログ管理を最適化できます：

### 6.1 集中ログ管理

Fluentd、Logstash、Graylogなどの集中ログ管理システムを使用して、複数のpyspiderNX2インスタンスからログを収集し、一元管理することができます。

例（Fluentdの設定）：

```
<source>
  @type tail
  path /path/to/logs/pyspider_*.log
  pos_file /var/log/td-agent/pyspider.pos
  tag pyspider
  <parse>
    @type regexp
    expression /^(?<time>\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2},\d{3}) \[(?<level>[^\]]+)\] (?<component>[^:]+): (?<message>.*)$/
    time_format %Y-%m-%d %H:%M:%S,%L
  </parse>
</source>

<match pyspider.**>
  @type elasticsearch
  host elasticsearch.example.com
  port 9200
  logstash_format true
  logstash_prefix pyspider
  <buffer>
    @type file
    path /var/log/td-agent/buffer/pyspider
    flush_interval 5s
  </buffer>
</match>
```

### 6.2 ログローテーションの最適化

大規模環境では、ログファイルのサイズと数が急速に増加する可能性があります。以下の設定を調整して、ログローテーションを最適化できます：

1. **ファイルサイズの制限**：
   - 大きなファイルサイズ（例：100MB）を設定して、ローテーションの頻度を減らします。

2. **バックアップ数の制限**：
   - バックアップファイルの数を制限して、ディスク使用量を抑えます。

3. **時間ベースのローテーション**：
   - サイズベースではなく、時間ベース（日次、週次など）のローテーションを使用します。

### 6.3 ログレベルの最適化

本番環境では、適切なログレベルを設定することが重要です：

1. **通常運用時**：
   - `INFO`レベルを使用して、重要な情報を記録しながらログサイズを抑えます。

2. **問題調査時**：
   - 一時的に`DEBUG`レベルに切り替えて、詳細な情報を収集します。
   - 問題が解決したら、`INFO`レベルに戻します。

3. **コンポーネント別の設定**：
   - 問題が発生しているコンポーネントのみ`DEBUG`レベルに設定し、他のコンポーネントは`INFO`レベルのままにします。
