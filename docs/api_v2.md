# pyspiderNX2 APIv2 ドキュメント

## 概要

pyspiderNX2のAPIv2は、pyspiderの機能にプログラム的にアクセスするためのRESTful APIです。このAPIを使用して、プロジェクトの管理、タスクの実行、結果の取得などを行うことができます。

すべてのAPIエンドポイントは `/api/v2/` プレフィックスで始まります。

## 認証

現在、APIv2は認証を必要としません。将来のバージョンでは、JWTベースの認証が追加される予定です。

## レスポンス形式

すべてのAPIレスポンスはJSON形式で返されます。成功した場合は、リクエストに応じたデータが返されます。エラーが発生した場合は、以下の形式でエラー情報が返されます：

```json
{
  "error": "エラーメッセージ"
}
```

HTTPステータスコードも適切に設定されます（200: 成功、400: リクエストエラー、404: 見つからない、500: サーバーエラーなど）。

## プロジェクト管理

### プロジェクト一覧の取得

```
GET /api/v2/projects
```

すべてのプロジェクトの一覧を取得します。

**レスポンス例**：

```json
[
  {
    "name": "test_project",
    "group": "default",
    "status": "RUNNING",
    "rate": 1.0,
    "burst": 10,
    "updatetime": 1621234567.89
  },
  ...
]
```

### プロジェクトの取得

```
GET /api/v2/projects/<project_name>
```

指定されたプロジェクトの詳細情報を取得します。

**レスポンス例**：

```json
{
  "name": "test_project",
  "group": "default",
  "status": "RUNNING",
  "script": "...",
  "rate": 1.0,
  "burst": 10,
  "updatetime": 1621234567.89
}
```

### プロジェクトの作成

```
POST /api/v2/projects
```

新しいプロジェクトを作成します。

**リクエスト本文**：

```json
{
  "name": "new_project",
  "group": "default",
  "status": "RUNNING",
  "script": "...",
  "rate": 1.0,
  "burst": 10
}
```

**レスポンス例**：

```json
{
  "status": "ok"
}
```

### プロジェクトの更新

```
PUT /api/v2/projects/<project_name>
```

既存のプロジェクトを更新します。

**リクエスト本文**：

```json
{
  "group": "new_group",
  "status": "PAUSED",
  "script": "...",
  "rate": 2.0,
  "burst": 20
}
```

**レスポンス例**：

```json
{
  "status": "ok"
}
```

### プロジェクトの削除

```
DELETE /api/v2/projects/<project_name>
```

指定されたプロジェクトを削除します。

**レスポンス例**：

```json
{
  "status": "ok"
}
```

### プロジェクトの実行

```
POST /api/v2/projects/<project_name>/run
```

指定されたプロジェクトを実行します。

**レスポンス例**：

```json
{
  "status": "ok",
  "result": "..."
}
```

## プロジェクトグループ管理

### プロジェクトグループ一覧の取得

```
GET /api/v2/project-groups
```

すべてのプロジェクトグループの一覧を取得します。

**レスポンス例**：

```json
[
  {
    "name": "default",
    "projects": ["test_project", "another_project"],
    "project_count": 2
  },
  ...
]
```

### プロジェクトグループの取得

```
GET /api/v2/project-groups/<group_name>
```

指定されたプロジェクトグループの詳細情報を取得します。

**レスポンス例**：

```json
{
  "name": "default",
  "projects": ["test_project", "another_project"],
  "project_count": 2
}
```

### プロジェクトグループの作成

```
POST /api/v2/project-groups
```

新しいプロジェクトグループを作成します。

**リクエスト本文**：

```json
{
  "name": "new_group",
  "projects": ["project1", "project2"]
}
```

**レスポンス例**：

```json
{
  "status": "ok",
  "group": "new_group",
  "updated_projects": ["project1", "project2"]
}
```

### プロジェクトグループの削除

```
DELETE /api/v2/project-groups/<group_name>
```

指定されたプロジェクトグループを削除します。

**レスポンス例**：

```json
{
  "status": "ok",
  "updated_projects": ["project1", "project2"]
}
```

## タスク管理

### タスク一覧の取得

```
GET /api/v2/tasks
```

タスクの一覧を取得します。

**クエリパラメータ**：

- `status`: タスクのステータス（`active`, `pending`, `success`, `failed`, `all`）
- `project`: プロジェクト名
- `limit`: 取得するタスクの最大数（デフォルト: 10）
- `offset`: 取得開始位置（デフォルト: 0）

**レスポンス例**：

```json
{
  "tasks": [
    {
      "taskid": "on_start",
      "project": "test_project",
      "url": "data:,on_start",
      "status": "SUCCESS",
      "updatetime": 1621234567.89,
      ...
    },
    ...
  ],
  "total": 10
}
```

### タスクの取得

```
GET /api/v2/task/<project>:<taskid>
```

指定されたタスクの詳細情報を取得します。

**レスポンス例**：

```json
{
  "taskid": "on_start",
  "project": "test_project",
  "url": "data:,on_start",
  "status": "SUCCESS",
  "status_string": "SUCCESS",
  "updatetime": 1621234567.89,
  ...
}
```

### タスクの再実行

```
POST /api/v2/task/<project>:<taskid>/rerun
```

指定されたタスクを再実行します。

**レスポンス例**：

```json
{
  "status": "ok",
  "result": "..."
}
```

### タスクの一括操作

```
POST /api/v2/tasks/bulk
```

複数のタスクに対して一括操作を行います。

**リクエスト本文**：

```json
{
  "operation": "rerun",
  "tasks": ["project1:task1", "project2:task2"]
}
```

**レスポンス例**：

```json
{
  "status": "ok",
  "operation": "rerun",
  "total": 2,
  "success_count": 2,
  "failed_count": 0,
  "results": {
    "success": [
      {
        "taskid": "project1:task1",
        "result": "..."
      },
      ...
    ],
    "failed": []
  }
}
```

## 結果管理

### プロジェクト結果の取得

```
GET /api/v2/results/<project_name>
```

指定されたプロジェクトの結果を取得します。

**クエリパラメータ**：

- `limit`: 取得する結果の最大数（デフォルト: 20）
- `offset`: 取得開始位置（デフォルト: 0）

**レスポンス例**：

```json
{
  "results": [
    {
      "taskid": "on_start",
      "url": "data:,on_start",
      "result": { ... },
      "updatetime": 1621234567.89,
      ...
    },
    ...
  ],
  "total": 10,
  "offset": 0,
  "limit": 20
}
```

### プロジェクト結果のエクスポート

```
GET /api/v2/results/<project_name>/export
```

指定されたプロジェクトの結果をエクスポートします。

**クエリパラメータ**：

- `format`: エクスポート形式（`json`, `csv`, `xml`）
- `limit`: エクスポートする結果の最大数（デフォルト: 1000）

**レスポンス**：

指定された形式でファイルがダウンロードされます。

## 統計情報

### カウンター情報の取得

```
GET /api/v2/counter
```

プロジェクトのタスクカウンター情報を取得します。

**クエリパラメータ**：

- `project`: プロジェクト名（省略可）

**レスポンス例**：

```json
{
  "test_project": {
    "5m": {
      "pending": 0,
      "success": 10,
      "retry": 0,
      "failed": 0,
      "task": 10,
      "title": "pending: 0, success: 10, retry: 0, failed: 0"
    },
    "1h": { ... },
    "1d": { ... },
    "all": { ... },
    "paused": false
  },
  ...
}
```

### システム統計情報の取得

```
GET /api/v2/stats
```

システム統計情報を取得します。

**レスポンス例**：

```json
{
  "cpu": {
    "usage": 10.5
  },
  "memory": {
    "total": 8192,
    "used": 4096,
    "percent": 50.0
  },
  "scheduler": {
    "tasks": {
      "pending": 10,
      "active": 5,
      "completed": 100
    },
    ...
  },
  "components": {
    "scheduler": {
      "status": "RUNNING",
      "updatetime": 1621234567.89
    },
    "fetcher": { ... },
    "processor": { ... },
    "result_worker": { ... }
  },
  "queue": {
    "scheduler2fetcher": 5,
    "fetcher2processor": 3,
    "processor2result": 2,
    ...
  }
}
```

### スケジューラ設定の取得と更新

```
GET /api/v2/scheduler/config
```

スケジューラの設定を取得します。

**レスポンス例**：

```json
{
  "info": {
    "version": "0.5.0",
    "mode": "redis",
    ...
  },
  "status": {
    "status": "RUNNING",
    "updatetime": 1621234567.89
  },
  "stats": {
    "tasks": {
      "pending": 10,
      "active": 5,
      "completed": 100
    },
    ...
  },
  "queue": {
    "scheduler2fetcher": 5,
    "fetcher2processor": 3,
    "processor2result": 2,
    ...
  }
}
```

```
PUT /api/v2/scheduler/config
```

スケジューラの設定を更新します。

**リクエスト本文**：

```json
{
  "max_rate": 5.0,
  "max_burst": 50,
  "concurrency": 10,
  "pause": false
}
```

**レスポンス例**：

```json
{
  "status": "ok",
  "updated": {
    "max_rate": 5.0,
    "max_burst": 50,
    "concurrency": 10,
    "pause": false
  }
}
```

## コンポーネント管理

### コンポーネントステータスの取得

```
GET /api/v2/components/status
```

すべてのコンポーネントのステータスを取得します。

**レスポンス例**：

```json
{
  "scheduler": {
    "status": "RUNNING",
    "updatetime": 1621234567.89
  },
  "fetcher": {
    "status": "RUNNING",
    "updatetime": 1621234567.89
  },
  "processor": {
    "status": "RUNNING",
    "updatetime": 1621234567.89
  },
  "result_worker": {
    "status": "RUNNING",
    "updatetime": 1621234567.89
  }
}
```
