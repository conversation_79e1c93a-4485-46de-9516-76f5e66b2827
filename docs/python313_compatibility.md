# Python 3.13 互換性ガイド

このドキュメントでは、pyspiderNX2のPython 3.13との互換性について説明します。

## 1. Python 3.13の主な変更点

Python 3.13では、以下の主な変更点があります：

1. **sixモジュールの削除**：
   - Python 2と3の互換性を提供していた`six`モジュールが標準ライブラリから削除されました。
   - pyspiderNX2では、`six`モジュールへの依存を削除し、Python 3のネイティブ機能を使用するように修正しました。

2. **datetime.UTCの追加**：
   - Python 3.13では、`datetime.UTC`が追加されました。
   - pyspiderNX2では、`datetime.UTC`が利用可能な場合はそれを使用し、そうでない場合は`datetime.timezone.utc`にフォールバックするように修正しました。

3. **xmlrpc.clientの変更**：
   - Python 3.13では、`xmlrpc.client`モジュールの動作が変更されました。
   - pyspiderNX2では、`xmlrpc.client.ServerProxy`の使用時に`use_builtin_types=True`を指定するように修正しました。

4. **その他の変更**：
   - Python 3.13では、その他にも多くの変更がありますが、pyspiderNX2のコードベースには影響しません。

## 2. 互換性対応の詳細

### 2.1 sixモジュールの削除

sixモジュールはPython 3.13で削除されたため、pyspiderNX2では以下の対応を行いました：

1. **テストコードの修正**：
   - `tests/test_run.py`と`tests/test_processor.py`から`six`のインポートを削除しました。
   - `six.moves.xmlrpc_client`の代わりに`xmlrpc.client`を使用するように修正しました。

2. **依存関係の更新**：
   - `requirements.txt`と`pyproject.toml`から`six`への依存を削除しました。
   - `types-six`への依存も削除しました。

### 2.2 datetime.UTCの対応

Python 3.13では`datetime.UTC`が追加されたため、pyspiderNX2では以下の対応を行いました：

```python
# Python 3.13 compatibility: UTC timezone
try:
    from datetime import UTC
except ImportError:
    # For older Python versions
    UTC = datetime.timezone.utc
```

この対応により、Python 3.13と古いバージョンの両方で互換性が確保されます。

### 2.3 xmlrpc.clientの対応

Python 3.13では`xmlrpc.client`の動作が変更されたため、pyspiderNX2では以下の対応を行いました：

1. **ServerProxyの使用方法の変更**：
   - `use_builtin_types=True`を指定するように修正しました。
   - これにより、Python 3.13での互換性が確保されます。

```python
from xmlrpc.client import ServerProxy
client = ServerProxy(uri, use_builtin_types=True)
```

2. **テストコードの修正**：
   - `tests/test_xmlrpc.py`を修正して、Python 3.13との互換性を確保しました。

### 2.4 互換性テストの追加

Python 3.13との互換性を確認するために、以下のテストを追加しました：

1. **`tests/test_python313_compatibility.py`**：
   - datetime.UTCの互換性テスト
   - xmlrpc.clientの互換性テスト
   - WSGIXMLRPCApplicationの互換性テスト
   - JSONの互換性テスト
   - ファイル操作の互換性テスト
   - Python 3.13固有の機能のテスト（Python 3.13でのみ実行）

## 3. Python 3.13での動作確認方法

### 3.1 Python 3.13のインストール

Python 3.13をインストールするには、以下のコマンドを実行します：

```bash
# Ubuntuの場合
sudo add-apt-repository ppa:deadsnakes/ppa
sudo apt-get update
sudo apt-get install python3.13 python3.13-dev python3.13-venv

# macOSの場合（Homebrewを使用）
brew install python@3.13
```

### 3.2 仮想環境の作成

Python 3.13の仮想環境を作成するには、以下のコマンドを実行します：

```bash
python3.13 -m venv venv-py313
source venv-py313/bin/activate
```

### 3.3 依存関係のインストール

依存関係をインストールするには、以下のコマンドを実行します：

```bash
pip install -e .
```

### 3.4 テストの実行

テストを実行するには、以下のコマンドを実行します：

```bash
# 全てのテストを実行
pytest

# Python 3.13互換性テストのみ実行
pytest tests/test_python313_compatibility.py
```

## 4. 既知の問題と制限事項

### 4.1 既知の問題

現在、Python 3.13での既知の問題はありません。

### 4.2 制限事項

1. **サードパーティライブラリの互換性**：
   - 一部のサードパーティライブラリがPython 3.13に対応していない場合があります。
   - その場合は、互換性のあるバージョンを指定するか、代替のライブラリを使用する必要があります。

2. **実験的サポート**：
   - Python 3.13のサポートは実験的なものであり、今後の更新で変更される可能性があります。
   - 本番環境では、安定版のPython 3.10または3.11を使用することをお勧めします。

## 5. 今後の対応

### 5.1 継続的な互換性の確保

Python 3.13との互換性を継続的に確保するために、以下の対応を行います：

1. **テストの自動化**：
   - CI/CDパイプラインにPython 3.13でのテストを追加します。
   - これにより、互換性の問題を早期に発見できます。

2. **依存関係の更新**：
   - サードパーティライブラリがPython 3.13に対応したら、依存関係を更新します。
   - これにより、最新の機能と修正を利用できます。

3. **ドキュメントの更新**：
   - Python 3.13での動作に関する情報を継続的に更新します。
   - これにより、ユーザーは最新の情報を得ることができます。

### 5.2 Python 3.14への対応

Python 3.14がリリースされた場合は、以下の対応を行います：

1. **互換性の確認**：
   - Python 3.14での互換性を確認します。
   - 必要に応じて、コードを修正します。

2. **テストの追加**：
   - Python 3.14での互換性テストを追加します。
   - これにより、互換性の問題を早期に発見できます。

3. **ドキュメントの更新**：
   - Python 3.14での動作に関する情報を追加します。
   - これにより、ユーザーは最新の情報を得ることができます。
