# PostgreSQLの設定方法

pyspiderNX2はPostgreSQLをサポートしており、データの永続化に使用できます。このドキュメントでは、PostgreSQLの設定方法について説明します。

## 前提条件

- PostgreSQL 9.5以上がインストールされていること
- psycopg2-binaryがインストールされていること（`pip install psycopg2-binary`）

## PostgreSQLのインストール

### Ubuntuの場合

```bash
sudo apt update
sudo apt install postgresql postgresql-contrib
```

### CentOSの場合

```bash
sudo yum install postgresql-server postgresql-contrib
sudo postgresql-setup initdb
sudo systemctl start postgresql
sudo systemctl enable postgresql
```

### macOSの場合

```bash
brew install postgresql
brew services start postgresql
```

## PostgreSQLの設定

### 1. PostgreSQLユーザーの作成

```bash
sudo -u postgres psql
```

PostgreSQLプロンプトで以下のコマンドを実行します：

```sql
CREATE USER pyspider WITH PASSWORD 'pyspider_password';
```

### 2. データベースの作成

PostgreSQLプロンプトで以下のコマンドを実行します：

```sql
CREATE DATABASE pyspider_taskdb OWNER pyspider;
CREATE DATABASE pyspider_projectdb OWNER pyspider;
CREATE DATABASE pyspider_resultdb OWNER pyspider;
```

### 3. 権限の設定

PostgreSQLプロンプトで以下のコマンドを実行します：

```sql
GRANT ALL PRIVILEGES ON DATABASE pyspider_taskdb TO pyspider;
GRANT ALL PRIVILEGES ON DATABASE pyspider_projectdb TO pyspider;
GRANT ALL PRIVILEGES ON DATABASE pyspider_resultdb TO pyspider;
```

## pyspiderNX2の設定

### 1. 設定ファイルの作成

`postgresql_config.json`ファイルを作成し、以下の内容を記述します：

```json
{
  "taskdb": "sqlalchemy+postgresql+taskdb://pyspider:pyspider_password@localhost:5432/pyspider_taskdb",
  "projectdb": "sqlalchemy+postgresql+projectdb://pyspider:pyspider_password@localhost:5432/pyspider_projectdb",
  "resultdb": "sqlalchemy+postgresql+resultdb://pyspider:pyspider_password@localhost:5432/pyspider_resultdb",
  "message_queue": "redis://localhost:6379/0",
  "webui": {
    "port": 5000,
    "username": "",
    "password": "",
    "need-auth": false,
    "scheduler-rpc": "http://localhost:23333/"
  },
  "fetcher": {
    "xmlrpc-port": 24444,
    "puppeteer-endpoint": "http://localhost:22223"
  },
  "scheduler": {
    "xmlrpc-port": 23333
  },
  "processor": {
    "process-time-limit": 30
  },
  "result_worker": {
  }
}
```

### 2. 起動スクリプトの実行

```bash
./start_postgresql_mode.sh
```

## 接続文字列の形式

PostgreSQL接続文字列の形式は以下の通りです：

```
sqlalchemy+postgresql+<type>://<username>:<password>@<host>:<port>/<database>
```

- `<type>`: `taskdb`, `projectdb`, `resultdb`のいずれか
- `<username>`: PostgreSQLユーザー名
- `<password>`: PostgreSQLパスワード
- `<host>`: PostgreSQLホスト名（通常は`localhost`）
- `<port>`: PostgreSQLポート番号（通常は`5432`）
- `<database>`: PostgreSQLデータベース名

## トラブルシューティング

### 接続エラー

接続エラーが発生した場合は、以下を確認してください：

1. PostgreSQLサービスが実行中であること
2. ユーザー名とパスワードが正しいこと
3. データベースが存在すること
4. ファイアウォールがPostgreSQLポート（通常は5432）を許可していること

### 認証エラー

認証エラーが発生した場合は、`pg_hba.conf`ファイルを確認してください。このファイルは通常、以下の場所にあります：

- Ubuntu: `/etc/postgresql/<version>/main/pg_hba.conf`
- CentOS: `/var/lib/pgsql/data/pg_hba.conf`
- macOS: `/usr/local/var/postgres/pg_hba.conf`

以下の行を追加または修正してください：

```
# IPv4 local connections:
host    all             all             127.0.0.1/32            md5
# IPv6 local connections:
host    all             all             ::1/128                 md5
```

変更後、PostgreSQLサービスを再起動してください：

```bash
sudo systemctl restart postgresql
```

## パフォーマンスの最適化

PostgreSQLのパフォーマンスを最適化するには、以下の設定を検討してください：

1. `shared_buffers`: メモリの25%程度（例：2GBのメモリなら512MB）
2. `work_mem`: クエリごとのメモリ割り当て（例：64MB）
3. `maintenance_work_mem`: メンテナンス操作用のメモリ（例：128MB）
4. `effective_cache_size`: OSのディスクキャッシュを含むメモリの75%程度

これらの設定は`postgresql.conf`ファイルで行います。

## バックアップと復元

### バックアップ

```bash
pg_dump -U pyspider pyspider_taskdb > taskdb_backup.sql
pg_dump -U pyspider pyspider_projectdb > projectdb_backup.sql
pg_dump -U pyspider pyspider_resultdb > resultdb_backup.sql
```

### 復元

```bash
psql -U pyspider -d pyspider_taskdb -f taskdb_backup.sql
psql -U pyspider -d pyspider_projectdb -f projectdb_backup.sql
psql -U pyspider -d pyspider_resultdb -f resultdb_backup.sql
```
