# pyspiderNX2 開発者ガイド

## 1. アーキテクチャ概要

pyspiderNX2は以下のコンポーネントで構成されています：

- **スケジューラ**: タスクのスケジューリングを担当
- **フェッチャー**: Webページの取得を担当
- **プロセッサ**: スクリプトの実行を担当
- **リザルトワーカー**: 結果の保存を担当
- **WebUI**: ユーザーインターフェース
- **WebUI-Next**: 新しいユーザーインターフェース（Next.js製）

### 1.1 コンポーネント間の通信

コンポーネント間の通信は、以下の方法で行われます：

- **通常モード**: XML-RPC
- **Redisモード**: Redisキュー

Redisモードでは、以下のキューが使用されます：

- `scheduler2fetcher`: スケジューラからフェッチャーへのタスク
- `fetcher2processor`: フェッチャーからプロセッサへの結果
- `processor2result`: プロセッサからリザルトワーカーへの結果

### 1.2 データベース

pyspiderNX2は、以下のデータベースを使用します：

- **projectdb**: プロジェクト情報を保存
- **taskdb**: タスク情報を保存
- **resultdb**: スクレイピング結果を保存

デフォルトでは、SQLiteデータベースが使用されますが、MongoDB、MySQL、PostgreSQLなども設定可能です。

## 2. 開発環境のセットアップ

### 2.1 前提条件

- Python 3.7以上
- Redis 6.0.0以上
- Node.js 14.0.0以上
- Git

### 2.2 開発環境のセットアップ手順

```bash
# リポジトリのクローン
git clone https://github.com/your-username/pyspiderNX2.git
cd pyspiderNX2

# 開発用の仮想環境を作成
python -m venv venv
source venv/bin/activate  # Windowsの場合: venv\Scripts\activate

# 依存関係のインストール
pip install -r requirements.txt
pip install -r requirements-dev.txt

# webui-nextの開発環境をセットアップ
cd pyspider/webui-next
npm install
```

### 2.3 開発用サーバーの起動

```bash
# 開発モードでpyspiderを起動
./start_redis_mode.sh --debug

# 別のターミナルでwebui-nextを起動
cd pyspider/webui-next
npm run dev
```

## 3. コードの構成

### 3.1 ディレクトリ構造

```
pyspiderNX2/
├── data/                  # データベースファイル
├── pyspider/              # メインのソースコード
│   ├── database/          # データベース関連のコード
│   ├── fetcher/           # フェッチャー関連のコード
│   ├── libs/              # 共通ライブラリ
│   ├── processor/         # プロセッサ関連のコード
│   ├── result/            # リザルトワーカー関連のコード
│   ├── scheduler/         # スケジューラ関連のコード
│   ├── webui/             # WebUI関連のコード
│   └── webui-next/        # Next.js製WebUI
├── docs/                  # ドキュメント
├── tests/                 # テストコード
├── requirements.txt       # 依存関係
└── start_*.sh             # 起動スクリプト
```

### 3.2 主要なモジュール

- **pyspider/libs/base_handler.py**: スクリプトの基底クラス
- **pyspider/scheduler/scheduler.py**: スケジューラの実装
- **pyspider/fetcher/tornado_fetcher.py**: フェッチャーの実装
- **pyspider/processor/processor.py**: プロセッサの実装
- **pyspider/result/result_worker.py**: リザルトワーカーの実装
- **pyspider/webui/app.py**: WebUIのFlaskアプリケーション
- **pyspider/webui/api_v2.py**: APIv2の実装

## 4. APIの拡張

### 4.1 新しいAPIエンドポイントの追加

新しいAPIエンドポイントを追加するには、`pyspider/webui/api_v2.py`ファイルを編集します：

```python
@app.route('/api/v2/your-endpoint', methods=['GET', 'POST'])
def your_endpoint_v2():
    """Your endpoint description."""
    # リクエストの処理
    data = request.get_json()
    
    # レスポンスの生成
    return json_response({
        'status': 'ok',
        'data': your_data
    })
```

### 4.2 APIドキュメントの更新

新しいAPIエンドポイントを追加した場合は、`docs/api_v2.md`ファイルを更新してください。

## 5. WebUI-Nextの拡張

### 5.1 新しいページの追加

新しいページを追加するには、`pyspider/webui-next/pages`ディレクトリに新しいファイルを作成します：

```jsx
// pages/your-page.js
import { useState, useEffect } from 'react';
import Layout from '../components/Layout';

export default function YourPage() {
  const [data, setData] = useState(null);
  
  useEffect(() => {
    // データの取得
    fetch('/api/v2/your-endpoint')
      .then(res => res.json())
      .then(data => setData(data));
  }, []);
  
  return (
    <Layout>
      <h1>Your Page</h1>
      {data && (
        <div>
          {/* データの表示 */}
        </div>
      )}
    </Layout>
  );
}
```

### 5.2 コンポーネントの追加

再利用可能なコンポーネントを追加するには、`pyspider/webui-next/components`ディレクトリに新しいファイルを作成します：

```jsx
// components/YourComponent.js
import { useState } from 'react';

export default function YourComponent({ prop1, prop2 }) {
  const [state, setState] = useState(initialState);
  
  return (
    <div>
      {/* コンポーネントの内容 */}
    </div>
  );
}
```

## 6. テスト

### 6.1 単体テストの作成

単体テストは、`tests`ディレクトリに作成します：

```python
# tests/test_your_module.py
import unittest
from pyspider.your_module import your_function

class TestYourModule(unittest.TestCase):
    def setUp(self):
        # テストの準備
        pass
    
    def test_your_function(self):
        # テストケース
        result = your_function(input_data)
        self.assertEqual(result, expected_output)
    
    def tearDown(self):
        # テストの後処理
        pass
```

### 6.2 テストの実行

```bash
# すべてのテストを実行
pytest

# 特定のテストを実行
pytest tests/test_your_module.py

# カバレッジレポートを生成
pytest --cov=pyspider
```

## 7. デバッグ

### 7.1 ログの設定

ログレベルを変更するには、以下のコードを使用します：

```python
import logging
logging.getLogger('your_module').setLevel(logging.DEBUG)
```

### 7.2 デバッグモードでの起動

```bash
# デバッグモードでpyspiderを起動
./start_redis_mode.sh --debug
```

## 8. パフォーマンスの最適化

### 8.1 データベースの最適化

データベースのパフォーマンスを最適化するには、以下のスクリプトを実行します：

```bash
# データベースの最適化
python pyspider/database/optimize_db.py
```

### 8.2 キャッシュの利用

頻繁にアクセスされるデータをキャッシュするには、`pyspider/libs/cache.py`モジュールを使用します：

```python
from pyspider.libs.cache import cache_result

@cache_result(expire=60)  # 60秒間キャッシュ
def your_function(param1, param2):
    # 重い処理
    return result
```

## 9. デプロイ

### 9.1 本番環境での設定

本番環境では、以下の設定を推奨します：

- SQLiteではなく、MongoDBやMySQLなどのデータベースを使用
- Redisモードでの実行
- Nginxなどのリバースプロキシの使用
- スーパーバイザーなどのプロセス管理ツールの使用

### 9.2 Dockerでのデプロイ

Dockerを使用してデプロイする場合は、以下のコマンドを実行します：

```bash
# Dockerイメージのビルド
docker build -t pyspidernx2 .

# Dockerコンテナの実行
docker run -d -p 5000:5000 -p 3000:3000 --name pyspidernx2 pyspidernx2
```

## 10. 貢献

### 10.1 コーディング規約

- PEP 8に従ったPythonコード
- ESLintに従ったJavaScriptコード
- 適切なコメントとドキュメント文字列
- テストの作成

### 10.2 プルリクエストの手順

1. リポジトリをフォーク
2. 新しいブランチを作成
3. 変更を加える
4. テストを実行
5. プルリクエストを作成

## 11. ライセンス

pyspiderNX2は、Apache License 2.0の下で公開されています。詳細は、`LICENSE`ファイルを参照してください。
