# pyspiderNX2 互換性ガイド

このドキュメントでは、pyspiderNX2の互換性に関する情報と、最新のライブラリとの互換性を確保するための設定方法について説明します。

## 1. SQLAlchemy 2.0の互換性

### 1.1 互換性の問題

SQLAlchemyは、Pythonの人気のあるORMライブラリですが、バージョンによって互換性の問題が発生する場合があります。pyspiderNX2では、以下の互換性の問題に対応しています：

1. **`convert_unicode`パラメータの非推奨化**：
   - SQLAlchemy 1.4以降では、`convert_unicode`パラメータが非推奨になりました。
   - SQLAlchemy 2.0では、このパラメータは完全に削除されました。

2. **`make_url`の変更**：
   - SQLAlchemy 2.0では、`make_url`関数の戻り値であるURLオブジェクトが不変になりました。
   - 以前のバージョンでは、URLオブジェクトを変更できましたが、2.0以降では新しいオブジェクトを作成する必要があります。

3. **`table_names`属性の変更**：
   - SQLAlchemy 2.0では、`engine.table_names()`メソッドが非推奨になり、代わりに`inspect(engine).get_table_names()`を使用する必要があります。

4. **SQL文の実行方法の変更**：
   - SQLAlchemy 2.0では、SQL文を実行する際に`text()`関数でラップする必要があります。
   - また、トランザクション管理も変更され、`with conn.begin()`のようなコンテキストマネージャを使用する必要があります。

### 1.2 対応方法

pyspiderNX2では、以下の対応を行っています：

1. **`convert_unicode`パラメータの削除**：
   - すべての`create_engine`呼び出しから`convert_unicode`パラメータを削除しました。
   - これにより、SQLAlchemy 1.4以降との互換性が確保されます。

2. **バージョン互換性のあるテーブル名取得**：
   - `get_table_names`関数を追加して、SQLAlchemyのバージョンに応じて適切なメソッドを使用するようにしました。
   - SQLAlchemy 1.xでは`engine.table_names()`を使用し、2.xでは`inspect(engine).get_table_names()`を使用します。

```python
def get_table_names(engine):
    """Get table names from SQLAlchemy engine in a version-compatible way"""
    try:
        # SQLAlchemy 1.x
        if hasattr(engine, 'table_names'):
            return engine.table_names()
        # SQLAlchemy 2.x
        else:
            inspector = inspect(engine)
            return inspector.get_table_names()
    except Exception as e:
        logger.error(f"Error getting table names: {e}")
        return []
```

3. **SQL文の実行方法の更新**：
   - SQL文を実行する際に`sqlalchemy.text()`関数でラップするように修正しました。
   - トランザクション管理を`with conn.begin()`のようなコンテキストマネージャを使用するように更新しました。

```python
# SQLAlchemy 2.0 compatibility: execute returns a result object
with conn.begin():
    conn.execute(sqlalchemy.text(f"CREATE DATABASE IF NOT EXISTS {database}"))
```

## 2. Redis 6.xの互換性

### 2.1 互換性の問題

Redisは、人気のあるインメモリデータベースですが、Pythonクライアントライブラリのバージョンによって互換性の問題が発生する場合があります。pyspiderNX2では、以下の互換性の問題に対応しています：

1. **`StrictRedis`クラスの統合**：
   - Redis 3.x-4.xでは、`StrictRedis`クラスが推奨されていました。
   - Redis 5.x以降では、`Redis`クラスに統合され、`StrictRedis`は`Redis`のエイリアスになりました。
   - Redis 6.xでは、`Redis`クラスのみを使用することが推奨されています。

2. **接続パラメータの変更**：
   - Redis 6.xでは、新しい接続パラメータが追加され、一部のパラメータの動作が変更されました。
   - 特に、`decode_responses`パラメータのデフォルト値が将来変更される可能性があります。

3. **Redis Clusterサポートの変更**：
   - Redis 6.xでは、Redis Clusterのサポートが強化され、接続方法が変更されました。

### 2.2 対応方法

pyspiderNX2では、以下の対応を行っています：

1. **`Redis`クラスの使用**：
   - すべての`StrictRedis`クラスの使用を`Redis`クラスに置き換えました。
   - バージョン分岐を削除し、常に`Redis`クラスを使用するようにしました。

```python
# Redis 6.x compatibility
self.redis = redis.Redis(host=host, port=port, db=db, socket_timeout=30, decode_responses=False)
```

2. **明示的なパラメータ設定**：
   - `decode_responses`パラメータを明示的に設定し、将来のデフォルト値の変更に備えました。
   - `socket_timeout`パラメータを設定して、接続タイムアウトを適切に管理するようにしました。

3. **Redis Clusterサポートの更新**：
   - Redis Clusterの接続方法を更新し、Redis 6.xに対応しました。

```python
# Redis 6.x compatibility for Cluster mode
self.redis = RedisCluster(
    startup_nodes=cluster_nodes,
    decode_responses=False,
    skip_full_coverage_check=True,
    socket_timeout=30
)
```

4. **Redis URL接続の更新**：
   - `redis.from_url`メソッドを使用する際に、明示的なパラメータを設定するようにしました。

```python
# Redis 6.x compatibility
self.redis = redis.from_url(redis_url, decode_responses=True, socket_timeout=30)
```

### 1.3 推奨されるSQLAlchemyバージョン

pyspiderNX2は、以下のSQLAlchemyバージョンとの互換性があります：

- SQLAlchemy 1.3.x（安定版）
- SQLAlchemy 1.4.x（推奨）
- SQLAlchemy 2.0.x（実験的サポート）

最も安定した動作を得るには、SQLAlchemy 1.4.xを使用することをお勧めします。

## 2. Redisの互換性

### 2.1 互換性の問題

Redisは、pyspiderのメッセージキューとして使用されていますが、バージョンによって互換性の問題が発生する場合があります：

1. **Redis-pyバージョンの互換性**：
   - 元のpyspiderはRedis 3.0.0との互換性を要求していましたが、最新のRedisバージョンは5.x以上です。
   - Redis-py 4.0.0以降では、一部のAPIが変更されました。

2. **接続パラメータの変更**：
   - Redis-py 4.0.0以降では、一部の接続パラメータが変更されました。
   - 例えば、`StrictRedis`クラスは非推奨になり、代わりに`Redis`クラスを使用する必要があります。

3. **タイムアウト設定**：
   - 最新のRedisバージョンでは、デフォルトのタイムアウト設定が変更されました。
   - 長時間実行されるタスクでは、タイムアウトエラーが発生する可能性があります。

### 2.2 対応方法

pyspiderNX2では、以下の対応を行っています：

1. **バージョン互換性のある接続**：
   - Redis-pyのバージョンに応じて、適切な接続クラスを使用するようにしました。
   - Redis-py 4.x以降では`Redis`クラスを使用し、それ以前のバージョンでは`StrictRedis`クラスを使用します。

```python
try:
    # Redis 5.x.x+
    self.redis = redis.Redis(host=host, port=port, db=db, socket_timeout=30)
except TypeError:
    # Redis 3.x.x - 4.x.x
    self.redis = redis.StrictRedis(host=host, port=port, db=db)
```

2. **タイムアウト設定の追加**：
   - 長時間実行されるタスクでタイムアウトエラーを防ぐために、`socket_timeout`パラメータを追加しました。
   - デフォルトのタイムアウトを30秒に設定しています。

3. **クラスタモードのサポート**：
   - Redis Clusterモードをサポートするために、`RedisCluster`クラスの接続パラメータも更新しました。

### 2.3 推奨されるRedisバージョン

pyspiderNX2は、以下のRedisバージョンとの互換性があります：

- Redis 3.x（元のpyspiderとの互換性）
- Redis 4.x
- Redis 5.x（推奨）
- Redis 6.x（実験的サポート）

最も安定した動作を得るには、Redis 5.xを使用することをお勧めします。

## 3. その他のライブラリの互換性

### 3.1 Python 3.13との互換性

pyspiderNX2は、Python 3.13との互換性を確保するために、以下の対応を行っています：

1. **xmlrpc.client**：
   - Python 3.13では、xmlrpcの動作が変更されました。
   - タイムアウト設定を追加し、接続エラーを適切に処理するようにしました。

2. **asyncio**：
   - Python 3.13では、asyncioの動作が変更されました。
   - 非同期処理を適切に処理するように修正しました。

### 3.2 推奨されるPythonバージョン

pyspiderNX2は、以下のPythonバージョンとの互換性があります：

- Python 3.7.x
- Python 3.8.x
- Python 3.9.x
- Python 3.10.x（推奨）
- Python 3.11.x
- Python 3.12.x
- Python 3.13.x（実験的サポート）

最も安定した動作を得るには、Python 3.10.xを使用することをお勧めします。

## 4. 互換性の確認方法

### 4.1 依存関係の確認

現在の依存関係を確認するには、以下のコマンドを実行します：

```bash
pip freeze | grep -E 'sqlalchemy|redis|tornado'
```

### 4.2 互換性テストの実行

互換性テストを実行するには、以下のコマンドを実行します：

```bash
cd tests
python -m unittest discover
```

### 4.3 互換性の問題の報告

互換性の問題を発見した場合は、GitHubのIssueで報告してください。報告する際は、以下の情報を含めてください：

- Pythonバージョン
- 依存ライブラリのバージョン（SQLAlchemy、Redis-pyなど）
- 発生した問題の詳細
- 再現手順
