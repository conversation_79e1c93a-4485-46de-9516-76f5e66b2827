# PySpiderNX2 データベース + Redisモード起動スクリプト

## 🎯 **概要**

PySpiderNX2では、異なるデータベースとRedisメッセージキューを組み合わせた3つの起動モードを提供しています。

### 📊 **利用可能なモード**

| モード | データベース | メッセージキュー | 起動スクリプト |
|--------|-------------|-----------------|---------------|
| **PostgreSQL + Redis** | PostgreSQL | Redis | `start_redis_mode.sh` |
| **SQLite + Redis** | SQLite | Redis | `start_sqlite_redis_mode.sh` |
| **MySQL + Redis** | MySQL | Redis | `start_mysql_redis_mode.sh` |

## 🚀 **起動方法**

### 1. **PostgreSQL + Redisモード**
```bash
./start_redis_mode.sh
```

**特徴:**
- 高性能なPostgreSQLデータベース
- Redisメッセージキューによる高速処理
- 大規模データ処理に最適
- 本格的な本番環境向け

**設定ファイル:** `redis_config.json`

### 2. **SQLite + Redisモード**
```bash
./start_sqlite_redis_mode.sh
```

**特徴:**
- 軽量なSQLiteデータベース（ファイルベース）
- Redisメッセージキューによる高速処理
- 開発・テスト環境に最適
- データベースサーバー不要

**設定ファイル:** `sqlite_redis_config.json`
**データベースファイル:** `data/pyspider_*.db`

### 3. **MySQL + Redisモード**
```bash
./start_mysql_redis_mode.sh
```

**特徴:**
- 人気の高いMySQLデータベース
- Redisメッセージキューによる高速処理
- 中規模から大規模環境に対応
- 既存MySQL環境との統合が容易

**設定ファイル:** `mysql_redis_config.json`

## ⚙️ **設定方法**

### SQLite + Redisモード設定

SQLiteモードは設定不要で即座に利用できます：

```bash
# データディレクトリが自動作成されます
./start_sqlite_redis_mode.sh
```

**自動作成されるファイル:**
- `data/pyspider_taskdb.db` - タスク管理データベース
- `data/pyspider_projectdb.db` - プロジェクト管理データベース
- `data/pyspider_resultdb.db` - 結果保存データベース

### MySQL + Redisモード設定

#### **環境変数による設定**
```bash
# MySQL接続情報を環境変数で設定
export MYSQL_HOST=localhost
export MYSQL_PORT=3306
export MYSQL_USER=pyspider
export MYSQL_PASSWORD=your_password
export MYSQL_DATABASE=pyspider

# 起動
./start_mysql_redis_mode.sh
```

#### **デフォルト設定**
環境変数が未設定の場合、以下のデフォルト値が使用されます：
- **ホスト:** localhost:3306
- **ユーザー:** pyspider
- **パスワード:** pyspider123
- **データベース:** pyspider

#### **MySQLデータベース準備**
```sql
-- MySQLサーバーでデータベースとユーザーを作成
CREATE DATABASE pyspider_taskdb CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE pyspider_projectdb CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE pyspider_resultdb CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

CREATE USER 'pyspider'@'localhost' IDENTIFIED BY 'pyspider123';
GRANT ALL PRIVILEGES ON pyspider_*.* TO 'pyspider'@'localhost';
FLUSH PRIVILEGES;
```

### PostgreSQL + Redisモード設定

既存の`redis_config.json`を使用します。PostgreSQL設定については既存ドキュメントを参照してください。

## 🔧 **Redis設定**

全モードで共通のRedis設定が使用されます：

### **Redis自動管理**
- Redisサーバーの自動起動・確認
- 接続テストと自動復旧
- フォールバック機能（XMLRPCモード）

### **Redis接続情報**
- **ポート:** 6379
- **接続URL:** redis://localhost:6379/0
- **バインド:** 127.0.0.1（ローカルのみ）

## 📊 **パフォーマンス比較**

| 項目 | SQLite + Redis | MySQL + Redis | PostgreSQL + Redis |
|------|---------------|---------------|-------------------|
| **起動速度** | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐ |
| **設定の簡単さ** | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐ |
| **スケーラビリティ** | ⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ |
| **同時接続性能** | ⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ |
| **データ整合性** | ⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ |
| **メモリ使用量** | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐ |

## 🎯 **用途別推奨モード**

### **開発・テスト環境**
```bash
./start_sqlite_redis_mode.sh
```
- 設定不要で即座に開始
- ファイルベースで管理が簡単
- 軽量で高速起動

### **中規模本番環境**
```bash
./start_mysql_redis_mode.sh
```
- 既存MySQL環境との統合
- 適度なパフォーマンス
- 豊富な管理ツール

### **大規模本番環境**
```bash
./start_redis_mode.sh
```
- 最高のパフォーマンス
- 高い同時接続性能
- 企業レベルの信頼性

## 🛠️ **管理コマンド**

### **共通管理コマンド**
```bash
# 停止
./stop_pyspider.sh

# 監視
./monitor_puppeteer_ports.sh

# Redis監視
redis-cli monitor

# ログ確認
tail -f scheduler.log
tail -f processor.log
tail -f webui.log
```

### **データベース固有の管理**

#### **SQLiteモード**
```bash
# データベースファイル確認
ls -la data/

# SQLiteデータベース接続
sqlite3 data/pyspider_taskdb.db

# データベースバックアップ
cp -r data/ backup_$(date +%Y%m%d)/
```

#### **MySQLモード**
```bash
# MySQL接続
mysql -h localhost -u pyspider -p

# データベース確認
mysql -h localhost -u pyspider -p -e "SHOW DATABASES LIKE 'pyspider_%';"

# データベースバックアップ
mysqldump -h localhost -u pyspider -p --all-databases > backup_$(date +%Y%m%d).sql
```

## 🔍 **トラブルシューティング**

### **SQLiteモード**
```bash
# データディレクトリの権限確認
ls -la data/

# ディスク容量確認
df -h .

# SQLiteファイルの整合性チェック
sqlite3 data/pyspider_taskdb.db "PRAGMA integrity_check;"
```

### **MySQLモード**
```bash
# MySQL接続テスト
mysql -h localhost -u pyspider -p -e "SELECT 1;"

# MySQL設定確認
echo "Host: $MYSQL_HOST"
echo "Port: $MYSQL_PORT"
echo "User: $MYSQL_USER"
echo "Database: $MYSQL_DATABASE"

# MySQL権限確認
mysql -h localhost -u pyspider -p -e "SHOW GRANTS;"
```

### **Redis共通**
```bash
# Redis接続テスト
redis-cli ping

# Redis情報確認
redis-cli info

# Redis設定確認
redis-cli config get "*"
```

## 📝 **ログファイル**

各モードで生成されるログファイル：

### **共通ログ**
- `scheduler.log` - スケジューラログ
- `fetcher.log` - フェッチャーログ
- `processor.log` - プロセッサログ
- `result_worker.log` - リザルトワーカーログ
- `webui.log` - WebUIログ

### **モード固有ログ**
- `pyspider_start_sqlite_redis.log` - SQLite+Redisモード起動ログ
- `pyspider_start_mysql_redis.log` - MySQL+Redisモード起動ログ
- `pyspider_start_redis.log` - PostgreSQL+Redisモード起動ログ

## 🎉 **まとめ**

PySpiderNX2のデータベース + Redisモードにより、用途に応じた最適な構成を選択できます：

- **簡単開始:** SQLite + Redis
- **バランス重視:** MySQL + Redis  
- **高性能重視:** PostgreSQL + Redis

全モードでRedisによる高速メッセージキューとフォールバック機能を提供し、安定した運用を実現します。
