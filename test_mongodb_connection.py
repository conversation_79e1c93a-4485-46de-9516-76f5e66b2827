import json
import pymongo
from pymongo import MongoClient
import sys

def test_mongodb_connection(config_file='mongodb_config.json'):
    try:
        # 設定ファイルの読み込み
        with open(config_file, 'r') as f:
            config = json.load(f)

        # MongoDBへの接続
        connection_string = f"mongodb://{config['host']}:{config['port']}"
        if config['username'] and config['password']:
            connection_string = f"mongodb://{config['username']}:{config['password']}@{config['host']}:{config['port']}"

        client = MongoClient(connection_string)
        
        # 接続テスト
        client.admin.command('ping')
        print("MongoDBへの接続に成功しました！")
        
        # データベースの選択
        db = client[config['database']]
        
        # コレクションの一覧を表示
        collections = db.list_collection_names()
        print(f"\n利用可能なコレクション:")
        for collection in collections:
            print(f"- {collection}")
            
        return True
        
    except Exception as e:
        print(f"エラーが発生しました: {str(e)}")
        return False

if __name__ == "__main__":
    success = test_mongodb_connection()
    sys.exit(0 if success else 1) 