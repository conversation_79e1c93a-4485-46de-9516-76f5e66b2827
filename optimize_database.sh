#!/bin/bash

# データベース最適化スクリプト
# 使用方法: ./optimize_database.sh [データベースディレクトリのパス]

# デフォルトのデータベースディレクトリ
DB_DIR="./data"

# 引数があれば、それをデータベースディレクトリとして使用
if [ $# -gt 0 ]; then
    DB_DIR="$1"
fi

# スクリプトのディレクトリを取得
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"

# Pythonスクリプトを実行
echo "Optimizing databases in $DB_DIR..."
python "$SCRIPT_DIR/pyspider/database/optimize_db.py" --path "$DB_DIR"

# 終了コードを確認
if [ $? -eq 0 ]; then
    echo "Database optimization completed successfully."
else
    echo "Database optimization failed."
    exit 1
fi

exit 0
