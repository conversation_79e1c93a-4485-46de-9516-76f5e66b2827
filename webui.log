{"timestamp": "2025-06-08T09:41:12.089009", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203}
[I 250608 09:41:12 memory_optimizer:57] Memory optimizer initialized with max_memory_percent=80.0%, gc_interval=60s, check_interval=30s, auto_optimize=True
[I 250608 09:41:12 memory_optimizer:69] Memory monitoring started
[I 250608 09:41:12 app:166] CORS enabled with secure settings
[I 250608 09:41:12 app:179] Authentication config loaded: need_auth=True, username=admin
[I 250608 09:41:12 cache:140] Using Redis cache
[I 250608 09:41:12 alert_manager:236] Added alert rule: high_cpu_usage
[I 250608 09:41:12 alert_manager:236] Added alert rule: high_memory_usage
[I 250608 09:41:12 alert_manager:236] Added alert rule: high_disk_usage
[I 250608 09:41:12 alert_manager:236] Added alert rule: too_many_tasks
[I 250608 09:41:12 alert_manager:236] Added alert rule: too_many_errors
[I 250608 09:41:12 metrics:134] Scheduler is not directly accessible. Using default values.
[I 250608 09:41:12 alert_manager:124] Started alert check thread (interval: 60s)
[I 250608 09:41:12 alert_manager:61] Alert manager initialized (auto_check: True)
[I 250608 09:41:12 prometheus_metrics:88] Prometheus metrics initialized with prefix 'pyspider'
[I 250608 09:41:12 performance_metrics:143] Started GC stats collection thread (interval: 60s)
[I 250608 09:41:12 performance_metrics:149] Started process stats collection thread (interval: 10s)
[I 250608 09:41:12 performance_metrics:112] Performance metrics initialized (tracemalloc: False, auto_collect: True)
[I 250608 09:41:12 app:242] ✅ Enhanced API v2エンドポイントを登録しました
[I 250608 09:41:12 prometheus_endpoint:58] Prometheus metrics initialized
[I 250608 09:41:12 app:252] ✅ Prometheusエンドポイントを登録しました
[I 250608 09:41:12 app:287] ✅ Redis状態確認APIを登録しました
[I 250608 09:41:12 app:298] ✅ ファイル出力APIを登録しました
[I 250608 09:41:12 run:31] Created PID file for webui: /tmp/pyspider_webui.pid
[I 250608 09:41:13 run:94] Successfully connected to RPC server: http://localhost:23333/
[I 250608 09:41:13 app:62] WebDAV mode has been removed
[I 250608 09:41:13 app:71] webui running on 0.0.0.0:5000
[I 250608 09:41:50 login:175] before_request called for path: /api/v2/projects
[I 250608 09:41:50 login:175] before_request called for path: /api/projects
[I 250608 09:41:50 login:175] before_request called for path: /api/v2/projects
[I 250608 09:41:50 login:175] before_request called for path: /api/projects
[I 250608 09:41:50 login:175] before_request called for path: /api/v2/counter
[I 250608 09:41:50 login:175] before_request called for path: /api/v2/counter
[I 250608 09:41:50 login:175] before_request called for path: /api/v2/projects
[I 250608 09:41:50 login:175] before_request called for path: /api/projects
[I 250608 09:41:50 login:175] before_request called for path: /api/v2/counter
[I 250608 09:41:51 login:175] before_request called for path: /api/v2/tasks
[I 250608 09:41:51 login:175] before_request called for path: /api/v2/tasks
[I 250608 09:41:52 login:175] before_request called for path: /api/v2/metrics
[I 250608 09:41:52 login:175] before_request called for path: /api/v2/tasks
[I 250608 09:41:52 login:175] before_request called for path: /api/components/status
[I 250608 09:41:52 login:175] before_request called for path: /api/v2/metrics
[I 250608 09:41:52 login:175] before_request called for path: /api/components/status
[I 250608 09:41:52 login:175] before_request called for path: /api/v2/metrics
[I 250608 09:41:52 login:175] before_request called for path: /api/components/status
[I 250608 09:41:55 login:175] before_request called for path: /api/v2/projects
[I 250608 09:41:55 login:175] before_request called for path: /api/projects
[I 250608 09:41:56 login:175] before_request called for path: /api/v2/results
[I 250608 09:41:56 login:175] before_request called for path: /api/v2/projects
[I 250608 09:41:56 login:175] before_request called for path: /api/projects
[I 250608 09:41:57 login:175] before_request called for path: /api/v2/results
[I 250608 09:41:57 login:175] before_request called for path: /api/v2/file-output/stats
[E 250608 09:41:57 file_output:111] Error getting file output stats: cannot import name 'g' from 'pyspider.run' (/home/<USER>/workplace/python/pyspiderNX2/pyspider/run.py)
[I 250608 09:41:57 login:175] before_request called for path: /api/v2/file-output/stats
[E 250608 09:41:57 file_output:111] Error getting file output stats: cannot import name 'g' from 'pyspider.run' (/home/<USER>/workplace/python/pyspiderNX2/pyspider/run.py)
[I 250608 09:42:12 metrics:173] Gauges: {"memory_usage_rss": 100200448, "memory_usage_vms": 425930752, "memory_usage_percent": 0.29725177214121307, "system_memory_total": 33576665088, "system_memory_available": 28193300480, "system_memory_percent": 16.0}
[I 250608 09:42:44 login:175] before_request called for path: /api/v2/projects
[I 250608 09:42:44 login:175] before_request called for path: /api/projects
[I 250608 09:42:44 login:175] before_request called for path: /api/v2/projects
[I 250608 09:42:44 login:175] before_request called for path: /api/projects
[I 250608 09:42:44 login:175] before_request called for path: /api/v2/projects
[I 250608 09:42:44 login:175] before_request called for path: /api/projects
[I 250608 09:42:44 login:175] before_request called for path: /api/v2/projects
[I 250608 09:42:44 login:175] before_request called for path: /api/projects
[I 250608 09:42:44 login:175] before_request called for path: /dashboard-active-tasks
[I 250608 09:42:45 login:175] before_request called for path: /api/v2/tasks
[I 250608 09:42:45 login:175] before_request called for path: /dashboard-active-tasks
[I 250608 09:42:45 login:175] before_request called for path: /api/v2/tasks
[I 250608 09:43:12 metrics:173] Gauges: {"memory_usage_rss": 100593664, "memory_usage_vms": 427286528, "memory_usage_percent": 0.2995939702062647, "system_memory_total": 33576665088, "system_memory_available": 27556077568, "system_memory_percent": 17.9}
[I 250608 09:44:12 metrics:173] Gauges: {"memory_usage_rss": 100593664, "memory_usage_vms": 427286528, "memory_usage_percent": 0.2995939702062647, "system_memory_total": 33576665088, "system_memory_available": 27216007168, "system_memory_percent": 18.9}
[I 250608 09:44:51 login:175] before_request called for path: /dashboard-active-tasks
[I 250608 09:44:51 login:175] before_request called for path: /dashboard-active-tasks
[I 250608 09:44:52 login:175] before_request called for path: /api/v2/projects
[I 250608 09:44:52 login:175] before_request called for path: /api/projects
[I 250608 09:44:52 login:175] before_request called for path: /api/v2/projects
[I 250608 09:44:52 login:175] before_request called for path: /api/projects
[I 250608 09:45:07 login:175] before_request called for path: /api/projects/tinti
[I 250608 09:45:07 login:175] before_request called for path: /api/projects/tinti
[I 250608 09:45:07 login:175] before_request called for path: /api/v2/counter
[I 250608 09:45:07 login:175] before_request called for path: /api/projects/tinti
[I 250608 09:45:07 login:175] before_request called for path: /api/v2/counter
[I 250608 09:45:07 login:175] before_request called for path: /api/projects/tinti
[I 250608 09:45:07 login:175] before_request called for path: /api/projects/tinti
[I 250608 09:45:07 login:175] before_request called for path: /api/projects/tinti
[I 250608 09:45:07 login:175] before_request called for path: /api/debug/tinti
[I 250608 09:45:08 login:175] before_request called for path: /api/debug/tinti
[I 250608 09:45:12 metrics:173] Gauges: {"memory_usage_rss": 100593664, "memory_usage_vms": 427286528, "memory_usage_percent": 0.2995939702062647, "system_memory_total": 33576665088, "system_memory_available": 27091742720, "system_memory_percent": 19.3}
[I 250608 09:45:17 login:175] before_request called for path: /api/v2/counter
[I 250608 09:45:27 login:175] before_request called for path: /api/v2/counter
[I 250608 09:45:31 login:175] before_request called for path: /api/projects/tinti
[I 250608 09:45:31 login:175] before_request called for path: /api/v2/counter
[I 250608 09:45:31 login:175] before_request called for path: /api/projects/tinti
[I 250608 09:45:31 login:175] before_request called for path: /api/projects/tinti
[I 250608 09:45:31 login:175] before_request called for path: /api/v2/counter
[I 250608 09:45:31 login:175] before_request called for path: /api/projects/tinti
[I 250608 09:45:31 login:175] before_request called for path: /api/v2/counter
[I 250608 09:45:31 login:175] before_request called for path: /api/projects/tinti
[I 250608 09:45:31 login:175] before_request called for path: /api/projects/tinti
[I 250608 09:45:31 login:175] before_request called for path: /api/debug/tinti
[I 250608 09:45:31 login:175] before_request called for path: /api/projects/tinti
[I 250608 09:45:31 login:175] before_request called for path: /api/debug/tinti
[I 250608 09:45:31 login:175] before_request called for path: /api/debug/tinti
[I 250608 09:45:39 login:175] before_request called for path: /dashboard-active-tasks
[I 250608 09:45:39 login:175] before_request called for path: /dashboard-active-tasks
[I 250608 09:45:39 login:175] before_request called for path: /api/v2/projects
[I 250608 09:45:39 login:175] before_request called for path: /api/projects
[I 250608 09:45:39 login:175] before_request called for path: /api/v2/projects
[I 250608 09:45:39 login:175] before_request called for path: /api/projects
[I 250608 09:45:41 login:175] before_request called for path: /api/v2/counter
[I 250608 09:45:42 login:175] before_request called for path: /api/projects/tinti
[I 250608 09:45:42 login:175] before_request called for path: /api/projects/tinti
[I 250608 09:45:42 login:175] before_request called for path: /api/v2/counter
[I 250608 09:45:42 login:175] before_request called for path: /api/projects/tinti
[I 250608 09:45:42 login:175] before_request called for path: /api/v2/counter
[I 250608 09:45:42 login:175] before_request called for path: /api/projects/tinti
[I 250608 09:45:42 login:175] before_request called for path: /api/projects/tinti
[I 250608 09:45:42 login:175] before_request called for path: /api/projects/tinti
[I 250608 09:45:42 login:175] before_request called for path: /api/debug/tinti
[I 250608 09:45:42 login:175] before_request called for path: /api/debug/tinti
[I 250608 09:45:51 login:175] before_request called for path: /api/v2/counter
[I 250608 09:45:52 login:175] before_request called for path: /api/v2/counter
[I 250608 09:46:01 login:175] before_request called for path: /api/v2/counter
[I 250608 09:46:02 login:175] before_request called for path: /api/v2/counter
[I 250608 09:46:11 login:175] before_request called for path: /api/v2/counter
[I 250608 09:46:12 metrics:173] Gauges: {"memory_usage_rss": 100724736, "memory_usage_vms": 427286528, "memory_usage_percent": 0.29998433655044, "system_memory_total": 33576665088, "system_memory_available": 26822479872, "system_memory_percent": 20.1}
[I 250608 09:46:12 login:175] before_request called for path: /api/v2/counter
[I 250608 09:46:21 login:175] before_request called for path: /api/v2/counter
[I 250608 09:46:22 login:175] before_request called for path: /api/v2/counter
[I 250608 09:46:31 login:175] before_request called for path: /api/v2/counter
[I 250608 09:46:32 login:175] before_request called for path: /api/v2/counter
[I 250608 09:46:42 login:175] before_request called for path: /api/v2/counter
[I 250608 09:46:52 login:175] before_request called for path: /api/v2/counter
[I 250608 09:47:08 login:175] before_request called for path: /api/v2/counter
[I 250608 09:47:08 login:175] before_request called for path: /api/v2/counter
[I 250608 09:47:12 metrics:173] Gauges: {"memory_usage_rss": 100724736, "memory_usage_vms": 427286528, "memory_usage_percent": 0.29998433655044, "system_memory_total": 33576665088, "system_memory_available": 26934910976, "system_memory_percent": 19.8}
[I 250608 09:48:08 login:175] before_request called for path: /api/v2/counter
[I 250608 09:48:08 login:175] before_request called for path: /api/v2/counter
[I 250608 09:48:12 metrics:173] Gauges: {"memory_usage_rss": 100724736, "memory_usage_vms": 427286528, "memory_usage_percent": 0.29998433655044, "system_memory_total": 33576665088, "system_memory_available": 26820808704, "system_memory_percent": 20.1}
[I 250608 09:48:13 login:175] before_request called for path: /api/v2/projects
[I 250608 09:48:13 login:175] before_request called for path: /api/projects
[I 250608 09:48:13 login:175] before_request called for path: /api/v2/counter
[I 250608 09:48:13 login:175] before_request called for path: /api/v2/projects
[I 250608 09:48:13 login:175] before_request called for path: /api/projects
[I 250608 09:48:13 login:175] before_request called for path: /api/v2/counter
[I 250608 09:48:13 login:175] before_request called for path: /api/v2/projects
[I 250608 09:48:13 login:175] before_request called for path: /api/projects
[I 250608 09:48:13 login:175] before_request called for path: /api/v2/counter
[I 250608 09:48:20 login:175] before_request called for path: /api/v2/projects
[I 250608 09:48:20 login:175] before_request called for path: /api/projects
[I 250608 09:48:20 login:175] before_request called for path: /api/v2/counter
[I 250608 09:48:20 login:175] before_request called for path: /api/v2/projects
[I 250608 09:48:20 login:175] before_request called for path: /api/projects
[I 250608 09:48:20 login:175] before_request called for path: /api/v2/counter
[I 250608 09:48:22 login:175] before_request called for path: /api/v2/tasks
[I 250608 09:48:22 login:175] before_request called for path: /api/v2/tasks
[I 250608 09:48:24 login:175] before_request called for path: /api/v2/metrics
[I 250608 09:48:24 login:175] before_request called for path: /api/components/status
[I 250608 09:48:24 login:175] before_request called for path: /api/v2/metrics
[I 250608 09:48:24 login:175] before_request called for path: /api/components/status
[I 250608 09:48:26 login:175] before_request called for path: /dashboard-active-tasks
[I 250608 09:48:26 login:175] before_request called for path: /dashboard-active-tasks
[I 250608 09:48:26 login:175] before_request called for path: /api/v2/projects
[I 250608 09:48:26 login:175] before_request called for path: /api/projects
[I 250608 09:48:26 login:175] before_request called for path: /api/v2/projects
[I 250608 09:48:26 login:175] before_request called for path: /api/projects
[I 250608 09:48:29 login:175] before_request called for path: /api/v2/projects/tinti
[I 250608 09:48:29 login:175] before_request called for path: /api/v2/counter
[I 250608 09:48:29 login:175] before_request called for path: /api/v2/counter
[I 250608 09:48:29 login:175] before_request called for path: /api/v2/projects/tinti
[I 250608 09:48:29 login:175] before_request called for path: /api/projects/tinti
[I 250608 09:48:29 login:175] before_request called for path: /api/projects/tinti
[I 250608 09:48:29 login:175] before_request called for path: /api/debug/tinti
[I 250608 09:48:29 login:175] before_request called for path: /api/debug/tinti
[I 250608 09:48:39 login:175] before_request called for path: /api/v2/counter
[I 250608 09:48:41 login:175] before_request called for path: /api/v2/projects
[I 250608 09:48:41 login:175] before_request called for path: /api/projects
[I 250608 09:48:49 login:175] before_request called for path: /api/v2/counter
[I 250608 09:48:55 login:175] before_request called for path: /api/v2/projects
[I 250608 09:48:59 login:175] before_request called for path: /api/v2/counter
[I 250608 09:49:01 login:175] before_request called for path: /api/v2/counter
[I 250608 09:49:08 login:175] before_request called for path: /api/v2/counter
[I 250608 09:49:09 login:175] before_request called for path: /api/v2/counter
[I 250608 09:49:10 login:175] before_request called for path: /api/v2/projects
[I 250608 09:49:10 api_utils:82] Authentication disabled, skipping auth check for create_project_v2
[W 250608 09:49:10 projects:150] Failed to trigger on_start for project test_project: <Fault 1: '<class \'Exception\'>:method "trigger_on_start" is not supported'>
[I 250608 09:49:12 metrics:173] Gauges: {"memory_usage_rss": 100855808, "memory_usage_vms": 427425792, "memory_usage_percent": 0.30037470289461526, "system_memory_total": 33576665088, "system_memory_available": 26937053184, "system_memory_percent": 19.8}
[I 250608 09:49:19 login:175] before_request called for path: /api/v2/counter
[I 250608 09:49:22 login:175] before_request called for path: /api/v2/projects/test_project
[I 250608 09:49:27 login:175] before_request called for path: /api/v2/counter
[I 250608 09:49:27 login:175] before_request called for path: /api/v2/counter
[I 250608 09:49:27 login:175] before_request called for path: /api/v2/projects/tinti
[I 250608 09:49:28 login:175] before_request called for path: /api/v2/counter
[I 250608 09:49:28 login:175] before_request called for path: /api/v2/projects/tinti
[I 250608 09:49:28 login:175] before_request called for path: /api/v2/projects/tinti
[I 250608 09:49:28 login:175] before_request called for path: /api/projects/tinti
[I 250608 09:49:28 login:175] before_request called for path: /api/projects/tinti
[I 250608 09:49:28 login:175] before_request called for path: /api/projects/tinti
[I 250608 09:49:28 login:175] before_request called for path: /api/debug/tinti
[I 250608 09:49:28 login:175] before_request called for path: /api/debug/tinti
[I 250608 09:49:28 login:175] before_request called for path: /api/debug/tinti
[I 250608 09:49:32 login:175] before_request called for path: /api/v2/projects/test_project
[I 250608 09:49:37 login:175] before_request called for path: /api/v2/counter
[I 250608 09:49:38 login:175] before_request called for path: /api/v2/counter
[I 250608 09:49:41 login:175] before_request called for path: /api/v2/counter
[I 250608 09:49:48 login:175] before_request called for path: /api/v2/counter
[I 250608 09:49:51 login:175] before_request called for path: /api/v2/counter
[I 250608 09:49:55 login:175] before_request called for path: /api/v2/counter
[I 250608 09:49:55 login:175] before_request called for path: /api/v2/projects/tinti
[I 250608 09:49:55 login:175] before_request called for path: /api/v2/counter
[I 250608 09:49:55 login:175] before_request called for path: /api/v2/projects/tinti
[I 250608 09:49:55 login:175] before_request called for path: /api/v2/counter
[I 250608 09:49:55 login:175] before_request called for path: /api/v2/projects/tinti
[I 250608 09:49:55 login:175] before_request called for path: /api/projects/tinti
[I 250608 09:49:55 login:175] before_request called for path: /api/projects/tinti
[I 250608 09:49:55 login:175] before_request called for path: /api/projects/tinti
[I 250608 09:49:55 login:175] before_request called for path: /api/debug/tinti
[I 250608 09:49:55 login:175] before_request called for path: /api/debug/tinti
[I 250608 09:49:55 login:175] before_request called for path: /api/debug/tinti
[I 250608 09:50:01 login:175] before_request called for path: /api/v2/counter
[I 250608 09:50:05 login:175] before_request called for path: /api/v2/counter
[I 250608 09:50:11 login:175] before_request called for path: /api/v2/counter
[I 250608 09:50:12 metrics:173] Gauges: {"memory_usage_rss": 100986880, "memory_usage_vms": 427425792, "memory_usage_percent": 0.30037470289461526, "system_memory_total": 33576665088, "system_memory_available": 27046666240, "system_memory_percent": 19.4}
[I 250608 09:50:15 login:175] before_request called for path: /api/v2/counter
[I 250608 09:50:21 login:175] before_request called for path: /api/v2/counter
[I 250608 09:50:25 login:175] before_request called for path: /api/v2/counter
[I 250608 09:50:31 login:175] before_request called for path: /api/v2/counter
[I 250608 09:50:35 login:175] before_request called for path: /api/v2/counter
[I 250608 09:50:45 login:175] before_request called for path: /api/v2/counter
[I 250608 09:50:55 login:175] before_request called for path: /api/v2/counter
[I 250608 09:51:01 login:175] before_request called for path: /api/v2/counter
[W 250608 09:51:01 index:329] No counter data found for project: test_project
[I 250608 09:51:01 login:175] before_request called for path: /api/v2/projects/test_project
[I 250608 09:51:01 login:175] before_request called for path: /api/v2/counter
[W 250608 09:51:01 index:329] No counter data found for project: test_project
[I 250608 09:51:01 login:175] before_request called for path: /api/v2/projects/test_project
[I 250608 09:51:01 login:175] before_request called for path: /api/v2/counter
[W 250608 09:51:01 index:329] No counter data found for project: test_project
[I 250608 09:51:01 login:175] before_request called for path: /api/v2/projects/test_project
[I 250608 09:51:05 login:175] before_request called for path: /api/v2/counter
[I 250608 09:51:08 login:175] before_request called for path: /api/v2/counter
[I 250608 09:51:11 login:175] before_request called for path: /api/v2/counter
[W 250608 09:51:11 index:329] No counter data found for project: test_project
[I 250608 09:51:12 metrics:173] Gauges: {"memory_usage_rss": 101117952, "memory_usage_vms": 427425792, "memory_usage_percent": 0.3011554355829658, "system_memory_total": 33576665088, "system_memory_available": 26884886528, "system_memory_percent": 19.9}
[I 250608 09:51:15 login:175] before_request called for path: /api/v2/counter
[I 250608 09:51:21 login:175] before_request called for path: /api/v2/counter
[W 250608 09:51:21 index:329] No counter data found for project: test_project
[I 250608 09:51:25 login:175] before_request called for path: /api/v2/counter
[I 250608 09:51:31 login:175] before_request called for path: /api/v2/counter
[W 250608 09:51:31 index:329] No counter data found for project: test_project
[I 250608 09:51:35 login:175] before_request called for path: /api/v2/counter
[I 250608 09:51:41 login:175] before_request called for path: /api/v2/counter
[W 250608 09:51:41 index:329] No counter data found for project: test_project
[I 250608 09:51:45 login:175] before_request called for path: /api/v2/counter
[I 250608 09:51:51 login:175] before_request called for path: /api/v2/counter
[W 250608 09:51:51 index:329] No counter data found for project: test_project
[I 250608 09:51:55 login:175] before_request called for path: /api/v2/counter
[I 250608 09:52:01 login:175] before_request called for path: /api/v2/counter
[W 250608 09:52:01 index:329] No counter data found for project: test_project
[I 250608 09:52:08 login:175] before_request called for path: /api/v2/counter
[I 250608 09:52:08 login:175] before_request called for path: /api/v2/counter
[I 250608 09:52:12 metrics:173] Gauges: {"memory_usage_rss": 101117952, "memory_usage_vms": 427425792, "memory_usage_percent": 0.3011554355829658, "system_memory_total": 33576665088, "system_memory_available": 28060319744, "system_memory_percent": 16.4}
[I 250608 09:52:25 login:175] before_request called for path: /api/v2/counter
[W 250608 09:52:25 index:329] No counter data found for project: test_project
[I 250608 09:52:26 login:175] before_request called for path: /api/v2/counter
[I 250608 09:52:29 login:175] before_request called for path: /api/v2/counter
[W 250608 09:52:29 index:329] No counter data found for project: test_project
[I 250608 09:52:29 login:175] before_request called for path: /api/v2/projects/test_project
[I 250608 09:52:29 login:175] before_request called for path: /api/v2/counter
[W 250608 09:52:29 index:329] No counter data found for project: test_project
[I 250608 09:52:29 login:175] before_request called for path: /api/v2/counter
[W 250608 09:52:29 index:329] No counter data found for project: test_project
[I 250608 09:52:29 login:175] before_request called for path: /api/v2/projects/test_project
[I 250608 09:52:29 login:175] before_request called for path: /api/v2/projects/test_project
[I 250608 09:52:38 login:175] before_request called for path: /dashboard-active-tasks
[I 250608 09:52:38 login:175] before_request called for path: /dashboard-active-tasks
[I 250608 09:52:40 login:175] before_request called for path: /api/v2/projects
[I 250608 09:52:40 login:175] before_request called for path: /api/projects
[I 250608 09:52:40 login:175] before_request called for path: /api/v2/projects
[I 250608 09:52:40 login:175] before_request called for path: /api/projects
[I 250608 09:52:42 login:175] before_request called for path: /api/v2/counter
[I 250608 09:52:42 login:175] before_request called for path: /api/v2/counter
[I 250608 09:52:42 login:175] before_request called for path: /api/v2/projects/tinti
[I 250608 09:52:42 login:175] before_request called for path: /api/v2/projects/tinti
[I 250608 09:52:42 login:175] before_request called for path: /api/projects/tinti
[I 250608 09:52:42 login:175] before_request called for path: /api/projects/tinti
[I 250608 09:52:42 login:175] before_request called for path: /api/debug/tinti
[I 250608 09:52:42 login:175] before_request called for path: /api/debug/tinti
[I 250608 09:52:52 login:175] before_request called for path: /api/v2/counter
[I 250608 09:53:02 login:175] before_request called for path: /api/v2/counter
