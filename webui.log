{"timestamp": "2025-06-08T09:41:12.089009", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203}
[I 250608 09:41:12 memory_optimizer:57] Memory optimizer initialized with max_memory_percent=80.0%, gc_interval=60s, check_interval=30s, auto_optimize=True
[I 250608 09:41:12 memory_optimizer:69] Memory monitoring started
[I 250608 09:41:12 app:166] CORS enabled with secure settings
[I 250608 09:41:12 app:179] Authentication config loaded: need_auth=True, username=admin
[I 250608 09:41:12 cache:140] Using Redis cache
[I 250608 09:41:12 alert_manager:236] Added alert rule: high_cpu_usage
[I 250608 09:41:12 alert_manager:236] Added alert rule: high_memory_usage
[I 250608 09:41:12 alert_manager:236] Added alert rule: high_disk_usage
[I 250608 09:41:12 alert_manager:236] Added alert rule: too_many_tasks
[I 250608 09:41:12 alert_manager:236] Added alert rule: too_many_errors
[I 250608 09:41:12 metrics:134] Scheduler is not directly accessible. Using default values.
[I 250608 09:41:12 alert_manager:124] Started alert check thread (interval: 60s)
[I 250608 09:41:12 alert_manager:61] Alert manager initialized (auto_check: True)
[I 250608 09:41:12 prometheus_metrics:88] Prometheus metrics initialized with prefix 'pyspider'
[I 250608 09:41:12 performance_metrics:143] Started GC stats collection thread (interval: 60s)
[I 250608 09:41:12 performance_metrics:149] Started process stats collection thread (interval: 10s)
[I 250608 09:41:12 performance_metrics:112] Performance metrics initialized (tracemalloc: False, auto_collect: True)
[I 250608 09:41:12 app:242] ✅ Enhanced API v2エンドポイントを登録しました
[I 250608 09:41:12 prometheus_endpoint:58] Prometheus metrics initialized
[I 250608 09:41:12 app:252] ✅ Prometheusエンドポイントを登録しました
[I 250608 09:41:12 app:287] ✅ Redis状態確認APIを登録しました
[I 250608 09:41:12 app:298] ✅ ファイル出力APIを登録しました
[I 250608 09:41:12 run:31] Created PID file for webui: /tmp/pyspider_webui.pid
[I 250608 09:41:13 run:94] Successfully connected to RPC server: http://localhost:23333/
[I 250608 09:41:13 app:62] WebDAV mode has been removed
[I 250608 09:41:13 app:71] webui running on 0.0.0.0:5000
[I 250608 09:41:50 login:175] before_request called for path: /api/v2/projects
[I 250608 09:41:50 login:175] before_request called for path: /api/projects
[I 250608 09:41:50 login:175] before_request called for path: /api/v2/projects
[I 250608 09:41:50 login:175] before_request called for path: /api/projects
[I 250608 09:41:50 login:175] before_request called for path: /api/v2/counter
[I 250608 09:41:50 login:175] before_request called for path: /api/v2/counter
[I 250608 09:41:50 login:175] before_request called for path: /api/v2/projects
[I 250608 09:41:50 login:175] before_request called for path: /api/projects
[I 250608 09:41:50 login:175] before_request called for path: /api/v2/counter
[I 250608 09:41:51 login:175] before_request called for path: /api/v2/tasks
[I 250608 09:41:51 login:175] before_request called for path: /api/v2/tasks
[I 250608 09:41:52 login:175] before_request called for path: /api/v2/metrics
[I 250608 09:41:52 login:175] before_request called for path: /api/v2/tasks
[I 250608 09:41:52 login:175] before_request called for path: /api/components/status
[I 250608 09:41:52 login:175] before_request called for path: /api/v2/metrics
[I 250608 09:41:52 login:175] before_request called for path: /api/components/status
[I 250608 09:41:52 login:175] before_request called for path: /api/v2/metrics
[I 250608 09:41:52 login:175] before_request called for path: /api/components/status
[I 250608 09:41:55 login:175] before_request called for path: /api/v2/projects
[I 250608 09:41:55 login:175] before_request called for path: /api/projects
[I 250608 09:41:56 login:175] before_request called for path: /api/v2/results
[I 250608 09:41:56 login:175] before_request called for path: /api/v2/projects
[I 250608 09:41:56 login:175] before_request called for path: /api/projects
[I 250608 09:41:57 login:175] before_request called for path: /api/v2/results
[I 250608 09:41:57 login:175] before_request called for path: /api/v2/file-output/stats
[E 250608 09:41:57 file_output:111] Error getting file output stats: cannot import name 'g' from 'pyspider.run' (/home/<USER>/workplace/python/pyspiderNX2/pyspider/run.py)
[I 250608 09:41:57 login:175] before_request called for path: /api/v2/file-output/stats
[E 250608 09:41:57 file_output:111] Error getting file output stats: cannot import name 'g' from 'pyspider.run' (/home/<USER>/workplace/python/pyspiderNX2/pyspider/run.py)
