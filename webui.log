{"timestamp": "2025-06-08T09:41:12.089009", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203}
[I 250608 09:41:12 memory_optimizer:57] Memory optimizer initialized with max_memory_percent=80.0%, gc_interval=60s, check_interval=30s, auto_optimize=True
[I 250608 09:41:12 memory_optimizer:69] Memory monitoring started
[I 250608 09:41:12 app:166] CORS enabled with secure settings
[I 250608 09:41:12 app:179] Authentication config loaded: need_auth=True, username=admin
[I 250608 09:41:12 cache:140] Using Redis cache
[I 250608 09:41:12 alert_manager:236] Added alert rule: high_cpu_usage
[I 250608 09:41:12 alert_manager:236] Added alert rule: high_memory_usage
[I 250608 09:41:12 alert_manager:236] Added alert rule: high_disk_usage
[I 250608 09:41:12 alert_manager:236] Added alert rule: too_many_tasks
[I 250608 09:41:12 alert_manager:236] Added alert rule: too_many_errors
[I 250608 09:41:12 metrics:134] Scheduler is not directly accessible. Using default values.
[I 250608 09:41:12 alert_manager:124] Started alert check thread (interval: 60s)
[I 250608 09:41:12 alert_manager:61] Alert manager initialized (auto_check: True)
[I 250608 09:41:12 prometheus_metrics:88] Prometheus metrics initialized with prefix 'pyspider'
[I 250608 09:41:12 performance_metrics:143] Started GC stats collection thread (interval: 60s)
[I 250608 09:41:12 performance_metrics:149] Started process stats collection thread (interval: 10s)
[I 250608 09:41:12 performance_metrics:112] Performance metrics initialized (tracemalloc: False, auto_collect: True)
[I 250608 09:41:12 app:242] ✅ Enhanced API v2エンドポイントを登録しました
[I 250608 09:41:12 prometheus_endpoint:58] Prometheus metrics initialized
[I 250608 09:41:12 app:252] ✅ Prometheusエンドポイントを登録しました
[I 250608 09:41:12 app:287] ✅ Redis状態確認APIを登録しました
[I 250608 09:41:12 app:298] ✅ ファイル出力APIを登録しました
[I 250608 09:41:12 run:31] Created PID file for webui: /tmp/pyspider_webui.pid
[I 250608 09:41:13 run:94] Successfully connected to RPC server: http://localhost:23333/
[I 250608 09:41:13 app:62] WebDAV mode has been removed
[I 250608 09:41:13 app:71] webui running on 0.0.0.0:5000
[I 250608 09:41:50 login:175] before_request called for path: /api/v2/projects
[I 250608 09:41:50 login:175] before_request called for path: /api/projects
[I 250608 09:41:50 login:175] before_request called for path: /api/v2/projects
[I 250608 09:41:50 login:175] before_request called for path: /api/projects
[I 250608 09:41:50 login:175] before_request called for path: /api/v2/counter
[I 250608 09:41:50 login:175] before_request called for path: /api/v2/counter
[I 250608 09:41:50 login:175] before_request called for path: /api/v2/projects
[I 250608 09:41:50 login:175] before_request called for path: /api/projects
[I 250608 09:41:50 login:175] before_request called for path: /api/v2/counter
[I 250608 09:41:51 login:175] before_request called for path: /api/v2/tasks
[I 250608 09:41:51 login:175] before_request called for path: /api/v2/tasks
[I 250608 09:41:52 login:175] before_request called for path: /api/v2/metrics
[I 250608 09:41:52 login:175] before_request called for path: /api/v2/tasks
[I 250608 09:41:52 login:175] before_request called for path: /api/components/status
[I 250608 09:41:52 login:175] before_request called for path: /api/v2/metrics
[I 250608 09:41:52 login:175] before_request called for path: /api/components/status
[I 250608 09:41:52 login:175] before_request called for path: /api/v2/metrics
[I 250608 09:41:52 login:175] before_request called for path: /api/components/status
[I 250608 09:41:55 login:175] before_request called for path: /api/v2/projects
[I 250608 09:41:55 login:175] before_request called for path: /api/projects
[I 250608 09:41:56 login:175] before_request called for path: /api/v2/results
[I 250608 09:41:56 login:175] before_request called for path: /api/v2/projects
[I 250608 09:41:56 login:175] before_request called for path: /api/projects
[I 250608 09:41:57 login:175] before_request called for path: /api/v2/results
[I 250608 09:41:57 login:175] before_request called for path: /api/v2/file-output/stats
[E 250608 09:41:57 file_output:111] Error getting file output stats: cannot import name 'g' from 'pyspider.run' (/home/<USER>/workplace/python/pyspiderNX2/pyspider/run.py)
[I 250608 09:41:57 login:175] before_request called for path: /api/v2/file-output/stats
[E 250608 09:41:57 file_output:111] Error getting file output stats: cannot import name 'g' from 'pyspider.run' (/home/<USER>/workplace/python/pyspiderNX2/pyspider/run.py)
[I 250608 09:42:12 metrics:173] Gauges: {"memory_usage_rss": 100200448, "memory_usage_vms": 425930752, "memory_usage_percent": 0.29725177214121307, "system_memory_total": 33576665088, "system_memory_available": 28193300480, "system_memory_percent": 16.0}
[I 250608 09:42:44 login:175] before_request called for path: /api/v2/projects
[I 250608 09:42:44 login:175] before_request called for path: /api/projects
[I 250608 09:42:44 login:175] before_request called for path: /api/v2/projects
[I 250608 09:42:44 login:175] before_request called for path: /api/projects
[I 250608 09:42:44 login:175] before_request called for path: /api/v2/projects
[I 250608 09:42:44 login:175] before_request called for path: /api/projects
[I 250608 09:42:44 login:175] before_request called for path: /api/v2/projects
[I 250608 09:42:44 login:175] before_request called for path: /api/projects
[I 250608 09:42:44 login:175] before_request called for path: /dashboard-active-tasks
[I 250608 09:42:45 login:175] before_request called for path: /api/v2/tasks
[I 250608 09:42:45 login:175] before_request called for path: /dashboard-active-tasks
[I 250608 09:42:45 login:175] before_request called for path: /api/v2/tasks
[I 250608 09:43:12 metrics:173] Gauges: {"memory_usage_rss": 100593664, "memory_usage_vms": 427286528, "memory_usage_percent": 0.2995939702062647, "system_memory_total": 33576665088, "system_memory_available": 27556077568, "system_memory_percent": 17.9}
[I 250608 09:44:12 metrics:173] Gauges: {"memory_usage_rss": 100593664, "memory_usage_vms": 427286528, "memory_usage_percent": 0.2995939702062647, "system_memory_total": 33576665088, "system_memory_available": 27216007168, "system_memory_percent": 18.9}
[I 250608 09:44:51 login:175] before_request called for path: /dashboard-active-tasks
[I 250608 09:44:51 login:175] before_request called for path: /dashboard-active-tasks
[I 250608 09:44:52 login:175] before_request called for path: /api/v2/projects
[I 250608 09:44:52 login:175] before_request called for path: /api/projects
[I 250608 09:44:52 login:175] before_request called for path: /api/v2/projects
[I 250608 09:44:52 login:175] before_request called for path: /api/projects
[I 250608 09:45:07 login:175] before_request called for path: /api/projects/tinti
[I 250608 09:45:07 login:175] before_request called for path: /api/projects/tinti
[I 250608 09:45:07 login:175] before_request called for path: /api/v2/counter
[I 250608 09:45:07 login:175] before_request called for path: /api/projects/tinti
[I 250608 09:45:07 login:175] before_request called for path: /api/v2/counter
[I 250608 09:45:07 login:175] before_request called for path: /api/projects/tinti
[I 250608 09:45:07 login:175] before_request called for path: /api/projects/tinti
[I 250608 09:45:07 login:175] before_request called for path: /api/projects/tinti
[I 250608 09:45:07 login:175] before_request called for path: /api/debug/tinti
[I 250608 09:45:08 login:175] before_request called for path: /api/debug/tinti
[I 250608 09:45:12 metrics:173] Gauges: {"memory_usage_rss": 100593664, "memory_usage_vms": 427286528, "memory_usage_percent": 0.2995939702062647, "system_memory_total": 33576665088, "system_memory_available": 27091742720, "system_memory_percent": 19.3}
[I 250608 09:45:17 login:175] before_request called for path: /api/v2/counter
[I 250608 09:45:27 login:175] before_request called for path: /api/v2/counter
[I 250608 09:45:31 login:175] before_request called for path: /api/projects/tinti
[I 250608 09:45:31 login:175] before_request called for path: /api/v2/counter
[I 250608 09:45:31 login:175] before_request called for path: /api/projects/tinti
[I 250608 09:45:31 login:175] before_request called for path: /api/projects/tinti
[I 250608 09:45:31 login:175] before_request called for path: /api/v2/counter
[I 250608 09:45:31 login:175] before_request called for path: /api/projects/tinti
[I 250608 09:45:31 login:175] before_request called for path: /api/v2/counter
[I 250608 09:45:31 login:175] before_request called for path: /api/projects/tinti
[I 250608 09:45:31 login:175] before_request called for path: /api/projects/tinti
[I 250608 09:45:31 login:175] before_request called for path: /api/debug/tinti
[I 250608 09:45:31 login:175] before_request called for path: /api/projects/tinti
[I 250608 09:45:31 login:175] before_request called for path: /api/debug/tinti
[I 250608 09:45:31 login:175] before_request called for path: /api/debug/tinti
[I 250608 09:45:39 login:175] before_request called for path: /dashboard-active-tasks
[I 250608 09:45:39 login:175] before_request called for path: /dashboard-active-tasks
[I 250608 09:45:39 login:175] before_request called for path: /api/v2/projects
[I 250608 09:45:39 login:175] before_request called for path: /api/projects
[I 250608 09:45:39 login:175] before_request called for path: /api/v2/projects
[I 250608 09:45:39 login:175] before_request called for path: /api/projects
[I 250608 09:45:41 login:175] before_request called for path: /api/v2/counter
[I 250608 09:45:42 login:175] before_request called for path: /api/projects/tinti
[I 250608 09:45:42 login:175] before_request called for path: /api/projects/tinti
[I 250608 09:45:42 login:175] before_request called for path: /api/v2/counter
[I 250608 09:45:42 login:175] before_request called for path: /api/projects/tinti
[I 250608 09:45:42 login:175] before_request called for path: /api/v2/counter
[I 250608 09:45:42 login:175] before_request called for path: /api/projects/tinti
[I 250608 09:45:42 login:175] before_request called for path: /api/projects/tinti
[I 250608 09:45:42 login:175] before_request called for path: /api/projects/tinti
[I 250608 09:45:42 login:175] before_request called for path: /api/debug/tinti
[I 250608 09:45:42 login:175] before_request called for path: /api/debug/tinti
[I 250608 09:45:51 login:175] before_request called for path: /api/v2/counter
[I 250608 09:45:52 login:175] before_request called for path: /api/v2/counter
[I 250608 09:46:01 login:175] before_request called for path: /api/v2/counter
[I 250608 09:46:02 login:175] before_request called for path: /api/v2/counter
[I 250608 09:46:11 login:175] before_request called for path: /api/v2/counter
[I 250608 09:46:12 metrics:173] Gauges: {"memory_usage_rss": 100724736, "memory_usage_vms": 427286528, "memory_usage_percent": 0.29998433655044, "system_memory_total": 33576665088, "system_memory_available": 26822479872, "system_memory_percent": 20.1}
[I 250608 09:46:12 login:175] before_request called for path: /api/v2/counter
[I 250608 09:46:21 login:175] before_request called for path: /api/v2/counter
[I 250608 09:46:22 login:175] before_request called for path: /api/v2/counter
[I 250608 09:46:31 login:175] before_request called for path: /api/v2/counter
[I 250608 09:46:32 login:175] before_request called for path: /api/v2/counter
[I 250608 09:46:42 login:175] before_request called for path: /api/v2/counter
[I 250608 09:46:52 login:175] before_request called for path: /api/v2/counter
[I 250608 09:47:08 login:175] before_request called for path: /api/v2/counter
[I 250608 09:47:08 login:175] before_request called for path: /api/v2/counter
[I 250608 09:47:12 metrics:173] Gauges: {"memory_usage_rss": 100724736, "memory_usage_vms": 427286528, "memory_usage_percent": 0.29998433655044, "system_memory_total": 33576665088, "system_memory_available": 26934910976, "system_memory_percent": 19.8}
[I 250608 09:48:08 login:175] before_request called for path: /api/v2/counter
[I 250608 09:48:08 login:175] before_request called for path: /api/v2/counter
[I 250608 09:48:12 metrics:173] Gauges: {"memory_usage_rss": 100724736, "memory_usage_vms": 427286528, "memory_usage_percent": 0.29998433655044, "system_memory_total": 33576665088, "system_memory_available": 26820808704, "system_memory_percent": 20.1}
[I 250608 09:48:13 login:175] before_request called for path: /api/v2/projects
[I 250608 09:48:13 login:175] before_request called for path: /api/projects
[I 250608 09:48:13 login:175] before_request called for path: /api/v2/counter
[I 250608 09:48:13 login:175] before_request called for path: /api/v2/projects
[I 250608 09:48:13 login:175] before_request called for path: /api/projects
[I 250608 09:48:13 login:175] before_request called for path: /api/v2/counter
[I 250608 09:48:13 login:175] before_request called for path: /api/v2/projects
[I 250608 09:48:13 login:175] before_request called for path: /api/projects
[I 250608 09:48:13 login:175] before_request called for path: /api/v2/counter
[I 250608 09:48:20 login:175] before_request called for path: /api/v2/projects
[I 250608 09:48:20 login:175] before_request called for path: /api/projects
[I 250608 09:48:20 login:175] before_request called for path: /api/v2/counter
[I 250608 09:48:20 login:175] before_request called for path: /api/v2/projects
[I 250608 09:48:20 login:175] before_request called for path: /api/projects
[I 250608 09:48:20 login:175] before_request called for path: /api/v2/counter
[I 250608 09:48:22 login:175] before_request called for path: /api/v2/tasks
[I 250608 09:48:22 login:175] before_request called for path: /api/v2/tasks
[I 250608 09:48:24 login:175] before_request called for path: /api/v2/metrics
[I 250608 09:48:24 login:175] before_request called for path: /api/components/status
[I 250608 09:48:24 login:175] before_request called for path: /api/v2/metrics
[I 250608 09:48:24 login:175] before_request called for path: /api/components/status
[I 250608 09:48:26 login:175] before_request called for path: /dashboard-active-tasks
[I 250608 09:48:26 login:175] before_request called for path: /dashboard-active-tasks
[I 250608 09:48:26 login:175] before_request called for path: /api/v2/projects
[I 250608 09:48:26 login:175] before_request called for path: /api/projects
[I 250608 09:48:26 login:175] before_request called for path: /api/v2/projects
[I 250608 09:48:26 login:175] before_request called for path: /api/projects
[I 250608 09:48:29 login:175] before_request called for path: /api/v2/projects/tinti
[I 250608 09:48:29 login:175] before_request called for path: /api/v2/counter
[I 250608 09:48:29 login:175] before_request called for path: /api/v2/counter
[I 250608 09:48:29 login:175] before_request called for path: /api/v2/projects/tinti
[I 250608 09:48:29 login:175] before_request called for path: /api/projects/tinti
[I 250608 09:48:29 login:175] before_request called for path: /api/projects/tinti
[I 250608 09:48:29 login:175] before_request called for path: /api/debug/tinti
[I 250608 09:48:29 login:175] before_request called for path: /api/debug/tinti
[I 250608 09:48:39 login:175] before_request called for path: /api/v2/counter
[I 250608 09:48:41 login:175] before_request called for path: /api/v2/projects
[I 250608 09:48:41 login:175] before_request called for path: /api/projects
[I 250608 09:48:49 login:175] before_request called for path: /api/v2/counter
[I 250608 09:48:55 login:175] before_request called for path: /api/v2/projects
[I 250608 09:48:59 login:175] before_request called for path: /api/v2/counter
[I 250608 09:49:01 login:175] before_request called for path: /api/v2/counter
[I 250608 09:49:08 login:175] before_request called for path: /api/v2/counter
[I 250608 09:49:09 login:175] before_request called for path: /api/v2/counter
[I 250608 09:49:10 login:175] before_request called for path: /api/v2/projects
[I 250608 09:49:10 api_utils:82] Authentication disabled, skipping auth check for create_project_v2
[W 250608 09:49:10 projects:150] Failed to trigger on_start for project test_project: <Fault 1: '<class \'Exception\'>:method "trigger_on_start" is not supported'>
[I 250608 09:49:12 metrics:173] Gauges: {"memory_usage_rss": 100855808, "memory_usage_vms": 427425792, "memory_usage_percent": 0.30037470289461526, "system_memory_total": 33576665088, "system_memory_available": 26937053184, "system_memory_percent": 19.8}
[I 250608 09:49:19 login:175] before_request called for path: /api/v2/counter
[I 250608 09:49:22 login:175] before_request called for path: /api/v2/projects/test_project
[I 250608 09:49:27 login:175] before_request called for path: /api/v2/counter
[I 250608 09:49:27 login:175] before_request called for path: /api/v2/counter
[I 250608 09:49:27 login:175] before_request called for path: /api/v2/projects/tinti
[I 250608 09:49:28 login:175] before_request called for path: /api/v2/counter
[I 250608 09:49:28 login:175] before_request called for path: /api/v2/projects/tinti
[I 250608 09:49:28 login:175] before_request called for path: /api/v2/projects/tinti
[I 250608 09:49:28 login:175] before_request called for path: /api/projects/tinti
[I 250608 09:49:28 login:175] before_request called for path: /api/projects/tinti
[I 250608 09:49:28 login:175] before_request called for path: /api/projects/tinti
[I 250608 09:49:28 login:175] before_request called for path: /api/debug/tinti
[I 250608 09:49:28 login:175] before_request called for path: /api/debug/tinti
[I 250608 09:49:28 login:175] before_request called for path: /api/debug/tinti
[I 250608 09:49:32 login:175] before_request called for path: /api/v2/projects/test_project
[I 250608 09:49:37 login:175] before_request called for path: /api/v2/counter
[I 250608 09:49:38 login:175] before_request called for path: /api/v2/counter
[I 250608 09:49:41 login:175] before_request called for path: /api/v2/counter
[I 250608 09:49:48 login:175] before_request called for path: /api/v2/counter
[I 250608 09:49:51 login:175] before_request called for path: /api/v2/counter
[I 250608 09:49:55 login:175] before_request called for path: /api/v2/counter
[I 250608 09:49:55 login:175] before_request called for path: /api/v2/projects/tinti
[I 250608 09:49:55 login:175] before_request called for path: /api/v2/counter
[I 250608 09:49:55 login:175] before_request called for path: /api/v2/projects/tinti
[I 250608 09:49:55 login:175] before_request called for path: /api/v2/counter
[I 250608 09:49:55 login:175] before_request called for path: /api/v2/projects/tinti
[I 250608 09:49:55 login:175] before_request called for path: /api/projects/tinti
[I 250608 09:49:55 login:175] before_request called for path: /api/projects/tinti
[I 250608 09:49:55 login:175] before_request called for path: /api/projects/tinti
[I 250608 09:49:55 login:175] before_request called for path: /api/debug/tinti
[I 250608 09:49:55 login:175] before_request called for path: /api/debug/tinti
[I 250608 09:49:55 login:175] before_request called for path: /api/debug/tinti
[I 250608 09:50:01 login:175] before_request called for path: /api/v2/counter
[I 250608 09:50:05 login:175] before_request called for path: /api/v2/counter
[I 250608 09:50:11 login:175] before_request called for path: /api/v2/counter
[I 250608 09:50:12 metrics:173] Gauges: {"memory_usage_rss": 100986880, "memory_usage_vms": 427425792, "memory_usage_percent": 0.30037470289461526, "system_memory_total": 33576665088, "system_memory_available": 27046666240, "system_memory_percent": 19.4}
[I 250608 09:50:15 login:175] before_request called for path: /api/v2/counter
[I 250608 09:50:21 login:175] before_request called for path: /api/v2/counter
[I 250608 09:50:25 login:175] before_request called for path: /api/v2/counter
[I 250608 09:50:31 login:175] before_request called for path: /api/v2/counter
[I 250608 09:50:35 login:175] before_request called for path: /api/v2/counter
[I 250608 09:50:45 login:175] before_request called for path: /api/v2/counter
[I 250608 09:50:55 login:175] before_request called for path: /api/v2/counter
[I 250608 09:51:01 login:175] before_request called for path: /api/v2/counter
[W 250608 09:51:01 index:329] No counter data found for project: test_project
[I 250608 09:51:01 login:175] before_request called for path: /api/v2/projects/test_project
[I 250608 09:51:01 login:175] before_request called for path: /api/v2/counter
[W 250608 09:51:01 index:329] No counter data found for project: test_project
[I 250608 09:51:01 login:175] before_request called for path: /api/v2/projects/test_project
[I 250608 09:51:01 login:175] before_request called for path: /api/v2/counter
[W 250608 09:51:01 index:329] No counter data found for project: test_project
[I 250608 09:51:01 login:175] before_request called for path: /api/v2/projects/test_project
[I 250608 09:51:05 login:175] before_request called for path: /api/v2/counter
[I 250608 09:51:08 login:175] before_request called for path: /api/v2/counter
[I 250608 09:51:11 login:175] before_request called for path: /api/v2/counter
[W 250608 09:51:11 index:329] No counter data found for project: test_project
[I 250608 09:51:12 metrics:173] Gauges: {"memory_usage_rss": 101117952, "memory_usage_vms": 427425792, "memory_usage_percent": 0.3011554355829658, "system_memory_total": 33576665088, "system_memory_available": 26884886528, "system_memory_percent": 19.9}
[I 250608 09:51:15 login:175] before_request called for path: /api/v2/counter
[I 250608 09:51:21 login:175] before_request called for path: /api/v2/counter
[W 250608 09:51:21 index:329] No counter data found for project: test_project
[I 250608 09:51:25 login:175] before_request called for path: /api/v2/counter
[I 250608 09:51:31 login:175] before_request called for path: /api/v2/counter
[W 250608 09:51:31 index:329] No counter data found for project: test_project
[I 250608 09:51:35 login:175] before_request called for path: /api/v2/counter
[I 250608 09:51:41 login:175] before_request called for path: /api/v2/counter
[W 250608 09:51:41 index:329] No counter data found for project: test_project
[I 250608 09:51:45 login:175] before_request called for path: /api/v2/counter
[I 250608 09:51:51 login:175] before_request called for path: /api/v2/counter
[W 250608 09:51:51 index:329] No counter data found for project: test_project
[I 250608 09:51:55 login:175] before_request called for path: /api/v2/counter
[I 250608 09:52:01 login:175] before_request called for path: /api/v2/counter
[W 250608 09:52:01 index:329] No counter data found for project: test_project
[I 250608 09:52:08 login:175] before_request called for path: /api/v2/counter
[I 250608 09:52:08 login:175] before_request called for path: /api/v2/counter
[I 250608 09:52:12 metrics:173] Gauges: {"memory_usage_rss": 101117952, "memory_usage_vms": 427425792, "memory_usage_percent": 0.3011554355829658, "system_memory_total": 33576665088, "system_memory_available": 28060319744, "system_memory_percent": 16.4}
[I 250608 09:52:25 login:175] before_request called for path: /api/v2/counter
[W 250608 09:52:25 index:329] No counter data found for project: test_project
[I 250608 09:52:26 login:175] before_request called for path: /api/v2/counter
[I 250608 09:52:29 login:175] before_request called for path: /api/v2/counter
[W 250608 09:52:29 index:329] No counter data found for project: test_project
[I 250608 09:52:29 login:175] before_request called for path: /api/v2/projects/test_project
[I 250608 09:52:29 login:175] before_request called for path: /api/v2/counter
[W 250608 09:52:29 index:329] No counter data found for project: test_project
[I 250608 09:52:29 login:175] before_request called for path: /api/v2/counter
[W 250608 09:52:29 index:329] No counter data found for project: test_project
[I 250608 09:52:29 login:175] before_request called for path: /api/v2/projects/test_project
[I 250608 09:52:29 login:175] before_request called for path: /api/v2/projects/test_project
[I 250608 09:52:38 login:175] before_request called for path: /dashboard-active-tasks
[I 250608 09:52:38 login:175] before_request called for path: /dashboard-active-tasks
[I 250608 09:52:40 login:175] before_request called for path: /api/v2/projects
[I 250608 09:52:40 login:175] before_request called for path: /api/projects
[I 250608 09:52:40 login:175] before_request called for path: /api/v2/projects
[I 250608 09:52:40 login:175] before_request called for path: /api/projects
[I 250608 09:52:42 login:175] before_request called for path: /api/v2/counter
[I 250608 09:52:42 login:175] before_request called for path: /api/v2/counter
[I 250608 09:52:42 login:175] before_request called for path: /api/v2/projects/tinti
[I 250608 09:52:42 login:175] before_request called for path: /api/v2/projects/tinti
[I 250608 09:52:42 login:175] before_request called for path: /api/projects/tinti
[I 250608 09:52:42 login:175] before_request called for path: /api/projects/tinti
[I 250608 09:52:42 login:175] before_request called for path: /api/debug/tinti
[I 250608 09:52:42 login:175] before_request called for path: /api/debug/tinti
[I 250608 09:52:52 login:175] before_request called for path: /api/v2/counter
[I 250608 09:53:02 login:175] before_request called for path: /api/v2/counter
[I 250608 09:53:08 login:175] before_request called for path: /api/v2/counter
[I 250608 09:53:11 login:175] before_request called for path: /api/v2/counter
[I 250608 09:53:12 metrics:173] Gauges: {"memory_usage_rss": 101117952, "memory_usage_vms": 427425792, "memory_usage_percent": 0.3011554355829658, "system_memory_total": 33576665088, "system_memory_available": 26754416640, "system_memory_percent": 20.3}
[I 250608 09:53:21 login:175] before_request called for path: /api/v2/counter
[I 250608 09:53:31 login:175] before_request called for path: /api/v2/counter
[I 250608 09:53:37 login:175] before_request called for path: /api/v2/projects
[I 250608 09:53:37 login:175] before_request called for path: /api/v2/projects
[I 250608 09:53:37 login:175] before_request called for path: /api/projects
[I 250608 09:53:37 login:175] before_request called for path: /api/v2/results/test_project
[I 250608 09:53:38 login:175] before_request called for path: /api/v2/results
[I 250608 09:53:38 login:175] before_request called for path: /api/v2/projects
[I 250608 09:53:38 login:175] before_request called for path: /api/projects
[I 250608 09:53:38 login:175] before_request called for path: /api/v2/results
[I 250608 09:53:43 login:175] before_request called for path: /dashboard-active-tasks
[I 250608 09:53:43 login:175] before_request called for path: /dashboard-active-tasks
[I 250608 09:53:43 login:175] before_request called for path: /api/v2/projects
[I 250608 09:53:43 login:175] before_request called for path: /api/projects
[I 250608 09:53:43 login:175] before_request called for path: /api/v2/projects
[I 250608 09:53:43 login:175] before_request called for path: /api/projects
[I 250608 09:53:52 login:175] before_request called for path: /debug/tinti
[I 250608 09:53:52 login:60] Authorization header: None
[I 250608 09:53:52 login:77] No Authorization header found
[I 250608 09:53:52 login:175] before_request called for path: /debug/tinti
[I 250608 09:53:52 login:60] Authorization header: None
[I 250608 09:53:52 login:77] No Authorization header found
[I 250608 09:53:52 login:175] before_request called for path: /static/debug.min.css
[I 250608 09:53:52 login:175] before_request called for path: /static/debug.min.js
[I 250608 09:53:57 login:175] before_request called for path: /dashboard-active-tasks
[I 250608 09:53:57 login:175] before_request called for path: /dashboard-active-tasks
[I 250608 09:53:57 login:175] before_request called for path: /api/v2/projects
[I 250608 09:53:57 login:175] before_request called for path: /api/projects
[I 250608 09:53:57 login:175] before_request called for path: /api/v2/projects
[I 250608 09:53:57 login:175] before_request called for path: /api/projects
[I 250608 09:54:01 login:175] before_request called for path: /api/v2/task/_on_get_info.json
[I 250608 09:54:01 login:175] before_request called for path: /api/v2/task/_on_get_info.json
[I 250608 09:54:01 login:175] before_request called for path: /api/task/_on_get_info.json
[I 250608 09:54:01 login:175] before_request called for path: /api/task/_on_get_info.json
[I 250608 09:54:02 login:175] before_request called for path: /api/v2/task/_on_get_info.json
[I 250608 09:54:02 login:175] before_request called for path: /api/v2/task/_on_get_info.json
[I 250608 09:54:02 login:175] before_request called for path: /api/task/_on_get_info.json
[I 250608 09:54:02 login:175] before_request called for path: /api/task/_on_get_info.json
[I 250608 09:54:02 login:175] before_request called for path: /api/task/_on_get_info.json
[I 250608 09:54:02 login:175] before_request called for path: /api/task/_on_get_info.json
[I 250608 09:54:08 login:175] before_request called for path: /api/v2/counter
[I 250608 09:54:09 login:175] before_request called for path: /dashboard-active-tasks
[I 250608 09:54:09 login:175] before_request called for path: /dashboard-active-tasks
[I 250608 09:54:09 login:175] before_request called for path: /api/v2/projects
[I 250608 09:54:09 login:175] before_request called for path: /api/projects
[I 250608 09:54:09 login:175] before_request called for path: /api/v2/projects
[I 250608 09:54:09 login:175] before_request called for path: /api/projects
[I 250608 09:54:12 metrics:173] Gauges: {"memory_usage_rss": 102035456, "memory_usage_vms": 427425792, "memory_usage_percent": 0.3011554355829658, "system_memory_total": 33576665088, "system_memory_available": 27178639360, "system_memory_percent": 19.1}
[I 250608 09:54:16 login:175] before_request called for path: /dashboard-active-tasks
[I 250608 09:54:16 login:175] before_request called for path: /dashboard-active-tasks
[I 250608 09:54:16 login:175] before_request called for path: /api/v2/projects
[I 250608 09:54:16 login:175] before_request called for path: /api/projects
[I 250608 09:54:16 login:175] before_request called for path: /api/v2/projects
[I 250608 09:54:16 login:175] before_request called for path: /api/projects
[I 250608 09:54:59 login:175] before_request called for path: /api/v2/counter
[W 250608 09:54:59 index:329] No counter data found for project: test_project
[I 250608 09:54:59 login:175] before_request called for path: /api/v2/projects/test_project
[I 250608 09:54:59 login:175] before_request called for path: /api/v2/counter
[W 250608 09:54:59 index:329] No counter data found for project: test_project
[I 250608 09:54:59 login:175] before_request called for path: /api/v2/projects/test_project
[I 250608 09:54:59 login:175] before_request called for path: /api/v2/counter
[W 250608 09:54:59 index:329] No counter data found for project: test_project
[I 250608 09:54:59 login:175] before_request called for path: /api/v2/projects/test_project
[I 250608 09:55:06 login:175] before_request called for path: /api/v2/results/test_project
[I 250608 09:55:08 login:175] before_request called for path: /dashboard-active-tasks
[I 250608 09:55:08 login:175] before_request called for path: /dashboard-active-tasks
[I 250608 09:55:08 login:175] before_request called for path: /api/v2/projects
[I 250608 09:55:08 login:175] before_request called for path: /api/projects
[I 250608 09:55:08 login:175] before_request called for path: /api/v2/counter
[I 250608 09:55:08 login:175] before_request called for path: /api/v2/projects
[I 250608 09:55:08 login:175] before_request called for path: /api/projects
[I 250608 09:55:12 login:175] before_request called for path: /api/v2/projects/tinti
[I 250608 09:55:12 login:175] before_request called for path: /api/v2/counter
[I 250608 09:55:12 login:175] before_request called for path: /api/v2/counter
[I 250608 09:55:12 login:175] before_request called for path: /api/v2/projects/tinti
[I 250608 09:55:12 login:175] before_request called for path: /api/projects/tinti
[I 250608 09:55:12 login:175] before_request called for path: /api/projects/tinti
[I 250608 09:55:12 metrics:173] Gauges: {"memory_usage_rss": 102035456, "memory_usage_vms": 427999232, "memory_usage_percent": 0.3038879999921927, "system_memory_total": 33576665088, "system_memory_available": 27165097984, "system_memory_percent": 19.1}
[I 250608 09:55:12 login:175] before_request called for path: /api/debug/tinti
[I 250608 09:55:12 login:175] before_request called for path: /api/debug/tinti
[I 250608 09:55:21 login:175] before_request called for path: /api/v2/projects/test_project
[I 250608 09:55:22 login:175] before_request called for path: /api/v2/counter
[I 250608 09:55:32 login:175] before_request called for path: /api/v2/counter
[I 250608 09:55:42 login:175] before_request called for path: /api/v2/counter
[I 250608 09:55:52 login:175] before_request called for path: /api/v2/counter
[I 250608 09:55:58 login:175] before_request called for path: /api/v2/counter
[W 250608 09:55:58 index:329] No counter data found for project: test_project
[I 250608 09:55:58 login:175] before_request called for path: /api/v2/projects/test_project
[I 250608 09:55:58 login:175] before_request called for path: /api/v2/counter
[W 250608 09:55:58 index:329] No counter data found for project: test_project
[I 250608 09:55:58 login:175] before_request called for path: /api/v2/projects/test_project
[I 250608 09:55:58 login:175] before_request called for path: /api/v2/counter
[W 250608 09:55:58 index:329] No counter data found for project: test_project
[I 250608 09:55:58 login:175] before_request called for path: /api/v2/projects/test_project
[I 250608 09:56:01 login:175] before_request called for path: /api/v2/results/test_project
[I 250608 09:56:02 login:175] before_request called for path: /api/v2/counter
[I 250608 09:56:03 login:175] before_request called for path: /dashboard-active-tasks
[I 250608 09:56:03 login:175] before_request called for path: /dashboard-active-tasks
[I 250608 09:56:03 login:175] before_request called for path: /api/v2/projects
[I 250608 09:56:03 login:175] before_request called for path: /api/projects
[I 250608 09:56:03 login:175] before_request called for path: /api/v2/projects
[I 250608 09:56:03 login:175] before_request called for path: /api/projects
[I 250608 09:56:06 login:175] before_request called for path: /api/v2/projects/tinti
[I 250608 09:56:06 login:175] before_request called for path: /api/v2/counter
[I 250608 09:56:06 login:175] before_request called for path: /api/v2/counter
[I 250608 09:56:06 login:175] before_request called for path: /api/v2/projects/tinti
[I 250608 09:56:06 login:175] before_request called for path: /api/projects/tinti
[I 250608 09:56:06 login:175] before_request called for path: /api/projects/tinti
[I 250608 09:56:06 login:175] before_request called for path: /api/debug/tinti
[I 250608 09:56:06 login:175] before_request called for path: /api/debug/tinti
[I 250608 09:56:08 login:175] before_request called for path: /api/v2/counter
[I 250608 09:56:12 metrics:173] Gauges: {"memory_usage_rss": 102035456, "memory_usage_vms": 427999232, "memory_usage_percent": 0.3038879999921927, "system_memory_total": 33576665088, "system_memory_available": 26596528128, "system_memory_percent": 20.8}
[I 250608 09:56:12 login:175] before_request called for path: /api/v2/counter
[I 250608 09:56:16 login:175] before_request called for path: /api/v2/counter
[I 250608 09:56:22 login:175] before_request called for path: /api/v2/counter
[I 250608 09:56:24 login:175] before_request called for path: /api/v2/projects
[I 250608 09:56:24 login:175] before_request called for path: /api/projects
[I 250608 09:56:24 login:175] before_request called for path: /api/v2/projects
[I 250608 09:56:24 login:175] before_request called for path: /api/projects
[I 250608 09:56:24 login:175] before_request called for path: /api/v2/counter
[I 250608 09:56:24 login:175] before_request called for path: /api/v2/counter
[I 250608 09:56:24 login:175] before_request called for path: /api/v2/projects
[I 250608 09:56:24 login:175] before_request called for path: /api/projects
[I 250608 09:56:24 login:175] before_request called for path: /api/v2/counter
[I 250608 09:56:26 login:175] before_request called for path: /api/v2/counter
[I 250608 09:56:27 login:175] before_request called for path: /api/v2/counter
[W 250608 09:56:27 index:329] No counter data found for project: test_project
[I 250608 09:56:27 login:175] before_request called for path: /api/v2/projects/test_project
[I 250608 09:56:27 login:175] before_request called for path: /api/v2/counter
[W 250608 09:56:27 index:329] No counter data found for project: test_project
[I 250608 09:56:27 login:175] before_request called for path: /api/v2/projects/test_project
[I 250608 09:56:32 login:175] before_request called for path: /dashboard-active-tasks
[I 250608 09:56:32 login:175] before_request called for path: /dashboard-active-tasks
[I 250608 09:56:32 login:175] before_request called for path: /api/v2/projects
[I 250608 09:56:32 login:175] before_request called for path: /api/projects
[I 250608 09:56:32 login:175] before_request called for path: /api/v2/projects
[I 250608 09:56:32 login:175] before_request called for path: /api/projects
[I 250608 09:56:35 login:175] before_request called for path: /api/v2/projects/tinti
[I 250608 09:56:35 login:175] before_request called for path: /api/v2/counter
[I 250608 09:56:35 login:175] before_request called for path: /api/v2/counter
[I 250608 09:56:35 login:175] before_request called for path: /api/v2/projects/tinti
[I 250608 09:56:35 login:175] before_request called for path: /api/projects/tinti
[I 250608 09:56:35 login:175] before_request called for path: /api/projects/tinti
[I 250608 09:56:35 login:175] before_request called for path: /api/debug/tinti
[I 250608 09:56:35 login:175] before_request called for path: /api/debug/tinti
[I 250608 09:56:36 login:175] before_request called for path: /api/v2/counter
[I 250608 09:56:45 login:175] before_request called for path: /api/v2/counter
[I 250608 09:56:46 login:175] before_request called for path: /api/v2/counter
[I 250608 09:56:55 login:175] before_request called for path: /api/v2/counter
[I 250608 09:56:56 login:175] before_request called for path: /api/v2/counter
[I 250608 09:57:05 login:175] before_request called for path: /api/v2/counter
[I 250608 09:57:06 login:175] before_request called for path: /api/v2/counter
[I 250608 09:57:08 login:175] before_request called for path: /api/v2/counter
[I 250608 09:57:08 login:175] before_request called for path: /api/v2/counter
[I 250608 09:57:12 metrics:173] Gauges: {"memory_usage_rss": 102035456, "memory_usage_vms": 427999232, "memory_usage_percent": 0.3038879999921927, "system_memory_total": 33576665088, "system_memory_available": 26527633408, "system_memory_percent": 21.0}
[I 250608 09:57:15 login:175] before_request called for path: /api/v2/counter
[I 250608 09:57:16 login:175] before_request called for path: /api/v2/counter
[I 250608 09:57:25 login:175] before_request called for path: /api/v2/counter
[I 250608 09:57:35 login:175] before_request called for path: /api/v2/counter
[I 250608 09:57:43 login:175] before_request called for path: /api/v2/counter
[W 250608 09:57:43 index:329] No counter data found for project: test_project
[I 250608 09:57:43 login:175] before_request called for path: /api/v2/projects/test_project
[I 250608 09:57:43 login:175] before_request called for path: /api/v2/counter
[W 250608 09:57:43 index:329] No counter data found for project: test_project
[I 250608 09:57:43 login:175] before_request called for path: /api/v2/counter
[W 250608 09:57:43 index:329] No counter data found for project: test_project
[I 250608 09:57:43 login:175] before_request called for path: /api/v2/projects/test_project
[I 250608 09:57:43 login:175] before_request called for path: /api/v2/projects/test_project
[I 250608 09:57:45 login:175] before_request called for path: /api/v2/counter
[I 250608 09:57:47 login:175] before_request called for path: /dashboard-active-tasks
[I 250608 09:57:47 login:175] before_request called for path: /dashboard-active-tasks
[I 250608 09:57:47 login:175] before_request called for path: /api/v2/projects
[I 250608 09:57:47 login:175] before_request called for path: /api/projects
[I 250608 09:57:47 login:175] before_request called for path: /api/v2/projects
[I 250608 09:57:47 login:175] before_request called for path: /api/projects
[I 250608 09:57:50 login:175] before_request called for path: /api/v2/counter
[I 250608 09:57:50 login:175] before_request called for path: /api/v2/projects/tinti
[I 250608 09:57:50 login:175] before_request called for path: /api/v2/counter
[I 250608 09:57:50 login:175] before_request called for path: /api/v2/projects/tinti
[I 250608 09:57:50 login:175] before_request called for path: /api/projects/tinti
[I 250608 09:57:50 login:175] before_request called for path: /api/debug/tinti
[I 250608 09:57:50 login:175] before_request called for path: /api/projects/tinti
[I 250608 09:57:50 login:175] before_request called for path: /api/debug/tinti
[I 250608 09:57:55 login:175] before_request called for path: /api/v2/counter
[I 250608 09:58:00 login:175] before_request called for path: /api/v2/counter
[I 250608 09:58:03 login:175] before_request called for path: /api/v2/projects/test_project
[I 250608 09:58:06 login:175] before_request called for path: /api/v2/counter
[I 250608 09:58:06 login:175] before_request called for path: /api/v2/counter
[I 250608 09:58:06 login:175] before_request called for path: /api/v2/projects/tinti
[I 250608 09:58:06 login:175] before_request called for path: /api/v2/counter
[I 250608 09:58:06 login:175] before_request called for path: /api/v2/counter
[I 250608 09:58:06 login:175] before_request called for path: /api/v2/projects/tinti
[I 250608 09:58:06 login:175] before_request called for path: /api/v2/projects/tinti
[I 250608 09:58:07 login:175] before_request called for path: /api/projects/tinti
[I 250608 09:58:07 login:175] before_request called for path: /api/projects/tinti
[I 250608 09:58:07 login:175] before_request called for path: /api/debug/tinti
[I 250608 09:58:07 login:175] before_request called for path: /api/projects/tinti
[I 250608 09:58:07 login:175] before_request called for path: /api/debug/tinti
[I 250608 09:58:07 login:175] before_request called for path: /api/debug/tinti
[I 250608 09:58:08 login:175] before_request called for path: /api/v2/counter
[I 250608 09:58:08 login:175] before_request called for path: /api/v2/counter
[I 250608 09:58:08 login:175] before_request called for path: /api/v2/counter
[I 250608 09:58:12 metrics:173] Gauges: {"memory_usage_rss": 102035456, "memory_usage_vms": 427999232, "memory_usage_percent": 0.3038879999921927, "system_memory_total": 33576665088, "system_memory_available": 26895572992, "system_memory_percent": 19.9}
[I 250608 09:58:15 login:175] before_request called for path: /api/v2/counter
[I 250608 09:58:16 login:175] before_request called for path: /api/v2/counter
[I 250608 09:58:27 login:175] before_request called for path: /api/v2/counter
[I 250608 09:58:27 login:175] before_request called for path: /api/v2/counter
[I 250608 09:58:27 login:175] before_request called for path: /api/v2/counter
[I 250608 09:58:27 login:175] before_request called for path: /api/v2/counter
[I 250608 09:58:34 login:175] before_request called for path: /api/v2/counter
[I 250608 09:58:34 login:175] before_request called for path: /api/v2/projects/tinti
[I 250608 09:58:34 login:175] before_request called for path: /api/v2/counter
[I 250608 09:58:35 login:175] before_request called for path: /api/v2/projects/tinti
[I 250608 09:58:35 login:175] before_request called for path: /api/v2/counter
[I 250608 09:58:35 login:175] before_request called for path: /api/v2/projects/tinti
[I 250608 09:58:35 login:175] before_request called for path: /api/projects/tinti
[I 250608 09:58:35 login:175] before_request called for path: /api/projects/tinti
[I 250608 09:58:35 login:175] before_request called for path: /api/debug/tinti
[I 250608 09:58:35 login:175] before_request called for path: /api/projects/tinti
[I 250608 09:58:35 login:175] before_request called for path: /api/debug/tinti
[I 250608 09:58:35 login:175] before_request called for path: /api/debug/tinti
[I 250608 09:58:45 login:175] before_request called for path: /api/v2/counter
[W 250608 09:58:45 index:329] No counter data found for project: test_project
[I 250608 09:58:45 login:175] before_request called for path: /api/v2/projects/test_project
[I 250608 09:58:45 login:175] before_request called for path: /api/v2/counter
[W 250608 09:58:45 index:329] No counter data found for project: test_project
[I 250608 09:58:45 login:175] before_request called for path: /api/v2/projects/test_project
[I 250608 09:58:45 login:175] before_request called for path: /api/v2/counter
[W 250608 09:58:45 index:329] No counter data found for project: test_project
[I 250608 09:58:45 login:175] before_request called for path: /api/v2/projects/test_project
[I 250608 09:58:45 login:175] before_request called for path: /api/v2/counter
[I 250608 09:58:48 login:175] before_request called for path: /api/v2/counter
[W 250608 09:58:48 index:329] No counter data found for project: test_project
[I 250608 09:58:48 login:175] before_request called for path: /api/v2/projects/test_project
[I 250608 09:58:48 login:175] before_request called for path: /api/v2/counter
[W 250608 09:58:48 index:329] No counter data found for project: test_project
[I 250608 09:58:48 login:175] before_request called for path: /api/v2/projects/test_project
[I 250608 09:58:48 login:175] before_request called for path: /api/v2/counter
[W 250608 09:58:48 index:329] No counter data found for project: test_project
[I 250608 09:58:48 login:175] before_request called for path: /api/v2/projects/test_project
[I 250608 09:58:52 login:175] before_request called for path: /api/v2/counter
[W 250608 09:58:52 index:329] No counter data found for project: test_project
[I 250608 09:58:52 login:175] before_request called for path: /api/v2/projects/test_project
[I 250608 09:58:52 login:175] before_request called for path: /api/v2/counter
[W 250608 09:58:52 index:329] No counter data found for project: test_project
[I 250608 09:58:52 login:175] before_request called for path: /api/v2/projects/test_project
[I 250608 09:58:52 login:175] before_request called for path: /api/v2/counter
[W 250608 09:58:52 index:329] No counter data found for project: test_project
[I 250608 09:58:52 login:175] before_request called for path: /api/v2/projects/test_project
[I 250608 09:58:55 login:175] before_request called for path: /dashboard-active-tasks
[I 250608 09:58:55 login:175] before_request called for path: /dashboard-active-tasks
[I 250608 09:58:55 login:175] before_request called for path: /api/v2/counter
[I 250608 09:58:55 login:175] before_request called for path: /api/v2/projects
[I 250608 09:58:56 login:175] before_request called for path: /api/projects
[I 250608 09:58:56 login:175] before_request called for path: /api/v2/projects
[I 250608 09:58:56 login:175] before_request called for path: /api/projects
[I 250608 09:58:58 login:175] before_request called for path: /api/v2/projects/tinti
[I 250608 09:58:58 login:175] before_request called for path: /api/v2/counter
[I 250608 09:58:58 login:175] before_request called for path: /api/v2/counter
[I 250608 09:58:59 login:175] before_request called for path: /api/v2/projects/tinti
[I 250608 09:58:59 login:175] before_request called for path: /api/projects/tinti
[I 250608 09:58:59 login:175] before_request called for path: /api/projects/tinti
[I 250608 09:58:59 login:175] before_request called for path: /api/debug/tinti
[I 250608 09:58:59 login:175] before_request called for path: /api/debug/tinti
[I 250608 09:59:05 login:175] before_request called for path: /api/v2/counter
[I 250608 09:59:06 login:175] before_request called for path: /api/v2/projects
[I 250608 09:59:06 login:175] before_request called for path: /api/projects
[I 250608 09:59:06 login:175] before_request called for path: /api/v2/counter
[I 250608 09:59:06 login:175] before_request called for path: /api/v2/projects
[I 250608 09:59:06 login:175] before_request called for path: /api/projects
[I 250608 09:59:06 login:175] before_request called for path: /api/v2/counter
[I 250608 09:59:08 login:175] before_request called for path: /dashboard-active-tasks
[I 250608 09:59:08 login:175] before_request called for path: /api/v2/projects
[I 250608 09:59:08 login:175] before_request called for path: /dashboard-active-tasks
[I 250608 09:59:08 login:175] before_request called for path: /api/projects
[I 250608 09:59:08 login:175] before_request called for path: /api/v2/projects
[I 250608 09:59:08 login:175] before_request called for path: /api/projects
[I 250608 09:59:08 login:175] before_request called for path: /api/v2/counter
[I 250608 09:59:08 login:175] before_request called for path: /api/v2/counter
[I 250608 09:59:08 login:175] before_request called for path: /api/v2/counter
[I 250608 09:59:08 login:175] before_request called for path: /api/v2/counter
[I 250608 09:59:12 metrics:173] Gauges: {"memory_usage_rss": 102035456, "memory_usage_vms": 427999232, "memory_usage_percent": 0.3038879999921927, "system_memory_total": 33576665088, "system_memory_available": 26820415488, "system_memory_percent": 20.1}
[I 250608 09:59:15 login:175] before_request called for path: /api/v2/counter
[I 250608 09:59:25 login:175] before_request called for path: /api/v2/counter
[I 250608 09:59:35 login:175] before_request called for path: /api/v2/counter
[I 250608 10:00:02 login:175] before_request called for path: /api/v2/projects
[I 250608 10:00:08 login:175] before_request called for path: /api/v2/counter
[I 250608 10:00:08 login:175] before_request called for path: /api/v2/counter
[I 250608 10:00:08 login:175] before_request called for path: /api/v2/counter
[I 250608 10:00:08 login:175] before_request called for path: /api/v2/counter
[I 250608 10:00:08 login:175] before_request called for path: /api/v2/counter
[I 250608 10:00:10 login:175] before_request called for path: /dashboard-active-tasks
[I 250608 10:00:10 login:175] before_request called for path: /dashboard-active-tasks
[I 250608 10:00:10 login:175] before_request called for path: /api/v2/projects
[I 250608 10:00:10 login:175] before_request called for path: /api/projects
[I 250608 10:00:10 login:175] before_request called for path: /api/v2/projects
[I 250608 10:00:10 login:175] before_request called for path: /api/projects
[I 250608 10:00:12 metrics:173] Gauges: {"memory_usage_rss": 102035456, "memory_usage_vms": 427999232, "memory_usage_percent": 0.3038879999921927, "system_memory_total": 33576665088, "system_memory_available": 27282706432, "system_memory_percent": 18.7}
[I 250608 10:00:27 login:175] before_request called for path: /api/v2/tasks
[I 250608 10:00:35 login:175] before_request called for path: /api/v2/run
[I 250608 10:00:35 api:227] Creating newtask for project: test_project
[I 250608 10:00:35 api:228] Newtask details: {'project': 'test_project', 'taskid': 'on_start', 'url': 'data:,on_start', 'process': {'callback': 'on_start'}, 'schedule': {'age': 0, 'priority': 9, 'force_update': True}}
[I 250608 10:00:35 api:233] Task executed for project test_project, result: False
[I 250608 10:00:54 login:175] before_request called for path: /api/v2/counter
[W 250608 10:00:54 index:329] No counter data found for project: test_project
[I 250608 10:00:54 login:175] before_request called for path: /api/v2/projects/test_project
[I 250608 10:00:54 login:175] before_request called for path: /api/v2/counter
[W 250608 10:00:54 index:329] No counter data found for project: test_project
[I 250608 10:00:54 login:175] before_request called for path: /api/v2/counter
[W 250608 10:00:54 index:329] No counter data found for project: test_project
[I 250608 10:00:54 login:175] before_request called for path: /api/v2/projects/test_project
[I 250608 10:00:54 login:175] before_request called for path: /api/v2/projects/test_project
[I 250608 10:00:58 login:175] before_request called for path: /api/v2/results/test_project
[I 250608 10:01:00 login:175] before_request called for path: /dashboard-active-tasks
[I 250608 10:01:00 login:175] before_request called for path: /dashboard-active-tasks
[I 250608 10:01:00 login:175] before_request called for path: /api/v2/projects
[I 250608 10:01:00 login:175] before_request called for path: /api/projects
[I 250608 10:01:00 login:175] before_request called for path: /api/v2/projects
[I 250608 10:01:00 login:175] before_request called for path: /api/projects
[I 250608 10:01:04 login:175] before_request called for path: /api/v2/projects
[I 250608 10:01:04 api_utils:82] Authentication disabled, skipping auth check for create_project_v2
[W 250608 10:01:04 projects:150] Failed to trigger on_start for project tinti: <Fault 1: '<class \'Exception\'>:method "trigger_on_start" is not supported'>
[I 250608 10:01:08 login:175] before_request called for path: /api/v2/counter
[I 250608 10:01:08 login:175] before_request called for path: /api/v2/counter
[I 250608 10:01:08 login:175] before_request called for path: /api/v2/counter
[I 250608 10:01:08 login:175] before_request called for path: /api/v2/counter
[I 250608 10:01:08 login:175] before_request called for path: /api/v2/counter
[I 250608 10:01:12 metrics:173] Gauges: {"memory_usage_rss": 102035456, "memory_usage_vms": 427999232, "memory_usage_percent": 0.3038879999921927, "system_memory_total": 33576665088, "system_memory_available": 27250679808, "system_memory_percent": 18.8}
[I 250608 10:01:13 login:175] before_request called for path: /api/v2/counter
[I 250608 10:01:13 login:175] before_request called for path: /api/v2/projects/tinti
[I 250608 10:01:13 login:175] before_request called for path: /api/v2/counter
[I 250608 10:01:13 login:175] before_request called for path: /api/v2/projects/tinti
[I 250608 10:01:13 login:175] before_request called for path: /api/v2/counter
[I 250608 10:01:13 login:175] before_request called for path: /api/v2/projects/tinti
[I 250608 10:01:23 login:175] before_request called for path: /api/v2/counter
[I 250608 10:01:33 login:175] before_request called for path: /api/v2/counter
[I 250608 10:01:43 login:175] before_request called for path: /api/v2/counter
[I 250608 10:01:53 login:175] before_request called for path: /api/v2/counter
[I 250608 10:02:03 login:175] before_request called for path: /api/v2/counter
[I 250608 10:02:08 login:175] before_request called for path: /api/v2/counter
[I 250608 10:02:08 login:175] before_request called for path: /api/v2/counter
[I 250608 10:02:08 login:175] before_request called for path: /api/v2/counter
[I 250608 10:02:08 login:175] before_request called for path: /api/v2/counter
[I 250608 10:02:08 login:175] before_request called for path: /api/v2/counter
[I 250608 10:02:12 metrics:173] Gauges: {"memory_usage_rss": 102035456, "memory_usage_vms": 427999232, "memory_usage_percent": 0.3038879999921927, "system_memory_total": 33576665088, "system_memory_available": 27238256640, "system_memory_percent": 18.9}
[I 250608 10:02:13 login:175] before_request called for path: /api/v2/counter
[I 250608 10:02:23 login:175] before_request called for path: /api/v2/counter
[I 250608 10:02:33 login:175] before_request called for path: /api/v2/counter
[I 250608 10:03:08 login:175] before_request called for path: /api/v2/counter
[I 250608 10:03:08 login:175] before_request called for path: /api/v2/counter
[I 250608 10:03:08 login:175] before_request called for path: /api/v2/counter
[I 250608 10:03:08 login:175] before_request called for path: /api/v2/counter
[I 250608 10:03:08 login:175] before_request called for path: /api/v2/counter
[I 250608 10:03:08 login:175] before_request called for path: /api/v2/counter
[I 250608 10:03:12 metrics:173] Gauges: {"memory_usage_rss": 102035456, "memory_usage_vms": 427999232, "memory_usage_percent": 0.3038879999921927, "system_memory_total": 33576665088, "system_memory_available": 27038355456, "system_memory_percent": 19.5}
[I 250608 10:03:14 login:175] before_request called for path: /api/v2/counter
[I 250608 10:03:19 login:175] before_request called for path: /api/v2/projects
[I 250608 10:03:19 login:175] before_request called for path: /api/projects
[I 250608 10:03:19 login:175] before_request called for path: /api/v2/counter
[I 250608 10:03:19 login:175] before_request called for path: /api/v2/projects
[I 250608 10:03:19 login:175] before_request called for path: /api/projects
[I 250608 10:03:19 login:175] before_request called for path: /api/v2/counter
[I 250608 10:03:20 login:175] before_request called for path: /api/v2/tasks
[I 250608 10:03:22 login:175] before_request called for path: /api/v2/metrics
[I 250608 10:03:22 login:175] before_request called for path: /api/v2/tasks
[I 250608 10:03:22 login:175] before_request called for path: /api/components/status
[I 250608 10:03:22 login:175] before_request called for path: /api/v2/metrics
[I 250608 10:03:22 login:175] before_request called for path: /api/components/status
[I 250608 10:03:48 login:175] before_request called for path: /api/v2/projects
[I 250608 10:03:48 login:175] before_request called for path: /api/projects
[I 250608 10:03:48 login:175] before_request called for path: /api/v2/counter
[I 250608 10:03:48 login:175] before_request called for path: /api/v2/tasks
[I 250608 10:03:48 login:175] before_request called for path: /api/v2/metrics
[I 250608 10:03:48 login:175] before_request called for path: /api/components/status
[I 250608 10:04:08 login:175] before_request called for path: /api/v2/counter
[I 250608 10:04:08 login:175] before_request called for path: /api/v2/counter
[I 250608 10:04:08 login:175] before_request called for path: /api/v2/counter
[I 250608 10:04:08 login:175] before_request called for path: /api/v2/counter
[I 250608 10:04:08 login:175] before_request called for path: /api/v2/counter
[I 250608 10:04:12 metrics:173] Gauges: {"memory_usage_rss": 102035456, "memory_usage_vms": 427999232, "memory_usage_percent": 0.3038879999921927, "system_memory_total": 33576665088, "system_memory_available": 26798002176, "system_memory_percent": 20.2}
[I 250608 10:04:18 login:175] before_request called for path: /api/v2/projects
[I 250608 10:04:18 login:175] before_request called for path: /api/projects
[I 250608 10:04:18 login:175] before_request called for path: /api/v2/counter
[I 250608 10:04:18 login:175] before_request called for path: /api/v2/tasks
[I 250608 10:04:18 login:175] before_request called for path: /api/v2/metrics
[I 250608 10:04:18 login:175] before_request called for path: /api/components/status
[I 250608 10:04:48 login:175] before_request called for path: /api/v2/projects
[I 250608 10:04:48 login:175] before_request called for path: /api/projects
[I 250608 10:04:48 login:175] before_request called for path: /api/v2/counter
[I 250608 10:04:48 login:175] before_request called for path: /api/v2/tasks
[I 250608 10:04:48 login:175] before_request called for path: /api/v2/metrics
[I 250608 10:04:49 login:175] before_request called for path: /api/components/status
[I 250608 10:04:54 login:175] before_request called for path: /api/v2/projects
[I 250608 10:04:54 login:175] before_request called for path: /api/projects
[I 250608 10:04:54 login:175] before_request called for path: /api/v2/projects
[I 250608 10:04:54 login:175] before_request called for path: /api/projects
[I 250608 10:04:54 login:175] before_request called for path: /api/v2/counter
[I 250608 10:04:54 login:175] before_request called for path: /api/v2/counter
[I 250608 10:04:54 login:175] before_request called for path: /api/v2/projects
[I 250608 10:04:54 login:175] before_request called for path: /api/v2/tasks
[I 250608 10:04:54 login:175] before_request called for path: /api/projects
[I 250608 10:04:54 login:175] before_request called for path: /api/v2/counter
[I 250608 10:04:54 login:175] before_request called for path: /api/v2/tasks
[I 250608 10:04:54 login:175] before_request called for path: /api/v2/metrics
[I 250608 10:04:54 login:175] before_request called for path: /api/v2/tasks
[I 250608 10:04:54 login:175] before_request called for path: /api/components/status
[I 250608 10:04:55 login:175] before_request called for path: /api/v2/metrics
[I 250608 10:04:55 login:175] before_request called for path: /api/components/status
[I 250608 10:04:55 login:175] before_request called for path: /api/v2/metrics
[I 250608 10:04:55 login:175] before_request called for path: /api/components/status
[I 250608 10:05:08 login:175] before_request called for path: /api/v2/counter
[I 250608 10:05:08 login:175] before_request called for path: /api/v2/counter
[I 250608 10:05:08 login:175] before_request called for path: /api/v2/counter
[I 250608 10:05:08 login:175] before_request called for path: /api/v2/counter
[I 250608 10:05:08 login:175] before_request called for path: /api/v2/counter
[I 250608 10:05:12 metrics:173] Gauges: {"memory_usage_rss": 102035456, "memory_usage_vms": 427999232, "memory_usage_percent": 0.3038879999921927, "system_memory_total": 33576665088, "system_memory_available": 26719653888, "system_memory_percent": 20.4}
[I 250608 10:05:22 login:175] before_request called for path: /api/v2/projects
[I 250608 10:05:22 login:175] before_request called for path: /api/projects
[I 250608 10:05:22 login:175] before_request called for path: /api/v2/counter
[I 250608 10:05:22 login:175] before_request called for path: /api/v2/projects
[I 250608 10:05:22 login:175] before_request called for path: /api/projects
[I 250608 10:05:22 login:175] before_request called for path: /api/v2/counter
[I 250608 10:05:52 login:175] before_request called for path: /api/v2/projects
[I 250608 10:05:52 login:175] before_request called for path: /api/projects
[I 250608 10:05:52 login:175] before_request called for path: /api/v2/counter
[I 250608 10:06:08 login:175] before_request called for path: /api/v2/counter
[I 250608 10:06:08 login:175] before_request called for path: /api/v2/counter
[I 250608 10:06:08 login:175] before_request called for path: /api/v2/counter
[I 250608 10:06:08 login:175] before_request called for path: /api/v2/counter
[I 250608 10:06:08 login:175] before_request called for path: /api/v2/counter
[I 250608 10:06:12 metrics:173] Gauges: {"memory_usage_rss": 102035456, "memory_usage_vms": 427999232, "memory_usage_percent": 0.3038879999921927, "system_memory_total": 33576665088, "system_memory_available": 26991308800, "system_memory_percent": 19.6}
[I 250608 10:06:22 login:175] before_request called for path: /api/v2/projects
[I 250608 10:06:22 login:175] before_request called for path: /api/projects
[I 250608 10:06:22 login:175] before_request called for path: /api/v2/counter
[I 250608 10:06:52 login:175] before_request called for path: /api/v2/projects
[I 250608 10:06:52 login:175] before_request called for path: /api/projects
[I 250608 10:06:52 login:175] before_request called for path: /api/v2/counter
[I 250608 10:07:08 login:175] before_request called for path: /api/v2/counter
[I 250608 10:07:08 login:175] before_request called for path: /api/v2/counter
[I 250608 10:07:08 login:175] before_request called for path: /api/v2/counter
[I 250608 10:07:08 login:175] before_request called for path: /api/v2/counter
[I 250608 10:07:08 login:175] before_request called for path: /api/v2/counter
[I 250608 10:07:12 metrics:173] Gauges: {"memory_usage_rss": 102035456, "memory_usage_vms": 427999232, "memory_usage_percent": 0.3038879999921927, "system_memory_total": 33576665088, "system_memory_available": 26623598592, "system_memory_percent": 20.7}
[I 250608 10:07:22 login:175] before_request called for path: /api/v2/projects
[I 250608 10:07:22 login:175] before_request called for path: /api/projects
[I 250608 10:07:22 login:175] before_request called for path: /api/v2/counter
[I 250608 10:07:52 login:175] before_request called for path: /api/v2/projects
[I 250608 10:07:52 login:175] before_request called for path: /api/projects
[I 250608 10:07:52 login:175] before_request called for path: /api/v2/counter
[I 250608 10:08:08 login:175] before_request called for path: /api/v2/counter
[I 250608 10:08:08 login:175] before_request called for path: /api/v2/counter
[I 250608 10:08:08 login:175] before_request called for path: /api/v2/counter
[I 250608 10:08:08 login:175] before_request called for path: /api/v2/counter
[I 250608 10:08:08 login:175] before_request called for path: /api/v2/counter
[I 250608 10:08:12 metrics:173] Gauges: {"memory_usage_rss": 102166528, "memory_usage_vms": 427999232, "memory_usage_percent": 0.30427836633636796, "system_memory_total": 33576665088, "system_memory_available": 26975772672, "system_memory_percent": 19.7}
[I 250608 10:08:22 login:175] before_request called for path: /api/v2/projects
[I 250608 10:08:22 login:175] before_request called for path: /api/projects
[I 250608 10:08:22 login:175] before_request called for path: /api/v2/counter
