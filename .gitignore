# Python
*.py[cod]
__pycache__/
*.so
.Python
.log
env/
venv/
.venv/
ENV/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pytest_cache/
htmlcov/

# pyspider specific
data/*
images/*

# IDE
.idea/
.vscode/
*.swp
*.swo
.project
.pydevproject
.mr.developer.cfg

# Node.js / Next.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnp/
.pnp.js
.yarn/
.yarnrc.yml
.yarn-integrity

# Next.js specific
.next/
out/
.vercel/
.env*.local
.env.local
.env.development.local
.env.test.local
.env.production.local

# OS specific
.DS_Store
Thumbs.db
