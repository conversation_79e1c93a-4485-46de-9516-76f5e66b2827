#!/usr/bin/env python3
"""
usedatabase設定に基づいてconfig.jsonのデータベース設定を動的に更新するスクリプト
"""

import json
import os
import sys
import logging

# ログ設定
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def load_config(config_file='config.json'):
    """設定ファイルを読み込み"""
    try:
        with open(config_file, 'r') as f:
            return json.load(f)
    except FileNotFoundError:
        logger.error(f"設定ファイルが見つかりません: {config_file}")
        return None
    except json.JSONDecodeError as e:
        logger.error(f"設定ファイルのJSONが無効です: {e}")
        return None

def save_config(config, config_file='config.json'):
    """設定ファイルを保存"""
    try:
        with open(config_file, 'w') as f:
            json.dump(config, f, indent=4)
        logger.info(f"設定ファイルを更新しました: {config_file}")
        return True
    except Exception as e:
        logger.error(f"設定ファイルの保存に失敗しました: {e}")
        return False

def get_database_config(database_type):
    """データベースタイプに基づいて設定を取得"""
    
    # 環境変数から接続情報を取得
    mysql_host = os.environ.get('MYSQL_HOST', 'localhost')
    mysql_port = os.environ.get('MYSQL_PORT', '3306')
    mysql_user = os.environ.get('MYSQL_USER', 'pyspider')
    mysql_password = os.environ.get('MYSQL_PASSWORD', 'PySpider2024!SecurePass#')
    mysql_database = os.environ.get('MYSQL_DATABASE', 'pyspider')
    
    sqlite_data_dir = os.environ.get('SQLITE_DATA_DIR', 'data')
    sqlite_db_prefix = os.environ.get('SQLITE_DB_PREFIX', 'pyspider')
    
    postgres_host = os.environ.get('POSTGRES_HOST', 'localhost')
    postgres_port = os.environ.get('POSTGRES_PORT', '5432')
    postgres_user = os.environ.get('POSTGRES_USER', 'postgres')
    postgres_password = os.environ.get('POSTGRES_PASSWORD', 'postgres')
    postgres_database = os.environ.get('POSTGRES_DATABASE', 'pyspider')
    
    if database_type == 'mysql':
        return {
            "taskdb": f"mysql+taskdb://{mysql_user}:{mysql_password}@{mysql_host}:{mysql_port}/{mysql_database}_taskdb",
            "projectdb": f"mysql+projectdb://{mysql_user}:{mysql_password}@{mysql_host}:{mysql_port}/{mysql_database}_projectdb",
            "resultdb": f"mysql+resultdb://{mysql_user}:{mysql_password}@{mysql_host}:{mysql_port}/{mysql_database}_resultdb"
        }
    elif database_type == 'sqlite':
        return {
            "taskdb": f"sqlite+taskdb:///{sqlite_data_dir}/{sqlite_db_prefix}_taskdb.db",
            "projectdb": f"sqlite+projectdb:///{sqlite_data_dir}/{sqlite_db_prefix}_projectdb.db",
            "resultdb": f"sqlite+resultdb:///{sqlite_data_dir}/{sqlite_db_prefix}_resultdb.db"
        }
    elif database_type == 'postgresql':
        return {
            "taskdb": f"sqlalchemy+postgresql+taskdb://{postgres_user}:{postgres_password}@{postgres_host}:{postgres_port}/{postgres_database}_taskdb",
            "projectdb": f"sqlalchemy+postgresql+projectdb://{postgres_user}:{postgres_password}@{postgres_host}:{postgres_port}/{postgres_database}_projectdb",
            "resultdb": f"sqlalchemy+postgresql+resultdb://{postgres_user}:{postgres_password}@{postgres_host}:{postgres_port}/{postgres_database}_resultdb"
        }
    else:
        logger.error(f"サポートされていないデータベースタイプ: {database_type}")
        return None

def update_config_database(target_database=None):
    """config.jsonのデータベース設定を更新"""
    
    # 設定ファイルを読み込み
    config = load_config()
    if config is None:
        return False
    
    # usedatabase設定を取得
    current_database = config.get('usedatabase', 'sqlite')
    
    # 引数で指定された場合はそれを使用
    if target_database:
        current_database = target_database
        config['usedatabase'] = target_database
    
    logger.info(f"データベースタイプ: {current_database}")
    
    # データベース設定を取得
    db_config = get_database_config(current_database)
    if db_config is None:
        return False
    
    # 設定を更新
    config.update(db_config)
    
    # 設定ファイルを保存
    return save_config(config)

def main():
    """メイン関数"""
    target_database = None
    
    # コマンドライン引数をチェック
    if len(sys.argv) > 1:
        target_database = sys.argv[1].lower()
        if target_database not in ['mysql', 'sqlite', 'postgresql']:
            logger.error(f"無効なデータベースタイプ: {target_database}")
            logger.info("使用可能なタイプ: mysql, sqlite, postgresql")
            sys.exit(1)
    
    # 設定を更新
    if update_config_database(target_database):
        logger.info("設定の更新が完了しました")
        sys.exit(0)
    else:
        logger.error("設定の更新に失敗しました")
        sys.exit(1)

if __name__ == '__main__':
    main()
