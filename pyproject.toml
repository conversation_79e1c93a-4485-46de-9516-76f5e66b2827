[build-system]
requires = ["setuptools>=64.0.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "pyspiderNX2"
version = "0.6.0"
description = "An Enhanced and Modernized Version of PySpider"
readme = "README.md"
authors = [
    {name = "<PERSON> Binux", email = "<EMAIL>"},
    {name = "PySpiderNX2 Team", email = "<EMAIL>"},
]
license = {text = "Apache License, Version 2.0"}
classifiers = [
    "Development Status :: 4 - Beta",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Programming Language :: Python :: 3.13",
    "License :: OSI Approved :: Apache Software License",
    "Intended Audience :: Developers",
    "Operating System :: OS Independent",
    "Environment :: Web Environment",
    "Topic :: Internet :: WWW/HTTP",
    "Topic :: Software Development :: Libraries :: Application Frameworks",
    "Topic :: Software Development :: Libraries :: Python Modules",
]
requires-python = ">=3.8"
dependencies = [
    "Flask>=3.0.2",
    "Jinja2>=3.1.3",
    "Werkzeug>=3.0.0",
    "tornado>=6.4.0",
    # six removed for Python 3.13 compatibility
    "requests>=2.31.0",
    # urllib3 version compatible with requests 2.31.0
    "urllib3>=1.26.0,<3.0.0",
    "certifi>=2024.2.2",
    "charset-normalizer>=3.0.0",
    "idna>=3.4",
    "aiohttp>=3.9.0",
    # aiohttp dependencies
    "yarl>=1.9.0",
    "multidict>=6.0.0",
    "frozenlist>=1.4.0",
    "aiosignal>=1.3.0",
    "lxml>=5.1.0",
    "pycurl>=7.45.3",
    "PyYAML>=6.0.0",
    "psutil>=7.0.0",
    # SQLAlchemy 2.0.x をサポート
    "SQLAlchemy>=2.0.0",
    "pymongo>=4.6.1",
    # Redis 6.x をサポート
    "redis>=6.0.0",
    "mysql-connector-python>=8.3.0",
    "pika>=1.3.2",
    "amqp>=5.2.0",
    "kombu>=5.3.5",
    "celery>=5.3.0",
    "elasticsearch>=8.0.0,<9.0.0",
    "pillow>=10.0.0",
    "beautifulsoup4>=4.12.0",
    "pyquery>=2.0.0",
    "cssselect>=1.2.0",
    "cryptography>=41.0.0",
    "pyOpenSSL>=23.0.0",
    "tblib>=3.0.0",
    "click>=8.1.7",
    "tldextract>=3.4.0",
    "python-dateutil>=2.8.0",
    "u-msgpack-python>=2.8.0",
    "attrs>=23.0.0",
    "jsonpath-ng>=1.5.0",
    "propcache>=0.3.0",
    "pydantic>=2.0.0",
    "pydantic-settings>=2.0.0",
    "typing-extensions>=4.0.0",
    "pandas>=2.0.0",
    "chardet>=5.2.0",
    # psycopg2-binary はインストールが容易
    "psycopg2-binary>=2.9.9",
    "wsgidav>=4.3.0",
    "dicttoxml>=1.7.0",
    "python-multipart>=0.0.6",
    "Flask-Login>=0.6.3",
    "flask-cors>=4.0.0",
    # ログ管理のための追加依存関係
    "python-json-logger>=2.0.0",
    # メトリクスとモニタリングのための依存関係
    "prometheus_client>=0.17.0",
]

[project.optional-dependencies]
all = [
    "mysql-connector-python>=8.3.0",
    "pymongo>=4.6.1",
    "psycopg2-binary>=2.9.9",
    "elasticsearch>=8.0.0,<9.0.0",
    "kombu>=5.3.5",
    "amqp>=5.2.0",
    "SQLAlchemy>=2.0.0",
    "redis>=6.0.0",
    "pika>=1.3.2",
    "playwright>=1.40.0",
    "pyee>=11.0.0",
    "pyppeteer>=1.0.0",
    "websockets>=10.0.0",
    "python-json-logger>=2.0.0",
    "prometheus_client>=0.17.0",
]
test = [
    "coverage>=7.4.1",
    "Werkzeug>=3.0.1",
    "httpbin>=0.10.0",
    "pyproxy>=0.1.6",
    "types-requests>=2.0.0,<2.32.0.20250000",
    # types-six removed for Python 3.13 compatibility
    "lxml-stubs>=0.4.0",
    "pytest>=7.0.0",
    "pytest-cov>=4.0.0",
]

[project.scripts]
pyspider = "pyspider.run:main"
pyspider-webui-next = "pyspider.tools.webui_next_launcher:main"
pyspider-setup-webui-next = "pyspider.tools.setup_webui_next:main"
pyspider-setup-npm = "pyspider.tools.setup_npm_integrated:main"
pyspidernx2 = "pyspider.run:main"

[tool.setuptools]
packages = [
    "pyspider",
    "pyspider.config",
    "pyspider.data",
    "pyspider.database",
    "pyspider.database.base",
    "pyspider.database.mysql",
    "pyspider.database.sqlite",
    "pyspider.database.sqlalchemy",
    "pyspider.database.mongodb",
    "pyspider.database.redis",
    "pyspider.database.couchdb",
    "pyspider.debugger",
    "pyspider.fetcher",
    "pyspider.libs",
    "pyspider.message_queue",
    "pyspider.processor",
    "pyspider.result",
    "pyspider.root_npm",
    "pyspider.scheduler",
    "pyspider.tools",
    "pyspider.webui",
    "pyspider.webui.static",
    "pyspider.webui.templates"
]
package-dir = {pyspider = "pyspider"}

[project.urls]
"Homepage" = "https://github.com/igtmtakan/pyspiderNX2-3"
"Bug Tracker" = "https://github.com/igtmtakan/pyspiderNX2-3/issues"
"Documentation" = "https://github.com/igtmtakan/pyspiderNX2-3/docs"

[tool.setuptools.package-data]
pyspider = [
    "logging.conf",
    "logging.json",
    "fetcher/phantomjs_fetcher.js",
    "fetcher/splash_fetcher.lua",
    "fetcher/puppeteer_fetcher.js",
    "webui/static/*.js",
    "webui/static/*.css",
    "webui/static/*.html",
    "webui/static/*.json",
    "webui/static/*.map",
    "webui/static/css/*",
    "webui/static/js/*",
    "webui/static/img/*",
    "webui/static/fonts/*",
    "webui/templates/*",
    "webui-next/package.json",
    "webui-next/package-lock.json",
    "webui-next/next.config.js",
    "webui-next/postcss.config.js",
    "webui-next/tailwind.config.js",
    "webui-next/tsconfig.json",
    "webui-next/next-env.d.ts",
    "webui-next/api-server.js",
    "webui-next/README.md",
    "webui-next/README_AUTH.md",
    "webui-next/src/**/*",
    "webui-next/tests/**/*"
]


