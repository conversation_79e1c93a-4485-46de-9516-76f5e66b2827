{"taskdb": "sqlalchemy+postgresql+taskdb://postgres:postgres@localhost:5432/pyspider_taskdb", "projectdb": "sqlalchemy+postgresql+projectdb://postgres:postgres@localhost:5432/pyspider_projectdb", "resultdb": "sqlalchemy+postgresql+resultdb://postgres:postgres@localhost:5432/pyspider_resultdb", "message_queue": "redis://localhost:6379/0", "webui": {"port": 5000, "username": "", "password": "", "need-auth": false, "scheduler-rpc": "http://localhost:23333/"}, "fetcher": {"xmlrpc-port": 24444, "puppeteer-endpoint": "http://localhost:22223"}, "rate_limit": {"global": {"requests_per_minute": 60}, "per_domain": {"example.com": {"requests_per_minute": 10}, "httpbin.org": {"requests_per_minute": 5}, "gnavi.co.jp": {"requests_per_minute": 5}, "amazon.co.jp": {"requests_per_minute": 2}}}, "scheduler": {"xmlrpc-port": 23333}, "processor": {"process-time-limit": 30}, "result_worker": {}}