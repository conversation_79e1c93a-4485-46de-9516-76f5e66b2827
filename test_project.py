import time
from pyspider.libs.utils import utf8
from pyspider.database.sqlite.projectdb import ProjectDB

projectdb = ProjectDB('data/project.db')

# 問題のあるプロジェクトを修正
problem_project = projectdb.get('test_project_new')
if problem_project:
    # 正しい形式のスクリプトに修正
    problem_project['script'] = '''#!/usr/bin/env python
# -*- coding: utf-8 -*-
from pyspider.libs.base_handler import *

class Handler(BaseHandler):
    crawl_config = {
    }

    @every(minutes=24 * 60)
    def on_start(self):
        self.crawl('http://example.com/', callback=self.index_page)

    @config(age=10 * 24 * 60 * 60)
    def index_page(self, response):
        return {
            "title": response.doc('title').text(),
            "url": response.url,
            "time": time.time()
        }
'''
    # 修正したプロジェクトを保存
    projectdb.update(problem_project['name'], problem_project)
    print(f"プロジェクト '{problem_project['name']}' を修正しました")
else:
    print("プロジェクト 'test_project_new' が見つかりません")

# 全てのプロジェクトを表示
print("\n登録されているプロジェクト一覧:")
for project in projectdb.get_all():
    print(f"- {project['name']} (ステータス: {project['status']})")
