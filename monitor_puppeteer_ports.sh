#!/bin/bash
# Puppeteer Fetcherポート監視・管理スクリプト

# 色定義
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 設定
PUPPETEER_PORTS=(22223 22224 22225 22226 22227)
LOG_FILE="puppeteer_port_monitor.log"

# ログ関数
log_message() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

# ポート使用状況の表示
show_port_status() {
    echo -e "${BLUE}=== Puppeteer Fetcherポート使用状況 ===${NC}"
    echo -e "${BLUE}ポート\t状態\t\tPID\t\tプロセス${NC}"
    echo "------------------------------------------------------------"
    
    for PORT in "${PUPPETEER_PORTS[@]}"; do
        PID=$(lsof -i:$PORT -t 2>/dev/null)
        if [ ! -z "$PID" ]; then
            PROCESS=$(ps -p $PID -o comm= 2>/dev/null)
            echo -e "${RED}$PORT\t使用中\t\t$PID\t\t$PROCESS${NC}"
        else
            echo -e "${GREEN}$PORT\t利用可能\t\t-\t\t-${NC}"
        fi
    done
    echo ""
}

# 孤立プロセスの検出
detect_orphaned_processes() {
    echo -e "${YELLOW}=== 孤立プロセスの検出 ===${NC}"
    
    # Puppeteer Fetcherプロセス
    PUPPETEER_PIDS=$(ps aux | grep "node.*puppeteer_fetcher.js" | grep -v grep | awk '{print $2}')
    if [ ! -z "$PUPPETEER_PIDS" ]; then
        echo -e "${YELLOW}Puppeteer Fetcherプロセス:${NC}"
        for PID in $PUPPETEER_PIDS; do
            PORT_INFO=$(lsof -p $PID -i 2>/dev/null | grep LISTEN | awk '{print $9}')
            echo "  PID: $PID, ポート: $PORT_INFO"
        done
    else
        echo -e "${GREEN}孤立したPuppeteer Fetcherプロセスはありません${NC}"
    fi
    
    # 孤立したChromiumプロセス
    CHROME_PIDS=$(ps aux | grep -E "(chrome|chromium).*--no-sandbox" | grep -v grep | awk '{print $2}')
    if [ ! -z "$CHROME_PIDS" ]; then
        echo -e "${YELLOW}孤立したChromiumプロセス:${NC}"
        for PID in $CHROME_PIDS; do
            echo "  PID: $PID"
        done
    else
        echo -e "${GREEN}孤立したChromiumプロセスはありません${NC}"
    fi
    echo ""
}

# プロセスのクリーンアップ
cleanup_processes() {
    echo -e "${YELLOW}=== プロセスクリーンアップ ===${NC}"
    
    # 確認プロンプト
    read -p "すべてのPuppeteer関連プロセスを終了しますか？ (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "クリーンアップをキャンセルしました"
        return
    fi
    
    # ポート使用プロセスの終了
    for PORT in "${PUPPETEER_PORTS[@]}"; do
        PIDS=$(lsof -i:$PORT -t 2>/dev/null)
        if [ ! -z "$PIDS" ]; then
            log_message "ポート$PORTのプロセスを終了: $PIDS"
            kill -TERM $PIDS 2>/dev/null
            sleep 1
            # 強制終了が必要な場合
            REMAINING_PIDS=$(lsof -i:$PORT -t 2>/dev/null)
            if [ ! -z "$REMAINING_PIDS" ]; then
                log_message "強制終了: $REMAINING_PIDS"
                kill -9 $REMAINING_PIDS 2>/dev/null
            fi
        fi
    done
    
    # Puppeteer Fetcherプロセスの終了
    PUPPETEER_PIDS=$(ps aux | grep "node.*puppeteer_fetcher.js" | grep -v grep | awk '{print $2}')
    if [ ! -z "$PUPPETEER_PIDS" ]; then
        log_message "Puppeteer Fetcherプロセスを終了: $PUPPETEER_PIDS"
        kill -TERM $PUPPETEER_PIDS 2>/dev/null
        sleep 2
        # 強制終了が必要な場合
        REMAINING_PIDS=$(ps aux | grep "node.*puppeteer_fetcher.js" | grep -v grep | awk '{print $2}')
        if [ ! -z "$REMAINING_PIDS" ]; then
            log_message "強制終了: $REMAINING_PIDS"
            kill -9 $REMAINING_PIDS 2>/dev/null
        fi
    fi
    
    # 孤立したChromiumプロセスの終了
    CHROME_PIDS=$(ps aux | grep -E "(chrome|chromium).*--no-sandbox" | grep -v grep | awk '{print $2}')
    if [ ! -z "$CHROME_PIDS" ]; then
        log_message "孤立したChromiumプロセスを終了: $CHROME_PIDS"
        kill -TERM $CHROME_PIDS 2>/dev/null
        sleep 1
        # 強制終了が必要な場合
        REMAINING_PIDS=$(ps aux | grep -E "(chrome|chromium).*--no-sandbox" | grep -v grep | awk '{print $2}')
        if [ ! -z "$REMAINING_PIDS" ]; then
            log_message "強制終了: $REMAINING_PIDS"
            kill -9 $REMAINING_PIDS 2>/dev/null
        fi
    fi
    
    echo -e "${GREEN}クリーンアップ完了${NC}"
    log_message "プロセスクリーンアップ完了"
}

# リソース使用量の表示
show_resource_usage() {
    echo -e "${BLUE}=== リソース使用量 ===${NC}"
    
    # Puppeteer Fetcherプロセスのリソース使用量
    PUPPETEER_PIDS=$(ps aux | grep "node.*puppeteer_fetcher.js" | grep -v grep | awk '{print $2}')
    if [ ! -z "$PUPPETEER_PIDS" ]; then
        echo -e "${YELLOW}Puppeteer Fetcherプロセス:${NC}"
        echo "PID     CPU%    MEM%    VSZ     RSS     コマンド"
        for PID in $PUPPETEER_PIDS; do
            ps -p $PID -o pid,%cpu,%mem,vsz,rss,comm --no-headers 2>/dev/null
        done
    fi
    
    # Chromiumプロセスのリソース使用量
    CHROME_PIDS=$(ps aux | grep -E "(chrome|chromium).*--no-sandbox" | grep -v grep | awk '{print $2}')
    if [ ! -z "$CHROME_PIDS" ]; then
        echo -e "${YELLOW}Chromiumプロセス:${NC}"
        echo "PID     CPU%    MEM%    VSZ     RSS     コマンド"
        for PID in $CHROME_PIDS; do
            ps -p $PID -o pid,%cpu,%mem,vsz,rss,comm --no-headers 2>/dev/null | head -5
        done
    fi
    echo ""
}

# 連続監視モード
monitor_mode() {
    echo -e "${GREEN}=== 連続監視モード開始 ===${NC}"
    echo "Ctrl+Cで終了"
    
    while true; do
        clear
        echo -e "${GREEN}Puppeteer Fetcherポート監視 - $(date)${NC}"
        echo ""
        show_port_status
        detect_orphaned_processes
        show_resource_usage
        
        sleep 5
    done
}

# ヘルプ表示
show_help() {
    echo "Puppeteer Fetcherポート監視・管理スクリプト"
    echo ""
    echo "使用方法: $0 [オプション]"
    echo ""
    echo "オプション:"
    echo "  -s, --status      ポート使用状況を表示"
    echo "  -o, --orphaned    孤立プロセスを検出"
    echo "  -c, --cleanup     プロセスをクリーンアップ"
    echo "  -r, --resources   リソース使用量を表示"
    echo "  -m, --monitor     連続監視モード"
    echo "  -h, --help        このヘルプを表示"
    echo ""
}

# メイン処理
case "$1" in
    -s|--status)
        show_port_status
        ;;
    -o|--orphaned)
        detect_orphaned_processes
        ;;
    -c|--cleanup)
        cleanup_processes
        ;;
    -r|--resources)
        show_resource_usage
        ;;
    -m|--monitor)
        monitor_mode
        ;;
    -h|--help)
        show_help
        ;;
    "")
        show_port_status
        detect_orphaned_processes
        ;;
    *)
        echo "不明なオプション: $1"
        show_help
        exit 1
        ;;
esac
