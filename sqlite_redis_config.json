{"taskdb": "sqlite+taskdb:///data/pyspider_taskdb.db", "projectdb": "sqlite+projectdb:///data/pyspider_projectdb.db", "resultdb": "sqlite+resultdb:///data/pyspider_resultdb.db", "message_queue": "redis://localhost:6379/0", "redis_fallback": {"enabled": true, "auto_fallback": true, "check_interval": 30, "max_connection_attempts": 3, "fallback_mode": "xmlrpc", "comment": "Redis接続失敗時にXMLRPCモードに自動フォールバック"}, "webui": {"username": "", "password": "", "need_auth": false, "host": "0.0.0.0", "port": 5000}, "fetcher": {"xmlrpc_host": "0.0.0.0", "xmlrpc_port": 24444}, "scheduler": {"xmlrpc_host": "0.0.0.0", "xmlrpc_port": 23333}, "puppeteer_endpoint": "http://localhost:22223"}