{"timestamp": "2025-06-08T09:41:07.977794", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203}
[I 250608 09:41:08 memory_optimizer:57] Memory optimizer initialized with max_memory_percent=80.0%, gc_interval=60s, check_interval=30s, auto_optimize=True
[I 250608 09:41:08 memory_optimizer:69] Memory monitoring started
[I 250608 09:41:08 run:31] Created PID file for processor: /tmp/pyspider_processor.pid
[I 250608 09:41:08 processor:216] processor starting...
[I 250608 09:41:08 processor:204] process douyo:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:None
[I 250608 09:41:08 processor:204] process bunbunbun:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:None
[I 250608 09:41:08 processor:204] process iuiu:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:None
[I 250608 09:41:08 processor:204] process mysql_detailed_test_2:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:None
[I 250608 09:41:08 processor:204] process kokoko:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:None
[I 250608 09:41:08 processor:204] process mysql_detailed_test:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:None
[I 250608 09:41:08 processor:204] process jijijiji:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:None
[I 250608 09:41:08 processor:204] process tinti:_on_get_info data:,_on_get_info -> [200] len:12 -> result:None fol:0 msg:0 err:None
[I 250608 09:42:08 metrics:173] Gauges: {"memory_usage_rss": 83943424, "memory_usage_vms": 179486720, "memory_usage_percent": 0.25000524554774983, "system_memory_total": 33576665088, "system_memory_available": 28219846656, "system_memory_percent": 16.0}
[I 250608 09:43:08 metrics:173] Gauges: {"memory_usage_rss": 83943424, "memory_usage_vms": 179486720, "memory_usage_percent": 0.25000524554774983, "system_memory_total": 33576665088, "system_memory_available": 27283173376, "system_memory_percent": 18.7}
[I 250608 09:44:08 metrics:173] Gauges: {"memory_usage_rss": 83943424, "memory_usage_vms": 179486720, "memory_usage_percent": 0.25000524554774983, "system_memory_total": 33576665088, "system_memory_available": 27217784832, "system_memory_percent": 18.9}
[I 250608 09:45:08 metrics:173] Gauges: {"memory_usage_rss": 83943424, "memory_usage_vms": 179486720, "memory_usage_percent": 0.25000524554774983, "system_memory_total": 33576665088, "system_memory_available": 27094319104, "system_memory_percent": 19.3}
[I 250608 09:46:08 metrics:173] Gauges: {"memory_usage_rss": 83943424, "memory_usage_vms": 179486720, "memory_usage_percent": 0.25000524554774983, "system_memory_total": 33576665088, "system_memory_available": 26844278784, "system_memory_percent": 20.1}
[I 250608 09:47:08 metrics:173] Gauges: {"memory_usage_rss": 83943424, "memory_usage_vms": 179486720, "memory_usage_percent": 0.25000524554774983, "system_memory_total": 33576665088, "system_memory_available": 26940198912, "system_memory_percent": 19.8}
[I 250608 09:48:08 metrics:173] Gauges: {"memory_usage_rss": 83943424, "memory_usage_vms": 179486720, "memory_usage_percent": 0.25000524554774983, "system_memory_total": 33576665088, "system_memory_available": 26380505088, "system_memory_percent": 21.4}
[I 250608 09:49:08 metrics:173] Gauges: {"memory_usage_rss": 83943424, "memory_usage_vms": 179486720, "memory_usage_percent": 0.25000524554774983, "system_memory_total": 33576665088, "system_memory_available": 26956066816, "system_memory_percent": 19.7}
[I 250608 09:50:08 metrics:173] Gauges: {"memory_usage_rss": 83943424, "memory_usage_vms": 179486720, "memory_usage_percent": 0.25000524554774983, "system_memory_total": 33576665088, "system_memory_available": 27041333248, "system_memory_percent": 19.5}
[I 250608 09:51:08 metrics:173] Gauges: {"memory_usage_rss": 83943424, "memory_usage_vms": 179486720, "memory_usage_percent": 0.25000524554774983, "system_memory_total": 33576665088, "system_memory_available": 26886950912, "system_memory_percent": 19.9}
[I 250608 09:52:08 metrics:173] Gauges: {"memory_usage_rss": 83943424, "memory_usage_vms": 179486720, "memory_usage_percent": 0.25000524554774983, "system_memory_total": 33576665088, "system_memory_available": 28224065536, "system_memory_percent": 15.9}
