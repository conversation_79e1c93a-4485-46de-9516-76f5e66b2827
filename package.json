{"name": "pyspiderNX2", "version": "0.3.10", "description": "A powerful spider system in Python with modern web interfaces", "private": true, "scripts": {"install:all": "npm install && npm run install:webui-next && npm run install:webui-static", "install:webui-next": "cd pyspider/webui-next && npm install", "install:webui-static": "cd pyspider/webui/static && npm install", "dev:webui-next": "cd pyspider/webui-next && npm run dev", "dev:webui-static": "cd pyspider/webui/static && npm run dev", "dev:both": "concurrently \"npm run dev:webui-next\" \"npm run dev:webui-static\"", "build:webui-next": "cd pyspider/webui-next && npm run build", "build:webui-static": "cd pyspider/webui/static && npm run build", "build:all": "npm run build:webui-next && npm run build:webui-static", "start:webui-next": "cd pyspider/webui-next && npm start", "preview:webui-static": "cd pyspider/webui/static && npm run preview", "clean": "rm -rf node_modules pyspider/webui-next/node_modules pyspider/webui/static/node_modules", "clean:build": "rm -rf pyspider/webui-next/.next pyspider/webui/static/dist", "setup": "npm run install:all", "test": "echo \"No tests specified\" && exit 0", "test:puppeteer": "node scripts/test-puppeteer.js", "server:express": "node scripts/express-server.js"}, "dependencies": {"express": "^4.21.2", "monaco-editor": "^0.47.0", "playwright": "^1.42.1", "puppeteer": "^22.15.0"}, "devDependencies": {"concurrently": "^8.2.2"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "repository": {"type": "git", "url": "https://github.com/igtmtakan/pyspiderNX2-4.git"}, "keywords": ["spider", "crawler", "scraping", "python", "web-scraping", "automation"], "author": "igtmtakan", "license": "Apache-2.0"}