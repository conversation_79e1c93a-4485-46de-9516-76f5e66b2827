#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
API v2プロジェクト作成テストスイート
"""

import unittest
import json
import base64
import time
from unittest.mock import patch, MagicMock
import sys
import os

# プロジェクトルートをパスに追加
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

from pyspider.webui.app import app


class TestAPIv2ProjectCreation(unittest.TestCase):
    """API v2プロジェクト作成テスト"""

    def setUp(self):
        """テストセットアップ"""
        self.app = app
        self.app.config['TESTING'] = True
        self.app.config['need_auth'] = False  # 認証を無効化してテスト
        self.client = self.app.test_client()
        
        # モックプロジェクトDB
        self.mock_projectdb = MagicMock()
        self.mock_projectdb.get.return_value = None  # プロジェクトが存在しない
        self.mock_projectdb.insert.return_value = True

    def test_create_project_success(self):
        """プロジェクト作成成功テスト"""
        with patch('pyspider.webui.api.app.config.get') as mock_config:
            mock_config.side_effect = lambda key, default=None: {
                'projectdb': self.mock_projectdb,
                'need_auth': False
            }.get(key, default)
            
            response = self.client.post('/api/v2/projects-create',
                                      data=json.dumps({
                                          'name': 'test_project',
                                          'script': 'print("Hello World")',
                                          'group': 'test_group',
                                          'status': 'RUNNING'
                                      }),
                                      content_type='application/json')
            
            self.assertEqual(response.status_code, 200)
            data = json.loads(response.data)
            self.assertEqual(data['status'], 'success')
            self.assertEqual(data['project']['name'], 'test_project')
            self.assertEqual(data['project']['group'], 'test_group')

    def test_create_project_missing_name(self):
        """プロジェクト名なしテスト"""
        with patch('pyspider.webui.api.app.config.get') as mock_config:
            mock_config.side_effect = lambda key, default=None: {
                'projectdb': self.mock_projectdb,
                'need_auth': False
            }.get(key, default)
            
            response = self.client.post('/api/v2/projects-create',
                                      data=json.dumps({
                                          'script': 'print("Hello World")'
                                      }),
                                      content_type='application/json')
            
            self.assertEqual(response.status_code, 400)
            data = json.loads(response.data)
            self.assertEqual(data['code'], 'MISSING_PROJECT_NAME')

    def test_create_project_invalid_name_format(self):
        """無効なプロジェクト名形式テスト"""
        with patch('pyspider.webui.api.app.config.get') as mock_config:
            mock_config.side_effect = lambda key, default=None: {
                'projectdb': self.mock_projectdb,
                'need_auth': False
            }.get(key, default)
            
            response = self.client.post('/api/v2/projects-create',
                                      data=json.dumps({
                                          'name': 'invalid project name!',
                                          'script': 'print("Hello World")'
                                      }),
                                      content_type='application/json')
            
            self.assertEqual(response.status_code, 400)
            data = json.loads(response.data)
            self.assertEqual(data['code'], 'INVALID_PROJECT_NAME_FORMAT')

    def test_create_project_name_too_long(self):
        """プロジェクト名長すぎテスト"""
        with patch('pyspider.webui.api.app.config.get') as mock_config:
            mock_config.side_effect = lambda key, default=None: {
                'projectdb': self.mock_projectdb,
                'need_auth': False
            }.get(key, default)
            
            long_name = 'a' * 51  # 50文字制限を超える
            response = self.client.post('/api/v2/projects-create',
                                      data=json.dumps({
                                          'name': long_name,
                                          'script': 'print("Hello World")'
                                      }),
                                      content_type='application/json')
            
            self.assertEqual(response.status_code, 400)
            data = json.loads(response.data)
            self.assertEqual(data['code'], 'PROJECT_NAME_TOO_LONG')

    def test_create_project_already_exists(self):
        """プロジェクト既存テスト"""
        # 既存プロジェクトを返すようにモック設定
        self.mock_projectdb.get.return_value = {'name': 'existing_project'}
        
        with patch('pyspider.webui.api.app.config.get') as mock_config:
            mock_config.side_effect = lambda key, default=None: {
                'projectdb': self.mock_projectdb,
                'need_auth': False
            }.get(key, default)
            
            response = self.client.post('/api/v2/projects-create',
                                      data=json.dumps({
                                          'name': 'existing_project',
                                          'script': 'print("Hello World")'
                                      }),
                                      content_type='application/json')
            
            self.assertEqual(response.status_code, 409)
            data = json.loads(response.data)
            self.assertEqual(data['code'], 'PROJECT_EXISTS')

    def test_create_project_script_too_large(self):
        """スクリプトサイズ制限テスト"""
        with patch('pyspider.webui.api.app.config.get') as mock_config:
            mock_config.side_effect = lambda key, default=None: {
                'projectdb': self.mock_projectdb,
                'need_auth': False
            }.get(key, default)
            
            large_script = 'print("Hello")' * 10000  # 100KB制限を超える
            response = self.client.post('/api/v2/projects-create',
                                      data=json.dumps({
                                          'name': 'test_project',
                                          'script': large_script
                                      }),
                                      content_type='application/json')
            
            self.assertEqual(response.status_code, 400)
            data = json.loads(response.data)
            self.assertEqual(data['code'], 'SCRIPT_TOO_LARGE')

    def test_create_project_invalid_json(self):
        """無効なJSONテスト"""
        with patch('pyspider.webui.api.app.config.get') as mock_config:
            mock_config.side_effect = lambda key, default=None: {
                'projectdb': self.mock_projectdb,
                'need_auth': False
            }.get(key, default)
            
            response = self.client.post('/api/v2/projects-create',
                                      data='invalid json',
                                      content_type='application/json')
            
            self.assertEqual(response.status_code, 400)
            data = json.loads(response.data)
            self.assertEqual(data['code'], 'INVALID_JSON')

    def test_create_project_form_data(self):
        """フォームデータでのプロジェクト作成テスト"""
        with patch('pyspider.webui.api.app.config.get') as mock_config:
            mock_config.side_effect = lambda key, default=None: {
                'projectdb': self.mock_projectdb,
                'need_auth': False
            }.get(key, default)
            
            response = self.client.post('/api/v2/projects-create',
                                      data={
                                          'name': 'form_project',
                                          'script': 'print("Form Data")',
                                          'group': 'form_group'
                                      })
            
            self.assertEqual(response.status_code, 200)
            data = json.loads(response.data)
            self.assertEqual(data['status'], 'success')
            self.assertEqual(data['project']['name'], 'form_project')

    def test_create_project_default_script_generation(self):
        """デフォルトスクリプト生成テスト"""
        with patch('pyspider.webui.api.app.config.get') as mock_config, \
             patch('pyspider.libs.base_handler.BaseHandler.get_template') as mock_template:
            
            mock_config.side_effect = lambda key, default=None: {
                'projectdb': self.mock_projectdb,
                'need_auth': False
            }.get(key, default)
            
            mock_template.return_value = 'default script content'
            
            response = self.client.post('/api/v2/projects-create',
                                      data=json.dumps({
                                          'name': 'auto_script_project'
                                      }),
                                      content_type='application/json')
            
            self.assertEqual(response.status_code, 200)
            data = json.loads(response.data)
            self.assertEqual(data['status'], 'success')
            mock_template.assert_called_once()

    def test_cors_preflight_request(self):
        """CORSプリフライトリクエストテスト"""
        response = self.client.options('/api/v2/projects-create')
        
        self.assertEqual(response.status_code, 200)
        self.assertIn('Access-Control-Allow-Origin', response.headers)
        self.assertIn('Access-Control-Allow-Methods', response.headers)
        self.assertIn('Access-Control-Allow-Headers', response.headers)


if __name__ == '__main__':
    unittest.main()
