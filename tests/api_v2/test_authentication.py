#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
API v2認証テストスイート
"""

import unittest
import json
import base64
import time
from unittest.mock import patch, MagicMock
import sys
import os

# プロジェクトルートをパスに追加
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

from pyspider.webui.app import app
from pyspider.webui.login import need_auth_api_v2


class TestAPIv2Authentication(unittest.TestCase):
    """API v2認証テスト"""

    def setUp(self):
        """テストセットアップ"""
        self.app = app
        self.app.config['TESTING'] = True
        self.app.config['need_auth'] = True
        self.app.config['webui_username'] = 'admin'
        self.app.config['webui_password'] = 'PySpider2024!SecurePass#'
        self.client = self.app.test_client()
        
        # Basic認証ヘッダーの生成
        credentials = base64.b64encode(b'admin:PySpider2024!SecurePass#').decode('utf-8')
        self.auth_headers = {'Authorization': f'Basic {credentials}'}
        
        # 無効な認証ヘッダー
        invalid_credentials = base64.b64encode(b'admin:wrongpassword').decode('utf-8')
        self.invalid_auth_headers = {'Authorization': f'Basic {invalid_credentials}'}

    def test_auth_disabled_allows_access(self):
        """認証無効時のアクセステスト"""
        self.app.config['need_auth'] = False
        
        response = self.client.get('/api/v2/test')
        self.assertEqual(response.status_code, 200)
        
        data = json.loads(response.data)
        self.assertEqual(data['status'], 'success')

    def test_auth_enabled_requires_header(self):
        """認証有効時のヘッダー必須テスト"""
        self.app.config['need_auth'] = True
        
        response = self.client.post('/api/v2/projects-create',
                                  data=json.dumps({'name': 'test'}),
                                  content_type='application/json')
        
        self.assertEqual(response.status_code, 401)
        data = json.loads(response.data)
        self.assertEqual(data['code'], 'AUTH_HEADER_MISSING')

    def test_valid_basic_auth(self):
        """有効なBasic認証テスト"""
        self.app.config['need_auth'] = True
        
        with patch('pyspider.webui.api.app.config.get') as mock_config:
            mock_config.side_effect = lambda key, default=None: {
                'projectdb': MagicMock(),
                'need_auth': True,
                'webui_username': 'admin',
                'webui_password': 'PySpider2024!SecurePass#'
            }.get(key, default)
            
            response = self.client.post('/api/v2/projects-create',
                                      data=json.dumps({
                                          'name': 'test_auth_project',
                                          'script': 'print("test")'
                                      }),
                                      content_type='application/json',
                                      headers=self.auth_headers)
            
            # 認証は成功するはず（他のエラーは別途テスト）
            self.assertNotEqual(response.status_code, 401)

    def test_invalid_basic_auth(self):
        """無効なBasic認証テスト"""
        self.app.config['need_auth'] = True
        
        response = self.client.post('/api/v2/projects-create',
                                  data=json.dumps({'name': 'test'}),
                                  content_type='application/json',
                                  headers=self.invalid_auth_headers)
        
        self.assertEqual(response.status_code, 401)
        data = json.loads(response.data)
        self.assertEqual(data['code'], 'INVALID_CREDENTIALS')

    def test_malformed_auth_header(self):
        """不正な認証ヘッダーテスト"""
        self.app.config['need_auth'] = True
        
        response = self.client.post('/api/v2/projects-create',
                                  data=json.dumps({'name': 'test'}),
                                  content_type='application/json',
                                  headers={'Authorization': 'Invalid Header'})
        
        self.assertEqual(response.status_code, 401)
        data = json.loads(response.data)
        self.assertEqual(data['code'], 'AUTH_METHOD_UNSUPPORTED')

    def test_bearer_token_not_supported(self):
        """Bearerトークン未対応テスト"""
        self.app.config['need_auth'] = True
        
        response = self.client.post('/api/v2/projects-create',
                                  data=json.dumps({'name': 'test'}),
                                  content_type='application/json',
                                  headers={'Authorization': 'Bearer token123'})
        
        self.assertEqual(response.status_code, 501)
        data = json.loads(response.data)
        self.assertEqual(data['code'], 'AUTH_METHOD_NOT_SUPPORTED')

    def test_cors_headers_present(self):
        """CORSヘッダーの存在確認"""
        response = self.client.options('/api/v2/projects-create')
        
        self.assertEqual(response.status_code, 200)
        self.assertIn('Access-Control-Allow-Origin', response.headers)
        self.assertIn('Access-Control-Allow-Methods', response.headers)
        self.assertIn('Access-Control-Allow-Headers', response.headers)

    def test_auth_decorator_function(self):
        """認証デコレーター関数の直接テスト"""
        
        @need_auth_api_v2
        def test_function():
            return "success"
        
        # Flaskアプリケーションコンテキスト内でテスト
        with self.app.test_request_context('/test', headers=self.auth_headers):
            self.app.config['need_auth'] = True
            result = test_function()
            self.assertEqual(result, "success")
        
        # 認証なしでのテスト
        with self.app.test_request_context('/test'):
            self.app.config['need_auth'] = True
            result = test_function()
            # 認証エラーのレスポンスが返される
            self.assertIsNotNone(result)


if __name__ == '__main__':
    unittest.main()
