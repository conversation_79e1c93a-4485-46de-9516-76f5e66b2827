#!/usr/bin/env python
# -*- encoding: utf-8 -*-
# vim: set et sw=4 ts=4 sts=4 ff=unix fenc=utf8:
# Author: PySpiderNX2 Team
# Created on 2025-05-01 10:00:00

import unittest
import sys
import datetime
import xmlrpc.client
import json
import os
import tempfile
from unittest import mock

from pyspider.libs import utils
from pyspider.libs.wsgi_xmlrpc import WSGIXMLRPCApplication


class TestPython313Compatibility(unittest.TestCase):
    """Test Python 3.13 compatibility"""

    def test_datetime_utc(self):
        """Test datetime.UTC compatibility"""
        # Test with timestamp
        timestamp = 1620000000
        dt = utils.format_date(timestamp)
        self.assertIsInstance(dt, str)

        # Test with datetime object
        now = datetime.datetime.now(utils.UTC)
        dt = utils.format_date(now)
        self.assertIsInstance(dt, str)

    def test_xmlrpc_client(self):
        """Test xmlrpc.client compatibility"""
        # Create a mock ServerProxy
        with mock.patch('xmlrpc.client.ServerProxy') as mock_server_proxy:
            mock_server_proxy.return_value.method.return_value = 'result'

            # Create a ServerProxy with timeout
            server = xmlrpc.client.ServerProxy('http://localhost:8000',
                                              use_builtin_types=True)

            # Call a method
            result = server.method()

            # Check the result
            self.assertEqual(result, 'result')

            # Check that ServerProxy was called with the correct arguments
            mock_server_proxy.assert_called_once_with('http://localhost:8000',
                                                     use_builtin_types=True)

    def test_wsgi_xmlrpc(self):
        """Test WSGI XMLRPC compatibility"""
        # Create a WSGIXMLRPCApplication
        app = WSGIXMLRPCApplication()

        # Register a function
        def test_func():
            return 'test'

        app.register_function(test_func)

        # Create a mock environment
        environ = {
            'REQUEST_METHOD': 'POST',
            'CONTENT_LENGTH': '100',
            'wsgi.input': mock.MagicMock()
        }

        # Create a mock start_response
        start_response = mock.MagicMock()

        # Mock the dispatcher._marshaled_dispatch method
        with mock.patch.object(app.dispatcher, '_marshaled_dispatch') as mock_dispatch:
            mock_dispatch.return_value = b'<methodResponse><params><param><value><string>test</string></value></param></params></methodResponse>'

            # Call the application
            app(environ, start_response)

            # Check that start_response was called with the correct arguments
            start_response.assert_called_once()

    def test_json_compatibility(self):
        """Test JSON compatibility"""
        # Create a complex object
        obj = {
            'string': 'test',
            'int': 123,
            'float': 123.456,
            'bool': True,
            'none': None,
            'list': [1, 2, 3],
            'dict': {'a': 1, 'b': 2}
        }

        # Convert to JSON
        json_str = json.dumps(obj)

        # Convert back to object
        obj2 = json.loads(json_str)

        # Check that the objects are equal
        self.assertEqual(obj, obj2)

    def test_file_operations(self):
        """Test file operations compatibility"""
        # Create a temporary file
        with tempfile.NamedTemporaryFile(delete=False) as f:
            # Write to the file
            f.write(b'test')

            # Close the file
            f.close()

            # Open the file for reading
            with open(f.name, 'rb') as f2:
                # Read the file
                content = f2.read()

                # Check the content
                self.assertEqual(content, b'test')

            # Remove the file
            os.unlink(f.name)

    @unittest.skipIf(sys.version_info < (3, 13), "Only for Python 3.13+")
    def test_python313_specific(self):
        """Test Python 3.13 specific features"""
        # This test will only run on Python 3.13+

        # Test datetime.UTC
        from datetime import UTC
        now = datetime.datetime.now(UTC)
        self.assertIsInstance(now, datetime.datetime)

        # Test PEP 695 type alias
        type_alias_code = """
type Point = tuple[float, float]
def distance(p1: Point, p2: Point) -> float:
    return ((p2[0] - p1[0]) ** 2 + (p2[1] - p1[1]) ** 2) ** 0.5
"""
        # Just check that the code can be compiled
        compiled = compile(type_alias_code, '<string>', 'exec')
        self.assertIsNotNone(compiled)

        # Test PEP 692 (using **kwargs in decorator)
        decorator_code = """
def decorator(**options):
    def wrapper(func):
        return func
    return wrapper

@decorator(option1="value1", option2="value2")
def test_func():
    pass
"""
        # Just check that the code can be compiled
        compiled = compile(decorator_code, '<string>', 'exec')
        self.assertIsNotNone(compiled)

    def test_asyncio_compatibility(self):
        """Test asyncio compatibility with Python 3.13"""
        import asyncio

        async def async_function():
            await asyncio.sleep(0.001)
            return "result"

        # Run the async function
        if sys.version_info >= (3, 7):
            result = asyncio.run(async_function())
            self.assertEqual(result, "result")
        else:
            # For older Python versions
            loop = asyncio.get_event_loop()
            result = loop.run_until_complete(async_function())
            self.assertEqual(result, "result")


if __name__ == '__main__':
    unittest.main()
