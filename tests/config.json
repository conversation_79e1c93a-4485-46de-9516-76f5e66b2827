{"taskdb": "sqlite+taskdb:///data/task.db", "projectdb": "sqlite+projectdb:///data/project.db", "resultdb": "sqlite+resultdb:///data/result.db", "message_queue": "redis://localhost:6379/0", "webui": {"port": 5000, "username": "admin", "password": "change_this_password_immediately", "need-auth": true, "scheduler-rpc": "http://localhost:23333/", "csrf_protection": true, "session_timeout": 3600}, "fetcher": {"xmlrpc-port": 24444, "puppeteer-endpoint": "http://localhost:22223", "timeout": 30, "poolsize": 100, "optimize": true, "memory_check_interval": 30, "max_memory_percent": 80}, "scheduler": {"xmlrpc-port": 23333, "inqueue_limit": 5000, "delete_time": 86400, "active_tasks": 100, "loop_limit": 1000, "fail_pause_num": 5}, "processor": {"process-time-limit": 30, "threads": 4}, "result_worker": {"threads": 1}, "rate_limit": {"global": {"requests_per_minute": 60}, "per_domain": {"example.com": {"requests_per_minute": 10}}}, "logging": {"level": "INFO", "dir": "logs", "max_size": "100MB", "backup_count": 5, "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s"}, "security": {"allowed_ips": ["127.0.0.1", "::1", "***********/16", "10.0.0.0/8", "**********/12"], "csrf_secret_key": null, "session_secret_key": "4c63e106d3c064495ac437f6020b1bd2dbb7a80713b24c9e360ca35916bbfab2"}, "performance": {"memory_optimization": true, "connection_pool_optimization": true, "database_optimization": true, "query_cache": true}}