#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import sys
import unittest
import logging
import asyncio
from unittest import mock

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from pyspider.libs.spa_handler import <PERSON><PERSON><PERSON><PERSON><PERSON>
from pyspider.libs.enhanced_playwright import EnhancedPlaywright

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger('test_spa_handler')

class TestSPAHandler(unittest.TestCase):
    def setUp(self):
        self.spa_handler = SPAHandler()
    
    def test_detect_spa_framework_react(self):
        html = """
        <!DOCTYPE html>
        <html>
        <head>
            <title>React App</title>
            <script src="https://unpkg.com/react@17/umd/react.development.js"></script>
            <script src="https://unpkg.com/react-dom@17/umd/react-dom.development.js"></script>
        </head>
        <body>
            <div id="root"></div>
        </body>
        </html>
        """
        framework = self.spa_handler.detect_spa_framework(html)
        self.assertEqual(framework, 'react')
    
    def test_detect_spa_framework_vue(self):
        html = """
        <!DOCTYPE html>
        <html>
        <head>
            <title>Vue App</title>
            <script src="https://unpkg.com/vue@3.0.0/dist/vue.global.js"></script>
        </head>
        <body>
            <div id="app"></div>
        </body>
        </html>
        """
        framework = self.spa_handler.detect_spa_framework(html)
        self.assertEqual(framework, 'vue')
    
    def test_detect_spa_framework_angular(self):
        html = """
        <!DOCTYPE html>
        <html>
        <head>
            <title>Angular App</title>
            <script src="https://unpkg.com/angular@1.8.2/angular.min.js"></script>
        </head>
        <body ng-app="myApp">
            <div ng-controller="myCtrl"></div>
        </body>
        </html>
        """
        framework = self.spa_handler.detect_spa_framework(html)
        self.assertEqual(framework, 'angular')
    
    def test_detect_spa_framework_none(self):
        html = """
        <!DOCTYPE html>
        <html>
        <head>
            <title>Regular HTML</title>
        </head>
        <body>
            <div>Hello World</div>
        </body>
        </html>
        """
        framework = self.spa_handler.detect_spa_framework(html)
        self.assertIsNone(framework)
    
    def test_get_wait_strategy(self):
        # Reactの待機戦略
        react_strategy = self.spa_handler.get_wait_strategy('react')
        self.assertEqual(react_strategy['waitUntil'], 'networkidle')
        self.assertEqual(react_strategy['timeout'], 30000)
        self.assertIn('waitForSelector', react_strategy)
        
        # Vueの待機戦略
        vue_strategy = self.spa_handler.get_wait_strategy('vue')
        self.assertEqual(vue_strategy['waitUntil'], 'networkidle')
        self.assertEqual(vue_strategy['timeout'], 30000)
        self.assertIn('waitForSelector', vue_strategy)
        
        # Angularの待機戦略
        angular_strategy = self.spa_handler.get_wait_strategy('angular')
        self.assertEqual(angular_strategy['waitUntil'], 'networkidle')
        self.assertEqual(angular_strategy['timeout'], 40000)  # Angularは長め
        self.assertIn('waitForSelector', angular_strategy)
        
        # 不明なフレームワークの待機戦略
        unknown_strategy = self.spa_handler.get_wait_strategy('unknown')
        self.assertEqual(unknown_strategy['waitUntil'], 'networkidle')
        self.assertEqual(unknown_strategy['timeout'], 30000)
    
    def test_get_navigation_actions(self):
        # 通常のURL
        actions = self.spa_handler.get_navigation_actions('https://example.com')
        self.assertEqual(len(actions), 1)
        self.assertEqual(actions[0]['type'], 'goto')
        
        # ハッシュベースのURL
        hash_actions = self.spa_handler.get_navigation_actions('https://example.com/#/path')
        self.assertEqual(len(hash_actions), 2)
        self.assertEqual(hash_actions[0]['type'], 'goto')
        self.assertEqual(hash_actions[1]['type'], 'wait_for_timeout')
        
        # Reactフレームワークの場合
        react_actions = self.spa_handler.get_navigation_actions('https://example.com', 'react')
        self.assertEqual(len(react_actions), 3)
        self.assertEqual(react_actions[0]['type'], 'goto')
        self.assertEqual(react_actions[1]['type'], 'wait_for_timeout')
        self.assertEqual(react_actions[2]['type'], 'wait_for_selector')
    
    def test_get_content_ready_check(self):
        # Reactのコンテンツ準備チェック
        react_check = self.spa_handler.get_content_ready_check('react')
        self.assertIn('reactLoaded', react_check)
        
        # Vueのコンテンツ準備チェック
        vue_check = self.spa_handler.get_content_ready_check('vue')
        self.assertIn('vueLoaded', vue_check)
        
        # Angularのコンテンツ準備チェック
        angular_check = self.spa_handler.get_content_ready_check('angular')
        self.assertIn('angularLoaded', angular_check)
        
        # デフォルトのコンテンツ準備チェック
        default_check = self.spa_handler.get_content_ready_check()
        self.assertIn('idle', default_check)
        self.assertIn('noLoaders', default_check)

@unittest.skipIf(not os.environ.get('RUN_PLAYWRIGHT_TESTS'), 'Playwright tests are skipped by default')
class TestEnhancedPlaywright(unittest.TestCase):
    def setUp(self):
        self.playwright = EnhancedPlaywright(headless=True)
        self.loop = asyncio.get_event_loop()
        self.loop.run_until_complete(self.playwright.init())
    
    def tearDown(self):
        self.loop.run_until_complete(self.playwright.close())
    
    def test_fetch_spa(self):
        result = self.loop.run_until_complete(
            self.playwright.fetch_spa('https://reactjs.org')
        )
        self.assertEqual(result['status_code'], 200)
        self.assertEqual(result['framework'], 'react')
        self.assertIn('content', result)
        self.assertIn('cookies', result)
        
        # Vueサイトのテスト
        vue_result = self.loop.run_until_complete(
            self.playwright.fetch_spa('https://vuejs.org')
        )
        self.assertEqual(vue_result['status_code'], 200)
        self.assertEqual(vue_result['framework'], 'vue')
        
        # 通常のHTMLサイトのテスト
        html_result = self.loop.run_until_complete(
            self.playwright.fetch_spa('https://example.com')
        )
        self.assertEqual(html_result['status_code'], 200)
        self.assertIsNone(html_result['framework'])

if __name__ == '__main__':
    unittest.main()
