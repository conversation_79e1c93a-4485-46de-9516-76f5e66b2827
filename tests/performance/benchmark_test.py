#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
パフォーマンステストスイート
Next.js 15.x & SQLAlchemy 2.0 対応後のベンチマークテスト
"""

import time
import json
import requests
import threading
import statistics
import psutil
import os
from datetime import datetime
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed


class PerformanceBenchmark:
    def __init__(self, base_url='http://localhost:5000', webui_url='http://localhost:3000'):
        self.base_url = base_url
        self.webui_url = webui_url
        self.auth_header = 'Basic YWRtaW46UHlTcGlkZXIyMDI0IVNlY3VyZVBhc3Mj'
        self.results = {}
        
    def measure_time(self, func):
        """実行時間を測定するデコレータ"""
        def wrapper(*args, **kwargs):
            start_time = time.time()
            result = func(*args, **kwargs)
            end_time = time.time()
            execution_time = end_time - start_time
            return result, execution_time
        return wrapper
    
    def test_api_response_time(self, endpoint, iterations=10):
        """API応答時間テスト"""
        print(f"\n🔍 API応答時間テスト: {endpoint}")
        times = []
        
        for i in range(iterations):
            try:
                start_time = time.time()
                response = requests.get(
                    f"{self.base_url}{endpoint}",
                    headers={'Authorization': self.auth_header},
                    timeout=30
                )
                end_time = time.time()
                
                if response.status_code == 200:
                    response_time = (end_time - start_time) * 1000  # ms
                    times.append(response_time)
                    print(f"  試行 {i+1}: {response_time:.2f}ms")
                else:
                    print(f"  試行 {i+1}: エラー {response.status_code}")
                    
            except Exception as e:
                print(f"  試行 {i+1}: 例外 {e}")
        
        if times:
            avg_time = statistics.mean(times)
            min_time = min(times)
            max_time = max(times)
            median_time = statistics.median(times)
            
            print(f"  平均: {avg_time:.2f}ms")
            print(f"  最小: {min_time:.2f}ms")
            print(f"  最大: {max_time:.2f}ms")
            print(f"  中央値: {median_time:.2f}ms")
            
            return {
                'endpoint': endpoint,
                'average': avg_time,
                'min': min_time,
                'max': max_time,
                'median': median_time,
                'samples': len(times)
            }
        return None
    
    def test_concurrent_load(self, endpoint, concurrent_users=5, requests_per_user=10):
        """同時負荷テスト"""
        print(f"\n⚡ 同時負荷テスト: {endpoint}")
        print(f"  同時ユーザー数: {concurrent_users}")
        print(f"  ユーザーあたりリクエスト数: {requests_per_user}")
        
        def make_request():
            try:
                start_time = time.time()
                response = requests.get(
                    f"{self.base_url}{endpoint}",
                    headers={'Authorization': self.auth_header},
                    timeout=30
                )
                end_time = time.time()
                return {
                    'status_code': response.status_code,
                    'response_time': (end_time - start_time) * 1000,
                    'success': response.status_code == 200
                }
            except Exception as e:
                return {
                    'status_code': 0,
                    'response_time': 0,
                    'success': False,
                    'error': str(e)
                }
        
        all_results = []
        start_time = time.time()
        
        with ThreadPoolExecutor(max_workers=concurrent_users) as executor:
            futures = []
            for user in range(concurrent_users):
                for req in range(requests_per_user):
                    futures.append(executor.submit(make_request))
            
            for future in as_completed(futures):
                result = future.result()
                all_results.append(result)
        
        end_time = time.time()
        total_time = end_time - start_time
        
        successful_requests = [r for r in all_results if r['success']]
        failed_requests = [r for r in all_results if not r['success']]
        
        if successful_requests:
            response_times = [r['response_time'] for r in successful_requests]
            avg_response_time = statistics.mean(response_times)
            throughput = len(successful_requests) / total_time
            
            print(f"  総実行時間: {total_time:.2f}秒")
            print(f"  成功リクエスト: {len(successful_requests)}")
            print(f"  失敗リクエスト: {len(failed_requests)}")
            print(f"  平均応答時間: {avg_response_time:.2f}ms")
            print(f"  スループット: {throughput:.2f} req/sec")
            
            return {
                'endpoint': endpoint,
                'concurrent_users': concurrent_users,
                'total_requests': len(all_results),
                'successful_requests': len(successful_requests),
                'failed_requests': len(failed_requests),
                'total_time': total_time,
                'average_response_time': avg_response_time,
                'throughput': throughput
            }
        return None
    
    def test_memory_usage(self, duration=60):
        """メモリ使用量監視テスト"""
        print(f"\n💾 メモリ使用量監視テスト ({duration}秒)")
        
        memory_samples = []
        cpu_samples = []
        start_time = time.time()
        
        while time.time() - start_time < duration:
            # システム全体のメモリ使用量
            memory = psutil.virtual_memory()
            cpu = psutil.cpu_percent(interval=1)
            
            memory_samples.append(memory.percent)
            cpu_samples.append(cpu)
            
            print(f"  メモリ: {memory.percent:.1f}%, CPU: {cpu:.1f}%")
        
        avg_memory = statistics.mean(memory_samples)
        max_memory = max(memory_samples)
        avg_cpu = statistics.mean(cpu_samples)
        max_cpu = max(cpu_samples)
        
        print(f"  平均メモリ使用率: {avg_memory:.1f}%")
        print(f"  最大メモリ使用率: {max_memory:.1f}%")
        print(f"  平均CPU使用率: {avg_cpu:.1f}%")
        print(f"  最大CPU使用率: {max_cpu:.1f}%")
        
        return {
            'duration': duration,
            'average_memory': avg_memory,
            'max_memory': max_memory,
            'average_cpu': avg_cpu,
            'max_cpu': max_cpu,
            'samples': len(memory_samples)
        }
    
    def test_database_performance(self):
        """データベースパフォーマンステスト"""
        print(f"\n🗄️ データベースパフォーマンステスト")
        
        # プロジェクト一覧取得テスト
        projects_result = self.test_api_response_time('/api/v2/projects', 5)
        
        # タスク一覧取得テスト
        tasks_result = self.test_api_response_time('/api/v2/tasks?limit=100', 5)
        
        # メトリクス取得テスト
        metrics_result = self.test_api_response_time('/api/v2/metrics', 5)
        
        return {
            'projects': projects_result,
            'tasks': tasks_result,
            'metrics': metrics_result
        }
    
    def run_full_benchmark(self):
        """完全なベンチマークテストを実行"""
        print("🚀 pyspiderNX2 パフォーマンステスト開始")
        print("=" * 60)
        
        # テスト開始時刻
        start_time = datetime.now()
        
        # 1. API応答時間テスト
        api_tests = [
            '/api/v2/projects',
            '/api/v2/tasks?limit=20',
            '/api/v2/metrics',
            '/counter'
        ]
        
        api_results = []
        for endpoint in api_tests:
            result = self.test_api_response_time(endpoint)
            if result:
                api_results.append(result)
        
        # 2. 同時負荷テスト
        load_results = []
        for endpoint in ['/api/v2/projects', '/api/v2/metrics']:
            result = self.test_concurrent_load(endpoint, concurrent_users=3, requests_per_user=5)
            if result:
                load_results.append(result)
        
        # 3. メモリ使用量テスト
        memory_result = self.test_memory_usage(30)
        
        # 4. データベースパフォーマンステスト
        db_results = self.test_database_performance()
        
        # テスト終了時刻
        end_time = datetime.now()
        
        # 結果をまとめる
        benchmark_results = {
            'test_info': {
                'start_time': start_time.isoformat(),
                'end_time': end_time.isoformat(),
                'duration': str(end_time - start_time),
                'next_js_version': '15.1.3',
                'sqlalchemy_version': '2.0.40'
            },
            'api_response_times': api_results,
            'load_tests': load_results,
            'memory_usage': memory_result,
            'database_performance': db_results
        }
        
        # 結果をファイルに保存
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f'benchmark_results_{timestamp}.json'
        
        os.makedirs('tests/performance/results', exist_ok=True)
        with open(f'tests/performance/results/{filename}', 'w', encoding='utf-8') as f:
            json.dump(benchmark_results, f, indent=2, ensure_ascii=False)
        
        print(f"\n📊 ベンチマーク結果を保存しました: {filename}")
        
        return benchmark_results


if __name__ == '__main__':
    benchmark = PerformanceBenchmark()
    results = benchmark.run_full_benchmark()
    
    print("\n🎯 ベンチマークテスト完了")
    print("=" * 60)
