{"test_info": {"start_time": "2025-05-24T17:24:50.883962", "end_time": "2025-05-24T17:25:28.986944", "duration": "0:00:38.102982", "next_js_version": "15.1.3", "sqlalchemy_version": "2.0.40"}, "api_response_times": [{"endpoint": "/api/v2/projects", "average": 14.367389678955078, "min": 12.264728546142578, "max": 20.077228546142578, "median": 13.493895530700684, "samples": 10}, {"endpoint": "/api/v2/tasks?limit=20", "average": 7.358384132385254, "min": 6.009578704833984, "max": 8.653640747070312, "median": 7.363319396972656, "samples": 10}, {"endpoint": "/api/v2/metrics", "average": 244.0549612045288, "min": 233.5500717163086, "max": 259.0939998626709, "median": 239.7698163986206, "samples": 10}, {"endpoint": "/counter", "average": 9.420228004455566, "min": 6.952762603759766, "max": 13.050317764282227, "median": 9.242534637451172, "samples": 10}], "load_tests": [{"endpoint": "/api/v2/projects", "concurrent_users": 3, "total_requests": 15, "successful_requests": 15, "failed_requests": 0, "total_time": 0.17983627319335938, "average_response_time": 34.84185536702474, "throughput": 83.4092017902976}, {"endpoint": "/api/v2/metrics", "concurrent_users": 3, "total_requests": 15, "successful_requests": 15, "failed_requests": 0, "total_time": 3.848104953765869, "average_response_time": 768.5897668202718, "throughput": 3.8980225800028028}], "memory_usage": {"duration": 30, "average_memory": 20.733333333333334, "max_memory": 21.0, "average_cpu": 3.51, "max_cpu": 11.1, "samples": 30}, "database_performance": {"projects": {"endpoint": "/api/v2/projects", "average": 12.422704696655273, "min": 11.42740249633789, "max": 13.742446899414062, "median": 12.524127960205078, "samples": 5}, "tasks": {"endpoint": "/api/v2/tasks?limit=100", "average": 7.513189315795898, "min": 5.614757537841797, "max": 9.66954231262207, "median": 7.538557052612305, "samples": 5}, "metrics": {"endpoint": "/api/v2/metrics", "average": 237.47296333312988, "min": 234.34805870056152, "max": 242.7678108215332, "median": 236.3297939300537, "samples": 5}}}