#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
API v2統合テストスイート
"""

import unittest
import json
import base64
import time
import requests
import subprocess
import signal
import os
import sys
from threading import Thread
import tempfile

# プロジェクトルートをパスに追加
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))


class TestAPIv2Integration(unittest.TestCase):
    """API v2統合テスト"""

    @classmethod
    def setUpClass(cls):
        """テストクラスセットアップ"""
        cls.base_url = 'http://localhost:5000'
        cls.auth_headers = {
            'Authorization': 'Basic ' + base64.b64encode(b'admin:PySpider2024!SecurePass#').decode('utf-8'),
            'Content-Type': 'application/json'
        }

        # サーバーが起動しているかチェック
        try:
            response = requests.get(f'{cls.base_url}/api/v2/test', timeout=5)
            cls.server_running = True
        except requests.exceptions.RequestException:
            cls.server_running = False

    def setUp(self):
        """テストセットアップ"""
        if not self.server_running:
            self.skipTest("pyspider webui server is not running")

    def test_api_v2_test_endpoint(self):
        """API v2テストエンドポイント統合テスト"""
        # GETリクエスト
        response = requests.get(f'{self.base_url}/api/v2/test')
        self.assertEqual(response.status_code, 200)

        data = response.json()
        self.assertEqual(data['status'], 'success')
        self.assertIn('message', data)
        self.assertIn('timestamp', data)

        # POSTリクエスト
        test_data = {'test': 'data', 'number': 123}
        response = requests.post(f'{self.base_url}/api/v2/test',
                               json=test_data,
                               headers={'Content-Type': 'application/json'})

        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertEqual(data['status'], 'success')
        self.assertEqual(data['data'], test_data)

    def test_project_creation_without_auth(self):
        """認証なしでのプロジェクト作成テスト"""
        project_data = {
            'name': f'integration_test_{int(time.time())}',
            'script': 'print("Integration test project")',
            'group': 'integration_test',
            'status': 'RUNNING'
        }

        response = requests.post(f'{self.base_url}/api/v2/projects-create',
                               json=project_data,
                               headers={'Content-Type': 'application/json'})

        # 認証が有効な場合は401、無効な場合は200が期待される
        self.assertIn(response.status_code, [200, 401])

        if response.status_code == 401:
            data = response.json()
            self.assertEqual(data['code'], 'AUTH_HEADER_MISSING')

    def test_project_creation_with_auth(self):
        """認証付きでのプロジェクト作成テスト"""
        project_data = {
            'name': f'auth_test_{int(time.time())}',
            'script': 'print("Authenticated integration test")',
            'group': 'auth_test',
            'status': 'RUNNING'
        }

        response = requests.post(f'{self.base_url}/api/v2/projects-create',
                               json=project_data,
                               headers=self.auth_headers)

        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertEqual(data['status'], 'success')
        self.assertEqual(data['project']['name'], project_data['name'])
        self.assertEqual(data['project']['group'], project_data['group'])

    def test_project_creation_duplicate_name(self):
        """重複プロジェクト名での作成テスト"""
        project_name = f'duplicate_test_{int(time.time())}'
        project_data = {
            'name': project_name,
            'script': 'print("First project")',
            'group': 'duplicate_test'
        }

        # 最初のプロジェクト作成
        response1 = requests.post(f'{self.base_url}/api/v2/projects-create',
                                json=project_data,
                                headers=self.auth_headers)

        self.assertEqual(response1.status_code, 200)

        # 同じ名前で再度作成を試行
        response2 = requests.post(f'{self.base_url}/api/v2/projects-create',
                                json=project_data,
                                headers=self.auth_headers)

        self.assertEqual(response2.status_code, 409)
        data = response2.json()
        self.assertEqual(data['code'], 'PROJECT_EXISTS')

    def test_invalid_project_name_formats(self):
        """無効なプロジェクト名形式のテスト"""
        invalid_names = [
            'invalid name with spaces',
            'invalid@name',
            'invalid.name',
            'invalid/name',
            'invalid\\name',
            'invalid:name',
            'invalid;name',
            'invalid|name',
            'invalid<name>',
            'invalid"name"',
            "invalid'name'",
            'invalid?name',
            'invalid*name',
            'invalid+name',
            'invalid=name',
            'invalid[name]',
            'invalid{name}',
            'invalid(name)',
            'invalid%name',
            'invalid#name',
            'invalid!name',
            'invalid~name',
            'invalid`name`',
            'invalid^name',
            'invalid&name',
            'invalid$name'
        ]

        for invalid_name in invalid_names:
            project_data = {
                'name': invalid_name,
                'script': 'print("Invalid name test")'
            }

            response = requests.post(f'{self.base_url}/api/v2/projects-create',
                                   json=project_data,
                                   headers=self.auth_headers)

            self.assertEqual(response.status_code, 400,
                           f"Invalid name '{invalid_name}' should be rejected")
            data = response.json()
            self.assertEqual(data['code'], 'INVALID_PROJECT_NAME_FORMAT')

    def test_valid_project_name_formats(self):
        """有効なプロジェクト名形式のテスト"""
        valid_names = [
            'valid_name',
            'valid-name',
            'validname123',
            'VALIDNAME',
            'Valid_Name_123',
            'valid-name-123',
            'a',  # 最短
            'a' * 30  # タイムスタンプを考慮した長さ
        ]

        for valid_name in valid_names:
            # タイムスタンプを追加しても50文字以内になるように調整
            timestamp = str(int(time.time()))
            if len(valid_name) + len(timestamp) + 1 > 50:  # +1 for underscore
                # 長すぎる場合はタイムスタンプなしでテスト
                project_name = valid_name[:50]
            else:
                project_name = f'{valid_name}_{timestamp}'

            project_data = {
                'name': project_name,
                'script': 'print("Valid name test")'
            }

            response = requests.post(f'{self.base_url}/api/v2/projects-create',
                                   json=project_data,
                                   headers=self.auth_headers)

            self.assertEqual(response.status_code, 200,
                           f"Valid name '{valid_name}' should be accepted")
            data = response.json()
            self.assertEqual(data['status'], 'success')

    def test_cors_headers(self):
        """CORSヘッダーのテスト"""
        # OPTIONSリクエスト
        response = requests.options(f'{self.base_url}/api/v2/projects-create')

        self.assertEqual(response.status_code, 200)
        self.assertIn('Access-Control-Allow-Origin', response.headers)
        self.assertIn('Access-Control-Allow-Methods', response.headers)
        self.assertIn('Access-Control-Allow-Headers', response.headers)

    def test_content_type_handling(self):
        """Content-Type処理のテスト"""
        project_data = {
            'name': f'content_type_test_{int(time.time())}',
            'script': 'print("Content type test")'
        }

        # JSON Content-Type
        response = requests.post(f'{self.base_url}/api/v2/projects-create',
                               json=project_data,
                               headers=self.auth_headers)

        self.assertEqual(response.status_code, 200)

        # フォームデータ
        form_data = {
            'name': f'form_test_{int(time.time())}',
            'script': 'print("Form data test")'
        }

        auth_headers_form = {
            'Authorization': self.auth_headers['Authorization']
        }

        response = requests.post(f'{self.base_url}/api/v2/projects-create',
                               data=form_data,
                               headers=auth_headers_form)

        self.assertEqual(response.status_code, 200)

    def test_error_response_format(self):
        """エラーレスポンス形式のテスト"""
        # 無効なJSONでリクエスト
        response = requests.post(f'{self.base_url}/api/v2/projects-create',
                               data='invalid json',
                               headers=self.auth_headers)

        self.assertEqual(response.status_code, 400)
        data = response.json()

        # エラーレスポンスの必須フィールドを確認
        self.assertIn('status', data)
        self.assertIn('error', data)
        self.assertIn('code', data)
        self.assertIn('timestamp', data)
        self.assertEqual(data['status'], 'error')


if __name__ == '__main__':
    unittest.main()
