#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
テスト実行スクリプト
"""

import unittest
import sys
import os
import argparse

# プロジェクトルートをパスに追加
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))


def discover_tests(test_dir, pattern='test_*.py'):
    """テストを発見する"""
    loader = unittest.TestLoader()
    suite = loader.discover(test_dir, pattern=pattern)
    return suite


def run_test_suite(suite, verbosity=2):
    """テストスイートを実行する"""
    runner = unittest.TextTestRunner(verbosity=verbosity, buffer=True)
    result = runner.run(suite)
    return result


def print_summary(result):
    """テスト結果のサマリーを表示"""
    colors = {
        'green': '\033[92m',
        'red': '\033[91m',
        'yellow': '\033[93m',
        'blue': '\033[94m',
        'bold': '\033[1m',
        'end': '\033[0m'
    }

    print(f"\n{colors['bold']}{'='*60}{colors['end']}")
    print(f"{colors['bold']}テスト結果サマリー{colors['end']}")
    print(f"{colors['bold']}{'='*60}{colors['end']}")

    total_tests = result.testsRun
    failures = len(result.failures)
    errors = len(result.errors)
    skipped = len(result.skipped)
    success = total_tests - failures - errors - skipped

    print(f"実行テスト数: {colors['blue']}{total_tests}{colors['end']}")
    print(f"成功: {colors['green']}{success}{colors['end']}")
    print(f"失敗: {colors['red']}{failures}{colors['end']}")
    print(f"エラー: {colors['red']}{errors}{colors['end']}")
    print(f"スキップ: {colors['yellow']}{skipped}{colors['end']}")

    if failures > 0:
        print(f"\n{colors['red']}{colors['bold']}失敗したテスト:{colors['end']}")
        for test, _ in result.failures:
            print(f"{colors['red']}  - {test}{colors['end']}")

    if errors > 0:
        print(f"\n{colors['red']}{colors['bold']}エラーが発生したテスト:{colors['end']}")
        for test, _ in result.errors:
            print(f"{colors['red']}  - {test}{colors['end']}")

    success_rate = (success / total_tests * 100) if total_tests > 0 else 0
    print(f"\n成功率: {colors['green']}{success_rate:.1f}%{colors['end']}")

    if success_rate == 100:
        print(f"{colors['green']}{colors['bold']}🎉 すべてのテストが成功しました！{colors['end']}")
    elif success_rate >= 80:
        print(f"{colors['yellow']}{colors['bold']}⚠ 一部のテストが失敗しました{colors['end']}")
    else:
        print(f"{colors['red']}{colors['bold']}❌ 多くのテストが失敗しました{colors['end']}")


def main():
    """メイン関数"""
    parser = argparse.ArgumentParser(description='pyspiderNX2 テストスイート実行')
    parser.add_argument('--test-type', choices=['unit', 'api_v2', 'integration', 'all'],
                       default='all', help='実行するテストタイプ')
    parser.add_argument('--verbosity', '-v', type=int, choices=[0, 1, 2], default=2,
                       help='出力の詳細レベル')
    parser.add_argument('--pattern', default='test_*.py',
                       help='テストファイルのパターン')
    parser.add_argument('--failfast', action='store_true',
                       help='最初の失敗で停止')

    args = parser.parse_args()

    # テストディレクトリの設定
    test_dirs = []
    if args.test_type == 'all':
        test_dirs = ['unit', 'api_v2', 'integration']
    else:
        test_dirs = [args.test_type]

    # 全体のテストスイート
    all_suite = unittest.TestSuite()

    for test_dir in test_dirs:
        test_path = os.path.join(os.path.dirname(__file__), test_dir)
        if os.path.exists(test_path):
            suite = discover_tests(test_path, args.pattern)
            all_suite.addTest(suite)
            print(f"✓ {test_dir} テストを発見しました")
        else:
            print(f"⚠ {test_path} ディレクトリが見つかりません")

    if all_suite.countTestCases() == 0:
        print("❌ 実行するテストが見つかりませんでした")
        return 1

    print(f"\n🚀 {all_suite.countTestCases()} 個のテストを実行します...\n")

    # テスト実行
    result = run_test_suite(all_suite, args.verbosity)

    # 結果表示
    print_summary(result)

    # 終了コード
    if result.wasSuccessful():
        return 0
    else:
        return 1


if __name__ == '__main__':
    sys.exit(main())
