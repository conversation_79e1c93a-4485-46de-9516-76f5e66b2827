#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
ログ監視システム
リアルタイムでログを監視し、エラーや異常を検出
"""

import os
import time
import json
import re
from datetime import datetime, timedelta
from collections import defaultdict, deque
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler


class LogAnalyzer:
    def __init__(self):
        self.error_patterns = [
            r'ERROR',
            r'CRITICAL',
            r'FATAL',
            r'Exception',
            r'Traceback',
            r'Failed',
            r'Connection refused',
            r'Timeout',
            r'Memory error',
            r'Database error'
        ]
        
        self.warning_patterns = [
            r'WARNING',
            r'WARN',
            r'Deprecated',
            r'Retry',
            r'Slow query',
            r'High memory usage'
        ]
        
        self.performance_patterns = [
            r'(\d+\.?\d*)\s*ms',  # 応答時間
            r'(\d+\.?\d*)\s*seconds',  # 実行時間
            r'Memory:\s*(\d+\.?\d*)\s*MB',  # メモリ使用量
            r'CPU:\s*(\d+\.?\d*)%'  # CPU使用率
        ]
        
        self.stats = {
            'errors': deque(maxlen=1000),
            'warnings': deque(maxlen=1000),
            'performance': deque(maxlen=1000),
            'hourly_stats': defaultdict(lambda: {'errors': 0, 'warnings': 0, 'requests': 0})
        }
    
    def analyze_line(self, line, timestamp=None):
        """ログ行を分析"""
        if timestamp is None:
            timestamp = datetime.now()
        
        analysis = {
            'timestamp': timestamp,
            'line': line,
            'level': 'INFO',
            'issues': []
        }
        
        # エラーパターンチェック
        for pattern in self.error_patterns:
            if re.search(pattern, line, re.IGNORECASE):
                analysis['level'] = 'ERROR'
                analysis['issues'].append(f'Error pattern: {pattern}')
                self.stats['errors'].append(analysis)
                break
        
        # 警告パターンチェック
        if analysis['level'] != 'ERROR':
            for pattern in self.warning_patterns:
                if re.search(pattern, line, re.IGNORECASE):
                    analysis['level'] = 'WARNING'
                    analysis['issues'].append(f'Warning pattern: {pattern}')
                    self.stats['warnings'].append(analysis)
                    break
        
        # パフォーマンス情報抽出
        for pattern in self.performance_patterns:
            matches = re.findall(pattern, line, re.IGNORECASE)
            if matches:
                analysis['performance'] = matches
                self.stats['performance'].append(analysis)
        
        # 時間別統計更新
        hour_key = timestamp.strftime('%Y-%m-%d %H:00')
        if analysis['level'] == 'ERROR':
            self.stats['hourly_stats'][hour_key]['errors'] += 1
        elif analysis['level'] == 'WARNING':
            self.stats['hourly_stats'][hour_key]['warnings'] += 1
        
        # リクエスト数カウント
        if 'GET' in line or 'POST' in line or 'PUT' in line or 'DELETE' in line:
            self.stats['hourly_stats'][hour_key]['requests'] += 1
        
        return analysis
    
    def get_recent_errors(self, minutes=60):
        """最近のエラーを取得"""
        cutoff_time = datetime.now() - timedelta(minutes=minutes)
        recent_errors = [
            error for error in self.stats['errors']
            if error['timestamp'] > cutoff_time
        ]
        return recent_errors
    
    def get_performance_summary(self, minutes=60):
        """パフォーマンス要約を取得"""
        cutoff_time = datetime.now() - timedelta(minutes=minutes)
        recent_perf = [
            perf for perf in self.stats['performance']
            if perf['timestamp'] > cutoff_time
        ]
        
        response_times = []
        memory_usage = []
        cpu_usage = []
        
        for perf in recent_perf:
            if 'performance' in perf:
                for value in perf['performance']:
                    try:
                        num_value = float(value)
                        if 'ms' in perf['line']:
                            response_times.append(num_value)
                        elif 'MB' in perf['line']:
                            memory_usage.append(num_value)
                        elif '%' in perf['line']:
                            cpu_usage.append(num_value)
                    except ValueError:
                        continue
        
        summary = {}
        if response_times:
            summary['avg_response_time'] = sum(response_times) / len(response_times)
            summary['max_response_time'] = max(response_times)
        
        if memory_usage:
            summary['avg_memory_usage'] = sum(memory_usage) / len(memory_usage)
            summary['max_memory_usage'] = max(memory_usage)
        
        if cpu_usage:
            summary['avg_cpu_usage'] = sum(cpu_usage) / len(cpu_usage)
            summary['max_cpu_usage'] = max(cpu_usage)
        
        return summary
    
    def generate_report(self):
        """監視レポートを生成"""
        now = datetime.now()
        
        # 最近1時間のエラー
        recent_errors = self.get_recent_errors(60)
        
        # パフォーマンス要約
        perf_summary = self.get_performance_summary(60)
        
        # 時間別統計（最近24時間）
        last_24h = []
        for i in range(24):
            hour = now - timedelta(hours=i)
            hour_key = hour.strftime('%Y-%m-%d %H:00')
            stats = self.stats['hourly_stats'][hour_key]
            last_24h.append({
                'hour': hour_key,
                'errors': stats['errors'],
                'warnings': stats['warnings'],
                'requests': stats['requests']
            })
        
        report = {
            'timestamp': now.isoformat(),
            'summary': {
                'total_errors': len(self.stats['errors']),
                'total_warnings': len(self.stats['warnings']),
                'recent_errors_1h': len(recent_errors),
                'performance_summary': perf_summary
            },
            'recent_errors': [
                {
                    'timestamp': error['timestamp'].isoformat(),
                    'line': error['line'],
                    'issues': error['issues']
                }
                for error in recent_errors[-10:]  # 最新10件
            ],
            'hourly_stats': last_24h[:12]  # 最近12時間
        }
        
        return report


class LogFileHandler(FileSystemEventHandler):
    def __init__(self, analyzer, log_files):
        self.analyzer = analyzer
        self.log_files = log_files
        self.file_positions = {}
        
        # 初期位置を記録
        for log_file in log_files:
            if os.path.exists(log_file):
                with open(log_file, 'r', encoding='utf-8', errors='ignore') as f:
                    f.seek(0, 2)  # ファイル末尾に移動
                    self.file_positions[log_file] = f.tell()
    
    def on_modified(self, event):
        if event.is_directory:
            return
        
        if event.src_path in self.log_files:
            self.process_new_lines(event.src_path)
    
    def process_new_lines(self, file_path):
        """新しい行を処理"""
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                # 前回の位置から読み開始
                if file_path in self.file_positions:
                    f.seek(self.file_positions[file_path])
                
                new_lines = f.readlines()
                self.file_positions[file_path] = f.tell()
                
                for line in new_lines:
                    line = line.strip()
                    if line:
                        self.analyzer.analyze_line(line)
        
        except Exception as e:
            print(f"ログファイル処理エラー: {e}")


class LogMonitor:
    def __init__(self, log_directory='logs'):
        self.log_directory = log_directory
        self.analyzer = LogAnalyzer()
        self.observer = Observer()
        
        # 監視対象ログファイル
        self.log_files = [
            os.path.join(log_directory, 'pyspider.log'),
            os.path.join(log_directory, 'webui.log'),
            os.path.join(log_directory, 'error.log'),
            os.path.join(log_directory, 'performance.log'),
            os.path.join(log_directory, 'security_audit.log')
        ]
        
        # 存在するファイルのみを対象とする
        self.log_files = [f for f in self.log_files if os.path.exists(f)]
        
        if not self.log_files:
            print(f"警告: {log_directory} にログファイルが見つかりません")
    
    def start_monitoring(self):
        """監視開始"""
        if not self.log_files:
            print("監視対象のログファイルがありません")
            return
        
        print(f"ログ監視開始: {len(self.log_files)} ファイル")
        for log_file in self.log_files:
            print(f"  - {log_file}")
        
        # ファイルハンドラーを設定
        event_handler = LogFileHandler(self.analyzer, self.log_files)
        
        # ディレクトリを監視
        self.observer.schedule(event_handler, self.log_directory, recursive=False)
        self.observer.start()
        
        try:
            while True:
                time.sleep(60)  # 1分ごとにレポート生成
                self.generate_and_save_report()
        
        except KeyboardInterrupt:
            print("\n監視を停止します...")
            self.observer.stop()
        
        self.observer.join()
    
    def generate_and_save_report(self):
        """レポート生成と保存"""
        report = self.analyzer.generate_report()
        
        # コンソール出力
        print(f"\n📊 ログ監視レポート - {datetime.now().strftime('%H:%M:%S')}")
        print(f"  エラー数（総計）: {report['summary']['total_errors']}")
        print(f"  警告数（総計）: {report['summary']['total_warnings']}")
        print(f"  最近1時間のエラー: {report['summary']['recent_errors_1h']}")
        
        if report['summary']['performance_summary']:
            perf = report['summary']['performance_summary']
            if 'avg_response_time' in perf:
                print(f"  平均応答時間: {perf['avg_response_time']:.2f}ms")
            if 'avg_memory_usage' in perf:
                print(f"  平均メモリ使用量: {perf['avg_memory_usage']:.1f}MB")
        
        # ファイル保存
        timestamp = datetime.now().strftime('%Y%m%d_%H%M')
        os.makedirs('tests/monitoring/reports', exist_ok=True)
        
        with open(f'tests/monitoring/reports/log_report_{timestamp}.json', 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)


if __name__ == '__main__':
    monitor = LogMonitor()
    monitor.start_monitoring()
