{"taskdb": {"url": "sqlite+taskdb:///data/task.db"}, "projectdb": {"url": "sqlite+projectdb:///data/project.db"}, "resultdb": {"url": "sqlite+resultdb:///data/result.db"}, "message_queue": {"url": "redis://localhost:6379/db"}, "webui": {"username": "", "password": "", "need-auth": false, "host": "0.0.0.0", "port": 5000}, "fetcher": {"xmlrpc_host": "0.0.0.0", "xmlrpc_port": 24444}, "scheduler": {"xmlrpc_host": "0.0.0.0", "xmlrpc_port": 23333}, "result_worker": {"type": "file_output", "file_output": {"enabled": true, "output_dir": "test_results", "max_file_size": 10485760, "enable_rotation": true, "rotation_count": 5}}, "puppeteer_endpoint": "http://localhost:22223"}