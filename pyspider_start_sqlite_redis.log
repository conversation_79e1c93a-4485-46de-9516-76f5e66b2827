[2025-06-05 15:41:58] PySpider SQLite + Redisモード起動開始
[2025-06-05 15:41:58] SQLite + Redis設定ファイル作成開始
[2025-06-05 15:41:58] config.json SQLite設定更新成功
[2025-06-05 15:41:58] SQLite + Redis設定ファイル作成完了
[2025-06-05 15:41:58] 既存プロセスクリーンアップ開始
[2025-06-05 15:41:58] 既存プロセスクリーンアップ完了
[2025-06-05 15:41:58] SQLiteデータベース確認開始
[2025-06-05 15:41:58] データベースファイル未存在: data/pyspider_taskdb.db
[2025-06-05 15:41:58] データベースファイル未存在: data/pyspider_projectdb.db
[2025-06-05 15:41:58] データベースファイル未存在: data/pyspider_resultdb.db
[2025-06-05 15:41:58] SQLiteデータベース確認完了
[2025-06-05 15:41:58] Redis管理開始
[2025-06-05 15:41:58] Redis既存プロセス発見: 380514
477537
477538
477837
817284
817285
[2025-06-05 15:41:58] Redis接続テスト成功
[2025-06-05 15:41:58] Redis管理完了
[2025-06-05 15:42:00] Puppeteer Fetcher起動開始
[2025-06-05 15:42:06] Puppeteer Fetcher起動成功 (ポート: 22223, PID: 1093032)
[2025-06-05 15:42:06] スケジューラ起動開始
[2025-06-05 15:42:08] フェッチャー起動開始
[2025-06-05 15:42:10] プロセッサ起動開始
[2025-06-05 15:42:12] リザルトワーカー起動開始
[2025-06-05 15:42:14] WebUI起動開始
[2025-06-05 15:42:16] PySpider SQLite + Redisモード起動完了
[2025-06-05 16:03:49] PySpider終了処理開始
[2025-06-05 16:03:50] PySpider終了処理開始
[2025-06-05 16:03:52] PySpider終了処理完了
[2025-06-08 08:22:15] PySpider SQLite + Redisモード起動開始
[2025-06-08 08:22:15] SQLite + Redis設定ファイル作成開始
[2025-06-08 08:22:15] config.json SQLite設定更新成功
[2025-06-08 08:22:15] SQLite + Redis設定ファイル作成完了
[2025-06-08 08:22:15] 既存プロセスクリーンアップ開始
[2025-06-08 08:22:15] 既存プロセスクリーンアップ完了
[2025-06-08 08:22:15] SQLiteデータベース確認開始
[2025-06-08 08:22:15] データベースファイル未存在: data/pyspider_taskdb.db
[2025-06-08 08:22:15] データベースファイル未存在: data/pyspider_projectdb.db
[2025-06-08 08:22:15] データベースファイル未存在: data/pyspider_resultdb.db
[2025-06-08 08:22:15] SQLiteデータベース確認完了
[2025-06-08 08:22:15] Redis管理開始
[2025-06-08 08:22:15] Redis既存プロセス発見: 34722
34784
34845
36734
36782
36783
[2025-06-08 08:22:16] Redis接続テスト成功
[2025-06-08 08:22:16] Redis管理完了
[2025-06-08 08:22:18] Puppeteer Fetcher起動開始
[2025-06-08 08:22:26] Puppeteer Fetcher起動成功 (ポート: 22223, PID: 91461)
[2025-06-08 08:22:26] スケジューラ起動開始
[2025-06-08 08:23:39] PySpider SQLite + Redisモード起動開始
[2025-06-08 08:23:39] SQLite + Redis設定ファイル作成開始
[2025-06-08 08:23:39] config.json SQLite設定更新成功
[2025-06-08 08:23:39] SQLite + Redis設定ファイル作成完了
[2025-06-08 08:23:39] 既存プロセスクリーンアップ開始
[2025-06-08 08:23:39] Puppeteer Fetcherプロセスを終了: 91461
[2025-06-08 08:23:40] 既存プロセスクリーンアップ完了
[2025-06-08 08:23:40] SQLiteデータベース確認開始
[2025-06-08 08:23:40] データベースファイル確認: data/pyspider_taskdb.db
[2025-06-08 08:23:40] データベースファイル確認: data/pyspider_projectdb.db
[2025-06-08 08:23:40] データベースファイル確認: data/pyspider_resultdb.db
[2025-06-08 08:23:40] SQLiteデータベース確認完了
[2025-06-08 08:23:40] Redis管理開始
[2025-06-08 08:23:41] Redis既存プロセス発見: 34722
34784
34845
36734
36782
36783
[2025-06-08 08:23:41] Redis接続テスト成功
[2025-06-08 08:23:41] Redis管理完了
[2025-06-08 08:23:43] Puppeteer Fetcher起動開始
[2025-06-08 08:23:49] Puppeteer Fetcher起動成功 (ポート: 22223, PID: 93188)
[2025-06-08 08:23:49] スケジューラ起動開始
[2025-06-08 08:23:51] フェッチャー起動開始
[2025-06-08 08:23:53] プロセッサ起動開始
[2025-06-08 08:23:55] リザルトワーカー起動開始
[2025-06-08 08:23:57] WebUI起動開始
[2025-06-08 08:24:48] PySpider SQLite + Redisモード起動開始
[2025-06-08 08:24:48] SQLite + Redis設定ファイル作成開始
[2025-06-08 08:24:48] config.json SQLite設定更新成功
[2025-06-08 08:24:48] SQLite + Redis設定ファイル作成完了
[2025-06-08 08:24:48] 既存プロセスクリーンアップ開始
[2025-06-08 08:24:48] PySpiderプロセスを終了: 93327
93425
93450
93530
[2025-06-08 08:24:50] Puppeteer Fetcherプロセスを終了: 93188
[2025-06-08 08:24:52] 既存プロセスクリーンアップ完了
[2025-06-08 08:24:52] SQLiteデータベース確認開始
[2025-06-08 08:24:52] データベースファイル確認: data/pyspider_taskdb.db
[2025-06-08 08:24:52] データベースファイル確認: data/pyspider_projectdb.db
[2025-06-08 08:24:52] データベースファイル確認: data/pyspider_resultdb.db
[2025-06-08 08:24:52] SQLiteデータベース確認完了
[2025-06-08 08:24:52] Redis管理開始
[2025-06-08 08:24:52] Redis既存プロセス発見: 34722
34784
34845
36734
36782
36783
[2025-06-08 08:24:52] Redis接続テスト成功
[2025-06-08 08:24:52] Redis管理完了
[2025-06-08 08:24:54] Puppeteer Fetcher起動開始
[2025-06-08 08:25:00] Puppeteer Fetcher起動成功 (ポート: 22223, PID: 94835)
[2025-06-08 08:25:00] スケジューラ起動開始
[2025-06-08 08:25:02] フェッチャー起動開始
[2025-06-08 08:25:04] プロセッサ起動開始
[2025-06-08 08:25:06] リザルトワーカー起動開始
[2025-06-08 08:25:08] WebUI起動開始
[2025-06-08 08:25:11] PySpider SQLite + Redisモード起動完了
[2025-06-08 08:55:20] PySpider SQLite + Redisモード起動開始
[2025-06-08 08:55:20] MySQL + Redis設定ファイル作成開始
[2025-06-08 08:55:20] config.json MySQL設定更新成功
[2025-06-08 08:55:20] MySQL + Redis設定ファイル作成完了
[2025-06-08 08:55:20] 既存プロセスクリーンアップ開始
[2025-06-08 08:55:21] 既存プロセスクリーンアップ完了
[2025-06-08 08:55:21] MySQL接続確認スキップ、pyspiderに委任
[2025-06-08 08:55:21] Redis管理開始
[2025-06-08 08:55:21] Redis既存プロセス発見: 34722
34784
34845
36734
36782
36783
[2025-06-08 08:55:21] Redis接続テスト成功
[2025-06-08 08:55:21] Redis管理完了
[2025-06-08 08:55:23] Puppeteer Fetcher起動開始
[2025-06-08 08:55:30] Puppeteer Fetcher起動成功 (ポート: 22223, PID: 127161)
[2025-06-08 08:55:30] スケジューラ起動開始
[2025-06-08 08:55:32] フェッチャー起動開始
[2025-06-08 08:55:34] プロセッサ起動開始
[2025-06-08 08:55:36] リザルトワーカー起動開始
[2025-06-08 08:55:38] WebUI起動開始
[2025-06-08 08:55:40] WebUI-Next起動開始
[2025-06-08 08:55:45] 警告: WebUI-Next起動確認タイムアウト
[2025-06-08 08:55:45] PySpider SQLite + Redisモード起動完了
[2025-06-08 09:02:12] PySpider SQLite + Redisモード起動開始
[2025-06-08 09:02:12] MySQL + Redis設定ファイル作成開始
[2025-06-08 09:02:12] config.json MySQL設定更新成功
[2025-06-08 09:02:12] MySQL + Redis設定ファイル作成完了
[2025-06-08 09:02:12] 既存プロセスクリーンアップ開始
[2025-06-08 09:02:12] 既存プロセスクリーンアップ完了
[2025-06-08 09:02:12] MySQL接続確認スキップ、pyspiderに委任
[2025-06-08 09:02:12] Redis管理開始
[2025-06-08 09:02:12] Redis既存プロセス発見: 34722
34784
34845
36734
36782
36783
[2025-06-08 09:02:12] Redis接続テスト成功
[2025-06-08 09:02:12] Redis管理完了
[2025-06-08 09:02:14] Puppeteer Fetcher起動開始
[2025-06-08 09:02:21] Puppeteer Fetcher起動成功 (ポート: 22223, PID: 134856)
[2025-06-08 09:02:21] スケジューラ起動開始
[2025-06-08 09:02:23] フェッチャー起動開始
[2025-06-08 09:02:25] プロセッサ起動開始
[2025-06-08 09:02:27] リザルトワーカー起動開始
[2025-06-08 09:02:29] WebUI起動開始
[2025-06-08 09:02:34] WebUI-Next起動開始
[2025-06-08 09:02:34] WebUI-Next起動成功 (ポート: 3000)
[2025-06-08 09:02:34] PySpider SQLite + Redisモード起動完了
[2025-06-08 09:08:40] PySpider SQLite + Redisモード起動開始
[2025-06-08 09:08:40] MySQL + Redis設定ファイル作成開始
[2025-06-08 09:08:40] config.json MySQL設定更新成功
[2025-06-08 09:08:40] MySQL + Redis設定ファイル作成完了
[2025-06-08 09:08:40] 既存プロセスクリーンアップ開始
[2025-06-08 09:08:41] 既存プロセスクリーンアップ完了
[2025-06-08 09:08:41] MySQL接続確認スキップ、pyspiderに委任
[2025-06-08 09:08:41] Redis管理開始
[2025-06-08 09:08:41] Redis既存プロセス発見: 34722
34784
34845
36734
36782
36783
[2025-06-08 09:08:41] Redis接続テスト成功
[2025-06-08 09:08:41] Redis管理完了
[2025-06-08 09:08:43] Puppeteer Fetcher起動開始
[2025-06-08 09:08:49] Puppeteer Fetcher起動成功 (ポート: 22223, PID: 142042)
[2025-06-08 09:08:50] スケジューラ起動開始
[2025-06-08 09:08:52] フェッチャー起動開始
[2025-06-08 09:08:54] プロセッサ起動開始
[2025-06-08 09:08:56] リザルトワーカー起動開始
[2025-06-08 09:08:58] WebUI起動開始
[2025-06-08 09:09:00] WebUI-Next起動開始
[2025-06-08 09:09:00] WebUI-Next起動成功 (ポート: 3000)
[2025-06-08 09:09:00] PySpider SQLite + Redisモード起動完了
[2025-06-08 09:40:51] PySpider SQLite + Redisモード起動開始
[2025-06-08 09:40:51] MySQL + Redis設定ファイル作成開始
[2025-06-08 09:40:51] config.json MySQL設定更新成功
[2025-06-08 09:40:51] MySQL + Redis設定ファイル作成完了
[2025-06-08 09:40:51] 既存プロセスクリーンアップ開始
[2025-06-08 09:40:51] Puppeteer Fetcherプロセスを終了: 142042
[2025-06-08 09:40:52] 既存プロセスクリーンアップ完了
[2025-06-08 09:40:52] MySQL接続確認スキップ、pyspiderに委任
[2025-06-08 09:40:52] Redis管理開始
[2025-06-08 09:40:52] Redis新規起動開始
[2025-06-08 09:40:55] Redis新規起動成功 (PID: )
[2025-06-08 09:40:55] Redis管理完了
[2025-06-08 09:40:57] Puppeteer Fetcher起動開始
[2025-06-08 09:41:03] Puppeteer Fetcher起動成功 (ポート: 22223, PID: 184561)
[2025-06-08 09:41:03] スケジューラ起動開始
[2025-06-08 09:41:05] フェッチャー起動開始
[2025-06-08 09:41:07] プロセッサ起動開始
[2025-06-08 09:41:09] リザルトワーカー起動開始
[2025-06-08 09:41:11] WebUI起動開始
[2025-06-08 09:41:13] WebUI-Next起動開始
[2025-06-08 09:41:15] WebUI-Next起動成功 (ポート: 3000)
[2025-06-08 09:41:15] PySpider SQLite + Redisモード起動完了
