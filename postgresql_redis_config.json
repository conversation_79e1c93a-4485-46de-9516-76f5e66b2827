{"taskdb": "sqlalchemy+postgresql+psycopg2://pyspider:PySpider2024!SecurePass#@localhost:5432/pyspider_taskdb", "projectdb": "sqlalchemy+postgresql+psycopg2://pyspider:PySpider2024!SecurePass#@localhost:5432/pyspider_projectdb", "resultdb": "sqlalchemy+postgresql+psycopg2://pyspider:PySpider2024!SecurePass#@localhost:5432/pyspider_resultdb", "message_queue": "redis://localhost:6379/0", "usedatabase": "postgresql", "redis_fallback": {"host": "localhost", "port": 6379, "db": 0, "password": null}, "webui": {"username": "admin", "password": "PySpider2024!SecurePass#", "need-auth": true, "host": "0.0.0.0", "port": 5000}, "scheduler": {"xmlrpc-host": "0.0.0.0", "xmlrpc-port": 23333}, "fetcher": {"xmlrpc": false}, "processor": {"checker-disabled": false}, "result-worker": {"result-cls": "pyspider.result.result_worker:ResultWorker"}}