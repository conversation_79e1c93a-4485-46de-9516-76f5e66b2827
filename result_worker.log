{"timestamp": "2025-06-08T09:41:10.103881", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203}
[I 250608 09:41:10 memory_optimizer:57] Memory optimizer initialized with max_memory_percent=80.0%, gc_interval=60s, check_interval=30s, auto_optimize=True
[I 250608 09:41:10 memory_optimizer:69] Memory monitoring started
[I 250608 09:41:10 run:31] Created PID file for result_worker: /tmp/pyspider_result_worker.pid
[I 250608 09:41:10 result_worker:54] result_worker starting...
[I 250608 09:42:10 metrics:173] Gauges: {"memory_usage_rss": 64536576, "memory_usage_vms": 154652672, "memory_usage_percent": 0.19220662871329885, "system_memory_total": 33576665088, "system_memory_available": 28209782784, "system_memory_percent": 16.0}
[I 250608 09:43:10 metrics:173] Gauges: {"memory_usage_rss": 64536576, "memory_usage_vms": 154652672, "memory_usage_percent": 0.19220662871329885, "system_memory_total": 33576665088, "system_memory_available": 27621003264, "system_memory_percent": 17.7}
[I 250608 09:44:10 metrics:173] Gauges: {"memory_usage_rss": 64536576, "memory_usage_vms": 154652672, "memory_usage_percent": 0.19220662871329885, "system_memory_total": 33576665088, "system_memory_available": 27217784832, "system_memory_percent": 18.9}
[I 250608 09:45:10 metrics:173] Gauges: {"memory_usage_rss": 64536576, "memory_usage_vms": 154652672, "memory_usage_percent": 0.19220662871329885, "system_memory_total": 33576665088, "system_memory_available": 27093807104, "system_memory_percent": 19.3}
[I 250608 09:46:10 metrics:173] Gauges: {"memory_usage_rss": 64536576, "memory_usage_vms": 154652672, "memory_usage_percent": 0.19220662871329885, "system_memory_total": 33576665088, "system_memory_available": 26843873280, "system_memory_percent": 20.1}
[I 250608 09:47:10 metrics:173] Gauges: {"memory_usage_rss": 64536576, "memory_usage_vms": 154652672, "memory_usage_percent": 0.19220662871329885, "system_memory_total": 33576665088, "system_memory_available": 26939940864, "system_memory_percent": 19.8}
[I 250608 09:48:10 metrics:173] Gauges: {"memory_usage_rss": 64536576, "memory_usage_vms": 154652672, "memory_usage_percent": 0.19220662871329885, "system_memory_total": 33576665088, "system_memory_available": 26557186048, "system_memory_percent": 20.9}
[I 250608 09:49:10 metrics:173] Gauges: {"memory_usage_rss": 64536576, "memory_usage_vms": 154652672, "memory_usage_percent": 0.19220662871329885, "system_memory_total": 33576665088, "system_memory_available": 26957750272, "system_memory_percent": 19.7}
[I 250608 09:50:10 metrics:173] Gauges: {"memory_usage_rss": 64536576, "memory_usage_vms": 154652672, "memory_usage_percent": 0.19220662871329885, "system_memory_total": 33576665088, "system_memory_available": 27043360768, "system_memory_percent": 19.5}
[I 250608 09:51:10 metrics:173] Gauges: {"memory_usage_rss": 64536576, "memory_usage_vms": 154652672, "memory_usage_percent": 0.19220662871329885, "system_memory_total": 33576665088, "system_memory_available": 26886692864, "system_memory_percent": 19.9}
[I 250608 09:52:10 metrics:173] Gauges: {"memory_usage_rss": 64536576, "memory_usage_vms": 154652672, "memory_usage_percent": 0.19220662871329885, "system_memory_total": 33576665088, "system_memory_available": 28142010368, "system_memory_percent": 16.2}
[I 250608 09:53:10 metrics:173] Gauges: {"memory_usage_rss": 64536576, "memory_usage_vms": 154652672, "memory_usage_percent": 0.19220662871329885, "system_memory_total": 33576665088, "system_memory_available": 26886856704, "system_memory_percent": 19.9}
[I 250608 09:54:10 metrics:173] Gauges: {"memory_usage_rss": 64536576, "memory_usage_vms": 154652672, "memory_usage_percent": 0.19220662871329885, "system_memory_total": 33576665088, "system_memory_available": 27197186048, "system_memory_percent": 19.0}
[I 250608 09:55:10 metrics:173] Gauges: {"memory_usage_rss": 64536576, "memory_usage_vms": 154652672, "memory_usage_percent": 0.19220662871329885, "system_memory_total": 33576665088, "system_memory_available": 26961850368, "system_memory_percent": 19.7}
[I 250608 09:56:10 metrics:173] Gauges: {"memory_usage_rss": 64536576, "memory_usage_vms": 154652672, "memory_usage_percent": 0.19220662871329885, "system_memory_total": 33576665088, "system_memory_available": 26475630592, "system_memory_percent": 21.1}
