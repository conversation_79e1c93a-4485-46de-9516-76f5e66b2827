{"taskid": "test_task_1", "project": "example_project", "url": "https://example.com/page1", "result": {"title": "Example Page 1", "content": "This is test content 1"}, "updatetime": 1749350400.0, "timestamp": "2025-06-08T11:40:00"}
{"taskid": "test_task_2", "project": "example_project", "url": "https://example.com/page2", "result": {"title": "Example Page 2", "content": "This is test content 2"}, "updatetime": 1749350460.0, "timestamp": "2025-06-08T11:41:00"}
{"taskid": "test_task_3", "project": "example_project", "url": "https://example.com/page3", "result": {"title": "Example Page 3", "content": "This is test content 3"}, "updatetime": 1749350520.0, "timestamp": "2025-06-08T11:42:00"}
