#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
ファイル出力機能のテストスクリプト
"""

import time
import json
from pyspider.libs.base_handler import BaseHandler


class Handler(BaseHandler):
    """
    ファイル出力機能をテストするためのハンドラー
    """
    
    def on_start(self):
        """開始時に複数のURLをクロール"""
        urls = [
            'https://httpbin.org/json',
            'https://httpbin.org/uuid',
            'https://httpbin.org/ip',
            'https://httpbin.org/user-agent',
            'https://httpbin.org/headers'
        ]
        
        for i, url in enumerate(urls):
            self.crawl(url, callback=self.parse_data, save={'index': i})
    
    def parse_data(self, response):
        """データを解析してJSONL形式で保存"""
        try:
            data = response.json
            
            # 結果データを構造化
            result = {
                'url': response.url,
                'status_code': response.status_code,
                'timestamp': time.time(),
                'index': response.save.get('index', 0),
                'data': data,
                'headers': dict(response.headers),
                'processing_time': time.time() - response.save.get('start_time', time.time())
            }
            
            return result
            
        except Exception as e:
            return {
                'url': response.url,
                'error': str(e),
                'status_code': response.status_code,
                'timestamp': time.time()
            }
    
    @every(minutes=1)
    def periodic_crawl(self):
        """定期的なクロール（テスト用）"""
        self.crawl('https://httpbin.org/json', callback=self.parse_periodic)
    
    def parse_periodic(self, response):
        """定期クロールの結果処理"""
        return {
            'type': 'periodic',
            'url': response.url,
            'timestamp': time.time(),
            'data': response.json if response.json else None
        }
