[2025-06-05 07:23:45] PySpider MySQL + Redisモード起動開始
[2025-06-05 07:23:45] MySQL + Redis設定ファイル作成開始
[2025-06-05 07:23:45] config.json MySQL設定更新成功
[2025-06-05 07:23:45] MySQL + Redis設定ファイル作成完了
[2025-06-05 07:23:45] 既存プロセスクリーンアップ開始
[2025-06-05 07:23:45] Puppeteer Fetcherプロセスを終了: 986965
[2025-06-05 07:23:46] 既存プロセスクリーンアップ完了
[2025-06-05 07:23:46] MySQL接続確認スキップ、pyspiderに委任
[2025-06-05 07:23:46] Redis管理開始
[2025-06-05 07:23:46] Redis既存プロセス発見: 380514
477537
477538
477837
817284
817285
[2025-06-05 07:23:46] Redis接続テスト成功
[2025-06-05 07:23:46] Redis管理完了
[2025-06-05 07:23:48] Puppeteer Fetcher起動開始
[2025-06-05 07:23:54] Puppeteer Fetcher起動成功 (ポート: 22223, PID: 987708)
[2025-06-05 07:23:54] スケジューラ起動開始
[2025-06-05 07:23:57] フェッチャー起動開始
[2025-06-05 07:23:59] プロセッサ起動開始
[2025-06-05 07:24:01] リザルトワーカー起動開始
[2025-06-05 07:24:03] WebUI起動開始
[2025-06-05 07:24:05] PySpider MySQL + Redisモード起動完了
[2025-06-05 07:25:36] PySpider MySQL + Redisモード起動開始
[2025-06-05 07:25:36] MySQL + Redis設定ファイル作成開始
[2025-06-05 07:25:36] config.json MySQL設定更新成功
[2025-06-05 07:25:36] MySQL + Redis設定ファイル作成完了
[2025-06-05 07:25:36] 既存プロセスクリーンアップ開始
[2025-06-05 07:25:36] Puppeteer Fetcherプロセスを終了: 987708
[2025-06-05 07:25:37] 既存プロセスクリーンアップ完了
[2025-06-05 07:25:37] MySQL接続確認スキップ、pyspiderに委任
[2025-06-05 07:25:37] Redis管理開始
[2025-06-05 07:25:37] Redis既存プロセス発見: 380514
477537
477538
477837
817284
817285
[2025-06-05 07:25:37] Redis接続テスト成功
[2025-06-05 07:25:37] Redis管理完了
[2025-06-05 07:25:39] Puppeteer Fetcher起動開始
[2025-06-05 07:25:46] Puppeteer Fetcher起動成功 (ポート: 22223, PID: 988502)
[2025-06-05 07:25:46] スケジューラ起動開始
[2025-06-05 07:25:48] フェッチャー起動開始
[2025-06-05 07:25:50] プロセッサ起動開始
[2025-06-05 07:25:52] リザルトワーカー起動開始
[2025-06-05 07:25:54] WebUI起動開始
[2025-06-05 07:25:56] PySpider MySQL + Redisモード起動完了
[2025-06-05 07:31:17] PySpider MySQL + Redisモード起動開始
[2025-06-05 07:31:17] MySQL + Redis設定ファイル作成開始
[2025-06-05 07:31:18] config.json MySQL設定更新成功
[2025-06-05 07:31:18] MySQL + Redis設定ファイル作成完了
[2025-06-05 07:31:18] 既存プロセスクリーンアップ開始
[2025-06-05 07:31:18] Puppeteer Fetcherプロセスを終了: 988502
[2025-06-05 07:31:19] 既存プロセスクリーンアップ完了
[2025-06-05 07:31:19] MySQL接続確認スキップ、pyspiderに委任
[2025-06-05 07:31:19] Redis管理開始
[2025-06-05 07:31:19] Redis既存プロセス発見: 380514
477537
477538
477837
817284
817285
[2025-06-05 07:31:19] Redis接続テスト成功
[2025-06-05 07:31:19] Redis管理完了
[2025-06-05 07:31:21] Puppeteer Fetcher起動開始
[2025-06-05 07:31:27] Puppeteer Fetcher起動成功 (ポート: 22223, PID: 990287)
[2025-06-05 07:31:27] スケジューラ起動開始
[2025-06-05 07:31:30] フェッチャー起動開始
[2025-06-05 07:31:32] プロセッサ起動開始
[2025-06-05 07:31:34] リザルトワーカー起動開始
[2025-06-05 07:31:36] WebUI起動開始
[2025-06-05 07:31:38] PySpider MySQL + Redisモード起動完了
[2025-06-05 07:34:18] PySpider MySQL + Redisモード起動開始
[2025-06-05 07:34:18] MySQL + Redis設定ファイル作成開始
[2025-06-05 07:34:18] config.json MySQL設定更新成功
[2025-06-05 07:34:18] MySQL + Redis設定ファイル作成完了
[2025-06-05 07:34:18] 既存プロセスクリーンアップ開始
[2025-06-05 07:34:18] Puppeteer Fetcherプロセスを終了: 990287
[2025-06-05 07:34:19] 既存プロセスクリーンアップ完了
[2025-06-05 07:34:19] MySQL接続確認スキップ、pyspiderに委任
[2025-06-05 07:34:19] Redis管理開始
[2025-06-05 07:34:19] Redis既存プロセス発見: 380514
477537
477538
477837
817284
817285
[2025-06-05 07:34:19] Redis接続テスト成功
[2025-06-05 07:34:19] Redis管理完了
[2025-06-05 07:34:21] Puppeteer Fetcher起動開始
[2025-06-05 07:34:27] Puppeteer Fetcher起動成功 (ポート: 22223, PID: 991188)
[2025-06-05 07:34:27] スケジューラ起動開始
[2025-06-05 07:34:29] フェッチャー起動開始
[2025-06-05 07:34:31] プロセッサ起動開始
[2025-06-05 07:34:33] リザルトワーカー起動開始
[2025-06-05 07:34:35] WebUI起動開始
[2025-06-05 07:34:38] PySpider MySQL + Redisモード起動完了
[2025-06-05 17:12:20] PySpider MySQL + Redisモード起動開始
[2025-06-05 17:12:20] MySQL + Redis設定ファイル作成開始
[2025-06-05 17:12:20] config.json MySQL設定更新成功
[2025-06-05 17:12:20] MySQL + Redis設定ファイル作成完了
[2025-06-05 17:12:20] 既存プロセスクリーンアップ開始
[2025-06-05 17:12:20] PySpiderプロセスを終了: 1101867
1102061
[2025-06-05 17:12:23] 既存プロセスクリーンアップ完了
[2025-06-05 17:12:23] MySQL接続確認スキップ、pyspiderに委任
[2025-06-05 17:12:23] Redis管理開始
[2025-06-05 17:12:23] Redis既存プロセス発見: 380514
477537
477538
477837
817284
817285
[2025-06-05 17:12:23] Redis接続テスト成功
[2025-06-05 17:12:23] Redis管理完了
[2025-06-05 17:12:25] Puppeteer Fetcher起動開始
[2025-06-05 17:12:34] Puppeteer Fetcher起動成功 (ポート: 22223, PID: 1104769)
[2025-06-05 17:12:34] スケジューラ起動開始
[2025-06-05 17:12:36] フェッチャー起動開始
[2025-06-05 17:12:38] プロセッサ起動開始
[2025-06-05 17:12:40] リザルトワーカー起動開始
[2025-06-05 17:12:42] WebUI起動開始
[2025-06-05 17:12:44] PySpider MySQL + Redisモード起動完了
[2025-06-05 17:17:41] PySpider MySQL + Redisモード起動開始
[2025-06-05 17:17:41] MySQL + Redis設定ファイル作成開始
[2025-06-05 17:17:41] config.json MySQL設定更新成功
[2025-06-05 17:17:41] MySQL + Redis設定ファイル作成完了
[2025-06-05 17:17:41] 既存プロセスクリーンアップ開始
[2025-06-05 17:17:41] Puppeteer Fetcherプロセスを終了: 1104769
[2025-06-05 17:17:42] 既存プロセスクリーンアップ完了
[2025-06-05 17:17:42] MySQL接続確認スキップ、pyspiderに委任
[2025-06-05 17:17:42] Redis管理開始
[2025-06-05 17:17:42] Redis既存プロセス発見: 380514
477537
477538
477837
817284
817285
[2025-06-05 17:17:42] Redis接続テスト成功
[2025-06-05 17:17:42] Redis管理完了
[2025-06-05 17:17:44] Puppeteer Fetcher起動開始
[2025-06-05 17:17:50] Puppeteer Fetcher起動成功 (ポート: 22223, PID: 1105801)
[2025-06-05 17:17:50] スケジューラ起動開始
[2025-06-05 17:17:52] フェッチャー起動開始
[2025-06-05 17:17:54] プロセッサ起動開始
[2025-06-05 17:17:56] リザルトワーカー起動開始
[2025-06-05 17:17:58] WebUI起動開始
[2025-06-05 17:18:00] PySpider MySQL + Redisモード起動完了
[2025-06-05 17:21:09] PySpider終了処理開始
[2025-06-05 17:21:10] PySpider終了処理開始
[2025-06-05 17:21:12] PySpider終了処理完了
[2025-06-05 17:22:20] PySpider MySQL + Redisモード起動開始
[2025-06-05 17:22:20] MySQL + Redis設定ファイル作成開始
[2025-06-05 17:22:20] config.json MySQL設定更新成功
[2025-06-05 17:22:20] MySQL + Redis設定ファイル作成完了
[2025-06-05 17:22:20] 既存プロセスクリーンアップ開始
[2025-06-05 17:22:20] 既存プロセスクリーンアップ完了
[2025-06-05 17:22:20] MySQL接続確認スキップ、pyspiderに委任
[2025-06-05 17:22:20] Redis管理開始
[2025-06-05 17:22:20] Redis既存プロセス発見: 380514
477537
477538
477837
817284
817285
[2025-06-05 17:22:20] Redis接続テスト成功
[2025-06-05 17:22:20] Redis管理完了
[2025-06-05 17:22:22] Puppeteer Fetcher起動開始
[2025-06-05 17:22:28] Puppeteer Fetcher起動成功 (ポート: 22223, PID: 1106846)
[2025-06-05 17:22:28] スケジューラ起動開始
[2025-06-05 17:22:30] フェッチャー起動開始
[2025-06-05 17:22:33] プロセッサ起動開始
[2025-06-05 17:22:35] リザルトワーカー起動開始
[2025-06-05 17:22:37] WebUI起動開始
[2025-06-05 17:22:39] PySpider MySQL + Redisモード起動完了
[2025-06-05 17:40:50] PySpider MySQL + Redisモード起動開始
[2025-06-05 17:40:50] MySQL + Redis設定ファイル作成開始
[2025-06-05 17:40:50] config.json MySQL設定更新成功
[2025-06-05 17:40:50] MySQL + Redis設定ファイル作成完了
[2025-06-05 17:40:50] 既存プロセスクリーンアップ開始
[2025-06-05 17:40:50] Puppeteer Fetcherプロセスを終了: 1106846
[2025-06-05 17:40:51] 既存プロセスクリーンアップ完了
[2025-06-05 17:40:51] MySQL接続確認スキップ、pyspiderに委任
[2025-06-05 17:40:51] Redis管理開始
[2025-06-05 17:40:51] Redis既存プロセス発見: 380514
477537
477538
477837
817284
817285
[2025-06-05 17:40:51] Redis接続テスト成功
[2025-06-05 17:40:51] Redis管理完了
[2025-06-05 17:40:53] Puppeteer Fetcher起動開始
[2025-06-05 17:40:59] Puppeteer Fetcher起動成功 (ポート: 22223, PID: 1109456)
[2025-06-05 17:40:59] スケジューラ起動開始
[2025-06-05 17:41:01] フェッチャー起動開始
[2025-06-05 17:41:04] プロセッサ起動開始
[2025-06-05 17:41:06] リザルトワーカー起動開始
[2025-06-05 17:41:08] WebUI起動開始
[2025-06-05 17:41:10] PySpider MySQL + Redisモード起動完了
[2025-06-05 17:50:25] PySpider MySQL + Redisモード起動開始
[2025-06-05 17:50:25] MySQL + Redis設定ファイル作成開始
[2025-06-05 17:50:25] config.json MySQL設定更新成功
[2025-06-05 17:50:25] MySQL + Redis設定ファイル作成完了
[2025-06-05 17:50:25] 既存プロセスクリーンアップ開始
[2025-06-05 17:50:25] Puppeteer Fetcherプロセスを終了: 1109456
[2025-06-05 17:50:26] 既存プロセスクリーンアップ完了
[2025-06-05 17:50:26] MySQL接続確認スキップ、pyspiderに委任
[2025-06-05 17:50:26] Redis管理開始
[2025-06-05 17:50:26] Redis既存プロセス発見: 380514
477537
477538
477837
817284
817285
[2025-06-05 17:50:26] Redis接続テスト成功
[2025-06-05 17:50:26] Redis管理完了
[2025-06-05 17:50:28] Puppeteer Fetcher起動開始
[2025-06-05 17:50:34] Puppeteer Fetcher起動成功 (ポート: 22223, PID: 1111257)
[2025-06-05 17:50:34] スケジューラ起動開始
[2025-06-05 17:50:36] フェッチャー起動開始
[2025-06-05 17:50:39] プロセッサ起動開始
[2025-06-05 17:50:41] リザルトワーカー起動開始
[2025-06-05 17:50:43] WebUI起動開始
[2025-06-05 17:50:45] PySpider MySQL + Redisモード起動完了
[2025-06-05 17:57:04] PySpider MySQL + Redisモード起動開始
[2025-06-05 17:57:04] MySQL + Redis設定ファイル作成開始
[2025-06-05 17:57:04] config.json MySQL設定更新成功
[2025-06-05 17:57:04] MySQL + Redis設定ファイル作成完了
[2025-06-05 17:57:04] 既存プロセスクリーンアップ開始
[2025-06-05 17:57:04] Puppeteer Fetcherプロセスを終了: 1111257
[2025-06-05 17:57:05] 既存プロセスクリーンアップ完了
[2025-06-05 17:57:05] MySQL接続確認スキップ、pyspiderに委任
[2025-06-05 17:57:05] Redis管理開始
[2025-06-05 17:57:05] Redis既存プロセス発見: 380514
477537
477538
477837
817284
817285
[2025-06-05 17:57:05] Redis接続テスト成功
[2025-06-05 17:57:05] Redis管理完了
[2025-06-05 17:57:07] Puppeteer Fetcher起動開始
[2025-06-05 17:57:14] Puppeteer Fetcher起動成功 (ポート: 22223, PID: 1112684)
[2025-06-05 17:57:14] スケジューラ起動開始
[2025-06-05 17:57:16] フェッチャー起動開始
[2025-06-05 17:57:18] プロセッサ起動開始
[2025-06-05 17:57:20] リザルトワーカー起動開始
[2025-06-05 17:57:22] WebUI起動開始
[2025-06-05 17:57:24] PySpider MySQL + Redisモード起動完了
[2025-06-05 18:10:15] PySpider MySQL + Redisモード起動開始
[2025-06-05 18:10:15] MySQL + Redis設定ファイル作成開始
[2025-06-05 18:10:15] config.json MySQL設定更新成功
[2025-06-05 18:10:15] MySQL + Redis設定ファイル作成完了
[2025-06-05 18:10:15] 既存プロセスクリーンアップ開始
[2025-06-05 18:10:15] Puppeteer Fetcherプロセスを終了: 1112684
[2025-06-05 18:10:16] 既存プロセスクリーンアップ完了
[2025-06-05 18:10:16] MySQL接続確認スキップ、pyspiderに委任
[2025-06-05 18:10:16] Redis管理開始
[2025-06-05 18:10:16] Redis既存プロセス発見: 380514
477537
477538
477837
817284
817285
[2025-06-05 18:10:16] Redis接続テスト成功
[2025-06-05 18:10:16] Redis管理完了
[2025-06-05 18:10:18] Puppeteer Fetcher起動開始
[2025-06-05 18:10:24] Puppeteer Fetcher起動成功 (ポート: 22223, PID: 1114482)
[2025-06-05 18:10:24] スケジューラ起動開始
[2025-06-05 18:10:26] フェッチャー起動開始
[2025-06-05 18:10:28] プロセッサ起動開始
[2025-06-05 18:10:30] リザルトワーカー起動開始
[2025-06-05 18:10:33] WebUI起動開始
[2025-06-05 18:10:37] PySpider MySQL + Redisモード起動完了
[2025-06-05 18:26:11] PySpider MySQL + Redisモード起動開始
[2025-06-05 18:26:11] MySQL + Redis設定ファイル作成開始
[2025-06-05 18:26:11] config.json MySQL設定更新成功
[2025-06-05 18:26:11] MySQL + Redis設定ファイル作成完了
[2025-06-05 18:26:11] 既存プロセスクリーンアップ開始
[2025-06-05 18:26:11] Puppeteer Fetcherプロセスを終了: 1114482
[2025-06-05 18:26:12] 既存プロセスクリーンアップ完了
[2025-06-05 18:26:12] MySQL接続確認スキップ、pyspiderに委任
[2025-06-05 18:26:12] Redis管理開始
[2025-06-05 18:26:12] Redis既存プロセス発見: 380514
477537
477538
477837
817284
817285
[2025-06-05 18:26:12] Redis接続テスト成功
[2025-06-05 18:26:12] Redis管理完了
[2025-06-05 18:26:14] Puppeteer Fetcher起動開始
[2025-06-05 18:26:21] Puppeteer Fetcher起動成功 (ポート: 22223, PID: 1117791)
[2025-06-05 18:26:21] スケジューラ起動開始
[2025-06-05 18:26:23] フェッチャー起動開始
[2025-06-05 18:26:25] プロセッサ起動開始
[2025-06-05 18:26:27] リザルトワーカー起動開始
[2025-06-05 18:26:29] WebUI起動開始
[2025-06-05 18:26:36] PySpider MySQL + Redisモード起動完了
[2025-06-05 18:36:24] PySpider MySQL + Redisモード起動開始
[2025-06-05 18:36:24] MySQL + Redis設定ファイル作成開始
[2025-06-05 18:36:24] config.json MySQL設定更新成功
[2025-06-05 18:36:24] MySQL + Redis設定ファイル作成完了
[2025-06-05 18:36:24] 既存プロセスクリーンアップ開始
[2025-06-05 18:36:24] Puppeteer Fetcherプロセスを終了: 1117791
[2025-06-05 18:36:25] Puppeteer Fetcherプロセス強制終了: 1117791
[2025-06-05 18:36:25] 既存プロセスクリーンアップ完了
[2025-06-05 18:36:25] MySQL接続確認スキップ、pyspiderに委任
[2025-06-05 18:36:25] Redis管理開始
[2025-06-05 18:36:25] Redis既存プロセス発見: 380514
477537
477538
477837
817284
817285
[2025-06-05 18:36:25] Redis接続テスト成功
[2025-06-05 18:36:25] Redis管理完了
[2025-06-05 18:36:27] Puppeteer Fetcher起動開始
[2025-06-05 18:36:33] Puppeteer Fetcher起動成功 (ポート: 22223, PID: 1120036)
[2025-06-05 18:36:33] スケジューラ起動開始
[2025-06-05 18:36:36] フェッチャー起動開始
[2025-06-05 18:36:38] プロセッサ起動開始
[2025-06-05 18:36:40] リザルトワーカー起動開始
[2025-06-05 18:36:42] WebUI起動開始
[2025-06-05 18:36:48] PySpider MySQL + Redisモード起動完了
[2025-06-05 18:47:27] PySpider MySQL + Redisモード起動開始
[2025-06-05 18:47:27] MySQL + Redis設定ファイル作成開始
[2025-06-05 18:47:27] config.json MySQL設定更新成功
[2025-06-05 18:47:27] MySQL + Redis設定ファイル作成完了
[2025-06-05 18:47:27] 既存プロセスクリーンアップ開始
[2025-06-05 18:47:28] Puppeteer Fetcherプロセスを終了: 1120036
[2025-06-05 18:47:29] Puppeteer Fetcherプロセス強制終了: 1120036
[2025-06-05 18:47:29] 既存プロセスクリーンアップ完了
[2025-06-05 18:47:29] MySQL接続確認スキップ、pyspiderに委任
[2025-06-05 18:47:29] Redis管理開始
[2025-06-05 18:47:29] Redis既存プロセス発見: 380514
477537
477538
477837
817284
817285
[2025-06-05 18:47:29] Redis接続テスト成功
[2025-06-05 18:47:29] Redis管理完了
[2025-06-05 18:47:31] Puppeteer Fetcher起動開始
[2025-06-05 18:47:37] Puppeteer Fetcher起動成功 (ポート: 22223, PID: 1122218)
[2025-06-05 18:47:37] スケジューラ起動開始
[2025-06-05 18:47:39] フェッチャー起動開始
[2025-06-05 18:47:41] プロセッサ起動開始
[2025-06-05 18:47:43] リザルトワーカー起動開始
[2025-06-05 18:47:45] WebUI起動開始
[2025-06-05 18:47:50] PySpider MySQL + Redisモード起動完了
[2025-06-05 19:05:57] PySpider MySQL + Redisモード起動開始
[2025-06-05 19:05:57] MySQL + Redis設定ファイル作成開始
[2025-06-05 19:05:57] config.json MySQL設定更新成功
[2025-06-05 19:05:57] MySQL + Redis設定ファイル作成完了
[2025-06-05 19:05:57] 既存プロセスクリーンアップ開始
[2025-06-05 19:05:57] Puppeteer Fetcherプロセスを終了: 1122218
[2025-06-05 19:05:58] Puppeteer Fetcherプロセス強制終了: 1122218
[2025-06-05 19:05:58] 既存プロセスクリーンアップ完了
[2025-06-05 19:05:58] MySQL接続確認スキップ、pyspiderに委任
[2025-06-05 19:05:58] Redis管理開始
[2025-06-05 19:05:58] Redis既存プロセス発見: 380514
477537
477538
477837
817284
817285
[2025-06-05 19:05:58] Redis接続テスト成功
[2025-06-05 19:05:58] Redis管理完了
[2025-06-05 19:06:00] Puppeteer Fetcher起動開始
[2025-06-05 19:06:06] Puppeteer Fetcher起動成功 (ポート: 22223, PID: 1125235)
[2025-06-05 19:06:06] スケジューラ起動開始
[2025-06-05 19:06:09] フェッチャー起動開始
[2025-06-05 19:06:11] プロセッサ起動開始
[2025-06-05 19:06:13] リザルトワーカー起動開始
[2025-06-05 19:06:15] WebUI起動開始
[2025-06-05 19:06:19] PySpider MySQL + Redisモード起動完了
[2025-06-06 15:08:09] PySpider MySQL + Redisモード起動開始
[2025-06-06 15:08:09] MySQL + Redis設定ファイル作成開始
[2025-06-06 15:08:09] config.json MySQL設定更新成功
[2025-06-06 15:08:09] MySQL + Redis設定ファイル作成完了
[2025-06-06 15:08:09] 既存プロセスクリーンアップ開始
[2025-06-06 15:08:09] 既存プロセスクリーンアップ完了
[2025-06-06 15:08:09] MySQL接続確認スキップ、pyspiderに委任
[2025-06-06 15:08:09] Redis管理開始
[2025-06-06 15:08:09] Redis新規起動開始
[2025-06-06 15:08:12] Redis新規起動成功 (PID: )
[2025-06-06 15:08:12] Redis管理完了
[2025-06-06 15:08:14] Puppeteer Fetcher起動開始
[2025-06-06 15:08:21] Puppeteer Fetcher起動成功 (ポート: 22223, PID: 4777)
[2025-06-06 15:08:21] スケジューラ起動開始
[2025-06-06 15:08:23] フェッチャー起動開始
[2025-06-06 15:08:25] プロセッサ起動開始
[2025-06-06 15:08:27] リザルトワーカー起動開始
[2025-06-06 15:08:29] WebUI起動開始
[2025-06-06 15:08:31] PySpider MySQL + Redisモード起動完了
[2025-06-06 15:08:50] PySpider MySQL + Redisモード起動開始
[2025-06-06 15:08:50] MySQL + Redis設定ファイル作成開始
[2025-06-06 15:08:50] config.json MySQL設定更新成功
[2025-06-06 15:08:50] MySQL + Redis設定ファイル作成完了
[2025-06-06 15:08:50] 既存プロセスクリーンアップ開始
[2025-06-06 15:08:50] PySpiderプロセスを終了: 4948
4998
5015
5053
5072
[2025-06-06 15:08:52] Puppeteer Fetcherプロセスを終了: 4777
[2025-06-06 15:08:54] 既存プロセスクリーンアップ完了
[2025-06-06 15:08:54] MySQL接続確認スキップ、pyspiderに委任
[2025-06-06 15:08:54] Redis管理開始
[2025-06-06 15:08:54] Redis新規起動開始
[2025-06-06 15:08:57] Redis新規起動成功 (PID: )
[2025-06-06 15:08:57] Redis管理完了
[2025-06-06 15:08:59] Puppeteer Fetcher起動開始
[2025-06-06 15:09:05] Puppeteer Fetcher起動成功 (ポート: 22223, PID: 5473)
[2025-06-06 15:09:05] スケジューラ起動開始
[2025-06-06 15:09:07] フェッチャー起動開始
[2025-06-06 15:09:10] プロセッサ起動開始
[2025-06-06 15:09:12] リザルトワーカー起動開始
[2025-06-06 15:09:14] WebUI起動開始
[2025-06-06 15:09:16] PySpider MySQL + Redisモード起動完了
[2025-06-08 08:37:06] PySpider MySQL + Redisモード起動開始
[2025-06-08 08:37:06] MySQL + Redis設定ファイル作成開始
[2025-06-08 08:37:07] config.json MySQL設定更新成功
[2025-06-08 08:37:07] MySQL + Redis設定ファイル作成完了
[2025-06-08 08:37:07] 既存プロセスクリーンアップ開始
[2025-06-08 08:37:07] 既存プロセスクリーンアップ完了
[2025-06-08 08:37:07] MySQL接続確認スキップ、pyspiderに委任
[2025-06-08 08:37:07] Redis管理開始
[2025-06-08 08:37:07] Redis既存プロセス発見: 34722
34784
34845
36734
36782
36783
[2025-06-08 08:37:07] Redis接続テスト成功
[2025-06-08 08:37:07] Redis管理完了
[2025-06-08 08:37:09] Puppeteer Fetcher起動開始
[2025-06-08 08:37:15] Puppeteer Fetcher起動成功 (ポート: 22223, PID: 108334)
[2025-06-08 08:37:15] スケジューラ起動開始
[2025-06-08 08:37:17] フェッチャー起動開始
[2025-06-08 08:37:20] プロセッサ起動開始
[2025-06-08 08:37:22] リザルトワーカー起動開始
[2025-06-08 08:37:24] WebUI起動開始
[2025-06-08 08:37:26] PySpider MySQL + Redisモード起動完了
