#!/usr/bin/env python3
# -*- coding: utf-8 -*-

print('=== MySQL モード - プロジェクト取得テスト ===')
import sys
import os
# 相対パス対応
script_dir = os.path.dirname(os.path.abspath(__file__))
project_root = script_dir  # このファイルはプロジェクトルートにある
sys.path.insert(0, project_root)
os.chdir(project_root)

try:
    from pyspider.config.unified_config import get_config
    from pyspider.database import connect_database
    
    config = get_config()
    print(f'✅ 設定読み込み成功: usedatabase = {config.get("usedatabase")}')
    print(f'   projectdb URL: {config.get("projectdb")}')
    
    projectdb = connect_database(config.get('projectdb'))
    print(f'✅ データベース接続成功: {type(projectdb)}')
    
    projects = list(projectdb.get_all())
    print(f'✅ プロジェクト取得成功: {len(projects)}件')
    
    if projects:
        for project in projects:
            print(f'   - {project.get("name")} ({project.get("status")}) - Group: {project.get("group")}')
    else:
        print('   プロジェクトが存在しません')
        
    # テストプロジェクトを作成
    test_project = {
        'name': 'mysql_test_project_new',
        'group': 'mysql_test_group',
        'status': 'RUNNING',
        'script': 'print("MySQL test")',
        'comments': 'MySQL mode test project',
        'rate': 1,
        'burst': 10,
        'updatetime': 1234567890
    }
    
    projectdb.insert(test_project['name'], test_project)
    print(f'✅ 新しいテストプロジェクトを作成: {test_project["name"]}')
    
    # 再度プロジェクト一覧を取得
    projects = list(projectdb.get_all())
    print(f'✅ 更新後のプロジェクト数: {len(projects)}件')
        
except Exception as e:
    print(f'❌ エラー: {e}')
    import traceback
    traceback.print_exc()
