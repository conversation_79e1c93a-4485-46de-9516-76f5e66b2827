#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os

# products.pyファイルのパスを取得
products_file_path = os.path.join('pyspider', 'webui', 'products.py')

# ファイルを読み込む
with open(products_file_path, 'r', encoding='utf-8') as f:
    content = f.read()

# 構文エラーを修正（+++を削除または適切なコードに置き換え）
if '+++' in content:
    # +++を削除し、適切なtry-except構造に修正
    fixed_content = content.replace('+++', '')
    
    # ファイルに書き戻す
    with open(products_file_path, 'w', encoding='utf-8') as f:
        f.write(fixed_content)
    
    print('products.py の構文エラーを修正しました')
else:
    print('products.py に構文エラー（+++）が見つかりませんでした')
