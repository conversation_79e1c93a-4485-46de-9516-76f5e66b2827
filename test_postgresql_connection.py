#!/usr/bin/env python
# -*- encoding: utf-8 -*-

import sys
import logging
from pyspider.database import connect_database

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger('test_postgresql')

def test_connection(db_url):
    """PostgreSQL接続をテストする"""
    logger.info(f"接続テスト: {db_url}")
    try:
        db = connect_database(db_url)
        logger.info(f"接続成功: {db_url}")
        return True
    except Exception as e:
        logger.error(f"接続エラー: {e}")
        return False

def main():
    """メイン関数"""
    # PostgreSQL接続文字列
    taskdb_url = "sqlalchemy+postgresql+taskdb://postgres:postgres@localhost:5432/pyspider_taskdb"
    projectdb_url = "sqlalchemy+postgresql+projectdb://postgres:postgres@localhost:5432/pyspider_projectdb"
    resultdb_url = "sqlalchemy+postgresql+resultdb://postgres:postgres@localhost:5432/pyspider_resultdb"
    
    # 接続テスト
    success = True
    if not test_connection(taskdb_url):
        success = False
    if not test_connection(projectdb_url):
        success = False
    if not test_connection(resultdb_url):
        success = False
    
    if success:
        logger.info("すべての接続テストが成功しました")
        return 0
    else:
        logger.error("一部の接続テストが失敗しました")
        return 1

if __name__ == "__main__":
    sys.exit(main())
