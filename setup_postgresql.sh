#!/bin/bash

# pyspiderNX2 PostgreSQLセットアップスクリプト
# PostgreSQLデータベースとユーザーを作成

echo "=== pyspiderNX2 PostgreSQLセットアップ ==="
echo ""

# PostgreSQLサーバーの確認
echo "PostgreSQLサーバーの確認中..."
if ! pg_isready -h localhost -p 5432 > /dev/null 2>&1; then
    echo "エラー: PostgreSQLサーバーが起動していません"
    echo "PostgreSQLサーバーを起動してください:"
    echo "  sudo systemctl start postgresql"
    echo "  sudo systemctl enable postgresql"
    exit 1
fi
echo "PostgreSQL接続確認: OK"
echo ""

# PostgreSQLユーザーの作成
echo "=== PostgreSQLユーザーの作成 ==="
echo "pyspiderユーザーを作成中..."

# ユーザーが既に存在するかチェック
if sudo -u postgres psql -tAc "SELECT 1 FROM pg_roles WHERE rolname='pyspider'" | grep -q 1; then
    echo "pyspiderユーザーは既に存在します"
else
    # ユーザーを作成
    sudo -u postgres createuser pyspider
    if [ $? -eq 0 ]; then
        echo "pyspiderユーザーを作成しました"
    else
        echo "エラー: pyspiderユーザーの作成に失敗しました"
        exit 1
    fi
fi

# パスワードを設定
echo "pyspiderユーザーのパスワードを設定中..."
sudo -u postgres psql -c "ALTER USER pyspider PASSWORD 'PySpider2024!SecurePass#';"
if [ $? -eq 0 ]; then
    echo "パスワードを設定しました"
else
    echo "エラー: パスワードの設定に失敗しました"
    exit 1
fi
echo ""

# PostgreSQLデータベースの作成
echo "=== PostgreSQLデータベースの作成 ==="

# プロジェクトデータベース
echo "pyspider_projectdbデータベースを作成中..."
if sudo -u postgres psql -lqt | cut -d \| -f 1 | grep -qw pyspider_projectdb; then
    echo "pyspider_projectdbデータベースは既に存在します"
else
    sudo -u postgres createdb pyspider_projectdb -O pyspider
    if [ $? -eq 0 ]; then
        echo "pyspider_projectdbデータベースを作成しました"
    else
        echo "エラー: pyspider_projectdbデータベースの作成に失敗しました"
        exit 1
    fi
fi

# タスクデータベース
echo "pyspider_taskdbデータベースを作成中..."
if sudo -u postgres psql -lqt | cut -d \| -f 1 | grep -qw pyspider_taskdb; then
    echo "pyspider_taskdbデータベースは既に存在します"
else
    sudo -u postgres createdb pyspider_taskdb -O pyspider
    if [ $? -eq 0 ]; then
        echo "pyspider_taskdbデータベースを作成しました"
    else
        echo "エラー: pyspider_taskdbデータベースの作成に失敗しました"
        exit 1
    fi
fi

# 結果データベース
echo "pyspider_resultdbデータベースを作成中..."
if sudo -u postgres psql -lqt | cut -d \| -f 1 | grep -qw pyspider_resultdb; then
    echo "pyspider_resultdbデータベースは既に存在します"
else
    sudo -u postgres createdb pyspider_resultdb -O pyspider
    if [ $? -eq 0 ]; then
        echo "pyspider_resultdbデータベースを作成しました"
    else
        echo "エラー: pyspider_resultdbデータベースの作成に失敗しました"
        exit 1
    fi
fi

# スケジューラデータベース
echo "pyspider_schedulerデータベースを作成中..."
if sudo -u postgres psql -lqt | cut -d \| -f 1 | grep -qw pyspider_scheduler; then
    echo "pyspider_schedulerデータベースは既に存在します"
else
    sudo -u postgres createdb pyspider_scheduler -O pyspider
    if [ $? -eq 0 ]; then
        echo "pyspider_schedulerデータベースを作成しました"
    else
        echo "エラー: pyspider_schedulerデータベースの作成に失敗しました"
        exit 1
    fi
fi
echo ""

# 接続テスト
echo "=== 接続テスト ==="
echo "pyspiderユーザーでの接続をテスト中..."

# 環境変数を設定してパスワード入力を回避
export PGPASSWORD='PySpider2024!SecurePass#'

# 各データベースへの接続をテスト
databases=("pyspider_projectdb" "pyspider_taskdb" "pyspider_resultdb" "pyspider_scheduler")

for db in "${databases[@]}"; do
    echo "  $db への接続テスト..."
    if psql -h localhost -U pyspider -d $db -c "SELECT 1;" > /dev/null 2>&1; then
        echo "    ✅ $db 接続成功"
    else
        echo "    ❌ $db 接続失敗"
        exit 1
    fi
done

# 環境変数をクリア
unset PGPASSWORD
echo ""

# 権限の確認
echo "=== 権限の確認 ==="
echo "pyspiderユーザーの権限を確認中..."
sudo -u postgres psql -c "SELECT rolname, rolsuper, rolcreaterole, rolcreatedb FROM pg_roles WHERE rolname='pyspider';"
echo ""

echo "=== PostgreSQLセットアップ完了 ==="
echo ""
echo "作成されたデータベース:"
echo "  - pyspider_projectdb (プロジェクト情報)"
echo "  - pyspider_taskdb (タスク情報)"
echo "  - pyspider_resultdb (結果情報)"
echo "  - pyspider_scheduler (スケジューラ情報)"
echo ""
echo "ユーザー情報:"
echo "  - ユーザー名: pyspider"
echo "  - パスワード: PySpider2024!SecurePass#"
echo ""
echo "接続文字列例:"
echo "  postgresql+psycopg2://pyspider:PySpider2024!SecurePass#@localhost:5432/pyspider_projectdb"
echo ""
echo "次のステップ:"
echo "  ./start_unified_postgresql_mode.sh でpyspiderを起動してください"
echo ""
echo "=== セットアップ完了 ==="
