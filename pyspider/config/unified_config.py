#!/usr/bin/env python
# -*- encoding: utf-8 -*-
# vim: set et sw=4 ts=4 sts=4 ff=unix fenc=utf8:
# Author: PySpiderNX2 Team
# Created on 2025-01-XX

import os
import json
import logging
from typing import Dict, Any, Optional
from pathlib import Path

logger = logging.getLogger(__name__)

class UnifiedConfig:
    """統一設定管理クラス"""
    
    def __init__(self, config_file: Optional[str] = None):
        self.config_file = config_file or self._find_config_file()
        self.config = {}
        self.env_prefix = 'PYSPIDER_'
        self.load_config()
    
    def _find_config_file(self) -> str:
        """設定ファイルを検索"""
        possible_files = [
            'config.json',
            'pyspider_config.json',
            os.path.expanduser('~/.pyspider/config.json'),
            '/etc/pyspider/config.json'
        ]
        
        for file_path in possible_files:
            if os.path.exists(file_path):
                return file_path
        
        # デフォルト設定ファイルを作成
        return self._create_default_config()
    
    def _create_default_config(self) -> str:
        """デフォルト設定ファイルを作成"""
        default_config = {
            "taskdb": "sqlite+taskdb:///data/pyspider_taskdb.db",
            "projectdb": "sqlite+projectdb:///data/pyspider_projectdb.db",
            "resultdb": "sqlite+resultdb:///data/pyspider_resultdb.db",
            "message_queue": "redis://localhost:6379/0",
            "webui": {
                "port": 5000,
                "username": "admin",
                "password": "change_this_password_immediately",
                "need-auth": True,
                "scheduler-rpc": "http://localhost:23333/",
                "csrf_protection": True,
                "session_timeout": 3600
            },
            "fetcher": {
                "xmlrpc-port": 24444,
                "puppeteer-endpoint": "http://localhost:22223",
                "timeout": 30,
                "poolsize": 100,
                "optimize": True,
                "memory_check_interval": 30,
                "max_memory_percent": 80
            },
            "scheduler": {
                "xmlrpc-port": 23333,
                "inqueue_limit": 5000,
                "delete_time": 24 * 60 * 60,
                "active_tasks": 100,
                "loop_limit": 1000,
                "fail_pause_num": 5
            },
            "processor": {
                "process-time-limit": 30,
                "threads": 4
            },
            "result_worker": {
                "threads": 1
            },
            "rate_limit": {
                "global": {
                    "requests_per_minute": 60
                },
                "per_domain": {
                    "example.com": {
                        "requests_per_minute": 10
                    }
                }
            },
            "logging": {
                "level": "INFO",
                "dir": "logs",
                "max_size": "100MB",
                "backup_count": 5,
                "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
            },
            "security": {
                "allowed_ips": [
                    "127.0.0.1",
                    "::1",
                    "***********/16",
                    "10.0.0.0/8",
                    "**********/12"
                ],
                "csrf_secret_key": None,
                "session_secret_key": None
            },
            "performance": {
                "memory_optimization": True,
                "connection_pool_optimization": True,
                "database_optimization": True,
                "query_cache": True
            }
        }
        
        config_file = 'config.json'
        with open(config_file, 'w') as f:
            json.dump(default_config, f, indent=4)
        
        logger.info(f"Created default config file: {config_file}")
        return config_file
    
    def load_config(self):
        """設定を読み込み"""
        try:
            with open(self.config_file, 'r') as f:
                self.config = json.load(f)

            # 環境変数で設定を上書き
            self._load_env_overrides()

            # usedatabase設定に基づいてデータベースURLを動的に生成
            self._apply_usedatabase_config()

            # 設定を検証
            self._validate_config()

            logger.info(f"Config loaded from {self.config_file}")

        except FileNotFoundError:
            logger.error(f"Config file not found: {self.config_file}")
            self.config = {}
        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON in config file: {e}")
            self.config = {}
    
    def _load_env_overrides(self):
        """環境変数で設定を上書き"""
        env_mappings = {
            f'{self.env_prefix}TASKDB': ['taskdb'],
            f'{self.env_prefix}PROJECTDB': ['projectdb'],
            f'{self.env_prefix}RESULTDB': ['resultdb'],
            f'{self.env_prefix}MESSAGE_QUEUE': ['message_queue'],
            f'{self.env_prefix}WEBUI_PORT': ['webui', 'port'],
            f'{self.env_prefix}WEBUI_USERNAME': ['webui', 'username'],
            f'{self.env_prefix}WEBUI_PASSWORD': ['webui', 'password'],
            f'{self.env_prefix}WEBUI_AUTH': ['webui', 'need-auth'],
            f'{self.env_prefix}LOG_LEVEL': ['logging', 'level'],
            f'{self.env_prefix}LOG_DIR': ['logging', 'dir'],
        }
        
        for env_var, config_path in env_mappings.items():
            value = os.environ.get(env_var)
            if value is not None:
                self._set_nested_config(config_path, value)
    
    def _set_nested_config(self, path: list, value: str):
        """ネストした設定値を設定"""
        current = self.config
        for key in path[:-1]:
            if key not in current:
                current[key] = {}
            current = current[key]
        
        # 型変換
        if path[-1] in ['port', 'timeout', 'poolsize', 'threads']:
            value = int(value)
        elif path[-1] in ['need-auth', 'optimize', 'memory_optimization']:
            value = value.lower() in ['true', '1', 'yes', 'on']
        
        current[path[-1]] = value

    def _apply_usedatabase_config(self):
        """usedatabase設定に基づいてデータベースURLを動的に生成"""
        usedatabase = self.config.get('usedatabase')
        if not usedatabase:
            return

        logger.info(f"Applying usedatabase configuration: {usedatabase}")

        # データベース認証情報の設定
        db_credentials = {
            'mysql': {
                'username': 'pyspider',
                'password': 'PySpider2024!SecurePass#',
                'host': 'localhost',
                'port': 3306
            },
            'postgresql': {
                'username': 'pyspider',
                'password': 'PySpider2024!SecurePass#',
                'host': 'localhost',
                'port': 5432
            }
        }

        # データベースタイプごとのURL生成
        db_types = ['taskdb', 'projectdb', 'resultdb']

        for db_type in db_types:
            if usedatabase == 'sqlite':
                # SQLite設定
                self.config[db_type] = f"sqlite+{db_type}:///data/pyspider_{db_type}.db"
                logger.info(f"Set {db_type} to SQLite: {self.config[db_type]}")

            elif usedatabase == 'mysql':
                # MySQL設定（PyMySQLドライバーを使用）
                creds = db_credentials['mysql']
                self.config[db_type] = (
                    f"sqlalchemy+mysql+pymysql+{db_type}://{creds['username']}:{creds['password']}"
                    f"@{creds['host']}:{creds['port']}/pyspider_{db_type}"
                )
                logger.info(f"Set {db_type} to MySQL: {self.config[db_type]}")

            elif usedatabase == 'postgresql':
                # PostgreSQL設定
                creds = db_credentials['postgresql']
                self.config[db_type] = (
                    f"sqlalchemy+postgresql+{db_type}://{creds['username']}:{creds['password']}"
                    f"@{creds['host']}:{creds['port']}/pyspider_{db_type}"
                )
                logger.info(f"Set {db_type} to PostgreSQL: {self.config[db_type]}")

            else:
                logger.warning(f"Unknown usedatabase value: {usedatabase}. Supported values: sqlite, mysql, postgresql")

    def _validate_config(self):
        """設定を検証"""
        required_keys = ['taskdb', 'projectdb', 'resultdb', 'message_queue']
        
        for key in required_keys:
            if key not in self.config:
                logger.warning(f"Missing required config key: {key}")
        
        # セキュリティ設定の検証
        webui_config = self.config.get('webui', {})
        if webui_config.get('need-auth') and not webui_config.get('password'):
            logger.warning("Authentication enabled but no password set")
        
        if webui_config.get('password') == 'change_this_password_immediately':
            logger.warning("Default password detected. Please change it immediately!")
    
    def get(self, key: str, default: Any = None) -> Any:
        """設定値を取得"""
        keys = key.split('.')
        current = self.config
        
        for k in keys:
            if isinstance(current, dict) and k in current:
                current = current[k]
            else:
                return default
        
        return current
    
    def set(self, key: str, value: Any):
        """設定値を設定"""
        keys = key.split('.')
        current = self.config
        
        for k in keys[:-1]:
            if k not in current:
                current[k] = {}
            current = current[k]
        
        current[keys[-1]] = value
    
    def save(self):
        """設定をファイルに保存"""
        try:
            with open(self.config_file, 'w') as f:
                json.dump(self.config, f, indent=4)
            logger.info(f"Config saved to {self.config_file}")
        except Exception as e:
            logger.error(f"Failed to save config: {e}")
    
    def get_database_config(self, db_type: str) -> str:
        """データベース設定を取得"""
        return self.get(db_type, f"sqlite+{db_type}:///data/pyspider_{db_type}.db")
    
    def get_component_config(self, component: str) -> Dict[str, Any]:
        """コンポーネント設定を取得"""
        return self.get(component, {})

# グローバル設定インスタンス
config = UnifiedConfig()

def get_config() -> UnifiedConfig:
    """グローバル設定インスタンスを取得"""
    return config

def reload_config():
    """設定を再読み込み"""
    global config
    config.load_config()
