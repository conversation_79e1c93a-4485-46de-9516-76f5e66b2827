#!/usr/bin/env python
# -*- coding: utf-8 -*-

import time
import json
import logging
import queue
from typing import Dict, List, Any, Callable, Optional, Union, Iterable, TypeVar

from pyspider.libs.data_pipeline import DataPipeline
from pyspider.result.result_worker import ResultWorker

logger = logging.getLogger('enhanced_result_worker')

class EnhancedResultWorker(ResultWorker):
    """
    拡張されたResultWorker
    
    データ処理パイプラインを統合したResultWorkerを提供します。
    - ストリーム処理：大量のデータをストリームとして処理
    - データ変換：柔軟なデータ変換機能
    - データ品質チェック：取得したデータの品質を自動的にチェック
    """
    
    def __init__(self, resultdb, inqueue, pipeline_config=None, max_workers=4, queue_size=100):
        """
        初期化
        
        Args:
            resultdb: 結果データベース
            inqueue: 入力キュー
            pipeline_config: パイプライン設定
            max_workers: 最大ワーカー数
            queue_size: キューサイズ
        """
        super(EnhancedResultWorker, self).__init__(resultdb, inqueue)
        self.pipeline = DataPipeline(max_workers=max_workers, queue_size=queue_size)
        self.pipeline_config = pipeline_config or {}
        self.setup_pipeline()
    
    def setup_pipeline(self):
        """パイプラインを設定"""
        # 変換処理を設定
        transformers = self.pipeline_config.get('transformers', [])
        for transformer in transformers:
            self.pipeline.add_transformer(transformer)
        
        # 検証処理を設定
        validators = self.pipeline_config.get('validators', [])
        for validator in validators:
            self.pipeline.add_validator(validator)
        
        # フィルター処理を設定
        filters = self.pipeline_config.get('filters', [])
        for filter_func in filters:
            self.pipeline.add_filter(self._create_filter_func(filter_func))
        
        # 後処理を設定
        post_processors = self.pipeline_config.get('post_processors', [])
        for processor in post_processors:
            self.pipeline.add_post_processor(self._create_post_processor(processor))
    
    def _create_filter_func(self, filter_config):
        """フィルター関数を作成"""
        if callable(filter_config):
            return filter_config
        
        def filter_func(data):
            # フィルター条件を評価
            if isinstance(filter_config, dict):
                field = filter_config.get('field')
                operator = filter_config.get('operator', 'eq')
                value = filter_config.get('value')
                
                if field is None or value is None:
                    return True
                
                if isinstance(data, dict):
                    field_value = data.get(field)
                else:
                    try:
                        field_value = getattr(data, field)
                    except (AttributeError, TypeError):
                        return False
                
                if operator == 'eq':
                    return field_value == value
                elif operator == 'ne':
                    return field_value != value
                elif operator == 'gt':
                    return field_value > value
                elif operator == 'lt':
                    return field_value < value
                elif operator == 'ge':
                    return field_value >= value
                elif operator == 'le':
                    return field_value <= value
                elif operator == 'in':
                    return field_value in value
                elif operator == 'contains':
                    return value in field_value
                else:
                    return True
            
            return True
        
        return filter_func
    
    def _create_post_processor(self, processor_config):
        """後処理関数を作成"""
        if callable(processor_config):
            return processor_config
        
        def post_processor(data):
            # 後処理を実行
            if isinstance(processor_config, dict):
                processor_type = processor_config.get('type')
                
                if processor_type == 'add_field':
                    field = processor_config.get('field')
                    value = processor_config.get('value')
                    
                    if field and isinstance(data, dict):
                        data[field] = value
                
                elif processor_type == 'remove_field':
                    field = processor_config.get('field')
                    
                    if field and isinstance(data, dict) and field in data:
                        del data[field]
                
                elif processor_type == 'rename_field':
                    old_field = processor_config.get('old_field')
                    new_field = processor_config.get('new_field')
                    
                    if old_field and new_field and isinstance(data, dict) and old_field in data:
                        data[new_field] = data[old_field]
                        del data[old_field]
                
                elif processor_type == 'format':
                    template = processor_config.get('template')
                    
                    if template and isinstance(data, dict):
                        try:
                            data['formatted'] = template.format(**data)
                        except (KeyError, ValueError):
                            pass
            
            return data
        
        return post_processor
    
    def on_result(self, task, result):
        """
        結果を処理
        
        Args:
            task: タスク
            result: 結果
        """
        if not result:
            return
        
        # 処理対象のデータを作成
        data = {
            'taskid': task.get('taskid', ''),
            'project': task.get('project', ''),
            'url': task.get('url', ''),
            'result': result,
            'updatetime': time.time()
        }
        
        # パイプラインで処理
        processed_data = list(self.pipeline.process([data]))
        
        # 処理結果を保存
        for item in processed_data:
            if item and 'taskid' in item and 'project' in item and 'url' in item and 'result' in item:
                logger.info('result %s:%s %s -> %.30r' % (
                    item['project'], item['taskid'], item['url'], item['result']))
                
                self.resultdb.save(
                    project=item['project'],
                    taskid=item['taskid'],
                    url=item['url'],
                    result=item['result']
                )
    
    def quit(self):
        """終了処理"""
        self.pipeline.stop()
        super(EnhancedResultWorker, self).quit()
    
    def get_stats(self):
        """統計情報を取得"""
        return self.pipeline.get_stats()


class BatchEnhancedResultWorker(EnhancedResultWorker):
    """
    バッチ処理対応の拡張ResultWorker
    
    データをバッチで処理するResultWorkerを提供します。
    """
    
    def __init__(self, resultdb, inqueue, pipeline_config=None, max_workers=4, queue_size=100, batch_size=100, batch_timeout=60):
        """
        初期化
        
        Args:
            resultdb: 結果データベース
            inqueue: 入力キュー
            pipeline_config: パイプライン設定
            max_workers: 最大ワーカー数
            queue_size: キューサイズ
            batch_size: バッチサイズ
            batch_timeout: バッチタイムアウト（秒）
        """
        super(BatchEnhancedResultWorker, self).__init__(resultdb, inqueue, pipeline_config, max_workers, queue_size)
        self.batch_size = batch_size
        self.batch_timeout = batch_timeout
        self.batch = []
        self.last_batch_time = time.time()
    
    def run(self):
        """実行ループ"""
        logger.info("batch_enhanced_result_worker starting...")
        
        while not self._quit:
            try:
                # バッチがタイムアウトしたかチェック
                if self.batch and time.time() - self.last_batch_time > self.batch_timeout:
                    self._process_batch()
                
                # キューからデータを取得
                try:
                    task, result = self.inqueue.get(timeout=1)
                    
                    # バッチにデータを追加
                    if task and result:
                        data = {
                            'taskid': task.get('taskid', ''),
                            'project': task.get('project', ''),
                            'url': task.get('url', ''),
                            'result': result,
                            'updatetime': time.time()
                        }
                        self.batch.append(data)
                    
                    # バッチサイズに達したら処理
                    if len(self.batch) >= self.batch_size:
                        self._process_batch()
                    
                except queue.Empty:
                    continue
            except KeyboardInterrupt:
                break
            except Exception as e:
                logger.exception(e)
                continue
        
        # 残りのバッチを処理
        if self.batch:
            self._process_batch()
        
        logger.info("batch_enhanced_result_worker exiting...")
    
    def _process_batch(self):
        """バッチを処理"""
        if not self.batch:
            return
        
        try:
            # パイプラインで処理
            processed_data = list(self.pipeline.process(self.batch))
            
            # 処理結果を保存
            for item in processed_data:
                if item and 'taskid' in item and 'project' in item and 'url' in item and 'result' in item:
                    logger.info('result %s:%s %s -> %.30r' % (
                        item['project'], item['taskid'], item['url'], item['result']))
                    
                    self.resultdb.save(
                        project=item['project'],
                        taskid=item['taskid'],
                        url=item['url'],
                        result=item['result']
                    )
        except Exception as e:
            logger.exception(f"Error processing batch: {e}")
        
        # バッチをクリア
        self.batch = []
        self.last_batch_time = time.time()
