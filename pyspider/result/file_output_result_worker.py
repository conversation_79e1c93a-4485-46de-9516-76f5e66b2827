#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
FileOutputResultWorker - データベース保存とJSONLファイル出力を同時に行うResultWorker
"""

import os
import json
import time
import logging
from pathlib import Path
from datetime import datetime
from typing import Dict, Any, Optional

from pyspider.result.result_worker import ResultWorker

logger = logging.getLogger("file_output_result_worker")


class FileOutputResultWorker(ResultWorker):
    """
    データベース保存とJSONLファイル出力を同時に行うResultWorker
    
    機能:
    - 通常のResultWorkerと同様にデータベースに保存
    - 同時にJSONL形式でファイルに出力
    - プロジェクト別にファイルを分割
    - ファイルローテーション機能
    - 設定可能な出力ディレクトリ
    """
    
    def __init__(self, resultdb, inqueue, output_dir="results", 
                 enable_file_output=True, max_file_size=100*1024*1024,
                 enable_rotation=True, rotation_count=10):
        """
        初期化
        
        Args:
            resultdb: 結果データベース
            inqueue: 入力キュー
            output_dir (str): 出力ディレクトリ（デフォルト: "results"）
            enable_file_output (bool): ファイル出力を有効にするか（デフォルト: True）
            max_file_size (int): ファイルローテーションのサイズ閾値（デフォルト: 100MB）
            enable_rotation (bool): ファイルローテーションを有効にするか（デフォルト: True）
            rotation_count (int): 保持するローテーションファイル数（デフォルト: 10）
        """
        super().__init__(resultdb, inqueue)
        
        self.output_dir = Path(output_dir)
        self.enable_file_output = enable_file_output
        self.max_file_size = max_file_size
        self.enable_rotation = enable_rotation
        self.rotation_count = rotation_count
        
        # 出力ディレクトリを作成
        if self.enable_file_output:
            self.output_dir.mkdir(parents=True, exist_ok=True)
            logger.info(f"FileOutputResultWorker initialized with output_dir: {self.output_dir}")
    
    def _get_output_file_path(self, project: str) -> Path:
        """
        プロジェクト用の出力ファイルパスを取得
        
        Args:
            project (str): プロジェクト名
            
        Returns:
            Path: 出力ファイルパス
        """
        # プロジェクト名をファイル名に安全な形式に変換
        safe_project_name = "".join(c for c in project if c.isalnum() or c in ('-', '_')).rstrip()
        if not safe_project_name:
            safe_project_name = "unknown_project"
        
        return self.output_dir / f"{safe_project_name}_results.jsonl"
    
    def _rotate_file_if_needed(self, file_path: Path) -> None:
        """
        必要に応じてファイルをローテーション
        
        Args:
            file_path (Path): チェックするファイルパス
        """
        if not self.enable_rotation or not file_path.exists():
            return
        
        # ファイルサイズをチェック
        if file_path.stat().st_size >= self.max_file_size:
            logger.info(f"Rotating file {file_path} (size: {file_path.stat().st_size} bytes)")
            
            # 既存のローテーションファイルをシフト
            for i in range(self.rotation_count - 1, 0, -1):
                old_file = file_path.with_suffix(f".{i}.jsonl")
                new_file = file_path.with_suffix(f".{i + 1}.jsonl")
                
                if old_file.exists():
                    if new_file.exists():
                        new_file.unlink()  # 古いファイルを削除
                    old_file.rename(new_file)
            
            # 現在のファイルを .1 にリネーム
            rotated_file = file_path.with_suffix(".1.jsonl")
            if rotated_file.exists():
                rotated_file.unlink()
            file_path.rename(rotated_file)
            
            logger.info(f"File rotated: {file_path} -> {rotated_file}")
    
    def _write_to_file(self, project: str, data: Dict[str, Any]) -> bool:
        """
        データをJSONLファイルに書き込み
        
        Args:
            project (str): プロジェクト名
            data (Dict[str, Any]): 書き込むデータ
            
        Returns:
            bool: 書き込み成功フラグ
        """
        if not self.enable_file_output:
            return True
        
        try:
            file_path = self._get_output_file_path(project)
            
            # ファイルローテーションチェック
            self._rotate_file_if_needed(file_path)
            
            # JSONLファイルに追記
            with open(file_path, 'a', encoding='utf-8') as f:
                json_line = json.dumps(data, ensure_ascii=False, separators=(',', ':'))
                f.write(json_line + '\n')
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to write to file for project {project}: {e}")
            return False
    
    def on_result(self, task, result):
        """
        結果を処理（データベース保存 + ファイル出力）
        
        Args:
            task: タスク情報
            result: 結果データ
        """
        if not result:
            return
        
        if 'taskid' in task and 'project' in task and 'url' in task:
            project = task['project']
            taskid = task['taskid']
            url = task['url']
            
            logger.info('result %s:%s %s -> %.30r' % (project, taskid, url, result))
            
            # 1. データベースに保存（従来の処理）
            db_result = self.resultdb.save(
                project=project,
                taskid=taskid,
                url=url,
                result=result
            )
            
            # 2. ファイルに出力（新機能）
            if self.enable_file_output:
                file_data = {
                    'taskid': taskid,
                    'project': project,
                    'url': url,
                    'result': result,
                    'updatetime': time.time(),
                    'timestamp': datetime.now().isoformat()
                }
                
                file_success = self._write_to_file(project, file_data)
                
                if file_success:
                    logger.debug(f"Successfully wrote result to file for project {project}")
                else:
                    logger.warning(f"Failed to write result to file for project {project}")
            
            return db_result
            
        else:
            logger.warning('result UNKNOWN -> %.30r' % result)
            return
    
    def get_file_stats(self) -> Dict[str, Any]:
        """
        ファイル出力の統計情報を取得
        
        Returns:
            Dict[str, Any]: 統計情報
        """
        if not self.enable_file_output:
            return {"file_output_enabled": False}
        
        stats = {
            "file_output_enabled": True,
            "output_directory": str(self.output_dir),
            "max_file_size": self.max_file_size,
            "rotation_enabled": self.enable_rotation,
            "rotation_count": self.rotation_count,
            "files": []
        }
        
        # 出力ディレクトリ内のJSONLファイルを調査
        if self.output_dir.exists():
            for file_path in self.output_dir.glob("*.jsonl"):
                try:
                    file_stat = file_path.stat()
                    stats["files"].append({
                        "name": file_path.name,
                        "size": file_stat.st_size,
                        "modified": datetime.fromtimestamp(file_stat.st_mtime).isoformat(),
                        "lines": self._count_lines(file_path)
                    })
                except Exception as e:
                    logger.error(f"Error getting stats for {file_path}: {e}")
        
        return stats
    
    def _count_lines(self, file_path: Path) -> int:
        """
        ファイルの行数をカウント
        
        Args:
            file_path (Path): ファイルパス
            
        Returns:
            int: 行数
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return sum(1 for _ in f)
        except Exception:
            return 0


class ConfigurableFileOutputResultWorker(FileOutputResultWorker):
    """
    設定ファイルから設定を読み込むFileOutputResultWorker
    """
    
    def __init__(self, resultdb, inqueue, config=None):
        """
        設定から初期化
        
        Args:
            resultdb: 結果データベース
            inqueue: 入力キュー
            config (dict): 設定辞書
        """
        if config is None:
            config = {}
        
        # デフォルト設定
        default_config = {
            "output_dir": "results",
            "enable_file_output": True,
            "max_file_size": 100 * 1024 * 1024,  # 100MB
            "enable_rotation": True,
            "rotation_count": 10
        }
        
        # 設定をマージ
        merged_config = {**default_config, **config}
        
        super().__init__(
            resultdb=resultdb,
            inqueue=inqueue,
            output_dir=merged_config["output_dir"],
            enable_file_output=merged_config["enable_file_output"],
            max_file_size=merged_config["max_file_size"],
            enable_rotation=merged_config["enable_rotation"],
            rotation_count=merged_config["rotation_count"]
        )
        
        logger.info(f"ConfigurableFileOutputResultWorker initialized with config: {merged_config}")
