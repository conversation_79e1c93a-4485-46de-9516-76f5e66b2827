#!/usr/bin/env python
# -*- encoding: utf-8 -*-
# vim: set et sw=4 ts=4 sts=4 ff=unix fenc=utf8:
# Author: Binux<<EMAIL>>
#         http://binux.me
# Created on 2014-10-19 15:37:46

import time
import json
import logging
import queue as Queue
from pyspider.libs.memory_optimizer import memory_optimizer
logger = logging.getLogger("result")


class ResultWorker(object):

    """
    do with result
    override this if needed.
    """

    def __init__(self, resultdb, inqueue):
        self.resultdb = resultdb
        self.inqueue = inqueue
        self._quit = False

        # Initialize memory optimizer
        self.memory_optimizer = memory_optimizer
        self.memory_optimizer.start()

    def on_result(self, task, result):
        '''Called every result'''
        if not result:
            return
        if 'taskid' in task and 'project' in task and 'url' in task:
            logger.info('result %s:%s %s -> %.30r' % (
                task['project'], task['taskid'], task['url'], result))
            return self.resultdb.save(
                project=task['project'],
                taskid=task['taskid'],
                url=task['url'],
                result=result
            )
        else:
            logger.warning('result UNKNOW -> %.30r' % result)
            return

    def quit(self):
        self._quit = True

    def run(self):
        '''Run loop'''
        logger.info("result_worker starting...")

        # Batch processing variables
        batch_size = 10  # Process up to 10 results at once if available
        batch_timeout = 1  # Wait up to 1 second for results
        result_buffer_size = 1000  # Maximum size of result buffer before forced processing
        result_buffer = []  # Buffer to store results for batch processing
        last_process_time = time.time()  # Time of last batch processing
        max_buffer_time = 5  # Maximum time to hold results in buffer (seconds)

        while not self._quit:
            try:
                # Try to get a batch of results
                try:
                    # Always get at least one result (blocking)
                    task, result = self.inqueue.get(timeout=batch_timeout)
                    result_buffer.append((task, result))

                    # Try to get more results (non-blocking)
                    for _ in range(batch_size - 1):
                        try:
                            task, result = self.inqueue.get_nowait()
                            result_buffer.append((task, result))
                        except Queue.Empty:
                            break
                except Queue.Empty:
                    # No results available, process buffer if it's been too long
                    if result_buffer and time.time() - last_process_time > max_buffer_time:
                        self._process_result_buffer(result_buffer)
                        result_buffer = []
                        last_process_time = time.time()
                    continue

                # Process buffer if it's large enough or it's been too long
                if len(result_buffer) >= result_buffer_size or time.time() - last_process_time > max_buffer_time:
                    self._process_result_buffer(result_buffer)
                    result_buffer = []
                    last_process_time = time.time()

                # Check memory usage and optimize if needed
                if hasattr(self, 'memory_optimizer'):
                    self.memory_optimizer.check_memory()

            except KeyboardInterrupt:
                break
            except AssertionError as e:
                logger.error(e)
                continue
            except Exception as e:
                logger.exception(e)
                continue

        # Process any remaining results in buffer
        if result_buffer:
            try:
                self._process_result_buffer(result_buffer)
            except Exception as e:
                logger.exception(f"Error processing final result buffer: {e}")

        logger.info("result_worker exiting...")

        # Stop memory optimizer
        if hasattr(self, 'memory_optimizer'):
            self.memory_optimizer.stop()

    def _process_result_buffer(self, result_buffer):
        """Process a batch of results efficiently"""
        if not result_buffer:
            return

        # Group results by project for more efficient database operations
        projects = {}
        for task, result in result_buffer:
            if not result:
                continue

            if 'taskid' in task and 'project' in task and 'url' in task:
                project = task['project']
                if project not in projects:
                    projects[project] = []
                projects[project].append((task, result))
            else:
                logger.warning('result UNKNOWN -> %.30r', result)

        # Process results by project
        for project, items in projects.items():
            try:
                # Log batch processing
                logger.info(f'Processing batch of {len(items)} results for project {project}')

                # Process each result
                for task, result in items:
                    try:
                        self.resultdb.save(
                            project=task['project'],
                            taskid=task['taskid'],
                            url=task['url'],
                            result=result
                        )
                    except Exception as e:
                        logger.exception(f"Error saving result for task {task['taskid']}: {e}")
            except Exception as e:
                logger.exception(f"Error processing batch for project {project}: {e}")


class OneResultWorker(ResultWorker):
    '''Result Worker for one mode, write results to stdout'''
    def on_result(self, task, result):
        '''Called every result'''
        if not result:
            return
        if 'taskid' in task and 'project' in task and 'url' in task:
            logger.info('result %s:%s %s -> %.30r' % (
                task['project'], task['taskid'], task['url'], result))
            print(json.dumps({
                'taskid': task['taskid'],
                'project': task['project'],
                'url': task['url'],
                'result': result,
                'updatetime': time.time()
            }))
        else:
            logger.warning('result UNKNOW -> %.30r' % result)
            return
