#!/usr/bin/env python
# -*- coding: utf-8 -*-
import re
import json
import time
import sys
import os
import logging
from pyspider.libs.base_handler import *

class Handler(BaseHandler):
    """
    Playwrightを使用したスクレイピングの例
    動的に生成されるコンテンツを含むWebサイトからデータを抽出
    """
    
    crawl_config = {
        'headers': {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        },
        'timeout': 120,  # Playwrightの処理に時間がかかる場合があるため、タイムアウトを長めに設定
    }
    
    def __init__(self):
        super(Handler, self).__init__()
        self.playwright = None
        self.browser = None
    
    def _init_playwright(self):
        """Playwrightを初期化"""
        try:
            from playwright.sync_api import sync_playwright
            self.playwright = sync_playwright().start()
            self.browser = self.playwright.chromium.launch(headless=True)
            self.logger.info("Playwrightを初期化しました")
            return True
        except ImportError:
            self.logger.error("Playwrightがインストールされていません。pip install playwright && python -m playwright install を実行してください")
            return False
        except Exception as e:
            self.logger.error(f"Playwrightの初期化中にエラーが発生しました: {e}")
            return False
    
    def _close_playwright(self):
        """Playwrightを終了"""
        try:
            if self.browser:
                self.browser.close()
            if self.playwright:
                self.playwright.stop()
            self.browser = None
            self.playwright = None
            self.logger.info("Playwrightを終了しました")
        except Exception as e:
            self.logger.error(f"Playwrightの終了中にエラーが発生しました: {e}")
    
    @every(minutes=24 * 60)
    def on_start(self):
        # 動的コンテンツを含むWebサイトをクロール
        # 例として、GitHubのトレンドリポジトリページを使用
        self.crawl('https://github.com/trending', callback=self.index_page)
    
    def index_page(self, response):
        # Playwrightを初期化
        if not self._init_playwright():
            return {"error": "Playwrightの初期化に失敗しました"}
        
        try:
            # 新しいページを開く
            page = self.browser.new_page()
            
            # ユーザーエージェントを設定
            page.set_extra_http_headers({
                'User-Agent': self.crawl_config['headers']['User-Agent']
            })
            
            # URLに移動
            self.logger.info(f"Navigating to {response.url} with Playwright")
            page.goto(response.url, wait_until="networkidle", timeout=self.crawl_config['timeout'] * 1000)
            
            # ページのHTMLを取得
            html = page.content()
            
            # スクリーンショットを取得（デバッグ用）
            screenshot_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'github_trending.png')
            page.screenshot(path=screenshot_path)
            self.logger.info(f"スクリーンショットを保存しました: {screenshot_path}")
            
            # トレンドリポジトリのリストを抽出
            repo_pattern = r'<article class="Box-row">(.*?)</article>'
            repo_blocks = re.findall(repo_pattern, html, re.DOTALL)
            
            results = []
            
            for repo_block in repo_blocks:
                # リポジトリ名を抽出
                name_pattern = r'<h2 class="h3 lh-condensed">\s*<a href="([^"]+)"[^>]*>(.*?)</a>'
                name_match = re.search(name_pattern, repo_block, re.DOTALL)
                
                if name_match:
                    repo_url = f"https://github.com{name_match.group(1)}"
                    repo_name = re.sub(r'<[^>]+>', '', name_match.group(2)).strip()
                    
                    # 説明を抽出
                    description_pattern = r'<p class="col-9 color-fg-muted my-1 pr-4">(.*?)</p>'
                    description_match = re.search(description_pattern, repo_block, re.DOTALL)
                    description = ''
                    if description_match:
                        description = re.sub(r'<[^>]+>', '', description_match.group(1)).strip()
                    
                    # 言語を抽出
                    language_pattern = r'<span class="d-inline-block ml-0 mr-3">\s*<span[^>]*>(.*?)</span>\s*<span[^>]*>(.*?)</span>'
                    language_match = re.search(language_pattern, repo_block, re.DOTALL)
                    language = ''
                    if language_match:
                        language = re.sub(r'<[^>]+>', '', language_match.group(2)).strip()
                    
                    # スター数を抽出
                    stars_pattern = r'<a[^>]*href="[^"]+/stargazers"[^>]*>\s*<svg[^>]*>.*?</svg>\s*(.*?)\s*</a>'
                    stars_match = re.search(stars_pattern, repo_block, re.DOTALL)
                    stars = ''
                    if stars_match:
                        stars = re.sub(r'<[^>]+>', '', stars_match.group(1)).strip()
                    
                    # リポジトリの詳細ページをクロール
                    self.crawl(repo_url, callback=self.detail_page, save={
                        'name': repo_name,
                        'description': description,
                        'language': language,
                        'stars': stars
                    })
                    
                    results.append({
                        'name': repo_name,
                        'url': repo_url,
                        'description': description,
                        'language': language,
                        'stars': stars
                    })
            
            # ページを閉じる
            page.close()
            
            # 結果をJSONとして保存（デバッグ用）
            json_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'github_trending.json')
            with open(json_path, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2)
            self.logger.info(f"結果をJSONとして保存しました: {json_path}")
            
            return results
        
        except Exception as e:
            self.logger.error(f"Error crawling {response.url} with Playwright: {e}")
            return {"error": str(e)}
        
        finally:
            # Playwrightを終了
            self._close_playwright()
    
    def detail_page(self, response):
        # Playwrightを初期化
        if not self._init_playwright():
            return {"error": "Playwrightの初期化に失敗しました"}
        
        try:
            # 新しいページを開く
            page = self.browser.new_page()
            
            # ユーザーエージェントを設定
            page.set_extra_http_headers({
                'User-Agent': self.crawl_config['headers']['User-Agent']
            })
            
            # URLに移動
            self.logger.info(f"Navigating to {response.url} with Playwright")
            page.goto(response.url, wait_until="networkidle", timeout=self.crawl_config['timeout'] * 1000)
            
            # ページのHTMLを取得
            html = page.content()
            
            # READMEの内容を抽出
            readme_pattern = r'<article class="markdown-body entry-content container-lg"[^>]*>(.*?)</article>'
            readme_match = re.search(readme_pattern, html, re.DOTALL)
            readme = ''
            if readme_match:
                readme = re.sub(r'<[^>]+>', ' ', readme_match.group(1))
                readme = re.sub(r'\s+', ' ', readme).strip()
                # 長すぎる場合は切り詰める
                if len(readme) > 1000:
                    readme = readme[:1000] + '...'
            
            # ページを閉じる
            page.close()
            
            # 結果を返す
            return {
                'url': response.url,
                'name': response.save['name'],
                'description': response.save['description'],
                'language': response.save['language'],
                'stars': response.save['stars'],
                'readme_excerpt': readme
            }
        
        except Exception as e:
            self.logger.error(f"Error crawling {response.url} with Playwright: {e}")
            return {"error": str(e)}
        
        finally:
            # Playwrightを終了
            self._close_playwright()
