#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
webui-nextセットアップツール
pip install後にwebui-nextの依存関係をインストールし、ビルドを実行
"""

import os
import sys
import subprocess
import shutil
import json
from pathlib import Path


def run_command(cmd, cwd=None, check=True):
    """コマンドを実行"""
    print(f"実行中: {' '.join(cmd) if isinstance(cmd, list) else cmd}")
    try:
        result = subprocess.run(
            cmd, 
            cwd=cwd, 
            check=check, 
            capture_output=True, 
            text=True,
            shell=isinstance(cmd, str)
        )
        if result.stdout:
            print(result.stdout)
        return result
    except subprocess.CalledProcessError as e:
        print(f"エラー: {e}")
        if e.stderr:
            print(f"stderr: {e.stderr}")
        if check:
            raise
        return e


def check_node_npm():
    """Node.jsとnpmの存在確認"""
    try:
        node_result = run_command(['node', '--version'], check=False)
        npm_result = run_command(['npm', '--version'], check=False)
        
        if node_result.returncode == 0 and npm_result.returncode == 0:
            print(f"✅ Node.js: {node_result.stdout.strip()}")
            print(f"✅ npm: {npm_result.stdout.strip()}")
            return True
        else:
            print("❌ Node.jsまたはnpmが見つかりません")
            return False
    except Exception as e:
        print(f"❌ Node.js/npm確認エラー: {e}")
        return False


def find_pyspider_installation():
    """pyspiderのインストール場所を特定"""
    try:
        import pyspider
        pyspider_path = Path(pyspider.__file__).parent
        webui_next_path = pyspider_path / "webui-next"

        if webui_next_path.exists():
            print(f"✅ pyspider webui-next found: {webui_next_path}")
            return pyspider_path, webui_next_path
        else:
            print(f"❌ webui-nextディレクトリが見つかりません: {webui_next_path}")
            return pyspider_path, None
    except ImportError:
        print("❌ pyspiderがインストールされていません")
        return None, None


def setup_root_npm(pyspider_path):
    """ルートディレクトリのnpmセットアップ"""
    print("=== ルートディレクトリnpmセットアップ開始 ===")

    # ルートディレクトリのpackage.jsonを確認
    root_package_json = pyspider_path.parent / "package.json"
    if root_package_json.exists():
        print(f"✅ ルートpackage.json found: {root_package_json}")

        # ルートディレクトリでnpm install
        print("📦 ルートディレクトリでnpm install実行中...")
        result = run_command(['npm', 'install'], cwd=root_package_json.parent, check=False)
        if result.returncode != 0:
            print("⚠️  ルートディレクトリのnpm installに失敗しました")
            return False

        print("✅ ルートディレクトリnpmセットアップ完了")
        return True
    else:
        print("ℹ️  ルートディレクトリのpackage.jsonが見つかりません（スキップ）")
        return True


def setup_webui_next(webui_next_path):
    """webui-nextのセットアップ"""
    print("=== webui-nextセットアップ開始 ===")

    # package.jsonの存在確認
    package_json = webui_next_path / "package.json"
    if not package_json.exists():
        print(f"❌ package.jsonが見つかりません: {package_json}")
        return False

    # npm install
    print("📦 webui-next npm install実行中...")
    result = run_command(['npm', 'install'], cwd=webui_next_path, check=False)
    if result.returncode != 0:
        print("❌ webui-next npm installに失敗しました")
        return False

    # Next.js build
    print("🔨 Next.js build実行中...")
    result = run_command(['npm', 'run', 'build'], cwd=webui_next_path, check=False)
    if result.returncode != 0:
        print("⚠️  Next.js buildに失敗しました（開発時は無視可能）")
        # buildの失敗は致命的ではない（開発時）

    print("✅ webui-nextセットアップ完了")
    return True


def main():
    """メイン処理"""
    print("🚀 pyspiderNX2 包括的npmセットアップ")
    print("=" * 50)

    # Node.js/npm確認
    if not check_node_npm():
        print("\n❌ Node.js/npmをインストールしてください")
        print("参考: https://nodejs.org/")
        return False

    # pyspiderインストール場所確認
    pyspider_path, webui_next_path = find_pyspider_installation()
    if not pyspider_path:
        return False

    # ルートディレクトリのnpmセットアップ
    if not setup_root_npm(pyspider_path):
        print("⚠️  ルートディレクトリのnpmセットアップに失敗しましたが、続行します")

    # webui-nextセットアップ
    if webui_next_path and not setup_webui_next(webui_next_path):
        return False
    elif not webui_next_path:
        print("⚠️  webui-nextディレクトリが見つかりませんでした")
        return False

    print("\n🎉 包括的セットアップ完了！")
    print("\nwebui-nextの起動方法:")
    print(f"  cd {webui_next_path} && npm run dev")
    print("  または")
    print("  python -m pyspider.tools.webui_next_launcher")
    print("\nルートディレクトリのnpmスクリプト:")
    print(f"  cd {pyspider_path.parent} && npm run dev:webui-next")

    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
