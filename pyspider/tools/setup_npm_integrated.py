#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
pyspiderNX2 統合npmセットアップツール
ルートディレクトリとwebui-nextの両方のnpmセットアップを統合管理
"""

import sys
import subprocess
from pathlib import Path


def run_command(cmd, cwd=None, check=True):
    """コマンドを実行"""
    print(f"実行中: {' '.join(cmd) if isinstance(cmd, list) else cmd}")
    try:
        result = subprocess.run(
            cmd, 
            cwd=cwd, 
            check=check, 
            capture_output=True, 
            text=True,
            shell=isinstance(cmd, str)
        )
        if result.stdout:
            print(result.stdout)
        return result
    except subprocess.CalledProcessError as e:
        print(f"エラー: {e}")
        if e.stderr:
            print(f"stderr: {e.stderr}")
        if check:
            raise
        return e


def check_node_npm():
    """Node.jsとnpmの存在確認"""
    try:
        node_result = run_command(['node', '--version'], check=False)
        npm_result = run_command(['npm', '--version'], check=False)
        
        if node_result.returncode == 0 and npm_result.returncode == 0:
            print(f"✅ Node.js: {node_result.stdout.strip()}")
            print(f"✅ npm: {npm_result.stdout.strip()}")
            return True
        else:
            print("❌ Node.jsまたはnpmが見つかりません")
            return False
    except Exception as e:
        print(f"❌ Node.js/npm確認エラー: {e}")
        return False


def find_pyspider_installation():
    """pyspiderのインストール場所を特定"""
    try:
        import pyspider
        pyspider_path = Path(pyspider.__file__).parent
        webui_next_path = pyspider_path / "webui-next"
        root_npm_path = pyspider_path / "root_npm"

        print(f"✅ pyspider path: {pyspider_path}")
        if webui_next_path.exists():
            print(f"✅ webui-next path: {webui_next_path}")
        else:
            print(f"⚠️  webui-next path not found: {webui_next_path}")

        if root_npm_path.exists():
            print(f"✅ root_npm path: {root_npm_path}")
        else:
            print(f"⚠️  root_npm path not found: {root_npm_path}")

        return pyspider_path, webui_next_path if webui_next_path.exists() else None, root_npm_path if root_npm_path.exists() else None
    except ImportError:
        print("❌ pyspiderがインストールされていません")
        return None, None, None


def setup_integrated_npm(pyspider_path, webui_next_path, root_npm_path):
    """統合npmセットアップ"""
    print("=== 統合npmセットアップ開始 ===")

    success = True

    # 1. root_npmディレクトリのpackage.jsonを確認（パッケージ内）
    if root_npm_path:
        root_package_json = root_npm_path / "package.json"
        if root_package_json.exists():
            print(f"📦 パッケージ内ルートpackage.json found: {root_package_json}")

            # root_npmディレクトリでnpm install実行
            print("🔧 パッケージ内ルートディレクトリでnpm install実行中...")
            result = run_command(['npm', 'install'], cwd=root_npm_path, check=False)
            if result.returncode == 0:
                print("✅ パッケージ内ルートディレクトリnpm install完了")
            else:
                print("⚠️  パッケージ内ルートディレクトリnpm installに失敗")
                success = False
        else:
            print("ℹ️  パッケージ内ルートディレクトリのpackage.jsonが見つかりません")
            success = False

    # 2. 開発環境のルートディレクトリのpackage.jsonを確認
    dev_root_package_json = pyspider_path.parent / "package.json"
    if dev_root_package_json.exists():
        print(f"📦 開発環境ルートpackage.json found: {dev_root_package_json}")

        # 開発環境ルートディレクトリでnpm install:all実行
        print("🔧 開発環境ルートディレクトリでnpm run install:all実行中...")
        result = run_command(['npm', 'run', 'install:all'], cwd=dev_root_package_json.parent, check=False)
        if result.returncode == 0:
            print("✅ 開発環境ルートディレクトリnpm install:all完了")
            success = True  # 開発環境が成功すれば全体成功
        else:
            print("⚠️  開発環境ルートディレクトリnpm install:allに失敗、個別セットアップを実行")
    else:
        print("ℹ️  開発環境ルートディレクトリのpackage.jsonが見つかりません")
    
    # 2. webui-nextの個別セットアップ（ルートセットアップが失敗した場合）
    if not success and webui_next_path:
        print("📦 webui-next個別セットアップ実行中...")
        package_json = webui_next_path / "package.json"
        if package_json.exists():
            result = run_command(['npm', 'install'], cwd=webui_next_path, check=False)
            if result.returncode == 0:
                print("✅ webui-next npm install完了")
                
                # Next.js build
                print("🔨 Next.js build実行中...")
                result = run_command(['npm', 'run', 'build'], cwd=webui_next_path, check=False)
                if result.returncode == 0:
                    print("✅ Next.js build完了")
                else:
                    print("⚠️  Next.js buildに失敗（開発時は無視可能）")
            else:
                print("❌ webui-next npm installに失敗")
                return False
        else:
            print("❌ webui-next/package.jsonが見つかりません")
            return False
    
    print("✅ 統合npmセットアップ完了")
    return True


def main():
    """メイン処理"""
    print("🚀 pyspiderNX2 統合npmセットアップ")
    print("=" * 50)
    
    # Node.js/npm確認
    if not check_node_npm():
        print("\n❌ Node.js/npmをインストールしてください")
        print("参考: https://nodejs.org/")
        return False
    
    # pyspiderインストール場所確認
    pyspider_path, webui_next_path, root_npm_path = find_pyspider_installation()
    if not pyspider_path:
        return False

    # 統合npmセットアップ
    if not setup_integrated_npm(pyspider_path, webui_next_path, root_npm_path):
        return False
    
    print("\n🎉 統合セットアップ完了！")
    print("\n起動方法:")
    if webui_next_path:
        print(f"  webui-next: cd {webui_next_path} && npm run dev")
        print("  または: python -m pyspider.tools.webui_next_launcher")
    
    dev_root_package_json = pyspider_path.parent / "package.json"
    if dev_root_package_json.exists():
        print(f"  統合起動: cd {dev_root_package_json.parent} && npm run dev:webui-next")

    if root_npm_path and (root_npm_path / "package.json").exists():
        print(f"  パッケージ内npm: cd {root_npm_path} && npm run dev:webui-next")
    
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
