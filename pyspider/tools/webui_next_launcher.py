#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
pyspiderNX2 webui-next launcher
"""

import os
import sys
import subprocess
from pathlib import Path


def main():
    """webui-next開発サーバーを起動"""
    try:
        import pyspider
        pyspider_path = Path(pyspider.__file__).parent
        webui_next_path = pyspider_path / "webui-next"
        root_path = pyspider_path.parent

        # webui-nextディレクトリの確認
        if not webui_next_path.exists():
            print("❌ webui-nextディレクトリが見つかりません")
            print("セットアップを実行してください: python -m pyspider.tools.setup_webui_next")
            sys.exit(1)

        # package.jsonの存在確認
        package_json = webui_next_path / "package.json"
        if not package_json.exists():
            print("❌ webui-next/package.jsonが見つかりません")
            print("セットアップを実行してください: python -m pyspider.tools.setup_webui_next")
            sys.exit(1)

        # node_modulesの存在確認（webui-nextまたはルート）
        webui_node_modules = webui_next_path / "node_modules"
        root_node_modules = root_path / "node_modules"

        if not webui_node_modules.exists() and not root_node_modules.exists():
            print("❌ node_modulesが見つかりません")
            print("セットアップを実行してください: python -m pyspider.tools.setup_webui_next")
            sys.exit(1)

        print("🚀 webui-next開発サーバーを起動中...")
        print(f"📁 webui-nextディレクトリ: {webui_next_path}")
        print(f"📁 ルートディレクトリ: {root_path}")
        print("🌐 ブラウザで http://localhost:3000 にアクセスしてください")
        print("⏹️  停止するには Ctrl+C を押してください")
        print("-" * 50)

        # 開発サーバー起動（webui-nextディレクトリで実行）
        subprocess.run(['npm', 'run', 'dev'], cwd=webui_next_path)
        
    except ImportError:
        print("❌ pyspiderがインストールされていません")
        print("インストールしてください: pip install pyspiderNX2")
        sys.exit(1)
    except KeyboardInterrupt:
        print("\n✅ webui-nextサーバーを停止しました")
    except FileNotFoundError:
        print("❌ npmが見つかりません")
        print("Node.jsをインストールしてください: https://nodejs.org/")
        sys.exit(1)
    except Exception as e:
        print(f"❌ エラー: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
