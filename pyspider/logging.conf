[loggers]
keys=root,scheduler,fetcher,processor,webui,bench,werkzeug,result_worker,database

[logger_root]
level=INFO
handlers=screen,file
qualname=root
propagate=0

[logger_scheduler]
level=INFO
handlers=screen,scheduler_file
qualname=scheduler
propagate=0

[logger_fetcher]
level=INFO
handlers=screen,fetcher_file
qualname=fetcher
propagate=0

[logger_processor]
level=INFO
handlers=screen,processor_file
qualname=processor
propagate=0

[logger_webui]
level=INFO
handlers=screen,webui_file
qualname=webui
propagate=0

[logger_bench]
level=INFO
handlers=screen
qualname=bench
propagate=0

[logger_werkzeug]
level=INFO
handlers=screen,webui_file
qualname=werkzeug
propagate=0

[logger_result_worker]
level=INFO
handlers=screen,result_worker_file
qualname=result_worker
propagate=0

[logger_database]
level=INFO
handlers=screen,database_file
qualname=database
propagate=0

[handlers]
keys=screen,file,scheduler_file,fetcher_file,processor_file,webui_file,result_worker_file,database_file,error_file

[handler_screen]
class=logging.StreamHandler
formatter=pretty
level=INFO
args=(sys.stdout, )

[handler_file]
class=logging.handlers.RotatingFileHandler
formatter=standard
level=INFO
args=('logs/pyspider.log', 'a', 10485760, 5, 'utf8')

[handler_scheduler_file]
class=logging.handlers.RotatingFileHandler
formatter=standard
level=INFO
args=('logs/scheduler.log', 'a', 10485760, 5, 'utf8')

[handler_fetcher_file]
class=logging.handlers.RotatingFileHandler
formatter=standard
level=INFO
args=('logs/fetcher.log', 'a', 10485760, 5, 'utf8')

[handler_processor_file]
class=logging.handlers.RotatingFileHandler
formatter=standard
level=INFO
args=('logs/processor.log', 'a', 10485760, 5, 'utf8')

[handler_webui_file]
class=logging.handlers.RotatingFileHandler
formatter=standard
level=INFO
args=('logs/webui.log', 'a', 10485760, 5, 'utf8')

[handler_result_worker_file]
class=logging.handlers.RotatingFileHandler
formatter=standard
level=INFO
args=('logs/result_worker.log', 'a', 10485760, 5, 'utf8')

[handler_database_file]
class=logging.handlers.RotatingFileHandler
formatter=standard
level=INFO
args=('logs/database.log', 'a', 10485760, 5, 'utf8')

[handler_error_file]
class=logging.handlers.RotatingFileHandler
formatter=standard
level=ERROR
args=('logs/error.log', 'a', 10485760, 5, 'utf8')

[formatters]
keys=pretty,standard

[formatter_pretty]
class=pyspider.libs.log.LogFormatter

[formatter_standard]
format=%(asctime)s [%(levelname)s] %(name)s: %(message)s
datefmt=%Y-%m-%d %H:%M:%S
