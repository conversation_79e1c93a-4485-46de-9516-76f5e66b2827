import asyncio
import json
import time
import os
from typing import Dict, Any, Optional, List, Union
from urllib.parse import urlparse
import logging

try:
    from playwright.async_api import async_playwright, <PERSON><PERSON><PERSON>, <PERSON>, Response
    has_playwright = True
except ImportError:
    has_playwright = False

from pyspider.fetcher.playwright_actions import PlaywrightActions
from pyspider.libs.spa_handler import <PERSON><PERSON>Handler
from pyspider.libs.enhanced_playwright import EnhancedPlaywright

logger = logging.getLogger('fetcher')

class PyPlaywrightFetcher:
    def __init__(self):
        self.browser: Optional[Browser] = None
        self.playwright = None
        self.actions = None
        self.spa_handler = SPAHandler()
        self.enhanced_playwright = None

    async def init(self):
        """Initialize playwright and browser"""
        if not self.playwright:
            self.playwright = await async_playwright().start()
            browser_options = {
                'args': [
                    '--no-sandbox',
                    '--disable-setuid-sandbox',
                    '--ignore-certificate-errors',
                    '--ignore-ssl-errors'
                ]
            }
            self.browser = await self.playwright.chromium.launch(**browser_options)
            logger.info("Browser initialized successfully")

    async def close(self):
        """Close browser and playwright"""
        if self.browser:
            await self.browser.close()
        if self.playwright:
            await self.playwright.stop()

        if self.enhanced_playwright:
            await self.enhanced_playwright.close()

    async def fetch(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """Fetch a URL using playwright"""
        start_time = time.time()

        try:
            await self.init()
            context = await self.browser.new_context(
                ignore_https_errors=True
            )
            page = await context.new_page()

            # Configure viewport
            await page.set_viewport_size({
                'width': task.get('js_viewport_width', 1024),
                'height': task.get('js_viewport_height', 768 * 3)
            })

            # Set headers
            if task.get('headers'):
                await page.set_extra_http_headers(task['headers'])

            # Handle image loading
            if task.get('load_images') == "false":
                await page.route("**/*", lambda route: route.abort()
                    if route.request.resource_type == "image"
                    else route.continue_())

            # Initialize actions
            self.actions = PlaywrightActions(page)

            # Navigate to page
            response: Response = await page.goto(
                task['url'],
                timeout=task.get('timeout', 20) * 1000,
                wait_until='networkidle'
            )

            # Perform actions if specified
            actions_result = None
            if task.get('actions'):
                logger.info('Performing actions')
                actions_result = await self.actions.perform_actions(task['actions'])

            # Record actions if requested
            if task.get('record_actions'):
                logger.info('Recording actions')
                await self.actions.start_recording()

                # Wait for recording timeout
                record_timeout = task.get('record_timeout', 60) * 1000
                await page.wait_for_timeout(record_timeout)

                # Stop recording and get actions
                actions_result = await self.actions.stop_recording()

                # Save recorded actions if path is provided
                if task.get('record_actions_path'):
                    self.actions.save_recorded_actions(task['record_actions_path'])

            # Execute custom JavaScript
            script_result = None
            if task.get('js_script'):
                logger.info('Executing custom JavaScript')
                script_result = await page.evaluate(task['js_script'])

            # Take screenshot if requested
            if task.get('screenshot_path'):
                await page.screenshot(path=task['screenshot_path'])

            # Get cookies
            cookies = {}
            raw_cookies = await context.cookies()
            for cookie in raw_cookies:
                cookies[cookie['name']] = cookie['value']

            # Prepare result
            result = {
                'orig_url': task['url'],
                'status_code': response.status if response else 599,
                'error': None,
                'content': await page.content(),
                'headers': dict(response.headers) if response else {},
                'url': page.url,
                'cookies': cookies,
                'time': time.time() - start_time,
                'js_script_result': script_result,
                'actions_result': actions_result,
                'save': task.get('save')
            }

            await context.close()
            return result

        except Exception as e:
            from pyspider.libs.error_types import NetworkError, TimeoutError

            logger.error(f"Error during fetch: {str(e)}")

            # Determine the specific error type
            if 'timeout' in str(e).lower():
                error_type = TimeoutError(str(e))
            elif any(net_err in str(e).lower() for net_err in ['network', 'connection', 'connect', 'socket', 'dns']):
                error_type = NetworkError(str(e))
            else:
                error_type = Exception(str(e))

            return {
                'orig_url': task['url'],
                'status_code': 599,
                'error': str(e),
                'error_type': error_type.__class__.__name__,
                'content': None,
                'headers': {},
                'url': task['url'],
                'cookies': {},
                'time': time.time() - start_time,
                'js_script_result': None,
                'save': task.get('save')
            }

    async def fetch_spa(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """
        SPAサイト（Single Page Application）をフェッチ

        このメソッドは、SPAサイトのフェッチに特化しており、以下の機能を提供します：
        - SPAフレームワークの自動検出
        - フレームワーク固有の待機戦略
        - 動的コンテンツの完全な読み込み

        Args:
            task: フェッチタスク

        Returns:
            フェッチ結果
        """
        if not has_playwright:
            return {
                'orig_url': task['url'],
                'status_code': 501,
                'error': "Playwright is not installed. Please install it with 'pip install playwright'",
                'content': None,
                'headers': {},
                'url': task['url'],
                'cookies': {},
                'time': 0,
                'save': task.get('save')
            }

        # EnhancedPlaywrightを初期化
        if not self.enhanced_playwright:
            self.enhanced_playwright = EnhancedPlaywright(
                browser_type='chromium',
                headless=task.get('headless', True) != "false",
                user_agent=task.get('headers', {}).get('User-Agent'),
                viewport={
                    'width': task.get('js_viewport_width', 1280),
                    'height': task.get('js_viewport_height', 800)
                },
                timeout=task.get('timeout', 60),
                proxy=task.get('proxy'),
                ignore_https_errors=True,
                slow_mo=task.get('slow_mo', 0),
                persistent_context=task.get('persistent_context', False),
                storage_state=task.get('storage_state')
            )
            await self.enhanced_playwright.init()

        # SPAサイトをフェッチ
        options = {
            'screenshot': task.get('screenshot', False),
            'content_ready_timeout': task.get('content_ready_timeout', 10000),
            'save': task.get('save')
        }

        result = await self.enhanced_playwright.fetch_spa(task['url'], options)

        # カスタムJavaScriptを実行
        if task.get('js_script') and not result.get('error'):
            try:
                script_result = await self.enhanced_playwright.page.evaluate(task['js_script'])
                result['js_script_result'] = script_result
            except Exception as e:
                logger.error(f"Error executing JavaScript: {str(e)}")
                result['js_script_error'] = str(e)

        # アクションを実行
        if task.get('actions') and not result.get('error'):
            try:
                self.actions = PlaywrightActions(self.enhanced_playwright.page)
                actions_result = await self.actions.perform_actions(task['actions'])
                result['actions_result'] = actions_result

                # アクション実行後のコンテンツを更新
                result['content'] = await self.enhanced_playwright.page.content()
                result['url'] = self.enhanced_playwright.page.url
            except Exception as e:
                logger.error(f"Error performing actions: {str(e)}")
                result['actions_error'] = str(e)

        return result

async def create_fetcher():
    """Create and initialize a new PyPlaywrightFetcher instance"""
    fetcher = PyPlaywrightFetcher()
    await fetcher.init()
    return fetcher

if __name__ == "__main__":
    import asyncio

    async def main():
        fetcher = await create_fetcher()
        try:
            # 通常のフェッチをテスト
            print("Testing regular fetch...")
            result = await fetcher.fetch({
                'url': 'https://example.com',
                'timeout': 20
            })
            print(f"Regular fetch result: {result['status_code']}")

            # SPAサイトのフェッチをテスト
            print("\nTesting SPA fetch...")
            spa_result = await fetcher.fetch_spa({
                'url': 'https://reactjs.org',  # Reactベースのサイト
                'timeout': 30
            })
            print(f"SPA fetch result: {spa_result['status_code']}")
            print(f"Detected framework: {spa_result.get('framework', 'Unknown')}")

            # 別のSPAサイトをテスト
            print("\nTesting another SPA site...")
            vue_result = await fetcher.fetch_spa({
                'url': 'https://vuejs.org',  # Vueベースのサイト
                'timeout': 30
            })
            print(f"Vue site fetch result: {vue_result['status_code']}")
            print(f"Detected framework: {vue_result.get('framework', 'Unknown')}")
        finally:
            await fetcher.close()

    asyncio.run(main())