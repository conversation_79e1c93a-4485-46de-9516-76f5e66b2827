#!/usr/bin/env python
# -*- encoding: utf-8 -*-
# vim: set et sw=4 ts=4 sts=4 ff=unix fenc=utf8:
# Author: PySpider Team
# Created on 2023-05-18 10:00:00

import asyncio
import json
import time
import os
import logging
import traceback
from typing import Dict, Any, Optional, List, Union, Tuple, Callable
from urllib.parse import urlparse

try:
    from playwright.async_api import async_playwright, <PERSON><PERSON><PERSON>, Page, Response, ElementHandle, Locator
    has_playwright = True
except ImportError:
    has_playwright = False

from pyspider.libs.metrics import metrics
from pyspider.libs.utils import pretty_unicode

logger = logging.getLogger('fetcher.playwright')

class EnhancedPlaywrightFetcher:
    """
    Enhanced Playwright fetcher with advanced features
    """

    def __init__(self,
                 browser_type: str = 'chromium',
                 headless: bool = True,
                 proxy: Optional[str] = None,
                 user_agent: Optional[str] = None,
                 timeout: int = 20,
                 slow_mo: int = 0,
                 ignore_https_errors: bool = True,
                 max_pages: int = 10,
                 device_scale_factor: float = 1.0,
                 locale: str = 'en-US'):
        """
        Initialize EnhancedPlaywrightFetcher

        Args:
            browser_type: Browser type (chromium, firefox, webkit)
            headless: Whether to run browser in headless mode
            proxy: Proxy server URL
            user_agent: User agent string
            timeout: Default timeout in seconds
            slow_mo: Slow down operations by specified milliseconds
            ignore_https_errors: Whether to ignore HTTPS errors
            max_pages: Maximum number of pages to keep in pool
            device_scale_factor: Device scale factor
            locale: Browser locale
        """
        if not has_playwright:
            logger.error("Playwright is not installed. Please install it with 'pip install playwright' and run 'playwright install'")
            return

        self.browser_type = browser_type
        self.headless = headless
        self.proxy = proxy
        self.user_agent = user_agent
        self.timeout = timeout
        self.slow_mo = slow_mo
        self.ignore_https_errors = ignore_https_errors
        self.max_pages = max_pages
        self.device_scale_factor = device_scale_factor
        self.locale = locale

        # Playwright objects
        self.playwright = None
        self.browser = None
        self._page_pool = []
        self._lock = asyncio.Lock()
        self._initialized = False

        # Metrics
        metrics.gauge('playwright_max_pages', max_pages)

    async def init(self):
        """Initialize playwright and browser"""
        if self._initialized:
            return

        if not has_playwright:
            logger.error("Playwright is not installed")
            return

        async with self._lock:
            if self._initialized:
                return

            start_time = time.time()

            try:
                # Start playwright
                self.playwright = await async_playwright().start()

                # Select browser factory
                if self.browser_type == 'firefox':
                    browser_factory = self.playwright.firefox
                elif self.browser_type == 'webkit':
                    browser_factory = self.playwright.webkit
                else:
                    browser_factory = self.playwright.chromium

                # Launch browser
                browser_args = []
                if self.browser_type == 'chromium':
                    browser_args = [
                        '--disable-gpu',
                        '--disable-dev-shm-usage',
                        '--disable-setuid-sandbox',
                        '--no-sandbox',
                        '--disable-web-security',
                        '--disable-features=IsolateOrigins,site-per-process',
                        '--disable-site-isolation-trials'
                    ]

                # Prepare launch options
                launch_options = {
                    'headless': self.headless,
                    'args': browser_args,
                    'slow_mo': self.slow_mo,
                    'timeout': self.timeout * 1000
                }

                # Add proxy if specified
                if self.proxy:
                    launch_options['proxy'] = {
                        'server': self.proxy
                    }

                # Launch browser
                self.browser = await browser_factory.launch(**launch_options)

                # Initialize page pool
                for _ in range(min(3, self.max_pages)):  # Start with a few pages
                    page = await self._create_new_page()
                    self._page_pool.append(page)

                self._initialized = True

                # Record metrics
                metrics.gauge('playwright_pages', len(self._page_pool))
                metrics.increment('playwright_browser_launches')
                metrics.gauge('playwright_init_time', time.time() - start_time)

                logger.info(f"Playwright initialized with {self.browser_type} browser (headless: {self.headless})")
            except Exception as e:
                logger.error(f"Failed to initialize Playwright: {e}")
                if self.playwright:
                    await self.playwright.stop()
                    self.playwright = None
                raise

    async def close(self):
        """Close browser and playwright"""
        async with self._lock:
            if self.browser:
                await self.browser.close()
                self.browser = None

            if self.playwright:
                await self.playwright.stop()
                self.playwright = None

            self._page_pool = []
            self._initialized = False

            logger.info("Playwright closed")

    async def _create_new_page(self) -> Page:
        """Create a new browser page with default settings"""
        if not self.browser:
            raise RuntimeError("Browser not initialized")

        # Create context with options
        context_options = {
            'ignore_https_errors': self.ignore_https_errors,
            'locale': self.locale,
            'device_scale_factor': self.device_scale_factor
        }

        # Set user agent if specified
        if self.user_agent:
            context_options['user_agent'] = self.user_agent

        context = await self.browser.new_context(**context_options)

        # Create page
        page = await context.new_page()

        # Set default timeout
        page.set_default_timeout(self.timeout * 1000)

        return page

    async def get_page(self) -> Tuple[Page, bool]:
        """
        Get a page from pool or create a new one

        Returns:
            Tuple of (page, is_new)
        """
        if not has_playwright:
            raise ImportError("Playwright is not installed")

        async with self._lock:
            if not self._initialized:
                await self.init()

            # Try to get a page from pool
            if self._page_pool:
                page = self._page_pool.pop()
                return page, False

            # Create a new page if pool is empty
            page = await self._create_new_page()

            # Record metrics
            metrics.gauge('playwright_pages', len(self._page_pool))
            metrics.increment('playwright_pages_created')

            return page, True

    async def return_page(self, page: Page):
        """Return a page to the pool"""
        if not page:
            return

        async with self._lock:
            # Check if we already have enough pages in the pool
            if len(self._page_pool) >= self.max_pages:
                try:
                    await page.context.close()
                except Exception as e:
                    logger.error(f"Error closing page context: {e}")
                return

            # Clear page state
            try:
                await page.context.clear_cookies()
                await page.reload()
            except Exception as e:
                logger.error(f"Error clearing page state: {e}")
                try:
                    await page.context.close()
                except:
                    pass
                return

            # Add page back to pool
            self._page_pool.append(page)

            # Record metrics
            metrics.gauge('playwright_pages', len(self._page_pool))

    async def fetch(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """
        Fetch a URL using Playwright

        Args:
            task: Task dictionary with URL and options

        Returns:
            Fetch result
        """
        if not has_playwright:
            return {
                'status_code': 501,
                'error': "Playwright is not installed. Please install it with 'pip install playwright' and run 'playwright install'",
                'content': "",
                'time': 0,
                'orig_url': task.get('url', ''),
                'url': task.get('url', ''),
                'save': task.get('save')
            }

        url = task.get('url')
        if not url:
            return {
                'status_code': 400,
                'error': "URL is required",
                'content': "",
                'time': 0,
                'orig_url': '',
                'url': '',
                'save': task.get('save')
            }

        start_time = time.time()
        page = None
        is_new_page = False

        # Record metrics
        metrics.increment('playwright_requests')
        metrics.increment('playwright_requests_by_domain', tags={'domain': urlparse(url).netloc})

        try:
            # Get page
            page, is_new_page = await self.get_page()

            # Configure viewport
            await page.set_viewport_size({
                'width': task.get('js_viewport_width', 1024),
                'height': task.get('js_viewport_height', 768 * 3)
            })

            # Set headers
            if task.get('headers'):
                await page.set_extra_http_headers(task.get('headers'))

            # Handle image loading
            if task.get('load_images') == "false":
                await page.route("**/*", lambda route: route.abort()
                    if route.request.resource_type == "image"
                    else route.continue_())

            # Set request interception for resource types
            if task.get('resource_types'):
                resource_types = task.get('resource_types')
                if isinstance(resource_types, str):
                    resource_types = [r.strip() for r in resource_types.split(',')]

                async def handle_route(route):
                    if route.request.resource_type not in resource_types:
                        await route.abort()
                    else:
                        await route.continue_()

                await page.route("**/*", handle_route)

            # Navigate to page
            response = await page.goto(
                url,
                timeout=task.get('timeout', self.timeout) * 1000,
                wait_until=task.get('wait_until', 'networkidle')
            )

            # Wait for selector if specified
            if task.get('wait_for'):
                try:
                    await page.wait_for_selector(
                        task.get('wait_for'),
                        timeout=task.get('wait_for_timeout', 10) * 1000
                    )
                except Exception as e:
                    logger.warning(f"Wait for selector failed: {e}")

            # Wait for function if specified
            if task.get('wait_for_function'):
                try:
                    await page.wait_for_function(
                        task.get('wait_for_function'),
                        timeout=task.get('wait_for_function_timeout', 10) * 1000
                    )
                except Exception as e:
                    logger.warning(f"Wait for function failed: {e}")

            # Execute custom JavaScript
            script_result = None
            if task.get('js_script'):
                try:
                    script_result = await page.evaluate(task.get('js_script'))
                except Exception as e:
                    logger.error(f"JavaScript execution failed: {e}")
                    script_result = {'error': str(e)}

            # Take screenshot if requested
            screenshot = None
            if task.get('screenshot'):
                try:
                    screenshot = await page.screenshot(
                        type=task.get('screenshot_type', 'png'),
                        full_page=task.get('screenshot_full_page', True),
                        quality=task.get('screenshot_quality', 75) if task.get('screenshot_type') == 'jpeg' else None
                    )
                except Exception as e:
                    logger.error(f"Screenshot failed: {e}")

            # Get cookies
            cookies = {}
            raw_cookies = await page.context.cookies()
            for cookie in raw_cookies:
                cookies[cookie['name']] = cookie['value']

            # Get content
            content = await page.content()

            # Get status code
            status_code = response.status if response else 599

            # Get headers
            headers = {}
            if response:
                for key, value in response.headers.items():
                    headers[key.lower()] = value

            # Get final URL
            final_url = page.url

            # Record metrics
            metrics.increment('playwright_requests_success')
            metrics.gauge('playwright_request_time', time.time() - start_time)

            # Return result
            result = {
                'orig_url': url,
                'status_code': status_code,
                'error': None,
                'content': content,
                'headers': headers,
                'url': final_url,
                'cookies': cookies,
                'time': time.time() - start_time,
                'js_script_result': script_result,
                'save': task.get('save')
            }

            # Add screenshot if taken
            if screenshot:
                result['screenshot'] = screenshot

            return result

        except Exception as e:
            from pyspider.libs.error_types import NetworkError, TimeoutError

            logger.error(f"Error during fetch: {str(e)}")
            traceback.print_exc()

            # Determine the specific error type
            if 'timeout' in str(e).lower():
                error_type = TimeoutError(str(e))
            elif any(net_err in str(e).lower() for net_err in ['network', 'connection', 'connect', 'socket', 'dns']):
                error_type = NetworkError(str(e))
            else:
                error_type = Exception(str(e))

            # Record metrics
            metrics.increment('playwright_requests_error')
            metrics.increment(f'playwright_requests_error_{error_type.__class__.__name__.lower()}')

            # Return error result
            error_result = {
                'orig_url': url,
                'status_code': 599,
                'error': str(e),
                'error_type': error_type.__class__.__name__,
                'content': None,
                'headers': {},
                'url': url,
                'cookies': {},
                'time': time.time() - start_time,
                'js_script_result': None,
                'save': task.get('save')
            }

            return error_result

        finally:
            # Return page to pool
            if page:
                try:
                    await self.return_page(page)
                except Exception as e:
                    logger.error(f"Error returning page to pool: {e}")

    async def select(self, page: Page, selector: str) -> List[ElementHandle]:
        """
        Select elements using unified selector API

        Args:
            page: Playwright page
            selector: Selector (CSS or XPath)

        Returns:
            List of element handles
        """
        if not selector:
            return []

        # Check if it's an XPath selector
        is_xpath = selector.startswith('//') or selector.startswith('(//') or selector.startswith('xpath=')

        try:
            if is_xpath:
                # Remove xpath= prefix if present
                if selector.startswith('xpath='):
                    selector = selector[6:]

                # Use XPath selector
                elements = await page.query_selector_all(f'xpath={selector}')
            else:
                # Use CSS selector
                elements = await page.query_selector_all(selector)

            return elements
        except Exception as e:
            logger.error(f"Error selecting elements with selector '{selector}': {e}")
            return []

    async def extract_data(self, page: Page, selectors: Dict[str, str]) -> Dict[str, Any]:
        """
        Extract data from page using selectors

        Args:
            page: Playwright page
            selectors: Dictionary of field name to selector

        Returns:
            Dictionary of extracted data
        """
        result = {}

        for field, selector in selectors.items():
            try:
                # Check if it's a special selector
                if selector.startswith('js:'):
                    # JavaScript selector
                    js_code = selector[3:]
                    result[field] = await page.evaluate(js_code)
                elif selector.startswith('attr:'):
                    # Attribute selector (format: attr:selector::attribute)
                    parts = selector[5:].split('::')
                    if len(parts) != 2:
                        logger.error(f"Invalid attribute selector: {selector}")
                        continue

                    elem_selector, attribute = parts
                    elements = await self.select(page, elem_selector)
                    if elements:
                        result[field] = await elements[0].get_attribute(attribute)
                    else:
                        result[field] = None
                else:
                    # Regular selector
                    elements = await self.select(page, selector)
                    if elements:
                        result[field] = await elements[0].text_content()
                    else:
                        result[field] = None
            except Exception as e:
                logger.error(f"Error extracting data for field '{field}' with selector '{selector}': {e}")
                result[field] = None

        return result

    async def execute_actions(self, page: Page, actions: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Execute a series of actions on a page

        Args:
            page: Playwright page
            actions: List of actions to execute

        Returns:
            Dictionary with action results
        """
        results = []

        for i, action in enumerate(actions):
            try:
                action_type = action.get('type', '').lower()

                if action_type == 'click':
                    # Click action
                    selector = action.get('selector')
                    if not selector:
                        raise ValueError("Selector is required for click action")

                    elements = await self.select(page, selector)
                    if not elements:
                        raise ValueError(f"No elements found for selector: {selector}")

                    await elements[0].click(
                        delay=action.get('delay', 0),
                        button=action.get('button', 'left'),
                        click_count=action.get('click_count', 1),
                        timeout=action.get('timeout', 30000)
                    )

                    results.append({
                        'action': i,
                        'type': 'click',
                        'success': True
                    })

                elif action_type == 'fill':
                    # Fill action
                    selector = action.get('selector')
                    value = action.get('value', '')

                    if not selector:
                        raise ValueError("Selector is required for fill action")

                    elements = await self.select(page, selector)
                    if not elements:
                        raise ValueError(f"No elements found for selector: {selector}")

                    await elements[0].fill(value)

                    results.append({
                        'action': i,
                        'type': 'fill',
                        'success': True
                    })

                elif action_type == 'select':
                    # Select action
                    selector = action.get('selector')
                    value = action.get('value')

                    if not selector:
                        raise ValueError("Selector is required for select action")

                    if not value:
                        raise ValueError("Value is required for select action")

                    elements = await self.select(page, selector)
                    if not elements:
                        raise ValueError(f"No elements found for selector: {selector}")

                    await elements[0].select_option(value)

                    results.append({
                        'action': i,
                        'type': 'select',
                        'success': True
                    })

                elif action_type == 'wait':
                    # Wait action
                    timeout = action.get('timeout', 30000)

                    if 'selector' in action:
                        # Wait for selector
                        await page.wait_for_selector(
                            action.get('selector'),
                            timeout=timeout
                        )
                    elif 'time' in action:
                        # Wait for time
                        await page.wait_for_timeout(action.get('time'))
                    elif 'function' in action:
                        # Wait for function
                        await page.wait_for_function(
                            action.get('function'),
                            timeout=timeout
                        )
                    else:
                        # Default to waiting for load state
                        await page.wait_for_load_state(
                            action.get('state', 'networkidle')
                        )

                    results.append({
                        'action': i,
                        'type': 'wait',
                        'success': True
                    })

                elif action_type == 'screenshot':
                    # Screenshot action
                    screenshot = await page.screenshot(
                        type=action.get('format', 'png'),
                        full_page=action.get('full_page', True),
                        quality=action.get('quality', 75) if action.get('format') == 'jpeg' else None
                    )

                    results.append({
                        'action': i,
                        'type': 'screenshot',
                        'success': True,
                        'data': screenshot
                    })

                elif action_type == 'evaluate':
                    # Evaluate JavaScript
                    script = action.get('script')
                    if not script:
                        raise ValueError("Script is required for evaluate action")

                    result = await page.evaluate(script)

                    results.append({
                        'action': i,
                        'type': 'evaluate',
                        'success': True,
                        'result': result
                    })

                elif action_type == 'extract':
                    # Extract data
                    selectors = action.get('selectors', {})
                    if not selectors:
                        raise ValueError("Selectors are required for extract action")

                    data = await self.extract_data(page, selectors)

                    results.append({
                        'action': i,
                        'type': 'extract',
                        'success': True,
                        'data': data
                    })

                elif action_type == 'goto':
                    # Navigate to URL
                    url = action.get('url')
                    if not url:
                        raise ValueError("URL is required for goto action")

                    await page.goto(
                        url,
                        timeout=action.get('timeout', 30000),
                        wait_until=action.get('wait_until', 'networkidle')
                    )

                    results.append({
                        'action': i,
                        'type': 'goto',
                        'success': True,
                        'url': page.url
                    })

                else:
                    # Unknown action type
                    logger.warning(f"Unknown action type: {action_type}")
                    results.append({
                        'action': i,
                        'type': action_type,
                        'success': False,
                        'error': f"Unknown action type: {action_type}"
                    })

            except Exception as e:
                logger.error(f"Error executing action {i} ({action.get('type')}): {e}")
                results.append({
                    'action': i,
                    'type': action.get('type'),
                    'success': False,
                    'error': str(e)
                })

        return {
            'success': all(r.get('success', False) for r in results),
            'results': results
        }
