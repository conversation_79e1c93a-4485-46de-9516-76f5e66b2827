#!/usr/bin/env python
# -*- coding: utf-8 -*-

import logging
import time
import json
import re
from typing import Dict, Any, List, Optional, Union, Callable
from urllib.parse import urlparse

logger = logging.getLogger('spa_handler')

class SPAHandler:
    """
    SPAサイト（Single Page Application）対応のためのハンドラークラス
    
    このクラスは、SPAサイトのクローリングを効率的に行うための機能を提供します。
    - ルートURLの自動検出
    - ハッシュベースのナビゲーション対応
    - 動的コンテンツの待機戦略
    - SPAフレームワーク固有の処理
    """
    
    # SPAフレームワークの検出パターン
    SPA_FRAMEWORKS = {
        'react': [
            r'react\.development\.js',
            r'react\.production\.min\.js',
            r'react-dom',
            r'__REACT_DEVTOOLS_GLOBAL_HOOK__'
        ],
        'vue': [
            r'vue\.js',
            r'vue\.min\.js',
            r'__VUE__',
            r'Vue\.version'
        ],
        'angular': [
            r'angular\.js',
            r'angular\.min\.js',
            r'ng-app',
            r'ng-controller'
        ],
        'svelte': [
            r'svelte',
            r'__SVELTE__'
        ]
    }
    
    def __init__(self):
        self.detected_framework = None
    
    def detect_spa_framework(self, html_content: str) -> Optional[str]:
        """
        HTMLコンテンツからSPAフレームワークを検出
        
        Args:
            html_content: HTMLコンテンツ
            
        Returns:
            検出されたフレームワーク名、または None
        """
        for framework, patterns in self.SPA_FRAMEWORKS.items():
            for pattern in patterns:
                if re.search(pattern, html_content, re.IGNORECASE):
                    logger.info(f"Detected SPA framework: {framework}")
                    self.detected_framework = framework
                    return framework
        
        return None
    
    def get_wait_strategy(self, framework: Optional[str] = None) -> Dict[str, Any]:
        """
        フレームワークに適した待機戦略を取得
        
        Args:
            framework: SPAフレームワーク名
            
        Returns:
            待機戦略の設定
        """
        framework = framework or self.detected_framework
        
        # デフォルトの待機戦略
        default_strategy = {
            'waitUntil': 'networkidle',
            'timeout': 30000
        }
        
        # フレームワーク固有の待機戦略
        framework_strategies = {
            'react': {
                'waitUntil': 'networkidle',
                'timeout': 30000,
                'waitForSelector': '#root, [data-reactroot], [id="app"]'
            },
            'vue': {
                'waitUntil': 'networkidle',
                'timeout': 30000,
                'waitForSelector': '#app, [id="app"]'
            },
            'angular': {
                'waitUntil': 'networkidle',
                'timeout': 40000,  # Angularは少し長めに
                'waitForSelector': '[ng-app], [ng-controller], app-root'
            },
            'svelte': {
                'waitUntil': 'networkidle',
                'timeout': 30000
            }
        }
        
        return framework_strategies.get(framework, default_strategy)
    
    def get_navigation_actions(self, url: str, framework: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        SPAサイトのナビゲーションに必要なアクションを取得
        
        Args:
            url: ナビゲーション先のURL
            framework: SPAフレームワーク名
            
        Returns:
            ナビゲーションアクションのリスト
        """
        framework = framework or self.detected_framework
        parsed_url = urlparse(url)
        
        # ハッシュベースのルーティングを検出
        has_hash_routing = bool(parsed_url.fragment)
        
        # 基本的なナビゲーションアクション
        actions = [
            {
                'type': 'goto',
                'url': url,
                'options': self.get_wait_strategy(framework)
            }
        ]
        
        # フレームワーク固有のナビゲーションアクション
        if framework == 'react' or framework == 'vue':
            actions.append({
                'type': 'wait_for_timeout',
                'timeout': 1000  # 追加の待機時間
            })
            
            # ルートコンテナが読み込まれるのを待つ
            if framework == 'react':
                actions.append({
                    'type': 'wait_for_selector',
                    'selector': '#root, [data-reactroot], [id="app"]',
                    'options': {'state': 'attached'}
                })
            elif framework == 'vue':
                actions.append({
                    'type': 'wait_for_selector',
                    'selector': '#app, [id="app"]',
                    'options': {'state': 'attached'}
                })
        
        # ハッシュベースのルーティングの場合、追加の待機
        if has_hash_routing:
            actions.append({
                'type': 'wait_for_timeout',
                'timeout': 2000  # ハッシュ変更後の待機時間
            })
        
        return actions
    
    def get_content_ready_check(self, framework: Optional[str] = None) -> str:
        """
        コンテンツが読み込まれたかを確認するためのJavaScriptを取得
        
        Args:
            framework: SPAフレームワーク名
            
        Returns:
            実行するJavaScriptコード
        """
        framework = framework or self.detected_framework
        
        # デフォルトのチェック
        default_check = """
            () => {
                // ネットワークリクエストが完了しているか確認
                const idle = !window.fetch.active && 
                             !window.XMLHttpRequest.active && 
                             document.readyState === 'complete';
                             
                // ローディングインジケータが表示されていないか確認
                const noLoaders = !document.querySelector('.loading, .spinner, [role="progressbar"]');
                
                return idle && noLoaders;
            }
        """
        
        # フレームワーク固有のチェック
        framework_checks = {
            'react': """
                () => {
                    // Reactアプリが読み込まれているか確認
                    const reactLoaded = !!document.querySelector('#root, [data-reactroot], [id="app"]');
                    
                    // ローディングインジケータが表示されていないか確認
                    const noLoaders = !document.querySelector('.loading, .spinner, [role="progressbar"]');
                    
                    return reactLoaded && noLoaders && document.readyState === 'complete';
                }
            """,
            'vue': """
                () => {
                    // Vueアプリが読み込まれているか確認
                    const vueLoaded = !!document.querySelector('#app, [id="app"]');
                    
                    // ローディングインジケータが表示されていないか確認
                    const noLoaders = !document.querySelector('.loading, .spinner, [role="progressbar"]');
                    
                    return vueLoaded && noLoaders && document.readyState === 'complete';
                }
            """,
            'angular': """
                () => {
                    // Angularアプリが読み込まれているか確認
                    const angularLoaded = !!document.querySelector('[ng-app], [ng-controller], app-root');
                    
                    // ローディングインジケータが表示されていないか確認
                    const noLoaders = !document.querySelector('.loading, .spinner, [role="progressbar"]');
                    
                    return angularLoaded && noLoaders && document.readyState === 'complete';
                }
            """
        }
        
        return framework_checks.get(framework, default_check)
