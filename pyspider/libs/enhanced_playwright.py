#!/usr/bin/env python
# -*- coding: utf-8 -*-

import asyncio
import json
import logging
import os
import time
from typing import Dict, Any, List, Optional, Union, Callable, Tuple
from urllib.parse import urlparse

try:
    from playwright.async_api import async_playwright, Browser, Page, Response, BrowserContext
    has_playwright = True
except ImportError:
    has_playwright = False

from pyspider.libs.spa_handler import SPAHandler

logger = logging.getLogger('enhanced_playwright')

class EnhancedPlaywright:
    """
    拡張されたPlaywright統合クラス
    
    このクラスは、Playwrightの機能を拡張し、SPAサイトのクローリングを効率的に行うための機能を提供します。
    - SPAフレームワークの自動検出
    - 高度なブラウザ操作
    - エラーハンドリングの強化
    - パフォーマンス最適化
    """
    
    def __init__(self, 
                 browser_type: str = 'chromium',
                 headless: bool = True,
                 user_agent: Optional[str] = None,
                 viewport: Dict[str, int] = None,
                 timeout: int = 60,
                 proxy: Optional[str] = None,
                 ignore_https_errors: bool = True,
                 slow_mo: int = 0,
                 persistent_context: bool = False,
                 storage_state: Optional[str] = None):
        """
        初期化
        
        Args:
            browser_type: ブラウザタイプ ('chromium', 'firefox', 'webkit')
            headless: ヘッドレスモードで実行するかどうか
            user_agent: ユーザーエージェント
            viewport: ビューポートサイズ
            timeout: タイムアウト（秒）
            proxy: プロキシ設定
            ignore_https_errors: HTTPSエラーを無視するかどうか
            slow_mo: 操作を遅くする時間（ミリ秒）
            persistent_context: 永続的なコンテキストを使用するかどうか
            storage_state: ストレージ状態（Cookieやローカルストレージ）
        """
        if not has_playwright:
            logger.error("Playwright is not installed. Please install it with 'pip install playwright'")
            return
        
        self.browser_type = browser_type
        self.headless = headless
        self.user_agent = user_agent or 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        self.viewport = viewport or {'width': 1280, 'height': 800}
        self.timeout = timeout * 1000  # ミリ秒に変換
        self.proxy = proxy
        self.ignore_https_errors = ignore_https_errors
        self.slow_mo = slow_mo
        self.persistent_context = persistent_context
        self.storage_state = storage_state
        
        self.playwright = None
        self.browser = None
        self.context = None
        self.page = None
        self.spa_handler = SPAHandler()
        
        # 統計情報
        self.stats = {
            'pages_created': 0,
            'requests_sent': 0,
            'errors': 0,
            'total_time': 0
        }
    
    async def init(self) -> bool:
        """
        Playwrightを初期化
        
        Returns:
            初期化に成功したかどうか
        """
        if not has_playwright:
            return False
        
        try:
            self.playwright = await async_playwright().start()
            
            # ブラウザタイプに応じたファクトリを取得
            browser_factory = getattr(self.playwright, self.browser_type)
            
            # ブラウザ起動オプション
            browser_args = []
            if self.browser_type == 'chromium':
                browser_args = [
                    '--no-sandbox',
                    '--disable-setuid-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-accelerated-2d-canvas',
                    '--disable-gpu',
                    '--disable-web-security',
                    '--disable-features=IsolateOrigins,site-per-process',
                    '--disable-site-isolation-trials',
                    '--ignore-certificate-errors',
                    '--ignore-ssl-errors'
                ]
            
            # プロキシ設定
            proxy_settings = None
            if self.proxy:
                proxy_settings = {
                    'server': self.proxy
                }
            
            # 永続的なコンテキストを使用する場合
            if self.persistent_context:
                user_data_dir = os.path.join(os.getcwd(), 'playwright_user_data')
                os.makedirs(user_data_dir, exist_ok=True)
                
                self.context = await browser_factory.launch_persistent_context(
                    user_data_dir=user_data_dir,
                    headless=self.headless,
                    args=browser_args,
                    proxy=proxy_settings,
                    viewport=self.viewport,
                    user_agent=self.user_agent,
                    ignore_https_errors=self.ignore_https_errors,
                    slow_mo=self.slow_mo,
                    timeout=self.timeout
                )
                self.browser = None  # 永続的なコンテキストの場合はブラウザオブジェクトはない
            else:
                # 通常のブラウザを起動
                self.browser = await browser_factory.launch(
                    headless=self.headless,
                    args=browser_args,
                    proxy=proxy_settings,
                    slow_mo=self.slow_mo,
                    timeout=self.timeout
                )
                
                # コンテキストを作成
                context_options = {
                    'viewport': self.viewport,
                    'user_agent': self.user_agent,
                    'ignore_https_errors': self.ignore_https_errors
                }
                
                # ストレージ状態を設定
                if self.storage_state:
                    if os.path.exists(self.storage_state):
                        with open(self.storage_state, 'r') as f:
                            context_options['storage_state'] = json.load(f)
                    else:
                        context_options['storage_state'] = self.storage_state
                
                self.context = await self.browser.new_context(**context_options)
            
            # 新しいページを作成
            self.page = await self.context.new_page()
            self.stats['pages_created'] += 1
            
            logger.info(f"Enhanced Playwright initialized with {self.browser_type} browser")
            return True
        
        except Exception as e:
            logger.error(f"Failed to initialize Enhanced Playwright: {str(e)}")
            self.stats['errors'] += 1
            return False
    
    async def close(self) -> None:
        """
        Playwrightを終了
        """
        try:
            if self.context:
                await self.context.close()
            
            if self.browser:
                await self.browser.close()
            
            if self.playwright:
                await self.playwright.stop()
            
            logger.info("Enhanced Playwright closed")
        except Exception as e:
            logger.error(f"Error closing Enhanced Playwright: {str(e)}")
    
    async def fetch_spa(self, url: str, options: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        SPAサイトをフェッチ
        
        Args:
            url: フェッチするURL
            options: フェッチオプション
            
        Returns:
            フェッチ結果
        """
        options = options or {}
        start_time = time.time()
        self.stats['requests_sent'] += 1
        
        try:
            if not self.page:
                if not await self.init():
                    return {
                        'status_code': 500,
                        'error': "Failed to initialize Enhanced Playwright",
                        'content': "",
                        'time': time.time() - start_time,
                        'orig_url': url,
                        'url': url
                    }
            
            # SPAフレームワークの検出（前回のページから）
            if self.page.url != 'about:blank':
                content = await self.page.content()
                self.spa_handler.detect_spa_framework(content)
            
            # SPAサイト用のナビゲーションアクション
            navigation_actions = self.spa_handler.get_navigation_actions(url)
            
            # ナビゲーションアクションを実行
            for action in navigation_actions:
                if action['type'] == 'goto':
                    # ページに移動
                    response = await self.page.goto(
                        action['url'],
                        timeout=action['options'].get('timeout', self.timeout),
                        wait_until=action['options'].get('waitUntil', 'networkidle')
                    )
                elif action['type'] == 'wait_for_selector':
                    # セレクタが表示されるのを待つ
                    await self.page.wait_for_selector(
                        action['selector'],
                        state=action['options'].get('state', 'visible'),
                        timeout=action['options'].get('timeout', self.timeout)
                    )
                elif action['type'] == 'wait_for_timeout':
                    # 一定時間待機
                    await self.page.wait_for_timeout(action['timeout'])
            
            # SPAフレームワークの検出（現在のページから）
            content = await self.page.content()
            framework = self.spa_handler.detect_spa_framework(content)
            
            # コンテンツが完全に読み込まれたかを確認するJavaScriptを実行
            content_ready_check = self.spa_handler.get_content_ready_check(framework)
            try:
                await self.page.wait_for_function(
                    content_ready_check,
                    timeout=options.get('content_ready_timeout', 10000)
                )
            except Exception as e:
                logger.warning(f"Content ready check timed out: {str(e)}")
            
            # 結果を作成
            result = {
                'orig_url': url,
                'status_code': response.status if response else 599,
                'error': None,
                'content': await self.page.content(),
                'headers': dict(response.headers()) if response else {},
                'url': self.page.url,
                'cookies': await self.get_cookies(),
                'time': time.time() - start_time,
                'framework': framework,
                'screenshot': await self.page.screenshot() if options.get('screenshot', False) else None,
                'save': options.get('save')
            }
            
            self.stats['total_time'] += result['time']
            return result
            
        except Exception as e:
            logger.error(f"Error fetching SPA site: {str(e)}")
            self.stats['errors'] += 1
            
            return {
                'orig_url': url,
                'status_code': 599,
                'error': str(e),
                'content': "",
                'headers': {},
                'url': url,
                'cookies': {},
                'time': time.time() - start_time,
                'save': options.get('save')
            }
    
    async def get_cookies(self) -> Dict[str, str]:
        """
        現在のページのCookieを取得
        
        Returns:
            Cookieの辞書
        """
        cookies = {}
        if self.context:
            raw_cookies = await self.context.cookies()
            for cookie in raw_cookies:
                cookies[cookie['name']] = cookie['value']
        return cookies
