#!/usr/bin/env python
# -*- coding: utf-8 -*-

import time
import logging
import threading
import queue
import itertools
from typing import Dict, List, Any, Callable, Generator, Optional, Union, Iterable, TypeVar

logger = logging.getLogger('stream_processing')

T = TypeVar('T')
U = TypeVar('U')

class StreamProcessor:
    """
    ストリーム処理エンジン
    
    大量のデータを効率的に処理するためのストリーム処理エンジンを提供します。
    - ストリーム処理：データをストリームとして処理
    - パイプライン処理：複数の処理を連結して実行
    - 並列処理：複数のワーカーで並列に処理
    - バックプレッシャー：処理速度に応じて入力を制御
    """
    
    def __init__(self, max_workers: int = 4, queue_size: int = 100):
        """
        初期化
        
        Args:
            max_workers: 最大ワーカー数
            queue_size: キューサイズ
        """
        self.max_workers = max_workers
        self.queue_size = queue_size
        self.workers = []
        self.input_queue = queue.Queue(maxsize=queue_size)
        self.output_queue = queue.Queue(maxsize=queue_size)
        self.stop_event = threading.Event()
        self.processors = []
        self.stats = {
            'processed': 0,
            'errors': 0,
            'start_time': 0,
            'end_time': 0,
            'processing_time': 0
        }
    
    def add_processor(self, processor: Callable[[Any], Any]) -> 'StreamProcessor':
        """
        プロセッサを追加
        
        Args:
            processor: データを処理する関数
            
        Returns:
            自身のインスタンス（メソッドチェーン用）
        """
        self.processors.append(processor)
        return self
    
    def map(self, func: Callable[[T], U]) -> 'StreamProcessor':
        """
        マップ処理を追加
        
        Args:
            func: 各要素に適用する関数
            
        Returns:
            自身のインスタンス（メソッドチェーン用）
        """
        def processor(data):
            return func(data)
        
        return self.add_processor(processor)
    
    def filter(self, predicate: Callable[[T], bool]) -> 'StreamProcessor':
        """
        フィルター処理を追加
        
        Args:
            predicate: フィルター条件
            
        Returns:
            自身のインスタンス（メソッドチェーン用）
        """
        def processor(data):
            if predicate(data):
                return data
            return None
        
        return self.add_processor(processor)
    
    def flat_map(self, func: Callable[[T], Iterable[U]]) -> 'StreamProcessor':
        """
        フラットマップ処理を追加
        
        Args:
            func: 各要素を複数の要素に変換する関数
            
        Returns:
            自身のインスタンス（メソッドチェーン用）
        """
        def processor(data):
            result = func(data)
            if result is None:
                return None
            return list(result)
        
        return self.add_processor(processor)
    
    def reduce(self, func: Callable[[T, T], T], initial: Optional[T] = None) -> 'StreamProcessor':
        """
        リデュース処理を追加
        
        Args:
            func: 集約関数
            initial: 初期値
            
        Returns:
            自身のインスタンス（メソッドチェーン用）
        """
        result = initial
        
        def processor(data):
            nonlocal result
            if result is None:
                result = data
            else:
                result = func(result, data)
            return result
        
        return self.add_processor(processor)
    
    def batch(self, size: int) -> 'StreamProcessor':
        """
        バッチ処理を追加
        
        Args:
            size: バッチサイズ
            
        Returns:
            自身のインスタンス（メソッドチェーン用）
        """
        buffer = []
        
        def processor(data):
            nonlocal buffer
            if data is None:
                if buffer:
                    result = list(buffer)
                    buffer = []
                    return result
                return None
            
            buffer.append(data)
            if len(buffer) >= size:
                result = list(buffer)
                buffer = []
                return result
            return None
        
        return self.add_processor(processor)
    
    def window(self, size: int, slide: int = 1) -> 'StreamProcessor':
        """
        ウィンドウ処理を追加
        
        Args:
            size: ウィンドウサイズ
            slide: スライドサイズ
            
        Returns:
            自身のインスタンス（メソッドチェーン用）
        """
        buffer = []
        count = 0
        
        def processor(data):
            nonlocal buffer, count
            if data is None:
                return None
            
            buffer.append(data)
            count += 1
            
            if len(buffer) > size:
                buffer.pop(0)
            
            if len(buffer) == size and count % slide == 0:
                return list(buffer)
            
            return None
        
        return self.add_processor(processor)
    
    def _worker(self):
        """ワーカースレッドの処理"""
        while not self.stop_event.is_set():
            try:
                data = self.input_queue.get(timeout=0.1)
                if data is None:
                    self.output_queue.put(None)
                    break
                
                try:
                    # プロセッサチェーンを実行
                    for processor in self.processors:
                        if data is None:
                            break
                        data = processor(data)
                    
                    if data is not None:
                        self.output_queue.put(data)
                        self.stats['processed'] += 1
                except Exception as e:
                    logger.exception(f"Error processing data: {e}")
                    self.stats['errors'] += 1
                
                self.input_queue.task_done()
            except queue.Empty:
                continue
    
    def process(self, data_stream: Iterable[T]) -> Generator[Any, None, None]:
        """
        データストリームを処理
        
        Args:
            data_stream: 入力データストリーム
            
        Returns:
            処理結果のジェネレータ
        """
        self.stats['start_time'] = time.time()
        self.stats['processed'] = 0
        self.stats['errors'] = 0
        
        # ワーカースレッドを起動
        self.stop_event.clear()
        self.workers = []
        for _ in range(self.max_workers):
            worker = threading.Thread(target=self._worker)
            worker.daemon = True
            worker.start()
            self.workers.append(worker)
        
        # 入力データをキューに投入
        input_thread = threading.Thread(
            target=self._feed_input,
            args=(data_stream,)
        )
        input_thread.daemon = True
        input_thread.start()
        
        # 出力データを取得
        done_workers = 0
        while done_workers < self.max_workers:
            try:
                result = self.output_queue.get(timeout=0.1)
                if result is None:
                    done_workers += 1
                    continue
                
                yield result
                self.output_queue.task_done()
            except queue.Empty:
                continue
        
        # 終了処理
        for worker in self.workers:
            worker.join()
        
        self.stats['end_time'] = time.time()
        self.stats['processing_time'] = self.stats['end_time'] - self.stats['start_time']
    
    def _feed_input(self, data_stream: Iterable[T]):
        """入力データをキューに投入"""
        try:
            for data in data_stream:
                while not self.stop_event.is_set():
                    try:
                        self.input_queue.put(data, timeout=0.1)
                        break
                    except queue.Full:
                        continue
        finally:
            # 終了シグナルを送信
            for _ in range(self.max_workers):
                self.input_queue.put(None)
    
    def stop(self):
        """処理を停止"""
        self.stop_event.set()
        for worker in self.workers:
            worker.join()
    
    def get_stats(self) -> Dict[str, Any]:
        """統計情報を取得"""
        return self.stats
