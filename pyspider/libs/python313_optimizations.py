#!/usr/bin/env python
# -*- encoding: utf-8 -*-
# Python 3.13 optimizations and new features

import sys
import os
import threading
import concurrent.futures
from typing import Optional, Any, Dict, List
import logging

logger = logging.getLogger(__name__)

# Python 3.13 feature detection
PYTHON_313_PLUS = sys.version_info >= (3, 13)
FREE_THREADED_MODE = PYTHON_313_PLUS and hasattr(sys, '_is_gil_enabled') and not sys._is_gil_enabled()

class Python313Optimizer:
    """Python 3.13 specific optimizations"""
    
    def __init__(self):
        self.free_threaded = FREE_THREADED_MODE
        self.jit_available = self._check_jit_availability()
        
    def _check_jit_availability(self) -> bool:
        """Check if JIT compiler is available"""
        if not PYTHON_313_PLUS:
            return False
        try:
            # Check for experimental JIT
            import _jit
            return True
        except ImportError:
            return False
    
    def optimize_threading(self, max_workers: Optional[int] = None) -> concurrent.futures.ThreadPoolExecutor:
        """Create optimized thread pool for Python 3.13"""
        if self.free_threaded:
            # In free-threaded mode, we can use more threads efficiently
            if max_workers is None:
                max_workers = min(32, (os.cpu_count() or 1) * 4)
            logger.info(f"Using free-threaded mode with {max_workers} workers")
        else:
            # Traditional GIL mode
            if max_workers is None:
                max_workers = min(8, (os.cpu_count() or 1) + 4)
            logger.info(f"Using GIL mode with {max_workers} workers")
        
        return concurrent.futures.ThreadPoolExecutor(
            max_workers=max_workers,
            thread_name_prefix="pyspider_worker"
        )
    
    def enable_jit_optimization(self, func):
        """Enable JIT optimization for function if available"""
        if not self.jit_available:
            return func
        
        try:
            import _jit
            return _jit.compile(func)
        except Exception as e:
            logger.warning(f"JIT compilation failed: {e}")
            return func
    
    def get_performance_info(self) -> Dict[str, Any]:
        """Get Python 3.13 performance information"""
        info = {
            'python_version': sys.version,
            'python_313_plus': PYTHON_313_PLUS,
            'free_threaded_mode': self.free_threaded,
            'jit_available': self.jit_available,
            'cpu_count': os.cpu_count(),
        }
        
        if PYTHON_313_PLUS:
            info['gil_enabled'] = getattr(sys, '_is_gil_enabled', lambda: True)()
        
        return info

# Global optimizer instance
optimizer = Python313Optimizer()

def optimize_for_python313(func):
    """Decorator to optimize function for Python 3.13"""
    if not PYTHON_313_PLUS:
        return func
    
    # Apply JIT optimization if available
    optimized_func = optimizer.enable_jit_optimization(func)
    
    # Add performance monitoring
    def wrapper(*args, **kwargs):
        if logger.isEnabledFor(logging.DEBUG):
            import time
            start = time.perf_counter()
            result = optimized_func(*args, **kwargs)
            end = time.perf_counter()
            logger.debug(f"Function {func.__name__} took {end - start:.4f}s")
            return result
        return optimized_func(*args, **kwargs)
    
    return wrapper

def create_optimized_thread_pool(max_workers: Optional[int] = None) -> concurrent.futures.ThreadPoolExecutor:
    """Create thread pool optimized for Python 3.13"""
    return optimizer.optimize_threading(max_workers)

def is_free_threaded() -> bool:
    """Check if running in free-threaded mode"""
    return FREE_THREADED_MODE

def get_optimal_worker_count() -> int:
    """Get optimal worker count for current Python version"""
    if FREE_THREADED_MODE:
        return min(32, (os.cpu_count() or 1) * 4)
    else:
        return min(8, (os.cpu_count() or 1) + 4)

# Enhanced error handling with Python 3.13 improvements
class EnhancedErrorHandler:
    """Enhanced error handling using Python 3.13 features"""
    
    @staticmethod
    def format_exception_with_context(exc: Exception) -> str:
        """Format exception with enhanced context for Python 3.13"""
        import traceback
        
        if PYTHON_313_PLUS:
            # Use enhanced traceback formatting in Python 3.13
            return ''.join(traceback.format_exception(type(exc), exc, exc.__traceback__))
        else:
            # Fallback for older versions
            return traceback.format_exc()
    
    @staticmethod
    def log_enhanced_error(logger: logging.Logger, exc: Exception, context: str = ""):
        """Log error with enhanced information"""
        error_msg = EnhancedErrorHandler.format_exception_with_context(exc)
        if context:
            logger.error(f"{context}: {error_msg}")
        else:
            logger.error(error_msg)

# Type system improvements for Python 3.13
if PYTHON_313_PLUS:
    from typing import TypeVar, Generic, Protocol
    
    T = TypeVar('T')
    
    class Processable(Protocol):
        """Protocol for processable items"""
        def process(self) -> Any: ...
    
    class OptimizedProcessor(Generic[T]):
        """Generic processor optimized for Python 3.13"""
        
        def __init__(self, max_workers: Optional[int] = None):
            self.executor = create_optimized_thread_pool(max_workers)
        
        def process_items(self, items: List[T]) -> List[Any]:
            """Process items using optimized threading"""
            if not items:
                return []
            
            futures = []
            for item in items:
                if hasattr(item, 'process'):
                    future = self.executor.submit(item.process)
                    futures.append(future)
            
            results = []
            for future in concurrent.futures.as_completed(futures):
                try:
                    result = future.result()
                    results.append(result)
                except Exception as e:
                    EnhancedErrorHandler.log_enhanced_error(
                        logger, e, "Error processing item"
                    )
            
            return results
        
        def __enter__(self):
            return self
        
        def __exit__(self, exc_type, exc_val, exc_tb):
            self.executor.shutdown(wait=True)

# Memory optimization for Python 3.13
class MemoryOptimizer:
    """Memory optimization utilities for Python 3.13"""
    
    @staticmethod
    def optimize_dict_memory(data: Dict[str, Any]) -> Dict[str, Any]:
        """Optimize dictionary memory usage"""
        if not PYTHON_313_PLUS:
            return data
        
        # Python 3.13 has improved dict memory efficiency
        # Use compact representation when possible
        return {k: v for k, v in data.items() if v is not None}
    
    @staticmethod
    def get_memory_usage() -> Dict[str, int]:
        """Get current memory usage information"""
        import psutil
        process = psutil.Process()
        memory_info = process.memory_info()
        
        return {
            'rss': memory_info.rss,  # Resident Set Size
            'vms': memory_info.vms,  # Virtual Memory Size
            'percent': process.memory_percent(),
        }

# Export main components
__all__ = [
    'Python313Optimizer',
    'optimizer',
    'optimize_for_python313',
    'create_optimized_thread_pool',
    'is_free_threaded',
    'get_optimal_worker_count',
    'EnhancedErrorHandler',
    'MemoryOptimizer',
    'FREE_THREADED_MODE',
    'PYTHON_313_PLUS',
]

if PYTHON_313_PLUS:
    __all__.extend(['OptimizedProcessor', 'Processable'])
