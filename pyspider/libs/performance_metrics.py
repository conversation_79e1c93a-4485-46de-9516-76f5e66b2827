#!/usr/bin/env python
# -*- encoding: utf-8 -*-
# vim: set et sw=4 ts=4 sts=4 ff=unix fenc=utf8:
# Author: PySpider Team
# Created on 2023-05-18 12:00:00

import time
import logging
import threading
import functools
import traceback
import gc
import os
import json
from typing import Dict, Any, List, Optional, Union, Callable
from collections import defaultdict

try:
    import psutil
    has_psutil = True
except ImportError:
    has_psutil = False

try:
    import tracemalloc
    has_tracemalloc = True
except ImportError:
    has_tracemalloc = False

from pyspider.libs.prometheus_metrics import prometheus_metrics

logger = logging.getLogger('performance_metrics')

class PerformanceMetrics:
    """
    Performance metrics collection for PySpider
    """

    def __init__(self,
                 enable_tracemalloc: bool = False,
                 tracemalloc_snapshot_interval: int = 300,
                 gc_stats_interval: int = 60,
                 process_stats_interval: int = 10,
                 auto_collect: bool = True):
        """
        Initialize PerformanceMetrics

        Args:
            enable_tracemalloc: Whether to enable tracemalloc
            tracemalloc_snapshot_interval: Interval between tracemalloc snapshots in seconds
            gc_stats_interval: Interval between garbage collection stats collection in seconds
            process_stats_interval: Interval between process stats collection in seconds
            auto_collect: Whether to automatically collect metrics
        """
        self.enable_tracemalloc = enable_tracemalloc and has_tracemalloc
        self.tracemalloc_snapshot_interval = tracemalloc_snapshot_interval
        self.gc_stats_interval = gc_stats_interval
        self.process_stats_interval = process_stats_interval
        self.auto_collect = auto_collect

        # Metrics
        self.function_stats = defaultdict(lambda: {
            'calls': 0,
            'total_time': 0,
            'min_time': float('inf'),
            'max_time': 0,
            'avg_time': 0,
            'errors': 0
        })

        self.request_stats = defaultdict(lambda: {
            'count': 0,
            'total_time': 0,
            'min_time': float('inf'),
            'max_time': 0,
            'avg_time': 0,
            'errors': 0,
            'status_codes': defaultdict(int)
        })

        self.task_stats = defaultdict(lambda: {
            'count': 0,
            'total_time': 0,
            'min_time': float('inf'),
            'max_time': 0,
            'avg_time': 0,
            'errors': 0,
            'status': defaultdict(int)
        })

        self.memory_snapshots = []
        self.gc_stats = []
        self.process_stats = []

        # Locks
        self._function_stats_lock = threading.RLock()
        self._request_stats_lock = threading.RLock()
        self._task_stats_lock = threading.RLock()
        self._memory_snapshots_lock = threading.RLock()
        self._gc_stats_lock = threading.RLock()
        self._process_stats_lock = threading.RLock()

        # Collection threads
        self._tracemalloc_thread = None
        self._gc_stats_thread = None
        self._process_stats_thread = None
        self._stop_event = threading.Event()

        # Initialize
        self._init()

        logger.info(f"Performance metrics initialized (tracemalloc: {self.enable_tracemalloc}, auto_collect: {self.auto_collect})")

    def _init(self):
        """Initialize metrics collection"""
        # Initialize tracemalloc
        if self.enable_tracemalloc:
            try:
                tracemalloc.start()
                logger.info("Tracemalloc started")
            except Exception as e:
                logger.error(f"Error starting tracemalloc: {e}")
                self.enable_tracemalloc = False

        # Start collection threads
        if self.auto_collect:
            self.start_collection()

    def start_collection(self):
        """Start metrics collection"""
        self._stop_event.clear()

        # Start tracemalloc thread
        if self.enable_tracemalloc and not self._tracemalloc_thread:
            self._tracemalloc_thread = threading.Thread(target=self._tracemalloc_loop, daemon=True)
            self._tracemalloc_thread.start()
            logger.info(f"Started tracemalloc collection thread (interval: {self.tracemalloc_snapshot_interval}s)")

        # Start GC stats thread
        if not self._gc_stats_thread:
            self._gc_stats_thread = threading.Thread(target=self._gc_stats_loop, daemon=True)
            self._gc_stats_thread.start()
            logger.info(f"Started GC stats collection thread (interval: {self.gc_stats_interval}s)")

        # Start process stats thread
        if has_psutil and not self._process_stats_thread:
            self._process_stats_thread = threading.Thread(target=self._process_stats_loop, daemon=True)
            self._process_stats_thread.start()
            logger.info(f"Started process stats collection thread (interval: {self.process_stats_interval}s)")

    def stop_collection(self):
        """Stop metrics collection"""
        self._stop_event.set()

        # Stop tracemalloc thread
        if self._tracemalloc_thread:
            self._tracemalloc_thread.join(timeout=5)
            self._tracemalloc_thread = None

        # Stop GC stats thread
        if self._gc_stats_thread:
            self._gc_stats_thread.join(timeout=5)
            self._gc_stats_thread = None

        # Stop process stats thread
        if self._process_stats_thread:
            self._process_stats_thread.join(timeout=5)
            self._process_stats_thread = None

        logger.info("Stopped metrics collection")

    def _tracemalloc_loop(self):
        """Tracemalloc collection loop"""
        while not self._stop_event.is_set():
            try:
                self.take_tracemalloc_snapshot()
            except Exception as e:
                logger.error(f"Error taking tracemalloc snapshot: {e}")

            # Wait for next collection
            self._stop_event.wait(self.tracemalloc_snapshot_interval)

    def _gc_stats_loop(self):
        """GC stats collection loop"""
        while not self._stop_event.is_set():
            try:
                self.collect_gc_stats()
            except Exception as e:
                logger.error(f"Error collecting GC stats: {e}")

            # Wait for next collection
            self._stop_event.wait(self.gc_stats_interval)

    def _process_stats_loop(self):
        """Process stats collection loop"""
        while not self._stop_event.is_set():
            try:
                self.collect_process_stats()
            except Exception as e:
                logger.error(f"Error collecting process stats: {e}")

            # Wait for next collection
            self._stop_event.wait(self.process_stats_interval)

    def take_tracemalloc_snapshot(self) -> Dict[str, Any]:
        """
        Take a tracemalloc snapshot

        Returns:
            Snapshot information
        """
        if not self.enable_tracemalloc:
            return {'error': 'Tracemalloc is not enabled'}

        try:
            # Take snapshot
            snapshot = tracemalloc.take_snapshot()

            # Get top statistics
            top_stats = snapshot.statistics('lineno')

            # Process statistics
            stats = []
            for stat in top_stats[:50]:  # Limit to top 50
                frame = stat.traceback[0]
                stats.append({
                    'file': frame.filename,
                    'line': frame.lineno,
                    'size': stat.size,
                    'count': stat.count
                })

            # Create snapshot info
            snapshot_info = {
                'timestamp': time.time(),
                'stats': stats,
                'total_size': sum(stat.size for stat in top_stats),
                'total_count': sum(stat.count for stat in top_stats)
            }

            # Add to snapshots
            with self._memory_snapshots_lock:
                self.memory_snapshots.append(snapshot_info)

                # Limit number of snapshots
                if len(self.memory_snapshots) > 20:
                    self.memory_snapshots.pop(0)

            # Update Prometheus metrics
            prometheus_metrics.gauge('memory_tracemalloc_total_size', 'Total memory size tracked by tracemalloc').set(snapshot_info['total_size'])
            prometheus_metrics.gauge('memory_tracemalloc_total_count', 'Total object count tracked by tracemalloc').set(snapshot_info['total_count'])

            return snapshot_info
        except Exception as e:
            logger.error(f"Error taking tracemalloc snapshot: {e}")
            return {'error': str(e)}

    def collect_gc_stats(self) -> Dict[str, Any]:
        """
        Collect garbage collection stats

        Returns:
            GC stats
        """
        try:
            # Collect garbage
            gc.collect()

            # Get GC stats
            gc_counts = gc.get_count()
            gc_objects = len(gc.get_objects())
            gc_garbage = len(gc.garbage)

            # Create GC stats
            gc_stats = {
                'timestamp': time.time(),
                'counts': gc_counts,
                'objects': gc_objects,
                'garbage': gc_garbage
            }

            # Add to GC stats
            with self._gc_stats_lock:
                self.gc_stats.append(gc_stats)

                # Limit number of GC stats
                if len(self.gc_stats) > 100:
                    self.gc_stats.pop(0)

            # Update Prometheus metrics
            prometheus_metrics.gauge('gc_objects', 'Number of objects tracked by garbage collector').set(gc_objects)
            prometheus_metrics.gauge('gc_garbage', 'Number of uncollectable garbage objects').set(gc_garbage)

            return gc_stats
        except Exception as e:
            logger.error(f"Error collecting GC stats: {e}")
            return {'error': str(e)}

    def collect_process_stats(self) -> Dict[str, Any]:
        """
        Collect process stats

        Returns:
            Process stats
        """
        if not has_psutil:
            return {'error': 'psutil is not installed'}

        try:
            # Get process
            process = psutil.Process()

            # Get process stats
            cpu_percent = process.cpu_percent(interval=0.1)
            memory_info = process.memory_info()
            memory_percent = process.memory_percent()
            num_threads = process.num_threads()
            num_fds = process.num_fds() if hasattr(process, 'num_fds') else 0
            connections = len(process.connections())

            # Create process stats
            process_stats = {
                'timestamp': time.time(),
                'cpu_percent': cpu_percent,
                'memory_rss': memory_info.rss,
                'memory_vms': memory_info.vms,
                'memory_percent': memory_percent,
                'num_threads': num_threads,
                'num_fds': num_fds,
                'connections': connections
            }

            # Add to process stats
            with self._process_stats_lock:
                self.process_stats.append(process_stats)

                # Limit number of process stats
                if len(self.process_stats) > 1000:
                    self.process_stats.pop(0)

            # Update Prometheus metrics
            prometheus_metrics.gauge('process_cpu_percent', 'Process CPU usage percent').set(cpu_percent)
            prometheus_metrics.gauge('process_memory_rss', 'Process memory RSS').set(memory_info.rss)
            prometheus_metrics.gauge('process_memory_vms', 'Process memory VMS').set(memory_info.vms)
            prometheus_metrics.gauge('process_memory_percent', 'Process memory usage percent').set(memory_percent)
            prometheus_metrics.gauge('process_threads', 'Number of threads in the process').set(num_threads)
            prometheus_metrics.gauge('process_fds', 'Number of file descriptors used by the process').set(num_fds)
            prometheus_metrics.gauge('process_connections', 'Number of network connections by the process').set(connections)

            return process_stats
        except Exception as e:
            logger.error(f"Error collecting process stats: {e}")
            return {'error': str(e)}

    def track_function(self, func: Callable) -> Callable:
        """
        Decorator to track function performance

        Args:
            func: Function to track

        Returns:
            Wrapped function
        """
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()

            try:
                result = func(*args, **kwargs)
                success = True
            except Exception as e:
                success = False
                raise
            finally:
                end_time = time.time()
                duration = end_time - start_time

                # Update function stats
                with self._function_stats_lock:
                    stats = self.function_stats[func.__name__]
                    stats['calls'] += 1
                    stats['total_time'] += duration
                    stats['min_time'] = min(stats['min_time'], duration)
                    stats['max_time'] = max(stats['max_time'], duration)
                    stats['avg_time'] = stats['total_time'] / stats['calls']

                    if not success:
                        stats['errors'] += 1

                # Update Prometheus metrics
                prometheus_metrics.counter('function_calls_total', 'Total number of function calls', ['function']).labels(function=func.__name__).inc()

                if not success:
                    prometheus_metrics.counter('function_errors_total', 'Total number of function errors', ['function']).labels(function=func.__name__).inc()

                prometheus_metrics.histogram('function_duration_seconds', 'Function duration in seconds', ['function']).labels(function=func.__name__).observe(duration)

            return result

        return wrapper

    def track_request(self, url: str, status_code: int, duration: float, error: Optional[str] = None):
        """
        Track request performance

        Args:
            url: Request URL
            status_code: Response status code
            duration: Request duration in seconds
            error: Error message if any
        """
        # Extract domain from URL
        try:
            from urllib.parse import urlparse
            domain = urlparse(url).netloc
        except:
            domain = 'unknown'

        # Update request stats
        with self._request_stats_lock:
            stats = self.request_stats[domain]
            stats['count'] += 1
            stats['total_time'] += duration
            stats['min_time'] = min(stats['min_time'], duration)
            stats['max_time'] = max(stats['max_time'], duration)
            stats['avg_time'] = stats['total_time'] / stats['count']
            stats['status_codes'][str(status_code)] += 1

            if error:
                stats['errors'] += 1

        # Update Prometheus metrics
        prometheus_metrics.counter('request_total', 'Total number of requests', ['domain']).labels(domain=domain).inc()

        if error:
            prometheus_metrics.counter('request_errors_total', 'Total number of request errors', ['domain']).labels(domain=domain).inc()

        prometheus_metrics.counter('request_status_total', 'Total number of requests by status code', ['domain', 'status_code']).labels(domain=domain, status_code=str(status_code)).inc()

        prometheus_metrics.histogram('request_duration_seconds', 'Request duration in seconds', ['domain']).labels(domain=domain).observe(duration)

    def track_task(self, project: str, status: str, duration: float, error: Optional[str] = None):
        """
        Track task performance

        Args:
            project: Project name
            status: Task status
            duration: Task duration in seconds
            error: Error message if any
        """
        # Update task stats
        with self._task_stats_lock:
            stats = self.task_stats[project]
            stats['count'] += 1
            stats['total_time'] += duration
            stats['min_time'] = min(stats['min_time'], duration)
            stats['max_time'] = max(stats['max_time'], duration)
            stats['avg_time'] = stats['total_time'] / stats['count']
            stats['status'][status] += 1

            if error:
                stats['errors'] += 1

        # Update Prometheus metrics
        prometheus_metrics.counter('task_total', 'Total number of tasks', ['project']).labels(project=project).inc()

        if error:
            prometheus_metrics.counter('task_errors_total', 'Total number of task errors', ['project']).labels(project=project).inc()

        prometheus_metrics.counter('task_status_total', 'Total number of tasks by status', ['project', 'status']).labels(project=project, status=status).inc()

        prometheus_metrics.histogram('task_duration_seconds', 'Task duration in seconds', ['project']).labels(project=project).observe(duration)

    def get_function_stats(self) -> Dict[str, Dict[str, Any]]:
        """
        Get function stats

        Returns:
            Function stats
        """
        with self._function_stats_lock:
            return dict(self.function_stats)

    def get_request_stats(self) -> Dict[str, Dict[str, Any]]:
        """
        Get request stats

        Returns:
            Request stats
        """
        with self._request_stats_lock:
            return dict(self.request_stats)

    def get_task_stats(self) -> Dict[str, Dict[str, Any]]:
        """
        Get task stats

        Returns:
            Task stats
        """
        with self._task_stats_lock:
            return dict(self.task_stats)

    def get_memory_snapshots(self) -> List[Dict[str, Any]]:
        """
        Get memory snapshots

        Returns:
            Memory snapshots
        """
        with self._memory_snapshots_lock:
            return list(self.memory_snapshots)

    def get_gc_stats(self) -> List[Dict[str, Any]]:
        """
        Get GC stats

        Returns:
            GC stats
        """
        with self._gc_stats_lock:
            return list(self.gc_stats)

    def get_process_stats(self) -> List[Dict[str, Any]]:
        """
        Get process stats

        Returns:
            Process stats
        """
        with self._process_stats_lock:
            return list(self.process_stats)

    def get_all_stats(self) -> Dict[str, Any]:
        """
        Get all stats

        Returns:
            All stats
        """
        return {
            'function_stats': self.get_function_stats(),
            'request_stats': self.get_request_stats(),
            'task_stats': self.get_task_stats(),
            'memory_snapshots': self.get_memory_snapshots()[-5:],  # Last 5 snapshots
            'gc_stats': self.get_gc_stats()[-10:],  # Last 10 GC stats
            'process_stats': self.get_process_stats()[-60:],  # Last 60 process stats
            'timestamp': time.time()
        }

    def save_stats(self, filename: str) -> bool:
        """
        Save stats to file

        Args:
            filename: Filename to save stats to

        Returns:
            True if successful, False otherwise
        """
        try:
            stats = self.get_all_stats()

            with open(filename, 'w') as f:
                json.dump(stats, f, indent=2)

            logger.info(f"Saved stats to {filename}")
            return True
        except Exception as e:
            logger.error(f"Error saving stats to {filename}: {e}")
            return False


# Global instance
performance_metrics = PerformanceMetrics()
