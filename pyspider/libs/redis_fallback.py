#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
Redis必須化とXMLRPCフォールバック機能
Redis接続失敗時に自動的にXMLRPCモードに切り替える
"""

import time
import redis
import logging
import threading
from typing import Optional, Dict, Any, Callable
from functools import wraps

logger = logging.getLogger(__name__)

class RedisConnectionManager:
    """Redis接続管理とフォールバック機能"""
    
    def __init__(self, redis_url: str = "redis://localhost:6379/0", 
                 check_interval: int = 30, auto_fallback: bool = True):
        self.redis_url = redis_url
        self.check_interval = check_interval
        self.auto_fallback = auto_fallback
        self.redis_client: Optional[redis.Redis] = None
        self.is_redis_available = False
        self.fallback_mode = False
        self.last_check_time = 0
        self.connection_attempts = 0
        self.max_connection_attempts = 3
        self._lock = threading.Lock()
        
        # 初期接続試行
        self._try_redis_connection()
        
        # バックグラウンド監視開始
        if self.auto_fallback:
            self._start_monitoring()
    
    def _try_redis_connection(self) -> bool:
        """Redis接続を試行"""
        try:
            # Redis接続を作成
            self.redis_client = redis.from_url(
                self.redis_url,
                socket_connect_timeout=5,
                socket_timeout=5,
                retry_on_timeout=True,
                health_check_interval=30
            )
            
            # 接続テスト
            self.redis_client.ping()
            
            with self._lock:
                self.is_redis_available = True
                self.fallback_mode = False
                self.connection_attempts = 0
            
            logger.info(f"✅ Redis接続成功: {self.redis_url}")
            return True
            
        except Exception as e:
            with self._lock:
                self.is_redis_available = False
                self.connection_attempts += 1
                
                if self.auto_fallback and self.connection_attempts >= self.max_connection_attempts:
                    self.fallback_mode = True
                    logger.warning(f"⚠️ Redis接続失敗、XMLRPCモードに切り替え: {e}")
                else:
                    logger.error(f"❌ Redis接続失敗 ({self.connection_attempts}/{self.max_connection_attempts}): {e}")
            
            return False
    
    def _start_monitoring(self):
        """バックグラウンド監視を開始"""
        def monitor_loop():
            while True:
                try:
                    time.sleep(self.check_interval)
                    current_time = time.time()
                    
                    # 定期的な接続チェック
                    if current_time - self.last_check_time >= self.check_interval:
                        self.last_check_time = current_time
                        
                        if not self.is_redis_available:
                            # Redis復旧チェック
                            if self._try_redis_connection():
                                logger.info("🔄 Redis接続が復旧しました")
                        else:
                            # Redis生存確認
                            try:
                                self.redis_client.ping()
                            except Exception as e:
                                logger.warning(f"⚠️ Redis接続が切断されました: {e}")
                                self._try_redis_connection()
                
                except Exception as e:
                    logger.error(f"Redis監視エラー: {e}")
        
        monitor_thread = threading.Thread(target=monitor_loop, daemon=True)
        monitor_thread.start()
        logger.info("🔍 Redis接続監視を開始しました")
    
    def get_redis_client(self) -> Optional[redis.Redis]:
        """Redis クライアントを取得"""
        if self.is_redis_available and self.redis_client:
            return self.redis_client
        return None
    
    def is_fallback_mode(self) -> bool:
        """フォールバックモードかどうか"""
        return self.fallback_mode
    
    def force_fallback(self):
        """強制的にフォールバックモードに切り替え"""
        with self._lock:
            self.fallback_mode = True
            self.is_redis_available = False
        logger.info("🔄 強制的にXMLRPCフォールバックモードに切り替えました")
    
    def get_status(self) -> Dict[str, Any]:
        """接続状態を取得"""
        return {
            "redis_url": self.redis_url,
            "is_redis_available": self.is_redis_available,
            "fallback_mode": self.fallback_mode,
            "connection_attempts": self.connection_attempts,
            "last_check_time": self.last_check_time
        }


class MessageQueueFallback:
    """メッセージキューのフォールバック機能"""
    
    def __init__(self, redis_manager: RedisConnectionManager):
        self.redis_manager = redis_manager
        self.xmlrpc_queue = {}  # XMLRPCモード用のインメモリキュー
        self._lock = threading.Lock()
    
    def put(self, queue_name: str, message: Any) -> bool:
        """メッセージをキューに追加"""
        redis_client = self.redis_manager.get_redis_client()
        
        if redis_client and not self.redis_manager.is_fallback_mode():
            try:
                # Redisモード
                redis_client.lpush(queue_name, message)
                logger.debug(f"📤 Redis キューに追加: {queue_name}")
                return True
            except Exception as e:
                logger.warning(f"⚠️ Redis キュー追加失敗、フォールバックモードに切り替え: {e}")
                self.redis_manager.force_fallback()
        
        # XMLRPCフォールバックモード
        with self._lock:
            if queue_name not in self.xmlrpc_queue:
                self.xmlrpc_queue[queue_name] = []
            self.xmlrpc_queue[queue_name].append(message)
            logger.debug(f"📤 XMLRPC キューに追加: {queue_name}")
        
        return True
    
    def get(self, queue_name: str, timeout: int = 1) -> Optional[Any]:
        """キューからメッセージを取得"""
        redis_client = self.redis_manager.get_redis_client()
        
        if redis_client and not self.redis_manager.is_fallback_mode():
            try:
                # Redisモード
                result = redis_client.brpop(queue_name, timeout=timeout)
                if result:
                    logger.debug(f"📥 Redis キューから取得: {queue_name}")
                    # バイト文字列の場合は文字列に変換
                    message = result[1]
                    if isinstance(message, bytes):
                        try:
                            return message.decode('utf-8')
                        except UnicodeDecodeError:
                            return message
                    return message
                return None
            except Exception as e:
                logger.warning(f"⚠️ Redis キュー取得失敗、フォールバックモードに切り替え: {e}")
                self.redis_manager.force_fallback()
        
        # XMLRPCフォールバックモード
        with self._lock:
            if queue_name in self.xmlrpc_queue and self.xmlrpc_queue[queue_name]:
                message = self.xmlrpc_queue[queue_name].pop(0)
                logger.debug(f"📥 XMLRPC キューから取得: {queue_name}")
                return message
        
        return None
    
    def size(self, queue_name: str) -> int:
        """キューのサイズを取得"""
        redis_client = self.redis_manager.get_redis_client()
        
        if redis_client and not self.redis_manager.is_fallback_mode():
            try:
                # Redisモード
                return redis_client.llen(queue_name)
            except Exception as e:
                logger.warning(f"⚠️ Redis キューサイズ取得失敗: {e}")
                self.redis_manager.force_fallback()
        
        # XMLRPCフォールバックモード
        with self._lock:
            return len(self.xmlrpc_queue.get(queue_name, []))


def redis_fallback_decorator(fallback_func: Callable = None):
    """Redis操作のフォールバックデコレータ"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            try:
                # Redis操作を試行
                return func(*args, **kwargs)
            except Exception as e:
                logger.warning(f"⚠️ Redis操作失敗: {func.__name__} - {e}")
                
                # フォールバック関数が指定されている場合は実行
                if fallback_func:
                    logger.info(f"🔄 フォールバック実行: {fallback_func.__name__}")
                    return fallback_func(*args, **kwargs)
                
                # フォールバック関数がない場合は例外を再発生
                raise
        
        return wrapper
    return decorator


# グローバルインスタンス
_global_redis_manager: Optional[RedisConnectionManager] = None
_global_message_queue: Optional[MessageQueueFallback] = None

def initialize_redis_fallback(redis_url: str = "redis://localhost:6379/0", 
                             check_interval: int = 30, 
                             auto_fallback: bool = True) -> RedisConnectionManager:
    """Redis フォールバック機能を初期化"""
    global _global_redis_manager, _global_message_queue
    
    _global_redis_manager = RedisConnectionManager(
        redis_url=redis_url,
        check_interval=check_interval,
        auto_fallback=auto_fallback
    )
    
    _global_message_queue = MessageQueueFallback(_global_redis_manager)
    
    logger.info("✅ Redis フォールバック機能を初期化しました")
    return _global_redis_manager

def get_redis_manager() -> Optional[RedisConnectionManager]:
    """グローバル Redis マネージャーを取得"""
    return _global_redis_manager

def get_message_queue() -> Optional[MessageQueueFallback]:
    """グローバル メッセージキューを取得"""
    return _global_message_queue

def is_redis_available() -> bool:
    """Redis が利用可能かチェック"""
    if _global_redis_manager:
        return _global_redis_manager.is_redis_available
    return False

def is_fallback_mode() -> bool:
    """フォールバックモードかチェック"""
    if _global_redis_manager:
        return _global_redis_manager.is_fallback_mode()
    return True
