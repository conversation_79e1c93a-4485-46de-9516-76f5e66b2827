#!/usr/bin/env python
# -*- coding: utf-8 -*-

import re
import json
import logging
import datetime
from typing import Dict, List, Any, Callable, Optional, Union, Set, TypeVar, Tuple

logger = logging.getLogger('data_quality')

T = TypeVar('T')

class DataQualityChecker:
    """
    データ品質チェックシステム

    クローリングしたデータの品質を自動的にチェックする機能を提供します。
    - データの完全性チェック：必須フィールドの存在確認
    - データの正確性チェック：データ型、範囲、形式の確認
    - データの一貫性チェック：関連フィールド間の整合性確認
    - データの妥当性チェック：ビジネスルールに基づく妥当性確認
    """

    def __init__(self):
        """初期化"""
        self.validators = {}
        self._register_default_validators()

    def _register_default_validators(self):
        """デフォルトのバリデータを登録"""
        # 基本的なバリデータ
        self.register('required', self.required)
        self.register('not_empty', self.not_empty)
        self.register('type', self.type_check)
        self.register('min_length', self.min_length)
        self.register('max_length', self.max_length)
        self.register('length_range', self.length_range)
        self.register('pattern', self.pattern)
        self.register('enum', self.enum)

        # 数値バリデータ
        self.register('min', self.min_value)
        self.register('max', self.max_value)
        self.register('range', self.range_value)
        self.register('positive', self.positive)
        self.register('negative', self.negative)
        self.register('integer', self.integer)

        # 文字列バリデータ
        self.register('email', self.email)
        self.register('url', self.url)
        self.register('date', self.date)
        self.register('time', self.time)
        self.register('datetime', self.datetime)
        self.register('alpha', self.alpha)
        self.register('alphanumeric', self.alphanumeric)
        self.register('numeric', self.numeric)

        # コレクションバリデータ
        self.register('min_items', self.min_items)
        self.register('max_items', self.max_items)
        self.register('unique_items', self.unique_items)
        self.register('contains', self.contains)

        # オブジェクトバリデータ
        self.register('required_fields', self.required_fields)
        self.register('forbidden_fields', self.forbidden_fields)
        self.register('dependencies', self.dependencies)

        # 複合バリデータ
        self.register('and', self.and_validator)
        self.register('or', self.or_validator)
        self.register('not', self.not_validator)
        self.register('if_then_else', self.if_then_else)

        # カスタムバリデータ
        self.register('custom', self.custom)

    def register(self, name: str, func: Callable) -> None:
        """
        バリデータを登録

        Args:
            name: バリデータの名前
            func: バリデータ関数
        """
        self.validators[name] = func

    def get(self, name: str) -> Optional[Callable]:
        """
        バリデータを取得

        Args:
            name: バリデータの名前

        Returns:
            バリデータ関数、存在しない場合はNone
        """
        return self.validators.get(name)

    def validate(self, data: Any, validator: Union[str, Dict, List], *args, **kwargs) -> Tuple[bool, List[str]]:
        """
        データを検証

        Args:
            data: 検証するデータ
            validator: バリデータの名前または設定
            *args: バリデータに渡す位置引数
            **kwargs: バリデータに渡すキーワード引数

        Returns:
            (検証結果, エラーメッセージのリスト)
        """
        if isinstance(validator, str):
            # 文字列の場合はバリデータの名前として扱う
            func = self.get(validator)
            if func is None:
                logger.warning(f"Validator not found: {validator}")
                return False, [f"Validator not found: {validator}"]
            return func(data, *args, **kwargs)

        elif isinstance(validator, dict):
            # 辞書の場合は設定として扱う
            name = validator.get('name')
            if not name:
                logger.warning("Validator name not specified")
                return False, ["Validator name not specified"]

            func = self.get(name)
            if func is None:
                logger.warning(f"Validator not found: {name}")
                return False, [f"Validator not found: {name}"]

            args = validator.get('args', [])
            kwargs = validator.get('kwargs', {})
            return func(data, *args, **kwargs)

        elif isinstance(validator, list):
            # リストの場合は複数のバリデータを適用
            all_valid = True
            all_errors = []

            for v in validator:
                valid, errors = self.validate(data, v)
                if not valid:
                    all_valid = False
                    all_errors.extend(errors)

            return all_valid, all_errors

        else:
            logger.warning(f"Invalid validator type: {type(validator)}")
            return False, [f"Invalid validator type: {type(validator)}"]

    def validate_schema(self, data: Dict[str, Any], schema: Dict[str, Any]) -> Tuple[bool, Dict[str, List[str]]]:
        """
        スキーマに基づいてデータを検証

        Args:
            data: 検証するデータ
            schema: 検証スキーマ

        Returns:
            (検証結果, フィールド別エラーメッセージの辞書)
        """
        if not isinstance(data, dict):
            return False, {'_error': ['Data must be a dictionary']}

        all_valid = True
        errors = {}

        # 必須フィールドのチェック
        required_fields = schema.get('required', [])
        for field in required_fields:
            if field not in data:
                all_valid = False
                errors[field] = [f"Field '{field}' is required"]

        # 各フィールドの検証
        properties = schema.get('properties', {})
        for field, field_schema in properties.items():
            if field in data:
                field_validators = field_schema.get('validators', [])
                if field_validators:
                    valid, field_errors = self.validate(data[field], field_validators)
                    if not valid:
                        all_valid = False
                        errors[field] = field_errors

        return all_valid, errors

    # 基本的なバリデータ
    def required(self, data: Any) -> Tuple[bool, List[str]]:
        """必須チェック"""
        if data is None:
            return False, ["Value is required"]
        return True, []

    def not_empty(self, data: Any) -> Tuple[bool, List[str]]:
        """空でないことをチェック"""
        if data is None:
            return False, ["Value cannot be empty"]

        if isinstance(data, str) and not data.strip():
            return False, ["String cannot be empty"]

        if isinstance(data, (list, tuple, dict)) and not data:
            return False, ["Collection cannot be empty"]

        return True, []

    def type_check(self, data: Any, expected_type: Union[str, List[str]]) -> Tuple[bool, List[str]]:
        """型チェック"""
        if data is None:
            return True, []

        if isinstance(expected_type, str):
            expected_types = [expected_type]
        else:
            expected_types = expected_type

        for t in expected_types:
            if t == 'string' and isinstance(data, str):
                return True, []
            elif t == 'number' and isinstance(data, (int, float)):
                return True, []
            elif t == 'integer' and isinstance(data, int):
                return True, []
            elif t == 'boolean' and isinstance(data, bool):
                return True, []
            elif t == 'array' and isinstance(data, (list, tuple)):
                return True, []
            elif t == 'object' and isinstance(data, dict):
                return True, []
            elif t == 'null' and data is None:
                return True, []

        return False, [f"Expected type {' or '.join(expected_types)}, got {type(data).__name__}"]

    def min_length(self, data: Union[str, List, Dict], min_len: int) -> Tuple[bool, List[str]]:
        """最小長さチェック"""
        if data is None:
            return True, []

        if not hasattr(data, '__len__'):
            return False, ["Value has no length"]

        if len(data) < min_len:
            return False, [f"Length must be at least {min_len}"]

        return True, []

    def max_length(self, data: Union[str, List, Dict], max_len: int) -> Tuple[bool, List[str]]:
        """最大長さチェック"""
        if data is None:
            return True, []

        if not hasattr(data, '__len__'):
            return False, ["Value has no length"]

        if len(data) > max_len:
            return False, [f"Length must be at most {max_len}"]

        return True, []

    def length_range(self, data: Union[str, List, Dict], min_len: int, max_len: int) -> Tuple[bool, List[str]]:
        """長さ範囲チェック"""
        if data is None:
            return True, []

        if not hasattr(data, '__len__'):
            return False, ["Value has no length"]

        if len(data) < min_len or len(data) > max_len:
            return False, [f"Length must be between {min_len} and {max_len}"]

        return True, []

    def pattern(self, data: str, pattern: str) -> Tuple[bool, List[str]]:
        """パターンチェック"""
        if data is None:
            return True, []

        if not isinstance(data, str):
            return False, ["Value must be a string"]

        if not re.match(pattern, data):
            return False, [f"Value does not match pattern {pattern}"]

        return True, []

    def enum(self, data: Any, values: List[Any]) -> Tuple[bool, List[str]]:
        """列挙値チェック"""
        if data is None:
            return True, []

        if data not in values:
            return False, [f"Value must be one of {values}"]

        return True, []

    # 数値バリデータ
    def min_value(self, data: Union[int, float], min_val: Union[int, float]) -> Tuple[bool, List[str]]:
        """最小値チェック"""
        if data is None:
            return True, []

        if not isinstance(data, (int, float)):
            return False, ["Value must be a number"]

        if data < min_val:
            return False, [f"Value must be at least {min_val}"]

        return True, []

    def max_value(self, data: Union[int, float], max_val: Union[int, float]) -> Tuple[bool, List[str]]:
        """最大値チェック"""
        if data is None:
            return True, []

        if not isinstance(data, (int, float)):
            return False, ["Value must be a number"]

        if data > max_val:
            return False, [f"Value must be at most {max_val}"]

        return True, []

    def range_value(self, data: Union[int, float], min_val: Union[int, float], max_val: Union[int, float]) -> Tuple[bool, List[str]]:
        """値範囲チェック"""
        if data is None:
            return True, []

        if not isinstance(data, (int, float)):
            return False, ["Value must be a number"]

        if data < min_val or data > max_val:
            return False, [f"Value must be between {min_val} and {max_val}"]

        return True, []

    def positive(self, data: Union[int, float]) -> Tuple[bool, List[str]]:
        """正の値チェック"""
        if data is None:
            return True, []

        if not isinstance(data, (int, float)):
            return False, ["Value must be a number"]

        if data <= 0:
            return False, ["Value must be positive"]

        return True, []

    def negative(self, data: Union[int, float]) -> Tuple[bool, List[str]]:
        """負の値チェック"""
        if data is None:
            return True, []

        if not isinstance(data, (int, float)):
            return False, ["Value must be a number"]

        if data >= 0:
            return False, ["Value must be negative"]

        return True, []

    def integer(self, data: Any) -> Tuple[bool, List[str]]:
        """整数チェック"""
        if data is None:
            return True, []

        if not isinstance(data, int) or isinstance(data, bool):
            return False, ["Value must be an integer"]

        return True, []

    # 文字列バリデータ
    def email(self, data: str) -> Tuple[bool, List[str]]:
        """メールアドレスチェック"""
        if data is None:
            return True, []

        if not isinstance(data, str):
            return False, ["Value must be a string"]

        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        if not re.match(pattern, data):
            return False, ["Invalid email address"]

        return True, []

    def url(self, data: str) -> Tuple[bool, List[str]]:
        """URLチェック"""
        if data is None:
            return True, []

        if not isinstance(data, str):
            return False, ["Value must be a string"]

        pattern = r'^(https?|ftp)://[^\s/$.?#].[^\s]*$'
        if not re.match(pattern, data):
            return False, ["Invalid URL"]

        return True, []

    def date(self, data: str, format: str = '%Y-%m-%d') -> Tuple[bool, List[str]]:
        """日付チェック"""
        if data is None:
            return True, []

        if not isinstance(data, str):
            return False, ["Value must be a string"]

        try:
            datetime.datetime.strptime(data, format)
            return True, []
        except ValueError:
            return False, [f"Invalid date format, expected {format}"]

    def time(self, data: str, format: str = '%H:%M:%S') -> Tuple[bool, List[str]]:
        """時刻チェック"""
        if data is None:
            return True, []

        if not isinstance(data, str):
            return False, ["Value must be a string"]

        try:
            datetime.datetime.strptime(data, format)
            return True, []
        except ValueError:
            return False, [f"Invalid time format, expected {format}"]

    def datetime(self, data: str, format: str = '%Y-%m-%d %H:%M:%S') -> Tuple[bool, List[str]]:
        """日時チェック"""
        if data is None:
            return True, []

        if not isinstance(data, str):
            return False, ["Value must be a string"]

        try:
            datetime.datetime.strptime(data, format)
            return True, []
        except ValueError:
            return False, [f"Invalid datetime format, expected {format}"]

    def alpha(self, data: str) -> Tuple[bool, List[str]]:
        """アルファベットチェック"""
        if data is None:
            return True, []

        if not isinstance(data, str):
            return False, ["Value must be a string"]

        if not data.isalpha():
            return False, ["Value must contain only alphabetic characters"]

        return True, []

    def alphanumeric(self, data: str) -> Tuple[bool, List[str]]:
        """英数字チェック"""
        if data is None:
            return True, []

        if not isinstance(data, str):
            return False, ["Value must be a string"]

        if not data.isalnum():
            return False, ["Value must contain only alphanumeric characters"]

        return True, []

    def numeric(self, data: str) -> Tuple[bool, List[str]]:
        """数字チェック"""
        if data is None:
            return True, []

        if not isinstance(data, str):
            return False, ["Value must be a string"]

        if not data.isdigit():
            return False, ["Value must contain only numeric characters"]

        return True, []

    # コレクションバリデータ
    def min_items(self, data: List[Any], min_count: int) -> Tuple[bool, List[str]]:
        """最小項目数チェック"""
        if data is None:
            return True, []

        if not isinstance(data, (list, tuple)):
            return False, ["Value must be an array"]

        if len(data) < min_count:
            return False, [f"Array must contain at least {min_count} items"]

        return True, []

    def max_items(self, data: List[Any], max_count: int) -> Tuple[bool, List[str]]:
        """最大項目数チェック"""
        if data is None:
            return True, []

        if not isinstance(data, (list, tuple)):
            return False, ["Value must be an array"]

        if len(data) > max_count:
            return False, [f"Array must contain at most {max_count} items"]

        return True, []

    def unique_items(self, data: List[Any]) -> Tuple[bool, List[str]]:
        """一意性チェック"""
        if data is None:
            return True, []

        if not isinstance(data, (list, tuple)):
            return False, ["Value must be an array"]

        if len(data) != len(set(data)):
            return False, ["Array must contain unique items"]

        return True, []

    def contains(self, data: List[Any], value: Any) -> Tuple[bool, List[str]]:
        """含有チェック"""
        if data is None:
            return True, []

        if not isinstance(data, (list, tuple)):
            return False, ["Value must be an array"]

        if value not in data:
            return False, [f"Array must contain {value}"]

        return True, []

    # オブジェクトバリデータ
    def required_fields(self, data: Dict[str, Any], fields: List[str]) -> Tuple[bool, List[str]]:
        """必須フィールドチェック"""
        if data is None:
            return True, []

        if not isinstance(data, dict):
            return False, ["Value must be an object"]

        missing_fields = [field for field in fields if field not in data]
        if missing_fields:
            return False, [f"Missing required fields: {', '.join(missing_fields)}"]

        return True, []

    def forbidden_fields(self, data: Dict[str, Any], fields: List[str]) -> Tuple[bool, List[str]]:
        """禁止フィールドチェック"""
        if data is None:
            return True, []

        if not isinstance(data, dict):
            return False, ["Value must be an object"]

        forbidden = [field for field in fields if field in data]
        if forbidden:
            return False, [f"Forbidden fields present: {', '.join(forbidden)}"]

        return True, []

    def dependencies(self, data: Dict[str, Any], field: str, dependencies: List[str]) -> Tuple[bool, List[str]]:
        """依存関係チェック"""
        if data is None:
            return True, []

        if not isinstance(data, dict):
            return False, ["Value must be an object"]

        if field in data:
            missing_deps = [dep for dep in dependencies if dep not in data]
            if missing_deps:
                return False, [f"Field '{field}' depends on: {', '.join(missing_deps)}"]

        return True, []

    # 複合バリデータ
    def and_validator(self, data: Any, validators: List[Dict]) -> Tuple[bool, List[str]]:
        """AND条件バリデータ"""
        all_valid = True
        all_errors = []

        for validator in validators:
            valid, errors = self.validate(data, validator)
            if not valid:
                all_valid = False
                all_errors.extend(errors)

        return all_valid, all_errors

    def or_validator(self, data: Any, validators: List[Dict]) -> Tuple[bool, List[str]]:
        """OR条件バリデータ"""
        all_errors = []

        for validator in validators:
            valid, errors = self.validate(data, validator)
            if valid:
                return True, []
            all_errors.extend(errors)

        return False, [f"None of the conditions were met: {all_errors}"]

    def not_validator(self, data: Any, validator: Dict) -> Tuple[bool, List[str]]:
        """NOT条件バリデータ"""
        valid, _ = self.validate(data, validator)
        if valid:
            return False, ["Condition should not be met"]
        return True, []

    def if_then_else(self, data: Any, if_validator: Dict, then_validator: Dict, else_validator: Optional[Dict] = None) -> Tuple[bool, List[str]]:
        """IF-THEN-ELSE条件バリデータ"""
        if_valid, _ = self.validate(data, if_validator)

        if if_valid:
            return self.validate(data, then_validator)
        elif else_validator:
            return self.validate(data, else_validator)

        return True, []

    # カスタムバリデータ
    def custom(self, data: Any, func: Callable[[Any], Tuple[bool, List[str]]]) -> Tuple[bool, List[str]]:
        """カスタムバリデータ"""
        return func(data)
