#!/usr/bin/env python
# -*- coding: utf-8 -*-

import re
import json
import html
import logging
import datetime
import hashlib
import unicodedata
from typing import Dict, List, Any, Callable, Optional, Union, Tuple, Set, TypeVar

logger = logging.getLogger('data_transformers')

T = TypeVar('T')

class DataTransformer:
    """
    データ変換フィルター

    クローリングしたデータを変換するためのフィルター機能を提供します。
    - 基本的な変換：文字列処理、数値変換など
    - データクリーニング：空白除去、HTML除去など
    - データ正規化：日付形式の統一、数値の正規化など
    - データ抽出：正規表現による抽出、JSONパースなど
    - データ結合：複数のフィールドの結合
    """

    def __init__(self):
        """初期化"""
        self.transformers = {}
        self._register_default_transformers()

    def _register_default_transformers(self):
        """デフォルトの変換関数を登録"""
        # 基本的な変換
        self.register('strip', self.strip)
        self.register('lower', self.lower)
        self.register('upper', self.upper)
        self.register('capitalize', self.capitalize)
        self.register('default', self.default)
        self.register('replace', self.replace)

        # データクリーニング
        self.register('remove_html', self.remove_html)
        self.register('remove_whitespace', self.remove_whitespace)
        self.register('normalize_whitespace', self.normalize_whitespace)
        self.register('remove_punctuation', self.remove_punctuation)
        self.register('normalize_unicode', self.normalize_unicode)

        # データ正規化
        self.register('to_int', self.to_int)
        self.register('to_float', self.to_float)
        self.register('to_bool', self.to_bool)
        self.register('to_datetime', self.to_datetime)
        self.register('format_date', self.format_date)
        self.register('normalize_date', self.normalize_date)

        # データ抽出
        self.register('extract_regex', self.extract_regex)
        self.register('extract_json', self.extract_json)
        self.register('extract_numbers', self.extract_numbers)
        self.register('extract_emails', self.extract_emails)
        self.register('extract_urls', self.extract_urls)

        # データ変換
        self.register('truncate', self.truncate)
        self.register('hash', self.hash)
        self.register('join', self.join)
        self.register('split', self.split)
        self.register('slice', self.slice)

        # コレクション操作
        self.register('filter_empty', self.filter_empty)
        self.register('unique', self.unique)
        self.register('sort', self.sort)
        self.register('reverse', self.reverse)
        self.register('limit', self.limit)

        # 辞書操作
        self.register('pick', self.pick)
        self.register('omit', self.omit)
        self.register('rename_keys', self.rename_keys)
        self.register('flatten', self.flatten)
        self.register('unflatten', self.unflatten)

        # 統計・集計
        self.register('count', self.count)
        self.register('sum', self.sum)
        self.register('average', self.average)
        self.register('min', self.min)
        self.register('max', self.max)
        self.register('word_count', self.word_count)
        self.register('character_count', self.character_count)

    def register(self, name: str, func: Callable) -> None:
        """
        変換関数を登録

        Args:
            name: 変換関数の名前
            func: 変換関数
        """
        self.transformers[name] = func

    def get(self, name: str) -> Optional[Callable]:
        """
        変換関数を取得

        Args:
            name: 変換関数の名前

        Returns:
            変換関数、存在しない場合はNone
        """
        return self.transformers.get(name)

    def transform(self, data: Any, transformer: Union[str, Dict, List], *args, **kwargs) -> Any:
        """
        データを変換

        Args:
            data: 変換するデータ
            transformer: 変換関数の名前または設定
            *args: 変換関数に渡す位置引数
            **kwargs: 変換関数に渡すキーワード引数

        Returns:
            変換後のデータ
        """
        if data is None:
            return None

        if isinstance(transformer, str):
            # 文字列の場合は変換関数の名前として扱う
            func = self.get(transformer)
            if func is None:
                logger.warning(f"Transformer not found: {transformer}")
                return data
            return func(data, *args, **kwargs)

        elif isinstance(transformer, dict):
            # 辞書の場合は設定として扱う
            name = transformer.get('name')
            if not name:
                logger.warning("Transformer name not specified")
                return data

            func = self.get(name)
            if func is None:
                logger.warning(f"Transformer not found: {name}")
                return data

            args = transformer.get('args', [])
            kwargs = transformer.get('kwargs', {})
            return func(data, *args, **kwargs)

        elif isinstance(transformer, list):
            # リストの場合は複数の変換を順番に適用
            result = data
            for t in transformer:
                result = self.transform(result, t)
            return result

        else:
            logger.warning(f"Invalid transformer type: {type(transformer)}")
            return data

    # 基本的な変換
    def strip(self, data: str, chars: Optional[str] = None) -> str:
        """文字列の前後の空白を除去"""
        if not isinstance(data, str):
            return data
        return data.strip(chars)

    def lower(self, data: str) -> str:
        """文字列を小文字に変換"""
        if not isinstance(data, str):
            return data
        return data.lower()

    def upper(self, data: str) -> str:
        """文字列を大文字に変換"""
        if not isinstance(data, str):
            return data
        return data.upper()

    def capitalize(self, data: str) -> str:
        """文字列の先頭を大文字に変換"""
        if not isinstance(data, str):
            return data
        return data.capitalize()

    def default(self, data: Any, default_value: Any) -> Any:
        """データがNoneの場合にデフォルト値を返す"""
        return default_value if data is None else data

    def replace(self, data: str, old: str, new: str) -> str:
        """文字列を置換"""
        if not isinstance(data, str):
            return data
        return data.replace(old, new)

    # データクリーニング
    def remove_html(self, data: str) -> str:
        """HTMLタグを除去"""
        if not isinstance(data, str):
            return data
        return re.sub(r'<[^>]+>', '', data)

    def remove_whitespace(self, data: str) -> str:
        """空白を除去"""
        if not isinstance(data, str):
            return data
        return re.sub(r'\s+', '', data)

    def normalize_whitespace(self, data: str) -> str:
        """空白を正規化"""
        if not isinstance(data, str):
            return data
        return re.sub(r'\s+', ' ', data).strip()

    def remove_punctuation(self, data: str) -> str:
        """句読点を除去"""
        if not isinstance(data, str):
            return data
        return re.sub(r'[^\w\s]', '', data)

    def normalize_unicode(self, data: str, form: str = 'NFKC') -> str:
        """Unicodeを正規化"""
        if not isinstance(data, str):
            return data
        return unicodedata.normalize(form, data)

    # データ正規化
    def to_int(self, data: Any, default: Optional[int] = None) -> Optional[int]:
        """整数に変換"""
        if data is None:
            return default
        try:
            if isinstance(data, str):
                # 数字以外の文字を除去
                data = re.sub(r'[^\d.-]', '', data)
            return int(float(data))
        except (ValueError, TypeError):
            return default

    def to_float(self, data: Any, default: Optional[float] = None) -> Optional[float]:
        """浮動小数点数に変換"""
        if data is None:
            return default
        try:
            if isinstance(data, str):
                # 数字以外の文字を除去
                data = re.sub(r'[^\d.-]', '', data)
            return float(data)
        except (ValueError, TypeError):
            return default

    def to_bool(self, data: Any, default: Optional[bool] = None) -> Optional[bool]:
        """真偽値に変換"""
        if data is None:
            return default
        if isinstance(data, bool):
            return data
        if isinstance(data, (int, float)):
            return bool(data)
        if isinstance(data, str):
            return data.lower() in ('true', 'yes', 'y', '1', 'on')
        return default

    def to_datetime(self, data: Any, format: Optional[str] = None, default: Optional[datetime.datetime] = None) -> Optional[datetime.datetime]:
        """日時に変換"""
        if data is None:
            return default
        try:
            if isinstance(data, datetime.datetime):
                return data
            if isinstance(data, (int, float)):
                return datetime.datetime.fromtimestamp(data)
            if isinstance(data, str):
                if format:
                    return datetime.datetime.strptime(data, format)
                # 一般的な日付形式を試す
                formats = [
                    '%Y-%m-%d %H:%M:%S',
                    '%Y-%m-%d',
                    '%Y/%m/%d %H:%M:%S',
                    '%Y/%m/%d',
                    '%d.%m.%Y %H:%M:%S',
                    '%d.%m.%Y',
                    '%d/%m/%Y %H:%M:%S',
                    '%d/%m/%Y',
                ]
                for fmt in formats:
                    try:
                        return datetime.datetime.strptime(data, fmt)
                    except ValueError:
                        continue
        except (ValueError, TypeError):
            pass
        return default

    def format_date(self, data: Any, format: str = '%Y-%m-%d', input_format: Optional[str] = None) -> Optional[str]:
        """日付を指定した形式にフォーマット"""
        dt = self.to_datetime(data, format=input_format)
        if dt is None:
            return None
        return dt.strftime(format)

    def normalize_date(self, data: Any, format: str = '%Y-%m-%d') -> Optional[str]:
        """日付を正規化"""
        return self.format_date(data, format)

    # データ抽出
    def extract_regex(self, data: str, pattern: str, group: int = 0) -> Optional[str]:
        """正規表現でデータを抽出"""
        if not isinstance(data, str):
            return None
        match = re.search(pattern, data)
        if not match:
            return None
        try:
            return match.group(group)
        except IndexError:
            return None

    def extract_json(self, data: str, path: Optional[str] = None) -> Any:
        """JSON文字列からデータを抽出"""
        if not isinstance(data, str):
            return None
        try:
            result = json.loads(data)
            if path:
                # ドット区切りのパスでデータを取得
                for key in path.split('.'):
                    if isinstance(result, dict):
                        result = result.get(key)
                    elif isinstance(result, list) and key.isdigit():
                        index = int(key)
                        if 0 <= index < len(result):
                            result = result[index]
                        else:
                            return None
                    else:
                        return None
            return result
        except json.JSONDecodeError:
            return None

    def extract_numbers(self, data: str) -> List[float]:
        """文字列から数値を抽出"""
        if not isinstance(data, str):
            return []
        return [float(x) for x in re.findall(r'-?\d+\.?\d*', data)]

    def extract_emails(self, data: str) -> List[str]:
        """文字列からメールアドレスを抽出"""
        if not isinstance(data, str):
            return []
        pattern = r'[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}'
        return re.findall(pattern, data)

    def extract_urls(self, data: str) -> List[str]:
        """文字列からURLを抽出"""
        if not isinstance(data, str):
            return []
        pattern = r'https?://[^\s<>"]+|www\.[^\s<>"]+'
        return re.findall(pattern, data)

    # データ変換
    def truncate(self, data: str, length: int, suffix: str = '...') -> str:
        """文字列を指定した長さに切り詰める"""
        if not isinstance(data, str):
            return data
        if len(data) <= length:
            return data
        return data[:length] + suffix

    def hash(self, data: Any, algorithm: str = 'md5') -> str:
        """データをハッシュ化"""
        if data is None:
            return None
        data_str = str(data)
        if algorithm == 'md5':
            return hashlib.md5(data_str.encode()).hexdigest()
        elif algorithm == 'sha1':
            return hashlib.sha1(data_str.encode()).hexdigest()
        elif algorithm == 'sha256':
            return hashlib.sha256(data_str.encode()).hexdigest()
        else:
            return hashlib.md5(data_str.encode()).hexdigest()

    def join(self, data: List[str], separator: str = ' ') -> str:
        """リストを文字列に結合"""
        if not isinstance(data, (list, tuple)):
            return str(data) if data is not None else ''
        return separator.join(str(item) for item in data if item is not None)

    def split(self, data: str, separator: str = None, maxsplit: int = -1) -> List[str]:
        """文字列を分割"""
        if not isinstance(data, str):
            return [str(data)] if data is not None else []
        return data.split(separator, maxsplit)

    def slice(self, data: List[Any], start: int = 0, end: Optional[int] = None) -> List[Any]:
        """リストをスライス"""
        if not isinstance(data, (list, tuple)):
            return []
        return list(data[start:end])

    # コレクション操作
    def filter_empty(self, data: List[Any]) -> List[Any]:
        """空の要素を除去"""
        if not isinstance(data, (list, tuple)):
            return [data] if data is not None else []
        return [item for item in data if item is not None and (not isinstance(item, str) or item.strip())]

    def unique(self, data: List[Any]) -> List[Any]:
        """重複を除去"""
        if not isinstance(data, (list, tuple)):
            return [data] if data is not None else []
        seen = set()
        result = []
        for item in data:
            if item not in seen:
                seen.add(item)
                result.append(item)
        return result

    def sort(self, data: List[Any], key: Optional[str] = None, reverse: bool = False) -> List[Any]:
        """リストをソート"""
        if not isinstance(data, (list, tuple)):
            return [data] if data is not None else []
        if key and isinstance(data[0], dict):
            return sorted(data, key=lambda x: x.get(key), reverse=reverse)
        return sorted(data, reverse=reverse)

    def reverse(self, data: List[Any]) -> List[Any]:
        """リストを逆順にする"""
        if not isinstance(data, (list, tuple)):
            return [data] if data is not None else []
        return list(reversed(data))

    def limit(self, data: List[Any], count: int) -> List[Any]:
        """リストを指定した数に制限"""
        if not isinstance(data, (list, tuple)):
            return [data] if data is not None and count > 0 else []
        return data[:count]

    # 辞書操作
    def pick(self, data: Dict[str, Any], keys: List[str]) -> Dict[str, Any]:
        """指定したキーのみを抽出"""
        if not isinstance(data, dict):
            return {}
        return {k: data[k] for k in keys if k in data}

    def omit(self, data: Dict[str, Any], keys: List[str]) -> Dict[str, Any]:
        """指定したキーを除外"""
        if not isinstance(data, dict):
            return {}
        return {k: v for k, v in data.items() if k not in keys}

    def rename_keys(self, data: Dict[str, Any], mapping: Dict[str, str]) -> Dict[str, Any]:
        """キーをリネーム"""
        if not isinstance(data, dict):
            return {}
        result = {}
        for k, v in data.items():
            new_key = mapping.get(k, k)
            result[new_key] = v
        return result

    def flatten(self, data: Dict[str, Any], separator: str = '.') -> Dict[str, Any]:
        """ネストした辞書をフラット化"""
        if not isinstance(data, dict):
            return {}

        result = {}

        def _flatten(d, parent_key=''):
            for k, v in d.items():
                key = f"{parent_key}{separator}{k}" if parent_key else k
                if isinstance(v, dict):
                    _flatten(v, key)
                else:
                    result[key] = v

        _flatten(data)
        return result

    def unflatten(self, data: Dict[str, Any], separator: str = '.') -> Dict[str, Any]:
        """フラット化された辞書をネスト化"""
        if not isinstance(data, dict):
            return {}

        result = {}
        for key, value in data.items():
            parts = key.split(separator)
            d = result
            for part in parts[:-1]:
                if part not in d:
                    d[part] = {}
                d = d[part]
            d[parts[-1]] = value

        return result

    # 統計・集計
    def count(self, data: List[Any]) -> int:
        """要素数をカウント"""
        if not isinstance(data, (list, tuple)):
            return 1 if data is not None else 0
        return len(data)

    def sum(self, data: List[Union[int, float]]) -> Union[int, float]:
        """合計を計算"""
        if not isinstance(data, (list, tuple)):
            return data if isinstance(data, (int, float)) else 0
        return sum(item for item in data if isinstance(item, (int, float)))

    def average(self, data: List[Union[int, float]]) -> float:
        """平均を計算"""
        if not isinstance(data, (list, tuple)):
            return float(data) if isinstance(data, (int, float)) else 0.0
        nums = [item for item in data if isinstance(item, (int, float))]
        return sum(nums) / len(nums) if nums else 0.0

    def min(self, data: List[Union[int, float]]) -> Union[int, float]:
        """最小値を計算"""
        if not isinstance(data, (list, tuple)):
            return data if isinstance(data, (int, float)) else 0
        nums = [item for item in data if isinstance(item, (int, float))]
        return min(nums) if nums else 0

    def max(self, data: List[Union[int, float]]) -> Union[int, float]:
        """最大値を計算"""
        if not isinstance(data, (list, tuple)):
            return data if isinstance(data, (int, float)) else 0
        nums = [item for item in data if isinstance(item, (int, float))]
        return max(nums) if nums else 0

    def word_count(self, data: str) -> int:
        """単語数をカウント"""
        if not isinstance(data, str):
            return 0
        return len(data.split())

    def character_count(self, data: str) -> int:
        """文字数をカウント"""
        if not isinstance(data, str):
            return 0
        return len(data)
