#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
Python 3.13 Enhanced Error Handling and Debugging for pyspiderNX2
"""

import sys
import traceback
import logging
import inspect
import functools
import time
from typing import Any, Dict, List, Optional, Type, Union, Callable, TypeVar
from dataclasses import dataclass, field
from contextlib import contextmanager

# Python 3.13 specific imports
if sys.version_info >= (3, 13):
    from types import TracebackType
    from traceback import TracebackException
else:
    TracebackType = Any
    TracebackException = Any

F = TypeVar('F', bound=Callable[..., Any])

@dataclass
class ErrorContext:
    """Enhanced error context with debugging information"""
    timestamp: float = field(default_factory=time.time)
    function_name: str = ""
    module_name: str = ""
    line_number: int = 0
    local_vars: Dict[str, Any] = field(default_factory=dict)
    arguments: Dict[str, Any] = field(default_factory=dict)
    stack_trace: List[str] = field(default_factory=list)
    system_info: Dict[str, Any] = field(default_factory=dict)

class EnhancedError(Exception):
    """Enhanced exception with rich debugging information"""
    
    def __init__(self, message: str, context: Optional[ErrorContext] = None, 
                 original_exception: Optional[Exception] = None):
        super().__init__(message)
        self.message = message
        self.context = context or ErrorContext()
        self.original_exception = original_exception
        self.error_id = f"ERR_{int(time.time() * 1000)}"
    
    def __str__(self) -> str:
        """Enhanced string representation with context"""
        base_msg = f"[{self.error_id}] {self.message}"
        
        if self.context.function_name:
            base_msg += f" in {self.context.function_name}()"
        
        if self.context.line_number:
            base_msg += f" at line {self.context.line_number}"
        
        return base_msg
    
    def get_detailed_info(self) -> Dict[str, Any]:
        """Get detailed error information"""
        return {
            'error_id': self.error_id,
            'message': self.message,
            'timestamp': self.context.timestamp,
            'function': self.context.function_name,
            'module': self.context.module_name,
            'line': self.context.line_number,
            'arguments': self.context.arguments,
            'local_vars': self.context.local_vars,
            'stack_trace': self.context.stack_trace,
            'system_info': self.context.system_info,
            'original_exception': str(self.original_exception) if self.original_exception else None
        }

class PySpiderError(EnhancedError):
    """Base exception for pyspider-specific errors"""
    pass

class FetchError(PySpiderError):
    """Error during fetching process"""
    pass

class ProcessError(PySpiderError):
    """Error during processing"""
    pass

class ScheduleError(PySpiderError):
    """Error during scheduling"""
    pass

class DatabaseError(PySpiderError):
    """Error during database operations"""
    pass

class ConfigurationError(PySpiderError):
    """Error in configuration"""
    pass

class EnhancedTracebackFormatter:
    """Enhanced traceback formatter using Python 3.13 features"""
    
    @staticmethod
    def format_exception(exc_type: Type[Exception], exc_value: Exception, 
                        exc_traceback: Optional[TracebackType]) -> str:
        """Format exception with enhanced information"""
        if sys.version_info >= (3, 13):
            # Use Python 3.13 enhanced traceback formatting
            tb_exception = TracebackException.from_exception(exc_value)
            formatted_lines = list(tb_exception.format())
            return ''.join(formatted_lines)
        else:
            # Fallback for older versions
            return ''.join(traceback.format_exception(exc_type, exc_value, exc_traceback))
    
    @staticmethod
    def format_exception_with_context(exc: Exception, include_locals: bool = True) -> str:
        """Format exception with local variable context"""
        lines = []
        
        # Basic exception info
        lines.append(f"Exception: {type(exc).__name__}: {exc}")
        lines.append("")
        
        # Enhanced traceback
        if hasattr(exc, '__traceback__') and exc.__traceback__:
            tb = exc.__traceback__
            
            while tb is not None:
                frame = tb.tb_frame
                filename = frame.f_code.co_filename
                line_number = tb.tb_lineno
                function_name = frame.f_code.co_name
                
                lines.append(f"  File \"{filename}\", line {line_number}, in {function_name}")
                
                # Add source code line if available
                try:
                    import linecache
                    source_line = linecache.getline(filename, line_number).strip()
                    if source_line:
                        lines.append(f"    {source_line}")
                except:
                    pass
                
                # Add local variables if requested
                if include_locals and frame.f_locals:
                    lines.append("  Local variables:")
                    for var_name, var_value in frame.f_locals.items():
                        if not var_name.startswith('__'):
                            try:
                                var_repr = repr(var_value)
                                if len(var_repr) > 100:
                                    var_repr = var_repr[:97] + "..."
                                lines.append(f"    {var_name} = {var_repr}")
                            except:
                                lines.append(f"    {var_name} = <unable to represent>")
                
                lines.append("")
                tb = tb.tb_next
        
        return '\n'.join(lines)

def capture_error_context(frame: Optional[inspect.FrameInfo] = None) -> ErrorContext:
    """Capture detailed error context from current execution frame"""
    if frame is None:
        frame = inspect.currentframe()
        if frame:
            frame = frame.f_back  # Go up one level to get caller's frame
    
    context = ErrorContext()
    
    if frame:
        context.function_name = frame.f_code.co_name
        context.module_name = frame.f_globals.get('__name__', 'unknown')
        context.line_number = frame.f_lineno
        
        # Capture local variables (safely)
        try:
            for name, value in frame.f_locals.items():
                if not name.startswith('__'):
                    try:
                        # Only capture serializable values
                        if isinstance(value, (str, int, float, bool, list, dict, tuple)):
                            context.local_vars[name] = value
                        else:
                            context.local_vars[name] = str(type(value))
                    except:
                        context.local_vars[name] = '<unable to capture>'
        except:
            pass
        
        # Capture function arguments
        try:
            arg_info = inspect.getargvalues(frame)
            for arg_name in arg_info.args:
                if arg_name in frame.f_locals:
                    try:
                        context.arguments[arg_name] = frame.f_locals[arg_name]
                    except:
                        context.arguments[arg_name] = '<unable to capture>'
        except:
            pass
    
    # Capture stack trace
    try:
        context.stack_trace = traceback.format_stack()
    except:
        pass
    
    # Capture system information
    try:
        import psutil
        process = psutil.Process()
        context.system_info = {
            'memory_percent': process.memory_percent(),
            'cpu_percent': process.cpu_percent(),
            'num_threads': process.num_threads(),
            'python_version': sys.version,
        }
    except:
        context.system_info = {'python_version': sys.version}
    
    return context

def enhanced_exception_handler(exc_type: Type[Exception], exc_value: Exception, 
                             exc_traceback: Optional[TracebackType]) -> None:
    """Enhanced exception handler for uncaught exceptions"""
    logger = logging.getLogger('pyspider.errors')
    
    # Format the exception with enhanced information
    formatted_exc = EnhancedTracebackFormatter.format_exception_with_context(exc_value)
    
    # Log the error
    logger.error("Uncaught exception occurred:")
    logger.error(formatted_exc)
    
    # If it's an EnhancedError, log additional context
    if isinstance(exc_value, EnhancedError):
        detailed_info = exc_value.get_detailed_info()
        logger.error(f"Error details: {detailed_info}")

def with_error_context(func: F) -> F:
    """Decorator to add enhanced error context to functions"""
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except Exception as e:
            # Capture error context
            frame = inspect.currentframe()
            context = capture_error_context(frame)
            
            # Create enhanced error if it's not already one
            if not isinstance(e, EnhancedError):
                enhanced_error = PySpiderError(str(e), context, e)
                raise enhanced_error from e
            else:
                # Update context if it's already an enhanced error
                if not e.context.function_name:
                    e.context = context
                raise
    
    return wrapper  # type: ignore

@contextmanager
def error_context(operation_name: str):
    """Context manager for enhanced error handling"""
    start_time = time.time()
    logger = logging.getLogger('pyspider.operations')
    
    try:
        logger.debug(f"Starting operation: {operation_name}")
        yield
        duration = time.time() - start_time
        logger.debug(f"Completed operation: {operation_name} in {duration:.3f}s")
    except Exception as e:
        duration = time.time() - start_time
        frame = inspect.currentframe()
        context = capture_error_context(frame)
        
        logger.error(f"Operation failed: {operation_name} after {duration:.3f}s")
        
        if not isinstance(e, EnhancedError):
            enhanced_error = PySpiderError(
                f"Operation '{operation_name}' failed: {str(e)}", 
                context, 
                e
            )
            raise enhanced_error from e
        else:
            raise

class ErrorReporter:
    """Enhanced error reporting system"""
    
    def __init__(self, logger: Optional[logging.Logger] = None):
        self.logger = logger or logging.getLogger('pyspider.errors')
        self.error_counts: Dict[str, int] = {}
        self.recent_errors: List[Dict[str, Any]] = []
        self.max_recent_errors = 100
    
    def report_error(self, error: Exception, context: Optional[str] = None) -> str:
        """Report an error with enhanced information"""
        error_type = type(error).__name__
        self.error_counts[error_type] = self.error_counts.get(error_type, 0) + 1
        
        # Create error report
        report = {
            'timestamp': time.time(),
            'error_type': error_type,
            'message': str(error),
            'context': context,
            'count': self.error_counts[error_type]
        }
        
        # Add enhanced information if available
        if isinstance(error, EnhancedError):
            report.update(error.get_detailed_info())
        
        # Add to recent errors
        self.recent_errors.append(report)
        if len(self.recent_errors) > self.max_recent_errors:
            self.recent_errors.pop(0)
        
        # Log the error
        if isinstance(error, EnhancedError):
            formatted_error = EnhancedTracebackFormatter.format_exception_with_context(error)
            self.logger.error(f"Error in {context or 'unknown context'}:\n{formatted_error}")
        else:
            self.logger.error(f"Error in {context or 'unknown context'}: {error}")
        
        return report.get('error_id', f"ERR_{int(time.time() * 1000)}")
    
    def get_error_summary(self) -> Dict[str, Any]:
        """Get summary of recent errors"""
        return {
            'total_errors': sum(self.error_counts.values()),
            'error_types': dict(self.error_counts),
            'recent_errors': self.recent_errors[-10:],  # Last 10 errors
        }

# Global error reporter instance
error_reporter = ErrorReporter()

# Install enhanced exception handler
sys.excepthook = enhanced_exception_handler

# Export main components
__all__ = [
    'ErrorContext', 'EnhancedError', 'PySpiderError', 'FetchError', 
    'ProcessError', 'ScheduleError', 'DatabaseError', 'ConfigurationError',
    'EnhancedTracebackFormatter', 'capture_error_context', 
    'with_error_context', 'error_context', 'ErrorReporter', 'error_reporter'
]
