#!/usr/bin/env python
# -*- encoding: utf-8 -*-
# vim: set et sw=4 ts=4 sts=4 ff=unix fenc=utf8:
# Author: Binux<<EMAIL>>
#         http://binux.me
# Created on 2012-10-24 16:08:17

import os
import sys
import time
import logging
import logging.config
import logging.handlers
import json
import traceback
from datetime import datetime

try:
    import curses
except ImportError:
    curses = None

from tornado.log import LogFormatter as _LogFormatter


# デフォルトのログ設定
DEFAULT_LOG_FORMAT = '%(asctime)s [%(levelname)s] %(name)s: %(message)s'
DEFAULT_LOG_LEVEL = logging.INFO
DEFAULT_LOG_DIR = 'logs'

# ログレベルのマッピング
LOG_LEVEL_MAP = {
    'debug': logging.DEBUG,
    'info': logging.INFO,
    'warning': logging.WARNING,
    'error': logging.ERROR,
    'critical': logging.CRITICAL
}


class LogFormatter(_LogFormatter, object):
    """Init tornado.log.LogFormatter from logging.config.fileConfig"""
    def __init__(self, fmt=None, datefmt=None, color=True, *args, **kwargs):
        if fmt is None:
            fmt = _LogFormatter.DEFAULT_FORMAT
        super(LogFormatter, self).__init__(color=color, fmt=fmt, *args, **kwargs)


class SaveLogHandler(logging.Handler):
    """LogHandler that save records to a list"""

    def __init__(self, saveto=None, *args, **kwargs):
        self.saveto = saveto
        logging.Handler.__init__(self, *args, **kwargs)

    def emit(self, record):
        if self.saveto is not None:
            self.saveto.append(record)

    handle = emit


class RotatingFileHandler(logging.handlers.RotatingFileHandler):
    """拡張されたRotatingFileHandler

    ログディレクトリが存在しない場合は自動的に作成します。
    """
    def __init__(self, filename, mode='a', maxBytes=0, backupCount=0, encoding=None, delay=False):
        log_dir = os.path.dirname(filename)
        if log_dir and not os.path.exists(log_dir):
            os.makedirs(log_dir)
        super(RotatingFileHandler, self).__init__(
            filename, mode, maxBytes, backupCount, encoding, delay)


def enable_pretty_logging(logger=logging.getLogger()):
    """コンソールログを有効にする"""
    channel = logging.StreamHandler()
    channel.setFormatter(LogFormatter())
    logger.addHandler(channel)


def configure_logging(config_file=None, log_level=None, log_dir=None):
    """
    ロギングを設定する

    Args:
        config_file: ログ設定ファイル
        log_level: ログレベル（'debug', 'info', 'warning', 'error', 'critical'）
        log_dir: ログディレクトリ
    """
    # logsディレクトリが存在することを確認
    if log_dir:
        if not os.path.exists(log_dir):
            os.makedirs(log_dir)
    else:
        log_dir = 'logs'
        if not os.path.exists(log_dir):
            os.makedirs(log_dir)

    # 設定ファイルからロギング設定を読み込む
    if config_file and os.path.exists(config_file):
        try:
            # .confファイルの場合
            if config_file.endswith('.conf'):
                logging.config.fileConfig(config_file)
            else:
                # 設定ファイルが見つからない場合は基本的な設定を使用
                logging.basicConfig(level=logging.INFO)
        except Exception as e:
            sys.stderr.write(f"Error loading logging config: {e}\n")
            # 設定ファイルの読み込みに失敗した場合は基本的な設定を使用
            logging.basicConfig(level=logging.INFO)
    else:
        # 設定ファイルが見つからない場合は基本的な設定を使用
        logging.basicConfig(level=logging.INFO)

    # ログレベルの設定
    if log_level:
        if isinstance(log_level, str):
            log_level = LOG_LEVEL_MAP.get(log_level.lower(), DEFAULT_LOG_LEVEL)
        else:
            log_level = DEFAULT_LOG_LEVEL

        # ルートロガーのログレベルを設定
        logging.getLogger().setLevel(log_level)


def get_logger(name=None, log_level=None, log_format=None, log_file=None, log_dir=None,
               max_bytes=10*1024*1024, backup_count=5):
    """
    ロガーを取得する

    Args:
        name: ロガー名
        log_level: ログレベル（'debug', 'info', 'warning', 'error', 'critical'）
        log_format: ログフォーマット
        log_file: ログファイル名
        log_dir: ログディレクトリ
        max_bytes: ログファイルの最大サイズ（バイト）
        backup_count: 保持するバックアップファイルの数

    Returns:
        logging.Logger: 設定されたロガー
    """
    logger = logging.getLogger(name)

    # ログレベルの設定
    if log_level:
        if isinstance(log_level, str):
            log_level = LOG_LEVEL_MAP.get(log_level.lower(), DEFAULT_LOG_LEVEL)
        logger.setLevel(log_level)
    else:
        logger.setLevel(DEFAULT_LOG_LEVEL)

    # ハンドラが既に設定されている場合は追加しない
    if logger.handlers:
        return logger

    # ログフォーマットの設定
    if not log_format:
        log_format = DEFAULT_LOG_FORMAT
    formatter = logging.Formatter(log_format)

    # コンソールハンドラの設定
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(LogFormatter())
    logger.addHandler(console_handler)

    # ファイルハンドラの設定（指定されている場合）
    if log_file:
        if log_dir:
            if not os.path.exists(log_dir):
                os.makedirs(log_dir)
            log_file = os.path.join(log_dir, log_file)

        file_handler = RotatingFileHandler(
            log_file, maxBytes=max_bytes, backupCount=backup_count)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)

    return logger
