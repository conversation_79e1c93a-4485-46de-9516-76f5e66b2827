#!/usr/bin/env python
# -*- encoding: utf-8 -*-
# vim: set et sw=4 ts=4 sts=4 ff=unix fenc=utf8:
# Author: Binux<<EMAIL>>
#         http://binux.me
# Created on 2025-05-17 15:00:00

"""
キャッシュ機能を提供するモジュール
"""

import time
import json
import logging
import functools
import hashlib
from typing import Any, Callable, Dict, Optional, TypeVar, cast

logger = logging.getLogger('libs.cache')

# Redis接続がある場合はRedisを使用
try:
    import redis
    REDIS_AVAILABLE = True
except ImportError:
    REDIS_AVAILABLE = False

# 型変数
T = TypeVar('T')
F = TypeVar('F', bound=Callable[..., Any])

class MemoryCache:
    """メモリ内キャッシュ"""

    def __init__(self, max_size: int = 1000):
        self.cache: Dict[str, Dict[str, Any]] = {}
        self.max_size = max_size

    def get(self, key: str) -> Optional[Any]:
        """キャッシュからデータを取得"""
        if key not in self.cache:
            return None

        cache_item = self.cache[key]
        # 有効期限をチェック
        if 'expire' in cache_item and cache_item['expire'] < time.time():
            del self.cache[key]
            return None

        return cache_item['value']

    def set(self, key: str, value: Any, expire: Optional[int] = None) -> None:
        """キャッシュにデータを保存"""
        # キャッシュサイズをチェック
        if len(self.cache) >= self.max_size:
            # 最も古いキーを削除
            oldest_key = min(self.cache.keys(), key=lambda k: self.cache[k].get('time', 0))
            del self.cache[oldest_key]

        cache_item = {
            'value': value,
            'time': time.time()
        }

        if expire is not None:
            cache_item['expire'] = time.time() + expire

        self.cache[key] = cache_item

    def delete(self, key: str) -> None:
        """キャッシュからデータを削除"""
        if key in self.cache:
            del self.cache[key]

    def clear(self) -> None:
        """キャッシュをクリア"""
        self.cache.clear()

class RedisCache:
    """Redisを使用したキャッシュ"""

    def __init__(self, redis_url: str = 'redis://localhost:6379/1', prefix: str = 'pyspider:cache:'):
        # Redis 6.x compatibility
        self.redis = redis.from_url(redis_url, decode_responses=True, socket_timeout=30)
        self.prefix = prefix

    def _make_key(self, key: str) -> str:
        """キャッシュキーを生成"""
        return f"{self.prefix}{key}"

    def get(self, key: str) -> Optional[Any]:
        """キャッシュからデータを取得"""
        redis_key = self._make_key(key)
        value = self.redis.get(redis_key)
        if value is None:
            return None

        try:
            return json.loads(value)
        except json.JSONDecodeError:
            logger.warning(f"Failed to decode cached value for key: {key}")
            return None

    def set(self, key: str, value: Any, expire: Optional[int] = None) -> None:
        """キャッシュにデータを保存"""
        redis_key = self._make_key(key)
        try:
            # Flaskのレスポンスオブジェクトの場合は、データを抽出
            if hasattr(value, 'get_data'):
                try:
                    data = value.get_data(as_text=True)
                    value = json.loads(data)
                except:
                    logger.warning(f"Failed to extract data from response for key: {key}")
                    return

            json_value = json.dumps(value)
            if expire is not None:
                self.redis.setex(redis_key, expire, json_value)
            else:
                self.redis.set(redis_key, json_value)
        except (TypeError, json.JSONDecodeError):
            logger.warning(f"Failed to encode value for key: {key}")

    def delete(self, key: str) -> None:
        """キャッシュからデータを削除"""
        redis_key = self._make_key(key)
        self.redis.delete(redis_key)

    def clear(self) -> None:
        """キャッシュをクリア"""
        keys = self.redis.keys(f"{self.prefix}*")
        if keys:
            self.redis.delete(*keys)

# デフォルトのキャッシュインスタンス
if REDIS_AVAILABLE:
    try:
        default_cache = RedisCache()
        logger.info("Using Redis cache")
    except Exception as e:
        logger.warning(f"Failed to initialize Redis cache: {e}")
        default_cache = MemoryCache()
        logger.info("Falling back to memory cache")
else:
    default_cache = MemoryCache()
    logger.info("Using memory cache (Redis not available)")

def cache_result(expire: Optional[int] = 300, key_prefix: str = '', cache: Optional[Any] = None) -> Callable[[F], F]:
    """関数の結果をキャッシュするデコレータ"""
    def decorator(func: F) -> F:
        @functools.wraps(func)
        def wrapper(*args: Any, **kwargs: Any) -> Any:
            # キャッシュインスタンスを決定
            cache_instance = cache or default_cache

            # キャッシュキーを生成
            key_parts = [key_prefix or func.__name__]

            # 位置引数を追加
            for arg in args:
                key_parts.append(str(arg))

            # キーワード引数を追加（ソートして順序を一定に）
            for k in sorted(kwargs.keys()):
                key_parts.append(f"{k}={kwargs[k]}")

            # キーを結合してハッシュ化
            key = hashlib.md5(":".join(key_parts).encode()).hexdigest()

            # キャッシュから結果を取得
            cached_result = cache_instance.get(key)
            if cached_result is not None:
                return cached_result

            # 関数を実行
            result = func(*args, **kwargs)

            # 結果をキャッシュに保存
            cache_instance.set(key, result, expire)

            return result

        return cast(F, wrapper)

    return decorator
