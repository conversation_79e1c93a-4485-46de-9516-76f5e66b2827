#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
Python 3.13 Enhanced Type System for pyspiderNX2
"""

import sys
from typing import (
    TypeVar, Generic, Protocol, Union, Optional, Dict, List, Any, 
    Callable, Awaitable, TypedDict, Literal, Final, ClassVar,
    runtime_checkable, overload
)

# Python 3.13 specific type features
if sys.version_info >= (3, 13):
    from typing import TypeGuard, TypeIs, Never, Self
    from types import GenericAlias
else:
    # Fallback for older versions
    TypeGuard = Any
    TypeIs = Any
    Never = Any
    Self = Any
    GenericAlias = type

# Type variables
T = TypeVar('T')
K = TypeVar('K')
V = TypeVar('V')
ResponseT = TypeVar('ResponseT', bound='Response')
TaskT = TypeVar('TaskT', bound='Task')

# Literal types for status
TaskStatus = Literal['pending', 'running', 'success', 'failed', 'retry']
ProjectStatus = Literal['RUNNING', 'PAUSED', 'STOPPED', 'CHECKING', 'TODO', 'DEBUG']
FetchType = Literal['requests', 'tornado', 'puppeteer', 'playwright']

# TypedDict for structured data
class TaskDict(TypedDict, total=False):
    """Type definition for task dictionary"""
    taskid: str
    project: str
    url: str
    status: TaskStatus
    priority: int
    retries: int
    age: int
    updatetime: float
    schedule: Dict[str, Any]
    fetch: Dict[str, Any]
    process: Dict[str, Any]
    track: Dict[str, Any]

class ProjectDict(TypedDict, total=False):
    """Type definition for project dictionary"""
    name: str
    group: str
    status: ProjectStatus
    script: str
    comments: str
    rate: float
    burst: float
    updatetime: float

class ResponseDict(TypedDict, total=False):
    """Type definition for response dictionary"""
    status_code: int
    url: str
    orig_url: str
    headers: Dict[str, str]
    content: bytes
    text: str
    encoding: str
    cookies: Dict[str, str]
    time: float
    save: Dict[str, Any]

# Protocol definitions
@runtime_checkable
class Fetchable(Protocol):
    """Protocol for objects that can be fetched"""
    
    def fetch(self, task: TaskDict) -> Awaitable[ResponseDict]:
        """Fetch a task and return response"""
        ...

@runtime_checkable
class Processable(Protocol):
    """Protocol for objects that can be processed"""
    
    def process(self, task: TaskDict, response: ResponseDict) -> Any:
        """Process a task with its response"""
        ...

@runtime_checkable
class Schedulable(Protocol):
    """Protocol for objects that can be scheduled"""
    
    def schedule(self, task: TaskDict) -> bool:
        """Schedule a task for execution"""
        ...

@runtime_checkable
class Storable(Protocol[T]):
    """Protocol for objects that can store data"""
    
    def save(self, data: T) -> bool:
        """Save data to storage"""
        ...
    
    def load(self, key: str) -> Optional[T]:
        """Load data from storage"""
        ...

@runtime_checkable
class Configurable(Protocol):
    """Protocol for configurable objects"""
    
    def configure(self, config: Dict[str, Any]) -> Self:
        """Configure the object with given config"""
        ...

# Generic base classes
class BaseHandler(Generic[T]):
    """Generic base handler class"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None) -> None:
        self.config: Final[Dict[str, Any]] = config or {}
        self._cache: Dict[str, T] = {}
    
    def get_cached(self, key: str) -> Optional[T]:
        """Get cached value"""
        return self._cache.get(key)
    
    def set_cached(self, key: str, value: T) -> None:
        """Set cached value"""
        self._cache[key] = value

class DatabaseHandler(Generic[K, V], Storable[V]):
    """Generic database handler"""
    
    def __init__(self, connection_string: str) -> None:
        self.connection_string = connection_string
        self._data: Dict[K, V] = {}
    
    def save(self, data: V) -> bool:
        """Save data to database"""
        # Implementation would go here
        return True
    
    def load(self, key: str) -> Optional[V]:
        """Load data from database"""
        # Implementation would go here
        return None
    
    def get(self, key: K) -> Optional[V]:
        """Get value by key"""
        return self._data.get(key)
    
    def set(self, key: K, value: V) -> None:
        """Set value by key"""
        self._data[key] = value

# Type guards and type narrowing
def is_task_dict(obj: Any) -> TypeGuard[TaskDict]:
    """Type guard to check if object is a TaskDict"""
    return (
        isinstance(obj, dict) and
        'taskid' in obj and
        'project' in obj and
        'url' in obj
    )

def is_project_dict(obj: Any) -> TypeGuard[ProjectDict]:
    """Type guard to check if object is a ProjectDict"""
    return (
        isinstance(obj, dict) and
        'name' in obj and
        'status' in obj
    )

def is_response_dict(obj: Any) -> TypeGuard[ResponseDict]:
    """Type guard to check if object is a ResponseDict"""
    return (
        isinstance(obj, dict) and
        'status_code' in obj and
        'url' in obj
    )

# Python 3.13 specific type features
if sys.version_info >= (3, 13):
    def is_valid_status(status: str) -> TypeIs[TaskStatus]:
        """Type narrowing function for task status"""
        return status in ('pending', 'running', 'success', 'failed', 'retry')
    
    def is_valid_project_status(status: str) -> TypeIs[ProjectStatus]:
        """Type narrowing function for project status"""
        return status in ('RUNNING', 'PAUSED', 'STOPPED', 'CHECKING', 'TODO', 'DEBUG')
else:
    def is_valid_status(status: str) -> bool:
        """Fallback for older Python versions"""
        return status in ('pending', 'running', 'success', 'failed', 'retry')
    
    def is_valid_project_status(status: str) -> bool:
        """Fallback for older Python versions"""
        return status in ('RUNNING', 'PAUSED', 'STOPPED', 'CHECKING', 'TODO', 'DEBUG')

# Overloaded functions for better type inference
@overload
def create_task(taskid: str, project: str, url: str) -> TaskDict: ...

@overload
def create_task(taskid: str, project: str, url: str, **kwargs: Any) -> TaskDict: ...

def create_task(taskid: str, project: str, url: str, **kwargs: Any) -> TaskDict:
    """Create a task dictionary with proper typing"""
    task: TaskDict = {
        'taskid': taskid,
        'project': project,
        'url': url,
        'status': 'pending',
        'priority': 5,
        'retries': 0,
        'age': 0,
        'updatetime': 0.0,
    }
    
    # Add optional fields
    for key, value in kwargs.items():
        if key in TaskDict.__annotations__:
            task[key] = value  # type: ignore
    
    return task

# Generic factory pattern
class HandlerFactory(Generic[T]):
    """Generic factory for creating handlers"""
    
    def __init__(self, handler_class: type[T]) -> None:
        self.handler_class = handler_class
        self._instances: Dict[str, T] = {}
    
    def create(self, name: str, *args: Any, **kwargs: Any) -> T:
        """Create or get cached handler instance"""
        if name not in self._instances:
            self._instances[name] = self.handler_class(*args, **kwargs)
        return self._instances[name]
    
    def get(self, name: str) -> Optional[T]:
        """Get existing handler instance"""
        return self._instances.get(name)

# Type-safe configuration
class TypedConfig(Generic[T]):
    """Type-safe configuration container"""
    
    def __init__(self, default_value: T) -> None:
        self._value = default_value
        self._type = type(default_value)
    
    @property
    def value(self) -> T:
        """Get the configuration value"""
        return self._value
    
    @value.setter
    def value(self, new_value: T) -> None:
        """Set the configuration value with type checking"""
        if not isinstance(new_value, self._type):
            raise TypeError(f"Expected {self._type.__name__}, got {type(new_value).__name__}")
        self._value = new_value

# Export all type definitions
__all__ = [
    # Type variables
    'T', 'K', 'V', 'ResponseT', 'TaskT',
    
    # Literal types
    'TaskStatus', 'ProjectStatus', 'FetchType',
    
    # TypedDict classes
    'TaskDict', 'ProjectDict', 'ResponseDict',
    
    # Protocol classes
    'Fetchable', 'Processable', 'Schedulable', 'Storable', 'Configurable',
    
    # Generic classes
    'BaseHandler', 'DatabaseHandler', 'HandlerFactory', 'TypedConfig',
    
    # Type guards and functions
    'is_task_dict', 'is_project_dict', 'is_response_dict',
    'is_valid_status', 'is_valid_project_status',
    'create_task',
]
