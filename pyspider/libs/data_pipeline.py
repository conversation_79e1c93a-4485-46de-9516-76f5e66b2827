#!/usr/bin/env python
# -*- coding: utf-8 -*-

import time
import logging
import json
from typing import Dict, List, Any, Callable, Optional, Union, Iterable, TypeVar

from pyspider.libs.stream_processing import StreamProcessor
from pyspider.libs.data_transformers import DataTransformer
from pyspider.libs.data_quality import DataQualityChecker

logger = logging.getLogger('data_pipeline')

T = TypeVar('T')
U = TypeVar('U')

class DataPipeline:
    """
    データ処理パイプライン
    
    クローリングしたデータを処理するためのパイプラインを提供します。
    - ストリーム処理：大量のデータをストリームとして処理
    - データ変換：柔軟なデータ変換機能
    - データ品質チェック：取得したデータの品質を自動的にチェック
    """
    
    def __init__(self, max_workers: int = 4, queue_size: int = 100):
        """
        初期化
        
        Args:
            max_workers: 最大ワーカー数
            queue_size: キューサイズ
        """
        self.stream_processor = StreamProcessor(max_workers=max_workers, queue_size=queue_size)
        self.data_transformer = DataTransformer()
        self.data_quality_checker = DataQualityChecker()
        
        self.transformers = []
        self.validators = []
        self.filters = []
        self.post_processors = []
        
        self.stats = {
            'processed': 0,
            'transformed': 0,
            'validated': 0,
            'filtered': 0,
            'errors': 0,
            'start_time': 0,
            'end_time': 0,
            'processing_time': 0
        }
    
    def add_transformer(self, transformer: Union[str, Dict, List, Callable[[Any], Any]]) -> 'DataPipeline':
        """
        変換処理を追加
        
        Args:
            transformer: 変換処理
            
        Returns:
            自身のインスタンス（メソッドチェーン用）
        """
        self.transformers.append(transformer)
        return self
    
    def add_validator(self, validator: Union[str, Dict, List, Callable[[Any], bool]]) -> 'DataPipeline':
        """
        検証処理を追加
        
        Args:
            validator: 検証処理
            
        Returns:
            自身のインスタンス（メソッドチェーン用）
        """
        self.validators.append(validator)
        return self
    
    def add_filter(self, filter_func: Callable[[Any], bool]) -> 'DataPipeline':
        """
        フィルター処理を追加
        
        Args:
            filter_func: フィルター処理
            
        Returns:
            自身のインスタンス（メソッドチェーン用）
        """
        self.filters.append(filter_func)
        return self
    
    def add_post_processor(self, processor: Callable[[Any], Any]) -> 'DataPipeline':
        """
        後処理を追加
        
        Args:
            processor: 後処理
            
        Returns:
            自身のインスタンス（メソッドチェーン用）
        """
        self.post_processors.append(processor)
        return self
    
    def process(self, data_stream: Iterable[T]) -> Iterable[Any]:
        """
        データストリームを処理
        
        Args:
            data_stream: 入力データストリーム
            
        Returns:
            処理結果のイテレータ
        """
        self.stats['start_time'] = time.time()
        self.stats['processed'] = 0
        self.stats['transformed'] = 0
        self.stats['validated'] = 0
        self.stats['filtered'] = 0
        self.stats['errors'] = 0
        
        # ストリーム処理を構築
        processor = self.stream_processor
        
        # 変換処理を追加
        processor.add_processor(self._transform_data)
        
        # 検証処理を追加
        processor.add_processor(self._validate_data)
        
        # フィルター処理を追加
        processor.add_processor(self._filter_data)
        
        # 後処理を追加
        processor.add_processor(self._post_process_data)
        
        # 処理を実行
        for result in processor.process(data_stream):
            self.stats['processed'] += 1
            yield result
        
        self.stats['end_time'] = time.time()
        self.stats['processing_time'] = self.stats['end_time'] - self.stats['start_time']
    
    def _transform_data(self, data: Any) -> Any:
        """データを変換"""
        if data is None:
            return None
        
        try:
            result = data
            for transformer in self.transformers:
                if callable(transformer):
                    result = transformer(result)
                else:
                    result = self.data_transformer.transform(result, transformer)
                
                if result is None:
                    break
            
            if result is not None:
                self.stats['transformed'] += 1
            
            return result
        except Exception as e:
            logger.exception(f"Error transforming data: {e}")
            self.stats['errors'] += 1
            return None
    
    def _validate_data(self, data: Any) -> Any:
        """データを検証"""
        if data is None:
            return None
        
        try:
            for validator in self.validators:
                if callable(validator):
                    valid = validator(data)
                    if not valid:
                        return None
                else:
                    valid, _ = self.data_quality_checker.validate(data, validator)
                    if not valid:
                        return None
            
            self.stats['validated'] += 1
            return data
        except Exception as e:
            logger.exception(f"Error validating data: {e}")
            self.stats['errors'] += 1
            return None
    
    def _filter_data(self, data: Any) -> Any:
        """データをフィルタリング"""
        if data is None:
            return None
        
        try:
            for filter_func in self.filters:
                if not filter_func(data):
                    return None
            
            self.stats['filtered'] += 1
            return data
        except Exception as e:
            logger.exception(f"Error filtering data: {e}")
            self.stats['errors'] += 1
            return None
    
    def _post_process_data(self, data: Any) -> Any:
        """データを後処理"""
        if data is None:
            return None
        
        try:
            result = data
            for processor in self.post_processors:
                result = processor(result)
                if result is None:
                    break
            
            return result
        except Exception as e:
            logger.exception(f"Error post-processing data: {e}")
            self.stats['errors'] += 1
            return None
    
    def get_stats(self) -> Dict[str, Any]:
        """統計情報を取得"""
        return self.stats
    
    def reset(self) -> None:
        """パイプラインをリセット"""
        self.transformers = []
        self.validators = []
        self.filters = []
        self.post_processors = []
        
        self.stats = {
            'processed': 0,
            'transformed': 0,
            'validated': 0,
            'filtered': 0,
            'errors': 0,
            'start_time': 0,
            'end_time': 0,
            'processing_time': 0
        }
    
    def stop(self) -> None:
        """処理を停止"""
        self.stream_processor.stop()
