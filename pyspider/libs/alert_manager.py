#!/usr/bin/env python
# -*- encoding: utf-8 -*-
# vim: set et sw=4 ts=4 sts=4 ff=unix fenc=utf8:
# Author: PySpider Team
# Created on 2023-05-18 13:00:00

import time
import logging
import threading
import json
import os
import smtplib
import requests
from email.mime.text import MIMEText
from email.mime.multipart import MIME<PERSON><PERSON><PERSON>art
from typing import Dict, Any, List, Optional, Union, Callable
from collections import defaultdict

logger = logging.getLogger('alert_manager')

class AlertManager:
    """
    Alert manager for PySpider
    """

    def __init__(self,
                 config_file: Optional[str] = None,
                 check_interval: int = 60,
                 auto_check: bool = True):
        """
        Initialize AlertManager

        Args:
            config_file: Path to config file
            check_interval: Interval between alert checks in seconds
            auto_check: Whether to automatically check alerts
        """
        self.config_file = config_file
        self.check_interval = check_interval
        self.auto_check = auto_check

        # Alerts
        self.alerts = []
        self.alert_history = []
        self.alert_rules = []
        self.alert_channels = {}

        # Locks
        self._alerts_lock = threading.RLock()
        self._alert_history_lock = threading.RLock()
        self._alert_rules_lock = threading.RLock()
        self._alert_channels_lock = threading.RLock()

        # Check thread
        self._check_thread = None
        self._stop_event = threading.Event()

        # Initialize
        self._init()

        logger.info(f"Alert manager initialized (auto_check: {self.auto_check})")

    def _init(self):
        """Initialize alert manager"""
        # Load config
        if self.config_file and os.path.exists(self.config_file):
            self.load_config(self.config_file)
        else:
            # Set default rules
            self.add_rule({
                'name': 'high_cpu_usage',
                'description': 'High CPU usage',
                'condition': 'system.get("cpu_percent", 0) > 80',
                'duration': 300,  # 5 minutes
                'severity': 'warning'
            })

            self.add_rule({
                'name': 'high_memory_usage',
                'description': 'High memory usage',
                'condition': 'system.get("memory_percent", 0) > 80',
                'duration': 300,  # 5 minutes
                'severity': 'warning'
            })

            self.add_rule({
                'name': 'high_disk_usage',
                'description': 'High disk usage',
                'condition': 'system.get("disk_percent", 0) > 80',
                'duration': 300,  # 5 minutes
                'severity': 'warning'
            })

            self.add_rule({
                'name': 'too_many_tasks',
                'description': 'Too many tasks in queue',
                'condition': 'scheduler.get("queue_size", 0) > 5000',
                'duration': 600,  # 10 minutes
                'severity': 'warning'
            })

            self.add_rule({
                'name': 'too_many_errors',
                'description': 'Too many task errors',
                'condition': 'scheduler.get("failed_tasks_24h", 0) / max(scheduler.get("total_tasks_24h", 1), 1) > 0.2 if scheduler.get("total_tasks_24h", 0) > 100 else False',
                'duration': 3600,  # 1 hour
                'severity': 'warning'
            })

        # Start check thread
        if self.auto_check:
            self.start_check_thread()

    def start_check_thread(self):
        """Start alert check thread"""
        if self._check_thread and self._check_thread.is_alive():
            logger.warning("Alert check thread is already running")
            return

        self._stop_event.clear()
        self._check_thread = threading.Thread(target=self._check_loop, daemon=True)
        self._check_thread.start()

        logger.info(f"Started alert check thread (interval: {self.check_interval}s)")

    def stop_check_thread(self):
        """Stop alert check thread"""
        if self._check_thread and self._check_thread.is_alive():
            self._stop_event.set()
            self._check_thread.join(timeout=5)
            self._check_thread = None

            logger.info("Stopped alert check thread")

    def _check_loop(self):
        """Alert check loop"""
        while not self._stop_event.is_set():
            try:
                self.check_alerts()
            except Exception as e:
                logger.error(f"Error checking alerts: {e}")

            # Wait for next check
            self._stop_event.wait(self.check_interval)

    def load_config(self, config_file: str) -> bool:
        """
        Load config from file

        Args:
            config_file: Path to config file

        Returns:
            True if successful, False otherwise
        """
        try:
            with open(config_file, 'r') as f:
                config = json.load(f)

            # Load rules
            if 'rules' in config:
                with self._alert_rules_lock:
                    self.alert_rules = config['rules']

            # Load channels
            if 'channels' in config:
                with self._alert_channels_lock:
                    self.alert_channels = config['channels']

            logger.info(f"Loaded alert config from {config_file}")
            return True
        except Exception as e:
            logger.error(f"Error loading alert config from {config_file}: {e}")
            return False

    def save_config(self, config_file: Optional[str] = None) -> bool:
        """
        Save config to file

        Args:
            config_file: Path to config file

        Returns:
            True if successful, False otherwise
        """
        config_file = config_file or self.config_file

        if not config_file:
            logger.error("No config file specified")
            return False

        try:
            config = {
                'rules': self.get_rules(),
                'channels': self.get_channels()
            }

            with open(config_file, 'w') as f:
                json.dump(config, f, indent=2)

            logger.info(f"Saved alert config to {config_file}")
            return True
        except Exception as e:
            logger.error(f"Error saving alert config to {config_file}: {e}")
            return False

    def add_rule(self, rule: Dict[str, Any]) -> bool:
        """
        Add alert rule

        Args:
            rule: Alert rule

        Returns:
            True if successful, False otherwise
        """
        if not rule.get('name'):
            logger.error("Rule name is required")
            return False

        if not rule.get('condition'):
            logger.error("Rule condition is required")
            return False

        with self._alert_rules_lock:
            # Check if rule already exists
            for i, r in enumerate(self.alert_rules):
                if r.get('name') == rule.get('name'):
                    # Update existing rule
                    self.alert_rules[i] = rule
                    logger.info(f"Updated alert rule: {rule.get('name')}")
                    return True

            # Add new rule
            self.alert_rules.append(rule)
            logger.info(f"Added alert rule: {rule.get('name')}")
            return True

    def remove_rule(self, name: str) -> bool:
        """
        Remove alert rule

        Args:
            name: Rule name

        Returns:
            True if successful, False otherwise
        """
        with self._alert_rules_lock:
            for i, rule in enumerate(self.alert_rules):
                if rule.get('name') == name:
                    del self.alert_rules[i]
                    logger.info(f"Removed alert rule: {name}")
                    return True

            logger.warning(f"Alert rule not found: {name}")
            return False

    def get_rules(self) -> List[Dict[str, Any]]:
        """
        Get alert rules

        Returns:
            Alert rules
        """
        with self._alert_rules_lock:
            return list(self.alert_rules)

    def add_channel(self, name: str, channel: Dict[str, Any]) -> bool:
        """
        Add alert channel

        Args:
            name: Channel name
            channel: Channel configuration

        Returns:
            True if successful, False otherwise
        """
        if not name:
            logger.error("Channel name is required")
            return False

        if not channel.get('type'):
            logger.error("Channel type is required")
            return False

        with self._alert_channels_lock:
            self.alert_channels[name] = channel
            logger.info(f"Added alert channel: {name}")
            return True

    def remove_channel(self, name: str) -> bool:
        """
        Remove alert channel

        Args:
            name: Channel name

        Returns:
            True if successful, False otherwise
        """
        with self._alert_channels_lock:
            if name in self.alert_channels:
                del self.alert_channels[name]
                logger.info(f"Removed alert channel: {name}")
                return True

            logger.warning(f"Alert channel not found: {name}")
            return False

    def get_channels(self) -> Dict[str, Dict[str, Any]]:
        """
        Get alert channels

        Returns:
            Alert channels
        """
        with self._alert_channels_lock:
            return dict(self.alert_channels)

    def check_alerts(self, metrics: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """
        Check alerts

        Args:
            metrics: Metrics to check against

        Returns:
            Triggered alerts
        """
        if not metrics:
            # Get metrics from webui
            try:
                from pyspider.webui.metrics import get_scheduler_stats, get_system_stats
                from pyspider.webui.components_status import get_components_status

                metrics = {
                    'scheduler': get_scheduler_stats(),
                    'system': get_system_stats(),
                    'components': get_components_status()
                }
            except Exception as e:
                logger.error(f"Error getting metrics: {e}")
                return []

        triggered_alerts = []

        with self._alert_rules_lock:
            for rule in self.alert_rules:
                try:
                    # Check if rule is enabled
                    if not rule.get('enabled', True):
                        continue

                    # Check condition
                    condition = rule.get('condition')
                    if not condition:
                        continue

                    # Evaluate condition
                    result = self._evaluate_condition(condition, metrics)

                    if result:
                        # Rule triggered
                        alert = {
                            'rule': rule.get('name'),
                            'description': rule.get('description'),
                            'severity': rule.get('severity', 'warning'),
                            'timestamp': time.time(),
                            'metrics': metrics
                        }

                        # Check if alert is already active
                        is_new = True
                        with self._alerts_lock:
                            for a in self.alerts:
                                if a.get('rule') == rule.get('name'):
                                    # Update existing alert
                                    a['timestamp'] = alert['timestamp']
                                    a['metrics'] = alert['metrics']
                                    is_new = False
                                    break

                            if is_new:
                                # Add new alert
                                self.alerts.append(alert)

                                # Add to history
                                with self._alert_history_lock:
                                    self.alert_history.append(alert)

                                    # Limit history size
                                    if len(self.alert_history) > 1000:
                                        self.alert_history = self.alert_history[-1000:]

                                # Send alert
                                self._send_alert(alert)

                        triggered_alerts.append(alert)
                    else:
                        # Rule not triggered, check if alert is active
                        with self._alerts_lock:
                            for i, a in enumerate(self.alerts):
                                if a.get('rule') == rule.get('name'):
                                    # Remove alert
                                    del self.alerts[i]

                                    # Add resolved alert to history
                                    resolved_alert = dict(a)
                                    resolved_alert['resolved'] = True
                                    resolved_alert['resolved_timestamp'] = time.time()

                                    with self._alert_history_lock:
                                        self.alert_history.append(resolved_alert)

                                        # Limit history size
                                        if len(self.alert_history) > 1000:
                                            self.alert_history = self.alert_history[-1000:]

                                    # Send resolved alert
                                    self._send_resolved_alert(resolved_alert)

                                    break
                except Exception as e:
                    logger.error(f"Error checking rule {rule.get('name')}: {e}")

        return triggered_alerts

    def _evaluate_condition(self, condition: str, metrics: Dict[str, Any]) -> bool:
        """
        Evaluate alert condition

        Args:
            condition: Alert condition
            metrics: Metrics to check against

        Returns:
            True if condition is met, False otherwise
        """
        try:
            # Create locals for evaluation
            locals_dict = {}

            # Add metrics to locals
            for key, value in metrics.items():
                locals_dict[key] = value

                # ドット表記をサポートするために、ネストされた辞書を展開
                if isinstance(value, dict):
                    for subkey, subvalue in value.items():
                        dotted_key = f"{key}.{subkey}"
                        # 既存のキーを上書きしないように注意
                        if dotted_key not in locals_dict:
                            locals_dict[dotted_key] = subvalue

            # Evaluate condition
            result = eval(condition, {"__builtins__": {}}, locals_dict)

            return bool(result)
        except Exception as e:
            logger.error(f"Error evaluating condition '{condition}': {e}")
            return False

    def _send_alert(self, alert: Dict[str, Any]) -> bool:
        """
        Send alert

        Args:
            alert: Alert to send

        Returns:
            True if successful, False otherwise
        """
        with self._alert_channels_lock:
            for name, channel in self.alert_channels.items():
                try:
                    # Check if channel is enabled
                    if not channel.get('enabled', True):
                        continue

                    # Check if channel accepts this severity
                    severity = alert.get('severity', 'warning')
                    if severity not in channel.get('severities', ['warning', 'critical']):
                        continue

                    # Send alert
                    channel_type = channel.get('type')

                    if channel_type == 'email':
                        self._send_email_alert(channel, alert)
                    elif channel_type == 'webhook':
                        self._send_webhook_alert(channel, alert)
                    elif channel_type == 'slack':
                        self._send_slack_alert(channel, alert)
                    else:
                        logger.warning(f"Unknown channel type: {channel_type}")
                        continue

                    logger.info(f"Sent alert '{alert.get('rule')}' to channel '{name}'")
                except Exception as e:
                    logger.error(f"Error sending alert to channel '{name}': {e}")

        return True

    def _send_resolved_alert(self, alert: Dict[str, Any]) -> bool:
        """
        Send resolved alert

        Args:
            alert: Resolved alert to send

        Returns:
            True if successful, False otherwise
        """
        with self._alert_channels_lock:
            for name, channel in self.alert_channels.items():
                try:
                    # Check if channel is enabled
                    if not channel.get('enabled', True):
                        continue

                    # Check if channel accepts this severity
                    severity = alert.get('severity', 'warning')
                    if severity not in channel.get('severities', ['warning', 'critical']):
                        continue

                    # Check if channel accepts resolved alerts
                    if not channel.get('send_resolved', True):
                        continue

                    # Send resolved alert
                    channel_type = channel.get('type')

                    if channel_type == 'email':
                        self._send_email_resolved_alert(channel, alert)
                    elif channel_type == 'webhook':
                        self._send_webhook_resolved_alert(channel, alert)
                    elif channel_type == 'slack':
                        self._send_slack_resolved_alert(channel, alert)
                    else:
                        logger.warning(f"Unknown channel type: {channel_type}")
                        continue

                    logger.info(f"Sent resolved alert '{alert.get('rule')}' to channel '{name}'")
                except Exception as e:
                    logger.error(f"Error sending resolved alert to channel '{name}': {e}")

        return True

    def _send_email_alert(self, channel: Dict[str, Any], alert: Dict[str, Any]) -> bool:
        """
        Send email alert

        Args:
            channel: Email channel configuration
            alert: Alert to send

        Returns:
            True if successful, False otherwise
        """
        try:
            # Get email configuration
            smtp_server = channel.get('smtp_server')
            smtp_port = channel.get('smtp_port', 587)
            smtp_username = channel.get('smtp_username')
            smtp_password = channel.get('smtp_password')
            from_addr = channel.get('from_addr')
            to_addrs = channel.get('to_addrs', [])

            if not smtp_server or not from_addr or not to_addrs:
                logger.error("Missing email configuration")
                return False

            # Create message
            msg = MIMEMultipart()
            msg['From'] = from_addr
            msg['To'] = ', '.join(to_addrs)
            msg['Subject'] = f"PySpider Alert: {alert.get('rule')}"

            # Create message body
            body = f"Alert: {alert.get('rule')}\n"
            body += f"Description: {alert.get('description')}\n"
            body += f"Severity: {alert.get('severity')}\n"
            body += f"Timestamp: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(alert.get('timestamp')))}\n"

            msg.attach(MIMEText(body, 'plain'))

            # Send email
            with smtplib.SMTP(smtp_server, smtp_port) as server:
                if smtp_username and smtp_password:
                    server.starttls()
                    server.login(smtp_username, smtp_password)

                server.send_message(msg)

            return True
        except Exception as e:
            logger.error(f"Error sending email alert: {e}")
            return False

    def _send_webhook_alert(self, channel: Dict[str, Any], alert: Dict[str, Any]) -> bool:
        """
        Send webhook alert

        Args:
            channel: Webhook channel configuration
            alert: Alert to send

        Returns:
            True if successful, False otherwise
        """
        try:
            # Get webhook configuration
            url = channel.get('url')

            if not url:
                logger.error("Missing webhook URL")
                return False

            # Create payload
            payload = {
                'alert': alert.get('rule'),
                'description': alert.get('description'),
                'severity': alert.get('severity'),
                'timestamp': alert.get('timestamp'),
                'resolved': False
            }

            # Send webhook
            response = requests.post(url, json=payload)
            response.raise_for_status()

            return True
        except Exception as e:
            logger.error(f"Error sending webhook alert: {e}")
            return False

    def _send_slack_alert(self, channel: Dict[str, Any], alert: Dict[str, Any]) -> bool:
        """
        Send Slack alert

        Args:
            channel: Slack channel configuration
            alert: Alert to send

        Returns:
            True if successful, False otherwise
        """
        try:
            # Get Slack configuration
            webhook_url = channel.get('webhook_url')

            if not webhook_url:
                logger.error("Missing Slack webhook URL")
                return False

            # Create payload
            payload = {
                'text': f"*PySpider Alert: {alert.get('rule')}*",
                'attachments': [
                    {
                        'color': 'danger' if alert.get('severity') == 'critical' else 'warning',
                        'fields': [
                            {
                                'title': 'Description',
                                'value': alert.get('description'),
                                'short': False
                            },
                            {
                                'title': 'Severity',
                                'value': alert.get('severity'),
                                'short': True
                            },
                            {
                                'title': 'Timestamp',
                                'value': time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(alert.get('timestamp'))),
                                'short': True
                            }
                        ]
                    }
                ]
            }

            # Send webhook
            response = requests.post(webhook_url, json=payload)
            response.raise_for_status()

            return True
        except Exception as e:
            logger.error(f"Error sending Slack alert: {e}")
            return False

    def _send_email_resolved_alert(self, channel: Dict[str, Any], alert: Dict[str, Any]) -> bool:
        """
        Send email resolved alert

        Args:
            channel: Email channel configuration
            alert: Resolved alert to send

        Returns:
            True if successful, False otherwise
        """
        try:
            # Get email configuration
            smtp_server = channel.get('smtp_server')
            smtp_port = channel.get('smtp_port', 587)
            smtp_username = channel.get('smtp_username')
            smtp_password = channel.get('smtp_password')
            from_addr = channel.get('from_addr')
            to_addrs = channel.get('to_addrs', [])

            if not smtp_server or not from_addr or not to_addrs:
                logger.error("Missing email configuration")
                return False

            # Create message
            msg = MIMEMultipart()
            msg['From'] = from_addr
            msg['To'] = ', '.join(to_addrs)
            msg['Subject'] = f"PySpider Alert Resolved: {alert.get('rule')}"

            # Create message body
            body = f"Alert Resolved: {alert.get('rule')}\n"
            body += f"Description: {alert.get('description')}\n"
            body += f"Severity: {alert.get('severity')}\n"
            body += f"Timestamp: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(alert.get('timestamp')))}\n"
            body += f"Resolved Timestamp: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(alert.get('resolved_timestamp')))}\n"

            msg.attach(MIMEText(body, 'plain'))

            # Send email
            with smtplib.SMTP(smtp_server, smtp_port) as server:
                if smtp_username and smtp_password:
                    server.starttls()
                    server.login(smtp_username, smtp_password)

                server.send_message(msg)

            return True
        except Exception as e:
            logger.error(f"Error sending email resolved alert: {e}")
            return False

    def _send_webhook_resolved_alert(self, channel: Dict[str, Any], alert: Dict[str, Any]) -> bool:
        """
        Send webhook resolved alert

        Args:
            channel: Webhook channel configuration
            alert: Resolved alert to send

        Returns:
            True if successful, False otherwise
        """
        try:
            # Get webhook configuration
            url = channel.get('url')

            if not url:
                logger.error("Missing webhook URL")
                return False

            # Create payload
            payload = {
                'alert': alert.get('rule'),
                'description': alert.get('description'),
                'severity': alert.get('severity'),
                'timestamp': alert.get('timestamp'),
                'resolved': True,
                'resolved_timestamp': alert.get('resolved_timestamp')
            }

            # Send webhook
            response = requests.post(url, json=payload)
            response.raise_for_status()

            return True
        except Exception as e:
            logger.error(f"Error sending webhook resolved alert: {e}")
            return False

    def _send_slack_resolved_alert(self, channel: Dict[str, Any], alert: Dict[str, Any]) -> bool:
        """
        Send Slack resolved alert

        Args:
            channel: Slack channel configuration
            alert: Resolved alert to send

        Returns:
            True if successful, False otherwise
        """
        try:
            # Get Slack configuration
            webhook_url = channel.get('webhook_url')

            if not webhook_url:
                logger.error("Missing Slack webhook URL")
                return False

            # Create payload
            payload = {
                'text': f"*PySpider Alert Resolved: {alert.get('rule')}*",
                'attachments': [
                    {
                        'color': 'good',
                        'fields': [
                            {
                                'title': 'Description',
                                'value': alert.get('description'),
                                'short': False
                            },
                            {
                                'title': 'Severity',
                                'value': alert.get('severity'),
                                'short': True
                            },
                            {
                                'title': 'Timestamp',
                                'value': time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(alert.get('timestamp'))),
                                'short': True
                            },
                            {
                                'title': 'Resolved Timestamp',
                                'value': time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(alert.get('resolved_timestamp'))),
                                'short': True
                            }
                        ]
                    }
                ]
            }

            # Send webhook
            response = requests.post(webhook_url, json=payload)
            response.raise_for_status()

            return True
        except Exception as e:
            logger.error(f"Error sending Slack resolved alert: {e}")
            return False

    def get_alerts(self) -> List[Dict[str, Any]]:
        """
        Get active alerts

        Returns:
            Active alerts
        """
        with self._alerts_lock:
            return list(self.alerts)

    def get_alert_history(self) -> List[Dict[str, Any]]:
        """
        Get alert history

        Returns:
            Alert history
        """
        with self._alert_history_lock:
            return list(self.alert_history)


# Global instance
alert_manager = AlertManager()
