#!/usr/bin/env python
# -*- encoding: utf-8 -*-
# vim: set et sw=4 ts=4 sts=4 ff=unix fenc=utf8:
# Author: PySpider Team
# Created on 2023-05-18 11:00:00

import time
import logging
import threading
from typing import Dict, Any, List, Optional, Union, Callable, TYPE_CHECKING
from collections import defaultdict

# 型チェック時のみCollectorRegistryをインポート
if TYPE_CHECKING:
    from prometheus_client import CollectorRegistry

try:
    from prometheus_client import Counter, Gauge, Histogram, Summary, CollectorRegistry, push_to_gateway, generate_latest, REGISTRY
    from prometheus_client.exposition import MetricsHandler
    has_prometheus = True
except ImportError:
    has_prometheus = False
    # ダミークラスを定義して、インポートエラーを回避
    class Counter:
        pass
    class Gauge:
        pass
    class Histogram:
        pass
    class Summary:
        pass
    class CollectorRegistry:
        pass
    class MetricsHandler:
        pass
    def push_to_gateway(*args, **kwargs):
        pass
    def generate_latest(*args, **kwargs):
        return b""
    REGISTRY = None

logger = logging.getLogger('prometheus_metrics')

class PrometheusMetrics:
    """
    Prometheus metrics integration for PySpider
    """

    def __init__(self,
                 prefix: str = 'pyspider',
                 registry: Optional[CollectorRegistry] = None,
                 push_gateway: Optional[str] = None,
                 push_interval: int = 60,
                 job_name: str = 'pyspider'):
        """
        Initialize PrometheusMetrics

        Args:
            prefix: Prefix for all metrics
            registry: Prometheus registry
            push_gateway: Prometheus push gateway URL
            push_interval: Push interval in seconds
            job_name: Job name for push gateway
        """
        if not has_prometheus:
            logger.warning("prometheus_client is not installed. Please install it with 'pip install prometheus_client'")
            return

        self.prefix = prefix
        self.registry = registry or REGISTRY
        self.push_gateway = push_gateway
        self.push_interval = push_interval
        self.job_name = job_name

        # Metrics
        self._counters = {}
        self._gauges = {}
        self._histograms = {}
        self._summaries = {}

        # Push thread
        self._push_thread = None
        self._stop_event = threading.Event()

        # Initialize default metrics
        self._init_default_metrics()

        logger.info(f"Prometheus metrics initialized with prefix '{prefix}'")

        # Start push thread if push gateway is specified
        if self.push_gateway:
            self.start_push_thread()

    def _init_default_metrics(self):
        """Initialize default metrics"""
        # System metrics
        self.gauge('process_start_time_seconds', 'Start time of the process since unix epoch in seconds').set(time.time())

        # PySpider metrics
        self.counter('requests_total', 'Total number of requests')
        self.counter('requests_success_total', 'Total number of successful requests')
        self.counter('requests_error_total', 'Total number of failed requests')
        self.counter('tasks_total', 'Total number of tasks')
        self.counter('tasks_success_total', 'Total number of successful tasks')
        self.counter('tasks_error_total', 'Total number of failed tasks')

        self.gauge('scheduler_queue_size', 'Scheduler queue size')
        self.gauge('scheduler_processing_tasks', 'Number of tasks being processed')
        self.gauge('scheduler_pending_tasks', 'Number of pending tasks')

        self.histogram('request_duration_seconds', 'Request duration in seconds', buckets=(0.1, 0.5, 1.0, 2.0, 5.0, 10.0, 30.0, 60.0))
        self.histogram('task_duration_seconds', 'Task duration in seconds', buckets=(0.1, 0.5, 1.0, 2.0, 5.0, 10.0, 30.0, 60.0, 120.0, 300.0, 600.0))

    def start_push_thread(self):
        """Start push thread"""
        if not has_prometheus:
            return

        if not self.push_gateway:
            logger.warning("Push gateway URL is not specified")
            return

        if self._push_thread and self._push_thread.is_alive():
            logger.warning("Push thread is already running")
            return

        self._stop_event.clear()
        self._push_thread = threading.Thread(target=self._push_loop, daemon=True)
        self._push_thread.start()

        logger.info(f"Started pushing metrics to {self.push_gateway} every {self.push_interval} seconds")

    def stop_push_thread(self):
        """Stop push thread"""
        if self._push_thread and self._push_thread.is_alive():
            self._stop_event.set()
            self._push_thread.join(timeout=5)
            self._push_thread = None

            logger.info("Stopped pushing metrics")

    def _push_loop(self):
        """Push metrics loop"""
        while not self._stop_event.is_set():
            try:
                self.push()
            except Exception as e:
                logger.error(f"Error pushing metrics: {e}")

            # Wait for next push
            self._stop_event.wait(self.push_interval)

    def push(self):
        """Push metrics to push gateway"""
        if not has_prometheus or not self.push_gateway:
            return

        try:
            push_to_gateway(self.push_gateway, job=self.job_name, registry=self.registry)
            logger.debug(f"Pushed metrics to {self.push_gateway}")
        except Exception as e:
            logger.error(f"Error pushing metrics to {self.push_gateway}: {e}")

    def counter(self, name: str, documentation: str, labelnames: List[str] = None) -> Counter:
        """
        Get or create a counter

        Args:
            name: Metric name
            documentation: Metric documentation
            labelnames: Label names

        Returns:
            Counter
        """
        if not has_prometheus:
            return DummyMetric()

        key = self._get_key(name, labelnames)

        if key not in self._counters:
            self._counters[key] = Counter(
                f"{self.prefix}_{name}",
                documentation,
                labelnames=labelnames or [],
                registry=self.registry
            )

        return self._counters[key]

    def gauge(self, name: str, documentation: str, labelnames: List[str] = None) -> Gauge:
        """
        Get or create a gauge

        Args:
            name: Metric name
            documentation: Metric documentation
            labelnames: Label names

        Returns:
            Gauge
        """
        if not has_prometheus:
            return DummyMetric()

        key = self._get_key(name, labelnames)

        if key not in self._gauges:
            self._gauges[key] = Gauge(
                f"{self.prefix}_{name}",
                documentation,
                labelnames=labelnames or [],
                registry=self.registry
            )

        return self._gauges[key]

    def histogram(self, name: str, documentation: str, labelnames: List[str] = None, buckets=None) -> Histogram:
        """
        Get or create a histogram

        Args:
            name: Metric name
            documentation: Metric documentation
            labelnames: Label names
            buckets: Histogram buckets

        Returns:
            Histogram
        """
        if not has_prometheus:
            return DummyMetric()

        key = self._get_key(name, labelnames)

        if key not in self._histograms:
            self._histograms[key] = Histogram(
                f"{self.prefix}_{name}",
                documentation,
                labelnames=labelnames or [],
                buckets=buckets,
                registry=self.registry
            )

        return self._histograms[key]

    def summary(self, name: str, documentation: str, labelnames: List[str] = None) -> Summary:
        """
        Get or create a summary

        Args:
            name: Metric name
            documentation: Metric documentation
            labelnames: Label names

        Returns:
            Summary
        """
        if not has_prometheus:
            return DummyMetric()

        key = self._get_key(name, labelnames)

        if key not in self._summaries:
            self._summaries[key] = Summary(
                f"{self.prefix}_{name}",
                documentation,
                labelnames=labelnames or [],
                registry=self.registry
            )

        return self._summaries[key]

    def _get_key(self, name: str, labelnames: List[str] = None) -> str:
        """
        Get a key for a metric

        Args:
            name: Metric name
            labelnames: Label names

        Returns:
            Metric key
        """
        if not labelnames:
            return name

        return f"{name}_{','.join(sorted(labelnames))}"

    def get_metrics(self) -> bytes:
        """
        Get metrics in Prometheus format

        Returns:
            Metrics in Prometheus format
        """
        if not has_prometheus:
            return b""

        return generate_latest(self.registry)


class DummyMetric:
    """Dummy metric for when prometheus_client is not installed"""

    def __init__(self):
        self.value = 0
        self.label_values = {}

    def inc(self, amount=1):
        self.value += amount
        return self

    def dec(self, amount=1):
        self.value -= amount
        return self

    def set(self, value):
        if isinstance(value, (int, float)):
            self.value = value
        else:
            # 数値以外の場合は0を設定
            self.value = 0
        return self

    def observe(self, value):
        if isinstance(value, (int, float)):
            self.value = value
        else:
            # 数値以外の場合は0を設定
            self.value = 0
        return self

    def labels(self, *args, **kwargs):
        # 新しいインスタンスを作成して返す
        new_instance = DummyMetric()
        new_instance.label_values = kwargs
        return new_instance

    def time(self):
        class DummyTimer:
            def __enter__(self):
                self.start = time.time()
                return self

            def __exit__(self, exc_type, exc_val, exc_tb):
                pass

        return DummyTimer()


# Global instance
prometheus_metrics = PrometheusMetrics()
