#!/usr/bin/env python
# -*- encoding: utf-8 -*-
# vim: set et sw=4 ts=4 sts=4 ff=unix fenc=utf8:
# Author: PySpiderNX2 Team
# Created on 2025-01-XX

import os
import sys
import logging
import logging.handlers
from datetime import datetime
from typing import Dict, Any, Optional
import json

class EnhancedFormatter(logging.Formatter):
    """拡張ログフォーマッター"""
    
    def __init__(self, include_extra=True):
        super().__init__()
        self.include_extra = include_extra
    
    def format(self, record):
        # 基本フォーマット
        log_entry = {
            'timestamp': datetime.fromtimestamp(record.created).isoformat(),
            'level': record.levelname,
            'logger': record.name,
            'message': record.getMessage(),
            'module': record.module,
            'function': record.funcName,
            'line': record.lineno
        }
        
        # 例外情報を追加
        if record.exc_info:
            log_entry['exception'] = self.formatException(record.exc_info)
        
        # 追加情報を含める
        if self.include_extra:
            extra_fields = {}
            for key, value in record.__dict__.items():
                if key not in ['name', 'msg', 'args', 'levelname', 'levelno', 
                              'pathname', 'filename', 'module', 'lineno', 
                              'funcName', 'created', 'msecs', 'relativeCreated',
                              'thread', 'threadName', 'processName', 'process',
                              'getMessage', 'exc_info', 'exc_text', 'stack_info']:
                    extra_fields[key] = value
            
            if extra_fields:
                log_entry['extra'] = extra_fields
        
        return json.dumps(log_entry, ensure_ascii=False)

class SecurityAuditHandler(logging.Handler):
    """セキュリティ監査ログハンドラー"""
    
    def __init__(self, audit_file='security_audit.log'):
        super().__init__()
        self.audit_file = audit_file
        
        # セキュリティ関連のキーワード
        self.security_keywords = [
            'unauthorized', 'forbidden', 'authentication', 'csrf',
            'injection', 'xss', 'attack', 'suspicious', 'blocked'
        ]
    
    def emit(self, record):
        try:
            message = record.getMessage().lower()
            
            # セキュリティ関連のログかチェック
            if any(keyword in message for keyword in self.security_keywords):
                with open(self.audit_file, 'a', encoding='utf-8') as f:
                    audit_entry = {
                        'timestamp': datetime.fromtimestamp(record.created).isoformat(),
                        'level': record.levelname,
                        'message': record.getMessage(),
                        'module': record.module,
                        'extra': getattr(record, 'extra', {})
                    }
                    f.write(json.dumps(audit_entry, ensure_ascii=False) + '\n')
        
        except Exception:
            self.handleError(record)

class PerformanceHandler(logging.Handler):
    """パフォーマンス監視ログハンドラー"""
    
    def __init__(self, performance_file='performance.log'):
        super().__init__()
        self.performance_file = performance_file
        
        # パフォーマンス関連のキーワード
        self.performance_keywords = [
            'slow', 'timeout', 'memory', 'cpu', 'performance',
            'optimization', 'cache', 'query_time', 'response_time'
        ]
    
    def emit(self, record):
        try:
            message = record.getMessage().lower()
            
            # パフォーマンス関連のログかチェック
            if any(keyword in message for keyword in self.performance_keywords):
                with open(self.performance_file, 'a', encoding='utf-8') as f:
                    perf_entry = {
                        'timestamp': datetime.fromtimestamp(record.created).isoformat(),
                        'level': record.levelname,
                        'message': record.getMessage(),
                        'module': record.module,
                        'extra': getattr(record, 'extra', {})
                    }
                    f.write(json.dumps(perf_entry, ensure_ascii=False) + '\n')
        
        except Exception:
            self.handleError(record)

def setup_enhanced_logging(
    log_level: str = 'INFO',
    log_dir: str = 'logs',
    max_size: str = '100MB',
    backup_count: int = 5,
    enable_json: bool = True,
    enable_security_audit: bool = True,
    enable_performance_monitoring: bool = True
):
    """拡張ログ設定をセットアップ"""
    
    # ログディレクトリを作成
    os.makedirs(log_dir, exist_ok=True)
    
    # ルートロガーを取得
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, log_level.upper()))
    
    # 既存のハンドラーをクリア
    root_logger.handlers.clear()
    
    # コンソールハンドラー
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(logging.INFO)
    
    if enable_json:
        console_formatter = EnhancedFormatter(include_extra=False)
    else:
        console_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
    
    console_handler.setFormatter(console_formatter)
    root_logger.addHandler(console_handler)
    
    # ファイルハンドラー（ローテーション付き）
    max_bytes = _parse_size(max_size)
    file_handler = logging.handlers.RotatingFileHandler(
        os.path.join(log_dir, 'pyspider.log'),
        maxBytes=max_bytes,
        backupCount=backup_count,
        encoding='utf-8'
    )
    file_handler.setLevel(getattr(logging, log_level.upper()))
    
    if enable_json:
        file_formatter = EnhancedFormatter(include_extra=True)
    else:
        file_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s'
        )
    
    file_handler.setFormatter(file_formatter)
    root_logger.addHandler(file_handler)
    
    # エラーログ専用ハンドラー
    error_handler = logging.handlers.RotatingFileHandler(
        os.path.join(log_dir, 'error.log'),
        maxBytes=max_bytes,
        backupCount=backup_count,
        encoding='utf-8'
    )
    error_handler.setLevel(logging.ERROR)
    error_handler.setFormatter(file_formatter)
    root_logger.addHandler(error_handler)
    
    # セキュリティ監査ハンドラー
    if enable_security_audit:
        security_handler = SecurityAuditHandler(
            os.path.join(log_dir, 'security_audit.log')
        )
        security_handler.setLevel(logging.WARNING)
        root_logger.addHandler(security_handler)
    
    # パフォーマンス監視ハンドラー
    if enable_performance_monitoring:
        performance_handler = PerformanceHandler(
            os.path.join(log_dir, 'performance.log')
        )
        performance_handler.setLevel(logging.INFO)
        root_logger.addHandler(performance_handler)
    
    # 特定のロガーの設定
    _configure_specific_loggers()
    
    logging.info(f"Enhanced logging configured - Level: {log_level}, Dir: {log_dir}")

def _parse_size(size_str: str) -> int:
    """サイズ文字列をバイト数に変換"""
    size_str = size_str.upper()
    
    if size_str.endswith('KB'):
        return int(size_str[:-2]) * 1024
    elif size_str.endswith('MB'):
        return int(size_str[:-2]) * 1024 * 1024
    elif size_str.endswith('GB'):
        return int(size_str[:-2]) * 1024 * 1024 * 1024
    else:
        return int(size_str)

def _configure_specific_loggers():
    """特定のロガーを設定"""
    # 外部ライブラリのログレベルを調整
    logging.getLogger('urllib3').setLevel(logging.WARNING)
    logging.getLogger('requests').setLevel(logging.WARNING)
    logging.getLogger('aiohttp').setLevel(logging.WARNING)
    logging.getLogger('tornado').setLevel(logging.WARNING)
    
    # pyspiderのコンポーネント別ログレベル
    logging.getLogger('pyspider.scheduler').setLevel(logging.INFO)
    logging.getLogger('pyspider.fetcher').setLevel(logging.INFO)
    logging.getLogger('pyspider.processor').setLevel(logging.INFO)
    logging.getLogger('pyspider.webui').setLevel(logging.INFO)

def get_logger(name: str, extra_context: Optional[Dict[str, Any]] = None):
    """コンテキスト付きロガーを取得"""
    logger = logging.getLogger(name)
    
    if extra_context:
        # LoggerAdapterを使用してコンテキストを追加
        return logging.LoggerAdapter(logger, extra_context)
    
    return logger
