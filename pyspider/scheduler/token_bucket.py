#!/usr/bin/env python
# -*- encoding: utf-8 -*-
# vim: set et sw=4 ts=4 sts=4 ff=unix fenc=utf8:
# Author: Binux<<EMAIL>>
#         http://binux.me
# Created on 2014-02-07 16:53:08

import time
try:
    import threading as _threading
except ImportError:
    import dummy_threading as _threading


class Bucket(object):

    '''
    traffic flow control with token bucket
    '''

    update_interval = 30

    def __init__(self, rate=1, burst=None):
        try:
            self.rate = float(rate)
        except (ValueError, TypeError):
            self.rate = 1.0

        if burst is None:
            self.burst = self.rate * 10
        else:
            try:
                self.burst = float(burst)
            except (ValueError, TypeError):
                self.burst = self.rate * 10

        self.mutex = _threading.Lock()
        self.bucket = self.burst
        self.last_update = time.time()

    def get(self):
        '''Get the number of tokens in bucket'''
        now = time.time()
        try:
            if float(self.bucket) >= float(self.burst):
                self.last_update = now
                return float(self.bucket)
        except (ValueError, TypeError):
            self.bucket = float(self.rate) * 10
            self.last_update = now
            return float(self.bucket)

        try:
            bucket = float(self.rate) * (now - self.last_update)
        except (ValueError, TypeError):
            self.rate = 1.0
            bucket = self.rate * (now - self.last_update)

        self.mutex.acquire()
        if bucket > 1:
            try:
                self.bucket = float(self.bucket) + bucket
            except (ValueError, TypeError):
                self.bucket = bucket

            try:
                if float(self.bucket) > float(self.burst):
                    self.bucket = float(self.burst)
            except (ValueError, TypeError):
                self.burst = float(self.rate) * 10
                if float(self.bucket) > float(self.burst):
                    self.bucket = float(self.burst)

            self.last_update = now
        self.mutex.release()
        return float(self.bucket)

    def set(self, value):
        '''Set number of tokens in bucket'''
        try:
            self.bucket = float(value)
        except (ValueError, TypeError):
            self.bucket = float(self.rate) * 10

    def desc(self, value=1):
        '''Use value tokens'''
        try:
            self.bucket = float(self.bucket) - float(value)
        except (ValueError, TypeError):
            try:
                self.bucket = float(self.bucket) - 1
            except (ValueError, TypeError):
                self.bucket = float(self.rate) * 10 - 1
