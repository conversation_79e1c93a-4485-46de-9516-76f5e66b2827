#!/usr/bin/env python
# -*- coding: utf-8 -*-
import re
from pyspider.libs.base_handler import *

class Handler(BaseHandler):
    crawl_config = {
        'headers': {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        },
        'timeout': 60,
        'connect_timeout': 30,
        'retries': 3,
    }

    @every(minutes=24 * 60)
    def on_start(self):
        # Wikipediaの「プログラミング言語一覧」ページをクロール
        self.crawl('https://ja.wikipedia.org/wiki/プログラミング言語一覧', callback=self.index_page)

    @config(age=10 * 24 * 60 * 60)
    def index_page(self, response):
        # 正規表現を使用して、プログラミング言語の名前とリンクを抽出
        # Wikipediaの見出しを取得
        headings = response.doc('h2, h3').items()
        
        for heading in headings:
            heading_text = heading.text()
            # 見出しから[編集]を削除
            heading_text = re.sub(r'\[編集\]', '', heading_text).strip()
            
            if heading_text:
                self.logger.info(f"見出し: {heading_text}")
                
                # 見出しの次にあるリストを取得
                ul = heading.next('ul')
                if ul:
                    # リスト内の各項目を処理
                    for li in ul.find('li').items():
                        # 正規表現を使用してリンクとテキストを抽出
                        link_pattern = r'<a href="([^"]+)"[^>]*>([^<]+)</a>'
                        matches = re.findall(link_pattern, li.html())
                        
                        for href, text in matches:
                            # 言語の詳細ページをクロール
                            full_url = response.urljoin(href)
                            self.crawl(full_url, callback=self.detail_page, save={'language': text, 'category': heading_text})
                            
                        # リンクがない場合は、テキストのみを抽出
                        if not matches:
                            # 正規表現を使用してテキストを抽出（HTMLタグを除去）
                            text = re.sub(r'<[^>]+>', '', li.html()).strip()
                            if text:
                                self.logger.info(f"  言語: {text} (リンクなし)")

    @config(priority=2)
    def detail_page(self, response):
        language = response.save.get('language', '')
        category = response.save.get('category', '')
        
        # タイトルを取得
        title = response.doc('title').text()
        
        # 正規表現を使用して、最初の段落を抽出
        first_paragraph = ''
        paragraphs = response.doc('#mw-content-text p').items()
        for p in paragraphs:
            # 空の段落をスキップ
            if p.text().strip():
                first_paragraph = p.text()
                break
        
        # 正規表現を使用して、言語の登場年を抽出
        year_pattern = r'(\d{4})年'
        years = re.findall(year_pattern, first_paragraph)
        first_year = years[0] if years else 'N/A'
        
        # 正規表現を使用して、言語の開発者を抽出
        developer_pattern = r'開発者は([^。]+)'
        developer_match = re.search(developer_pattern, first_paragraph)
        developer = developer_match.group(1).strip() if developer_match else 'N/A'
        
        # 正規表現を使用して、言語の特徴を抽出（箇条書きから）
        features = []
        feature_lists = response.doc('#mw-content-text ul').items()
        for ul in feature_lists:
            for li in ul.find('li').items():
                feature_text = li.text().strip()
                if feature_text and len(feature_text) < 100:  # 短い特徴のみを抽出
                    features.append(feature_text)
        
        # 最初の3つの特徴のみを使用
        top_features = features[:3]
        
        # 正規表現を使用して、言語のサンプルコードを抽出
        code_sample = ''
        code_blocks = response.doc('pre, code').items()
        for code in code_blocks:
            code_text = code.text().strip()
            if code_text and len(code_text) < 500:  # 短いコードサンプルのみを抽出
                code_sample = code_text
                break
        
        # 正規表現を使用して、関連言語を抽出
        related_languages = []
        # 「〜言語」というパターンを探す
        language_pattern = r'([A-Za-z0-9]+言語)'
        language_matches = re.findall(language_pattern, first_paragraph)
        related_languages.extend(language_matches)
        
        # 英語の言語名を探す（大文字で始まる単語）
        english_language_pattern = r'\b([A-Z][a-z]+)\b'
        english_matches = re.findall(english_language_pattern, first_paragraph)
        # 一般的な単語を除外
        common_words = ['Wikipedia', 'Web', 'World', 'Internet', 'Computer', 'Programming']
        filtered_english = [lang for lang in english_matches if lang not in common_words]
        related_languages.extend(filtered_english)
        
        # 重複を削除
        related_languages = list(set(related_languages))
        
        return {
            "url": response.url,
            "title": title,
            "language": language,
            "category": category,
            "first_paragraph": first_paragraph,
            "first_appearance_year": first_year,
            "developer": developer,
            "features": top_features,
            "code_sample": code_sample,
            "related_languages": related_languages
        }
