#!/usr/bin/env python
# -*- encoding: utf-8 -*-
# vim: set et sw=4 ts=4 sts=4 ff=unix fenc=utf8:
# Author: Binux<<EMAIL>>
#         http://binux.me
# Created on 2014-12-10 20:36:27

import base64
import functools
from flask import Response, jsonify
try:
    import flask_login as login
except ImportError:
    from flask.ext import login
from .app import app

login_manager = login.LoginManager()
login_manager.init_app(app)


class AnonymousUser(login.AnonymousUserMixin):

    def is_anonymous(self):
        return True

    def is_active(self):
        return False

    def is_authenticated(self):
        return False

    def get_id(self):
        return


class User(login.UserMixin):

    def __init__(self, id, password):
        self.id = id
        self.password = password

    def is_authenticated(self):
        if not app.config.get('webui_username'):
            return True
        if self.id == app.config.get('webui_username') \
                and self.password == app.config.get('webui_password'):
            return True
        return False

    def is_active(self):
        return self.is_authenticated()


login_manager.anonymous_user = AnonymousUser


@login_manager.request_loader
def load_user_from_request(request):
    api_key = request.headers.get('Authorization')
    app.logger.info('Authorization header: %r', api_key)
    if api_key:
        if api_key.startswith("Basic "):
            api_key = api_key[len("Basic "):]
            try:
                decoded = base64.b64decode(api_key).decode('utf8')
                app.logger.info('Decoded credentials: %r', decoded)
                username, password = decoded.split(":", 1)
                user = User(username, password)
                app.logger.info('User authenticated: %r, active: %r', user.id, user.is_active())
                return user
            except Exception as e:
                app.logger.error('wrong api key: %r, %r', api_key, e)
                return None
        else:
            app.logger.warning('Authorization header does not start with Basic: %r', api_key)
    else:
        app.logger.info('No Authorization header found')
    return None
app.login_response = Response(
    "need auth.", 401, {'WWW-Authenticate': 'Basic realm="Login Required"'}
)


def need_auth(f):
    """認証が必要なエンドポイント用のデコレーター（従来のWebUI用）"""
    @functools.wraps(f)
    def decorated_function(*args, **kwargs):
        if app.config.get('need_auth', False):
            if not login.current_user.is_active():
                return jsonify({
                    'error': 'Authentication required',
                    'status': 'error',
                    'timestamp': __import__('time').time()
                }), 401
        return f(*args, **kwargs)
    return decorated_function


def need_auth_api_v2(f):
    """API v2専用の認証デコレーター"""
    @functools.wraps(f)
    def decorated_function(*args, **kwargs):
        from flask import request

        # 認証が無効な場合はスキップ
        if not app.config.get('need_auth', False):
            return f(*args, **kwargs)

        # Authorization ヘッダーから認証情報を取得
        auth_header = request.headers.get('Authorization')
        if not auth_header:
            return jsonify({
                'error': 'Authorization header required',
                'status': 'error',
                'code': 'AUTH_HEADER_MISSING',
                'timestamp': __import__('time').time()
            }), 401

        # Basic認証の処理
        if auth_header.startswith('Basic '):
            try:
                import base64
                encoded_credentials = auth_header[6:]  # "Basic " を除去
                decoded_credentials = base64.b64decode(encoded_credentials).decode('utf-8')
                username, password = decoded_credentials.split(':', 1)

                # 設定ファイルの認証情報と照合
                config_username = app.config.get('webui_username')
                config_password = app.config.get('webui_password')

                if username == config_username and password == config_password:
                    # 認証成功
                    return f(*args, **kwargs)
                else:
                    return jsonify({
                        'error': 'Invalid credentials',
                        'status': 'error',
                        'code': 'INVALID_CREDENTIALS',
                        'timestamp': __import__('time').time()
                    }), 401

            except Exception as e:
                app.logger.error('Authentication error: %s', e)
                return jsonify({
                    'error': 'Authentication failed',
                    'status': 'error',
                    'code': 'AUTH_PROCESSING_ERROR',
                    'timestamp': __import__('time').time()
                }), 401

        # Bearer token認証（将来の拡張用）
        elif auth_header.startswith('Bearer '):
            return jsonify({
                'error': 'Bearer token authentication not implemented',
                'status': 'error',
                'code': 'AUTH_METHOD_NOT_SUPPORTED',
                'timestamp': __import__('time').time()
            }), 501

        else:
            return jsonify({
                'error': 'Unsupported authentication method',
                'status': 'error',
                'code': 'AUTH_METHOD_UNSUPPORTED',
                'timestamp': __import__('time').time()
            }), 401

    return decorated_function


@app.before_request
def before_request():
    # API v2エンドポイントは個別の認証デコレーターで処理するため、ここではスキップ
    from flask import request
    app.logger.info('before_request called for path: %s', request.path)

    if app.config.get('need_auth', False):
        # API v2エンドポイントの場合は個別認証に任せる
        if request.path and '/api/v2/' in request.path:
            app.logger.info('API v2 endpoint detected, skipping before_request auth')
            return None

        app.logger.info('Checking current user in before_request: %r, is_active: %r',
                       login.current_user, login.current_user.is_active())

        if not login.current_user.is_active():
            app.logger.warning('User not active in before_request, returning 401')
            return app.login_response
