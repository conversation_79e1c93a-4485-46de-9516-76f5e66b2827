<!DOCTYPE html>
<html lang="ja">
  <head>
    <meta charset="utf-8">
    <title>製品一覧 - pyspider</title>
    <!--[if lt IE 9]>
      <script src="http://html5shim.googlecode.com/svn/trunk/html5.js"></script>
    <![endif]-->

    <meta name="description" content="製品一覧">
    <meta name="author" content="pyspider">
    <!-- Bootstrap 3.4.1 CSS (latest stable for Bootstrap 3) -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@3.4.1/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-HSMxcRTRxnN+Bdg0JdbxYKrThecOKuH5zCYotlSAcp1+c8xmyTe9GYg1l9a69psu" crossorigin="anonymous">
    <!-- jQuery UI 1.13.2 CSS (latest stable) -->
    <link href="https://code.jquery.com/ui/1.13.2/themes/smoothness/jquery-ui.css" rel="stylesheet">
    <!-- Font Awesome 6 (latest) -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" rel="stylesheet">
    <!-- DataTables Bootstrap 3 Integration -->
    <link href="https://cdn.datatables.net/1.13.7/css/dataTables.bootstrap.min.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='result.min.css') }}" rel="stylesheet">

    <!-- jQuery 3.7.1 (latest stable) -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js" integrity="sha256-/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo=" crossorigin="anonymous"></script>
    <!-- jQuery Migrate 3.4.1 (latest stable) -->
    <script src="https://code.jquery.com/jquery-migrate-3.4.1.min.js" integrity="sha256-UnTxHm+zKuDPLfufgEMnKGXDl6fEIjtM+n1Q6lL73ok=" crossorigin="anonymous"></script>
    <!-- Bootstrap 3.4.1 JS (latest stable for Bootstrap 3) -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@3.4.1/dist/js/bootstrap.min.js" integrity="sha384-aJ21OjlMXNL5UyIl/XNwTMqvzeRMZH2w8c5cRVpzpU8Y5bApTppSuUkhZXN0VxHd" crossorigin="anonymous"></script>
    <!-- jQuery UI 1.13.2 (latest stable) -->
    <script src="https://code.jquery.com/ui/1.13.2/jquery-ui.min.js" integrity="sha256-lSjKY0/srUM9BE3dPm+c4fBo1dky2v27Gdjm2uoZaL0=" crossorigin="anonymous"></script>
    <!-- DataTables with Bootstrap 3 Integration -->
    <script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.7/js/dataTables.bootstrap.min.js"></script>
  </head>

  <body>
    <div class="container">
      <div class="top-bar">
        <h1>製品一覧</h1>
        <div class="btn-group">
          <a href="/" class="btn btn-default btn-sm">
            <i class="fas fa-home"></i> ホーム
          </a>
        </div>
      </div>

      <div class="panel panel-default">
        <div class="panel-heading">
          <h3 class="panel-title">Amazon製品データ</h3>
        </div>
        <div class="panel-body">
          <table id="products-table" class="table table-striped table-bordered table-hover" style="width:100%">
            <thead>
              <tr>
                <th>製品名</th>
                <th>価格</th>
                <th>評価</th>
                <th>URL</th>
                <th>詳細</th>
              </tr>
            </thead>
            <tbody>
              <!-- データはJavaScriptで動的に読み込まれます -->
            </tbody>
          </table>
        </div>
      </div>
    </div>

    <script>
      $(document).ready(function() {
        // 製品データを取得してDataTablesで表示
        $.ajax({
          url: '/products',
          method: 'GET',
          dataType: 'json',
          success: function(response) {
            if (response.status === 'success') {
              const products = response.data;
              
              // DataTablesの初期化
              $('#products-table').DataTable({
                data: products,
                columns: [
                  { data: 'title', title: '製品名' },
                  { 
                    data: 'price', 
                    title: '価格',
                    render: function(data) {
                      return data ? '¥' + data : 'N/A';
                    }
                  },
                  { 
                    data: 'rating', 
                    title: '評価',
                    render: function(data) {
                      return data ? data + '/5' : 'N/A';
                    }
                  },
                  { 
                    data: 'url', 
                    title: 'URL',
                    render: function(data) {
                      return data ? '<a href="' + data + '" target="_blank"><i class="fas fa-external-link-alt"></i> リンク</a>' : 'N/A';
                    }
                  },
                  {
                    data: null,
                    title: '詳細',
                    render: function(data, type, row) {
                      return '<button class="btn btn-info btn-sm product-details" data-product=\'' + JSON.stringify(row).replace(/'/g, "&apos;") + '\'><i class="fas fa-info-circle"></i> 詳細</button>';
                    }
                  }
                ],
                language: {
                  url: 'https://cdn.datatables.net/plug-ins/1.13.7/i18n/ja.json'
                },
                responsive: true,
                pageLength: 10
              });
              
              // 詳細ボタンのクリックイベント
              $('#products-table').on('click', '.product-details', function() {
                const productData = JSON.parse($(this).attr('data-product'));
                let detailsHtml = '<div class="product-details-modal">';
                
                // 製品の詳細情報を表示
                for (const key in productData) {
                  if (productData.hasOwnProperty(key)) {
                    let value = productData[key];
                    if (key === 'url') {
                      value = '<a href="' + value + '" target="_blank">' + value + '</a>';
                    }
                    detailsHtml += '<p><strong>' + key + ':</strong> ' + value + '</p>';
                  }
                }
                
                detailsHtml += '</div>';
                
                // モーダルダイアログで表示
                $('<div>').html(detailsHtml).dialog({
                  title: productData.title || '製品詳細',
                  width: 600,
                  modal: true,
                  buttons: {
                    '閉じる': function() {
                      $(this).dialog('close');
                    }
                  }
                });
              });
            } else {
              alert('データの取得に失敗しました: ' + response.message);
            }
          },
          error: function(xhr, status, error) {
            alert('エラーが発生しました: ' + error);
          }
        });
      });
    </script>
  </body>
</html>