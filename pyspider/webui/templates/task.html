<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <title>Task - {{ task.project }}:{{ task.taskid }} - pyspider</title>
    <!--[if lt IE 9]>
      <script src="http://html5shim.googlecode.com/svn/trunk/html5.js"></script>
    <![endif]-->

    <meta name="description" content="pyspider taskboard of {{ task.project }}:{{task.taskid }}">
    <meta name="author" content="binux">
    <!-- Bootstrap 3.4.1 CSS (latest stable for Bootstrap 3) -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@3.4.1/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-HSMxcRTRxnN+Bdg0JdbxYKrThecOKuH5zCYotlSAcp1+c8xmyTe9GYg1l9a69psu" crossorigin="anonymous">
    <!-- jQuery UI 1.13.2 CSS (latest stable) -->
    <link href="https://code.jquery.com/ui/1.13.2/themes/smoothness/jquery-ui.css" rel="stylesheet">
    <!-- Font Awesome 6 (latest) -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='task.min.css') }}" rel="stylesheet">

    <!-- jQuery 3.7.1 (latest stable) -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js" integrity="sha256-/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo=" crossorigin="anonymous"></script>
    <!-- jQuery Migrate 3.4.1 (latest stable) -->
    <script src="https://code.jquery.com/jquery-migrate-3.4.1.min.js" integrity="sha256-UnTxHm+zKuDPLfufgEMnKGXDl6fEIjtM+n1Q6lL73ok=" crossorigin="anonymous"></script>
    <!-- Bootstrap 3.4.1 JS (latest stable for Bootstrap 3) -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@3.4.1/dist/js/bootstrap.min.js" integrity="sha384-aJ21OjlMXNL5UyIl/XNwTMqvzeRMZH2w8c5cRVpzpU8Y5bApTppSuUkhZXN0VxHd" crossorigin="anonymous"></script>
    <!-- jQuery UI 1.13.2 (latest stable) -->
    <script src="https://code.jquery.com/ui/1.13.2/jquery-ui.min.js" integrity="sha256-lSjKY0/srUM9BE3dPm+c4fBo1dky2v27Gdjm2uoZaL0=" crossorigin="anonymous"></script>
  </head>

  <body>
      <div class=base-info>
        <p>
          <span class="status status-{{ task.status }}">{{ status_to_string(task.status) }}</span>
          <a class=callback href="/debug/{{ task.project }}?taskid={{ task.taskid }}">{{ task.project }}.{{ task.process.callback }}</a>
          &gt;
          <a class=url href="{{ task.url }}" target=_blank>{{ task.url }}</a>
          {% if task.status in (2, 3, 4) %}
          (<span class=last-crawl>{{ task.lastcrawltime | format_date }}</span> crawled )
          {% else %}
          (<span class=update-time>{{ task.updatetime | format_date }}</span> updated )
          {% endif %}
        </p>
      </div>
      <div class=more-info>
        <dl>
          <dt>taskid</dt>
          <dd>{{ task.taskid }}</dd>
          <dt>lastcrawltime</dt>
          <dd>{{ task.lastcrawltime }} ({{ task.lastcrawltime | format_date }})</dd>
          <dt>updatetime</dt>
          <dd>{{ task.updatetime }} ({{ task.updatetime | format_date }})</dd>
          # if task.schedule and task.schedule.exetime
          <dt>exetime</dt>
          <dd>{{ task.schedule.exetime }} ({{ task.schedule.exetime | format_date }})</dd>
          # endif

          # if task.track and task.track.fetch
          <dt>
            track.fetch
            <i class="fas {{ "fa-check" if task.track.fetch.ok else "fa-times" }}"></i>
            {{ (task.track.fetch.time * 1000) | round(2) }}ms
          </dt>
          <dd>{{ json.dumps(task.track.fetch, indent=2, ensure_ascii=False) }}</dd>
          # endif

          # if task.track and task.track.process
          <dt>
            track.process
            <i class="fas {{ "fa-check" if task.track.process.ok else "fa-times" }}"></i>
            {{ (task.track.process.time * 1000) | round(2) }}ms
            # if task.track.process.follows
              +{{ task.track.process.follows | int }}
            # endif
          </dt>
          <dd>
            #- if task.track.process.exception
            {{- task.track.process.exception or '' }}
            # endif
            #- if task.track.process.logs
              {{- task.track.process.logs or '' }}
            # endif
            {{- json.dumps(task.track.process, indent=2, ensure_ascii=False) -}}
          </dd>
          # endif
        </dl>
        <dl>
          #- set not_shown_keys = ('status', 'url', 'project', 'taskid', 'lastcrawltime', 'updatetime', 'track', )
          #- for key, value in task.items() if key not in not_shown_keys
          <dt>{{ key }}</dt>
          <dd>{{ json.dumps(value, indent=2, ensure_ascii=False) if value is mapping else value }}</dd>
          #- endfor
        </dl>
        # if result and result.get('result'):
        <dl>
          <dt>result</dt>
          <dd>{{ json.dumps(result['result'], indent=2, ensure_ascii=False) }}</dd>
        </dl>
        # endif
      </div>
  </body>
</html>
<!-- vim: set et sw=2 ts=2 sts=2 ff=unix fenc=utf8: -->

