<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <title>Results - {{ project }} - pyspider</title>
    <!--[if lt IE 9]>
      <script src="http://html5shim.googlecode.com/svn/trunk/html5.js"></script>
    <![endif]-->

    <meta name="description" content="results of {{ project }}">
    <meta name="author" content="binux">
    <!-- Bootstrap 3.4.1 CSS (latest stable for Bootstrap 3) -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@3.4.1/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-HSMxcRTRxnN+Bdg0JdbxYKrThecOKuH5zCYotlSAcp1+c8xmyTe9GYg1l9a69psu" crossorigin="anonymous">
    <!-- jQuery UI 1.13.2 CSS (latest stable) -->
    <link href="https://code.jquery.com/ui/1.13.2/themes/smoothness/jquery-ui.css" rel="stylesheet">
    <!-- Font Awesome 6 (latest) -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='result.min.css') }}" rel="stylesheet">

    <!-- jQuery 3.7.1 (latest stable) -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js" integrity="sha256-/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo=" crossorigin="anonymous"></script>
    <!-- jQuery Migrate 3.4.1 (latest stable) -->
    <script src="https://code.jquery.com/jquery-migrate-3.4.1.min.js" integrity="sha256-UnTxHm+zKuDPLfufgEMnKGXDl6fEIjtM+n1Q6lL73ok=" crossorigin="anonymous"></script>
    <!-- Bootstrap 3.4.1 JS (latest stable for Bootstrap 3) -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@3.4.1/dist/js/bootstrap.min.js" integrity="sha384-aJ21OjlMXNL5UyIl/XNwTMqvzeRMZH2w8c5cRVpzpU8Y5bApTppSuUkhZXN0VxHd" crossorigin="anonymous"></script>
    <!-- jQuery UI 1.13.2 (latest stable) -->
    <script src="https://code.jquery.com/ui/1.13.2/jquery-ui.min.js" integrity="sha256-lSjKY0/srUM9BE3dPm+c4fBo1dky2v27Gdjm2uoZaL0=" crossorigin="anonymous"></script>
  </head>

  <body>
    <div class="top-bar">
      <h1>{{ project }} - Results</h1>
      <div class="btn-group">
        <a href="/results/dump/{{ project }}.json"
          target="_blank" class="btn btn-default btn-sm">
          <i class="fas fa-download"></i>
          JSON</a>
        <a href="/results/dump/{{ project }}.txt"
          target="_blank" class="btn btn-default btn-sm">URL-JSON</a>
        <a href="/results/dump/{{ project }}.csv"
          target="_blank" class="btn btn-default btn-sm">CSV</a>
      </div>
    </div>
    # set common_fields, results = result_formater(results)
    <table class="table table-condensed table-striped">
      <thead>
        <th>url</th>
        <th></th>
        # for field in common_fields|sort
        <th>
          {{ field }}
        </th>
        # endfor
        <th>
          ...
        </th>
      </thead>
      <tbody>
        # for result in results
        <tr>
          <td>
            <a class=url href="/task/{{ project }}:{{ result.taskid }}" target=_blank>{{ result.url }}</a>
          </td>
          <td>
            <a class=open-url href="{{ result.url }}" target="_blank"><i class="fas fa-external-link-alt"></i></a>
          </td>
          # for field in common_fields|sort
          <td>{{ json.dumps(result.result_formated[field], ensure_ascii=False) | truncate(100, True) }}</td>
          # endfor
          <td>
            {{ json.dumps(result.others, ensure_ascii=False) | truncate(100, True) }}
          </td>
        # endfor
      </tbody>
    </table>

    <div class="pagination-wrap">
      <ul class="pagination">
        # set current_page = int(offset/limit) + (1 if offset%limit else 0)
        # set count = count if count is not none else 0
        # set total_page = int(count/limit) + (1 if count%limit else 0)
        <li class="{{ "disabled" if current_page - 1 <= 0 else "" }}">
          <a href="{% if current_page>1 %}/results?project={{ project }}&offset={{ (current_page-1)*limit }}&limit={{ limit }}{% endif %}">&laquo;</a>
        </li>
        # set prev = 0
        # for i in range(0, total_page):
        # if abs(i-0) < 2 or abs(i-total_page) < 3 or -2 < i-current_page < 5:
          # set prev = i
          <li class="{% if i == current_page %}active{% endif %}">
            <a href="/results?project={{ project }}&offset={{ i*limit }}&limit={{ limit }}">{{ i + 1 }}</a>
          </li>
        # elif prev == i-1:
        <li class="disabled"><a>…</a></li>
        # endif
        # endfor
        <li class="{{ "disabled" if current_page + 1 >= total_page else "" }}">
          <a href="{% if current_page+1<total_page %}/results?project={{ project }}&offset={{ (current_page+1)*limit }}&limit={{ limit }}{% endif %}">&raquo;</a>
        </li>
      </ul>
    </div>
  </body>
</html>
