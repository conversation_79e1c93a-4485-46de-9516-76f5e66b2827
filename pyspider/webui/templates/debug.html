<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <title>{{ project_name }} - Debugger - pyspider</title>
    <!--[if lt IE 9]>
      <script src="http://html5shim.googlecode.com/svn/trunk/html5.js"></script>
    <![endif]-->

    <meta name="description" content="pyspider - debugger - {{ project_name }}">
    <meta name="author" content="binux">

    <!-- Bootstrap 3.4.1 CSS (latest stable for Bootstrap 3) -->
    <link href="https://stackpath.bootstrapcdn.com/bootstrap/3.4.1/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-HSMxcRTRxnN+Bdg0JdbxYKrThecOKuH5zCYotlSAcp1+c8xmyTe9GYg1l9a69psu" crossorigin="anonymous">
    <!-- jQuery UI 1.13.2 CSS (latest stable) -->
    <link href="https://code.jquery.com/ui/1.13.2/themes/smoothness/jquery-ui.css" rel="stylesheet">
    <link href="{{ url_for('cdn', path='codemirror/5.20.2/codemirror.min.css') }}" rel="stylesheet">
    <!-- Font Awesome 6 (latest) -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" rel="stylesheet">
    <link href="{{ url_for('cdn', path='codemirror/5.20.2/addon/dialog/dialog.min.css') }}" rel="stylesheet">
    <link href="{{ url_for('cdn', path='codemirror/5.20.2/addon/lint/lint.min.css') }}" rel="stylesheet">
    <link href="{{ url_for('static', filename='debug.min.css') }}" rel="stylesheet">

    <style>
      /* Custom styles for python-log pre */
      {% raw %}
      #python-log pre {
        margin: 0;
        padding: 10px;
        color: #000000;
      }
      {% endraw %}
    </style>

    <!-- jQuery 3.7.1 (latest stable) -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js" integrity="sha256-/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo=" crossorigin="anonymous"></script>
    <script src="{{ url_for('cdn', path='jsonlint/1.6.0/jsonlint.min.js') }}"></script>
    <script src="{{ url_for('cdn', path='codemirror/5.20.2/codemirror.min.js') }}"></script>
    <script src="{{ url_for('cdn', path='codemirror/5.20.2/mode/xml/xml.min.js') }}"></script>
    <script src="{{ url_for('cdn', path='codemirror/5.20.2/mode/css/css.min.js') }}"></script>
    <script src="{{ url_for('cdn', path='codemirror/5.20.2/mode/javascript/javascript.min.js') }}"></script>
    <script src="{{ url_for('cdn', path='codemirror/5.20.2/mode/htmlmixed/htmlmixed.min.js') }}"></script>
    <script src="{{ url_for('cdn', path='codemirror/5.20.2/mode/python/python.min.js') }}"></script>
    <script src="{{ url_for('cdn', path='codemirror/5.20.2/addon/search/search.min.js') }}"></script>
    <script src="{{ url_for('cdn', path='codemirror/5.20.2/addon/search/searchcursor.min.js') }}"></script>
    <script src="{{ url_for('cdn', path='codemirror/5.20.2/addon/dialog/dialog.min.js') }}"></script>
    <script src="{{ url_for('cdn', path='codemirror/5.20.2/addon/selection/active-line.min.js') }}"></script>
    <script src="{{ url_for('cdn', path='codemirror/5.20.2/addon/runmode/runmode.min.js') }}"></script>
    <script src="{{ url_for('cdn', path='codemirror/5.20.2/addon/lint/lint.min.js') }}"></script>
    <script src="{{ url_for('cdn', path='codemirror/5.20.2/addon/lint/json-lint.min.js') }}"></script>
    <script src="{{ url_for('cdn', path='codemirror/2.36.0/formatting.min.js') }}"></script>
    <script src="{{ url_for('cdn', path='URI.js/1.11.2/URI.min.js') }}"></script>
    <!-- Bootstrap 3.4.1 JS (latest stable for Bootstrap 3) -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@3.4.1/dist/js/bootstrap.min.js" integrity="sha384-aJ21OjlMXNL5UyIl/XNwTMqvzeRMZH2w8c5cRVpzpU8Y5bApTppSuUkhZXN0VxHd" crossorigin="anonymous"></script>
    <!-- jQuery UI 1.13.2 (latest stable) -->
    <script src="https://code.jquery.com/ui/1.13.2/jquery-ui.min.js" integrity="sha256-lSjKY0/srUM9BE3dPm+c4fBo1dky2v27Gdjm2uoZaL0=" crossorigin="anonymous"></script>
  </head>

  <body>
    <section id="control">
      <div class="title pull-left"><a href="/">pyspider</a> &gt; {{ project_name }}</div>
      <div class="pull-right">
        <a href="http://docs.pyspider.org/" target="_blank">Documentation</a>
      </div>
    </section>

    <!-- フラッシュメッセージの表示 -->
    {% with messages = get_flashed_messages(with_categories=true) %}
      {% if messages %}
        <div class="container" style="margin-top: 10px;">
          {% for category, message in messages %}
            <div class="alert alert-{{ category }}">
              {{ message }}
              <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
              </button>
            </div>
          {% endfor %}
        </div>
      {% endif %}
    {% endwith %}
    <section id="editarea">
      <div id="left-area" class="debug-panel" style="right: 50%">
        <div id="task-panel">
          <div id="task-editor" class="editor">
            <div id="run-task-btn">run</div>
            <div id="undo-redo-btn-group">
              <a href="javascript:;" id="undo-btn"> &lt; </a>|<a href="javascript:;" id="redo-btn">&gt; </a>
              <span id="history-wrap" style="display: none;">|<a target=_blank id="history-link">history</a></span>
            </div>
          </div>
          <div id="python-log" style="display: none;">
            <pre style="display: none;"></pre>
            <div id="python-log-show"></div>
          </div>
          <div id="debug-tabs">
            <div id="tab-web" class="tab" style="display: none;">
              <div id="css-selector-helper">
                <input class="copy-selector-input" />
                <button class="btn copy-selector"><i class="fas fa-clipboard" title="copy css selector"></i></button>
                <button class="btn add-to-editor"><i class="fas fa-arrow-right" title="add to editor"></i></button>
              </div>
              <div class="iframe-box"></div>
            </div>
            <div id="tab-html" class="tab" style="display: none;"><pre class="cm-s-default"></pre></div>
            <div id="tab-follows" class="tab">
              {# <div class="newtask">
                <span class="task-callback">__callback__</span> &gt; <span class="task-url">__url__</span>
                <div class="task-run"><i class="fas fa-play"></i></div>
                <div class="task-more"> <i class="fas fa-ellipsis-h"></i> </div>
              </div> #}
            </div>
            <div id="tab-messages" class="tab" style="display: none;">
              <pre class="cm-s-default"></pre>
            </div>
          </div>
        </div>
        <ul id="tab-control">
          <li data-id="tab-messages">messages<span class="num" style="display: none;"></span></li>
          <li data-id="tab-follows">follows<span class="num" style="display: none;"></span></li>
          <li data-id="tab-html">html</li>
          <li data-id="tab-web" class="active">web</li>
          <li id="J-enable-css-selector-helper">enable css selector helper</li>
        </ul>
        <div class="overlay" style="display: none;"></div>
      </div>

      <div id="right-area" class="debug-panel" style="left: 50%">
        <div id="python-editor" class="editor focus">
          <div id="save-task-btn">save</div>
        </div>
        <div class="overlay" style="display: none;"></div>
      </div>
    </section>

    <script>
      var task_content = {{ task | tojson | tojson | safe }};
      var script_content = {{ script | tojson | safe }};
    </script>
    <script src="{{ url_for('static', filename='debug.min.js') }}"></script>
  </body>
</html>
<!-- vim: set et sw=2 ts=2 sts=2 ff=unix fenc=utf8 syntax=htmldjango: -->

