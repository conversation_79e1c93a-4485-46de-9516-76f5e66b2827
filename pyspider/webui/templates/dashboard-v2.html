<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PySpider Dashboard</title>
    <link rel="stylesheet" href="//cdn.jsdelivr.net/npm/vuetify@3.4.0/dist/vuetify.min.css">
    <link rel="stylesheet" href="//cdn.jsdelivr.net/npm/@mdi/font@7.2.96/css/materialdesignicons.min.css">
    <style>
        /* ダッシュボード用のスタイル */
        .dashboard-card {
            transition: all 0.3s;
        }
        .dashboard-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 20px rgba(0, 0, 0, 0.1) !important;
        }
        .metric-card {
            text-align: center;
            padding: 16px;
        }
        .metric-value {
            font-size: 36px;
            font-weight: bold;
        }
        .metric-label {
            font-size: 14px;
            color: rgba(0, 0, 0, 0.6);
        }

        /* メトリクスダッシュボード用のスタイル */
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 16px;
            margin-bottom: 24px;
        }
        .charts-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 16px;
        }
        .chart-container {
            padding: 16px;
        }
        .timestamp {
            color: #7f8c8d;
            font-size: 0.9rem;
        }
        @media (max-width: 768px) {
            .charts-container {
                grid-template-columns: 1fr;
            }
        }
    </style>
    <script src="//cdn.jsdelivr.net/npm/vue@3.3.4/dist/vue.global.prod.js"></script>
    <script src="//cdn.jsdelivr.net/npm/vuetify@3.4.0/dist/vuetify.min.js"></script>
    <script src="//cdn.jsdelivr.net/npm/chart.js"></script>

</head>
<body>
    <div id="app">
        <v-app>
            <v-app-bar color="primary" density="compact">
                <v-app-bar-title>PySpider Dashboard</v-app-bar-title>
                <v-spacer></v-spacer>
                <v-btn variant="text" href="/index-v2">
                    <v-icon>mdi-view-list</v-icon> Projects
                </v-btn>
                <v-btn variant="text" href="/metrics-dashboard">
                    <v-icon>mdi-chart-line</v-icon> Metrics
                </v-btn>
            </v-app-bar>

            <v-main>
                <v-container fluid>
                    <!-- Summary Cards -->
                    <v-row>
                        <v-col cols="12" md="3">
                            <v-card class="dashboard-card metric-card">
                                <div class="metric-value" :style="{color: '#4CAF50'}">
                                    ${ totalTasks }
                                </div>
                                <div class="metric-label">
                                    Total Tasks
                                </div>
                            </v-card>
                        </v-col>
                        <v-col cols="12" md="3">
                            <v-card class="dashboard-card metric-card">
                                <div class="metric-value" :style="{color: '#2196F3'}">
                                    ${ totalPending }
                                </div>
                                <div class="metric-label">
                                    Pending Tasks
                                </div>
                            </v-card>
                        </v-col>
                        <v-col cols="12" md="3">
                            <v-card class="dashboard-card metric-card">
                                <div class="metric-value" :style="{color: '#4CAF50'}">
                                    ${ totalSuccess }
                                </div>
                                <div class="metric-label">
                                    Successful Tasks
                                </div>
                            </v-card>
                        </v-col>
                        <v-col cols="12" md="3">
                            <v-card class="dashboard-card metric-card">
                                <div class="metric-value" :style="{color: '#F44336'}">
                                    ${ totalError }
                                </div>
                                <div class="metric-label">
                                    Failed Tasks
                                </div>
                            </v-card>
                        </v-col>
                    </v-row>

                    <!-- System Metrics Section -->
                    <h2 class="text-h6 mt-6 mb-4">システムメトリクス</h2>
                    <v-row>
                        <v-col cols="12">
                            <v-card>
                                <v-card-title class="d-flex align-center">
                                    システム統計情報
                                    <v-spacer></v-spacer>
                                    <v-btn color="primary" @click="fetchMetricsData" icon>
                                        <v-icon>mdi-refresh</v-icon>
                                    </v-btn>
                                </v-card-title>
                                <v-card-text>
                                    <!-- Metrics Grid -->
                                    <div class="metrics-grid">
                                        <v-card v-for="(metric, key) in displayMetrics" :key="key" class="metric-card">
                                            <div class="metric-label">${ metric.label }</div>
                                            <div class="metric-value" :style="{color: getMetricColor(key)}">${ metric.value }</div>
                                            <div class="text-caption">${ metric.subtitle }</div>
                                        </v-card>
                                    </div>

                                    <!-- Charts -->
                                    <div class="charts-container mt-4">
                                        <v-card class="chart-container">
                                            <v-card-title class="text-subtitle-1">システムリソース使用率</v-card-title>
                                            <canvas id="systemResourcesChart"></canvas>
                                        </v-card>
                                        <v-card class="chart-container">
                                            <v-card-title class="text-subtitle-1">プロセスリソース使用率</v-card-title>
                                            <canvas id="processResourcesChart"></canvas>
                                        </v-card>
                                    </div>

                                    <div class="charts-container mt-4">
                                        <v-card class="chart-container">
                                            <v-card-title class="text-subtitle-1">スケジューラ統計</v-card-title>
                                            <canvas id="schedulerStatsChart"></canvas>
                                        </v-card>
                                        <v-card class="chart-container">
                                            <v-card-title class="text-subtitle-1">タスク統計（24時間）</v-card-title>
                                            <canvas id="taskStatsChart"></canvas>
                                        </v-card>
                                    </div>

                                    <div class="timestamp text-center mt-4" id="timestamp">
                                        最終更新: ${ metricsLastUpdated }
                                    </div>
                                </v-card-text>
                            </v-card>
                        </v-col>
                    </v-row>

                </v-container>
            </v-main>

            <!-- Snackbar for notifications -->
            <v-snackbar v-model="snackbar.show" :color="snackbar.color" location="top" timeout="3000">
                ${ snackbar.text }
                <template v-slot:actions>
                    <v-btn variant="text" @click="snackbar.show = false">Close</v-btn>
                </template>
            </v-snackbar>
        </v-app>
    </div>

    <script>
        window.projects = {{ projects|safe }};
        console.log('Projects data:', window.projects);

        const { createApp } = Vue;
        const vuetify = Vuetify.createVuetify();

        // Vue.jsのデリミタを変更
        const app = createApp({
            delimiters: ['${', '}'],
            data() {
                return {
                    projects: window.projects || [],
                    projectCounters: {},
                    counterInterval: null,
                    metricsInterval: null,
                    metricsData: {},
                    displayMetrics: {},
                    metricsLastUpdated: '読み込み中...',
                    snackbar: {
                        show: false,
                        text: '',
                        color: 'success'
                    }
                }
            },
            computed: {
                totalTasks() {
                    let total = 0;
                    for (const projectName in this.projectCounters) {
                        const counter = this.getProjectCounter(projectName);
                        if (counter) {
                            total += counter.task || 0;
                        }
                    }
                    return total;
                },
                totalPending() {
                    let total = 0;
                    for (const projectName in this.projectCounters) {
                        total += this.getProgressCount(projectName, 'pending');
                    }
                    return total;
                },
                totalSuccess() {
                    let total = 0;
                    for (const projectName in this.projectCounters) {
                        total += this.getProgressCount(projectName, 'success');
                    }
                    return total;
                },
                totalError() {
                    let total = 0;
                    for (const projectName in this.projectCounters) {
                        total += this.getProgressCount(projectName, 'failed');
                    }
                    return total;
                }
            },
            mounted() {
                // 初期データの取得
                this.fetchProjects();
                this.fetchCounterData();
                this.initCharts();
                this.fetchMetricsData();

                // 定期的なデータ更新
                this.counterInterval = setInterval(() => {
                    this.fetchCounterData();
                }, 5000);

                this.metricsInterval = setInterval(() => {
                    this.fetchMetricsData();
                }, 30000); // 30秒ごとに更新
            },
            beforeUnmount() {
                // インターバルのクリア
                if (this.counterInterval) clearInterval(this.counterInterval);
                if (this.metricsInterval) clearInterval(this.metricsInterval);
            },
            methods: {
                // プロジェクト一覧を取得
                fetchProjects() {
                    fetch('/index-v2/projects')
                        .then(response => response.json())
                        .then(data => {
                            this.projects = data;
                            console.log('Projects data:', data);
                        })
                        .catch(error => {
                            console.error('Error fetching projects:', error);
                        });
                },

                // カウンターデータを取得
                fetchCounterData() {
                    fetch('/counter')
                        .then(response => response.json())
                        .then(data => {
                            this.projectCounters = data;
                            console.log('Counter data:', data);
                        })
                        .catch(error => {
                            console.error('Error fetching counter data:', error);
                        });
                },

                // プロジェクトのカウンターデータを取得
                getProjectCounter(projectName) {
                    if (!this.projectCounters[projectName]) {
                        return null;
                    }
                    return this.projectCounters[projectName]['all'] || null;
                },

                // プログレスバーのカウントを取得
                getProgressCount(projectName, type) {
                    const counter = this.getProjectCounter(projectName);
                    if (!counter) return 0;
                    return counter[type] || 0;
                },

                // スナックバーを表示
                showSnackbar(text, color = 'success') {
                    this.snackbar.text = text;
                    this.snackbar.color = color;
                    this.snackbar.show = true;
                },

                // メトリクスデータを取得
                async fetchMetricsData() {
                    try {
                        const response = await fetch('/metrics');
                        const data = await response.json();
                        this.metricsData = data;

                        // メトリクスを表示用に整形
                        this.updateDisplayMetrics(data);

                        // チャートを更新
                        this.updateCharts(data);

                        // タイムスタンプを表示
                        const date = new Date(data.timestamp * 1000);
                        this.metricsLastUpdated = date.toLocaleString();
                    } catch (error) {
                        console.error('メトリクスの取得に失敗しました:', error);
                        this.showSnackbar('メトリクスの取得に失敗しました', 'error');
                    }
                },

                // メトリクスの色を取得
                getMetricColor(key) {
                    const colorMap = {
                        cpu: '#FF5252',
                        memory: '#2196F3',
                        disk: '#FFC107',
                        processCpu: '#FF5252',
                        processMemory: '#2196F3',
                        processMemoryPercent: '#2196F3',
                        connections: '#9C27B0',
                        openFiles: '#607D8B',
                        threads: '#795548',
                        uptime: '#009688',
                        queueSize: '#FF9800',
                        processingTasks: '#E91E63',
                        totalTasks24h: '#4CAF50'
                    };

                    return colorMap[key] || '#333333';
                },

                // メトリクスを表示用に整形
                updateDisplayMetrics(data) {
                    const system = data.system || {};
                    const scheduler = data.scheduler || {};

                    this.displayMetrics = {
                        cpu: {
                            label: 'CPU使用率',
                            value: `${system.cpu_percent?.toFixed(1) || 0}%`,
                            subtitle: 'システム全体'
                        },
                        memory: {
                            label: 'メモリ使用率',
                            value: `${system.memory_percent?.toFixed(1) || 0}%`,
                            subtitle: 'システム全体'
                        },
                        disk: {
                            label: 'ディスク使用率',
                            value: `${system.disk_percent?.toFixed(1) || 0}%`,
                            subtitle: 'システム全体'
                        },
                        processCpu: {
                            label: 'プロセスCPU',
                            value: `${system.process_cpu_percent?.toFixed(1) || 0}%`,
                            subtitle: 'PySpiderプロセス'
                        },
                        processMemory: {
                            label: 'プロセスメモリ',
                            value: `${system.process_memory_mb?.toFixed(2) || 0} MB`,
                            subtitle: 'PySpiderプロセス'
                        },
                        queueSize: {
                            label: 'キューサイズ',
                            value: scheduler.queue_size || 0,
                            subtitle: 'スケジューラキュー'
                        },
                        processingTasks: {
                            label: '処理中タスク',
                            value: scheduler.processing_tasks || 0,
                            subtitle: '現在処理中'
                        },
                        totalTasks24h: {
                            label: '24時間タスク数',
                            value: scheduler.total_tasks_24h || 0,
                            subtitle: '過去24時間'
                        }
                    };
                },

                // チャートを初期化
                initCharts() {
                    // システムリソースチャート
                    const systemCtx = document.getElementById('systemResourcesChart').getContext('2d');
                    window.systemResourcesChart = new Chart(systemCtx, {
                        type: 'bar',
                        data: {
                            labels: ['CPU使用率 (%)', 'メモリ使用率 (%)', 'ディスク使用率 (%)'],
                            datasets: [{
                                label: 'システムリソース',
                                data: [0, 0, 0],
                                backgroundColor: [
                                    'rgba(255, 99, 132, 0.5)',
                                    'rgba(54, 162, 235, 0.5)',
                                    'rgba(255, 206, 86, 0.5)'
                                ],
                                borderColor: [
                                    'rgba(255, 99, 132, 1)',
                                    'rgba(54, 162, 235, 1)',
                                    'rgba(255, 206, 86, 1)'
                                ],
                                borderWidth: 1
                            }]
                        },
                        options: {
                            responsive: true,
                            plugins: {
                                legend: {
                                    display: false
                                }
                            },
                            scales: {
                                y: {
                                    beginAtZero: true,
                                    max: 100,
                                    title: {
                                        display: true,
                                        text: '使用率 (%)'
                                    }
                                }
                            }
                        }
                    });

                    // プロセスリソースチャート
                    const processCtx = document.getElementById('processResourcesChart').getContext('2d');
                    window.processResourcesChart = new Chart(processCtx, {
                        type: 'bar',
                        data: {
                            labels: ['CPU使用率 (%)', 'メモリ使用率 (%)'],
                            datasets: [{
                                label: 'プロセスリソース',
                                data: [0, 0],
                                backgroundColor: [
                                    'rgba(75, 192, 192, 0.5)',
                                    'rgba(153, 102, 255, 0.5)'
                                ],
                                borderColor: [
                                    'rgba(75, 192, 192, 1)',
                                    'rgba(153, 102, 255, 1)'
                                ],
                                borderWidth: 1
                            }]
                        },
                        options: {
                            responsive: true,
                            plugins: {
                                legend: {
                                    display: false
                                }
                            },
                            scales: {
                                y: {
                                    beginAtZero: true,
                                    max: 100,
                                    title: {
                                        display: true,
                                        text: '使用率 (%)'
                                    }
                                }
                            }
                        }
                    });

                    // スケジューラ統計チャート
                    const schedulerCtx = document.getElementById('schedulerStatsChart').getContext('2d');
                    window.schedulerStatsChart = new Chart(schedulerCtx, {
                        type: 'bar',
                        data: {
                            labels: ['キューサイズ', '処理中タスク', '保留中タスク'],
                            datasets: [{
                                label: 'スケジューラ統計',
                                data: [0, 0, 0],
                                backgroundColor: [
                                    'rgba(255, 159, 64, 0.5)',
                                    'rgba(255, 99, 132, 0.5)',
                                    'rgba(54, 162, 235, 0.5)'
                                ],
                                borderColor: [
                                    'rgba(255, 159, 64, 1)',
                                    'rgba(255, 99, 132, 1)',
                                    'rgba(54, 162, 235, 1)'
                                ],
                                borderWidth: 1
                            }]
                        },
                        options: {
                            responsive: true,
                            plugins: {
                                legend: {
                                    display: false
                                }
                            },
                            scales: {
                                y: {
                                    beginAtZero: true,
                                    title: {
                                        display: true,
                                        text: '数'
                                    }
                                }
                            }
                        }
                    });

                    // タスク統計チャート
                    const taskCtx = document.getElementById('taskStatsChart').getContext('2d');
                    window.taskStatsChart = new Chart(taskCtx, {
                        type: 'pie',
                        data: {
                            labels: ['成功', '失敗', 'その他'],
                            datasets: [{
                                label: 'タスク統計（24時間）',
                                data: [0, 0, 0],
                                backgroundColor: [
                                    'rgba(75, 192, 192, 0.5)',
                                    'rgba(255, 99, 132, 0.5)',
                                    'rgba(201, 203, 207, 0.5)'
                                ],
                                borderColor: [
                                    'rgba(75, 192, 192, 1)',
                                    'rgba(255, 99, 132, 1)',
                                    'rgba(201, 203, 207, 1)'
                                ],
                                borderWidth: 1
                            }]
                        },
                        options: {
                            responsive: true
                        }
                    });
                },

                // チャートを更新
                updateCharts(data) {
                    const system = data.system || {};
                    const scheduler = data.scheduler || {};

                    // システムリソースチャートを更新
                    window.systemResourcesChart.data.datasets[0].data = [
                        system.cpu_percent || 0,
                        system.memory_percent || 0,
                        system.disk_percent || 0
                    ];
                    window.systemResourcesChart.update();

                    // プロセスリソースチャートを更新
                    window.processResourcesChart.data.datasets[0].data = [
                        system.process_cpu_percent || 0,
                        (system.process_memory_percent || 0) * 100
                    ];
                    window.processResourcesChart.update();

                    // スケジューラ統計チャートを更新
                    window.schedulerStatsChart.data.datasets[0].data = [
                        scheduler.queue_size || 0,
                        scheduler.processing_tasks || 0,
                        scheduler.pending_tasks || 0
                    ];
                    window.schedulerStatsChart.update();

                    // タスク統計チャートを更新
                    const successTasks = scheduler.success_tasks_24h || 0;
                    const failedTasks = scheduler.failed_tasks_24h || 0;
                    const totalTasks = scheduler.total_tasks_24h || 0;
                    const otherTasks = Math.max(0, totalTasks - successTasks - failedTasks);

                    window.taskStatsChart.data.datasets[0].data = [
                        successTasks,
                        failedTasks,
                        otherTasks
                    ];
                    window.taskStatsChart.update();
                }
            }
        });

        app.use(vuetify);
        app.mount('#app');
    </script>
</body>
</html>
