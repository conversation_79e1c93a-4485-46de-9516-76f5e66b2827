<!DOCTYPE html>
<html lang="ja">
  <head>
    <meta charset="utf-8">
    <title>Tasks - pyspider</title>
    <!--[if lt IE 9]>
      <script src="http://html5shim.googlecode.com/svn/trunk/html5.js"></script>
    <![endif]-->

    <meta name="description" content="last actived tasks">
    <meta name="author" content="binux">
    <!-- Bootstrap 3.4.1 CSS (latest stable for Bootstrap 3) -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@3.4.1/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-HSMxcRTRxnN+Bdg0JdbxYKrThecOKuH5zCYotlSAcp1+c8xmyTe9GYg1l9a69psu" crossorigin="anonymous">
    <!-- jQuery UI 1.13.2 CSS (latest stable) -->
    <link href="https://code.jquery.com/ui/1.13.2/themes/smoothness/jquery-ui.css" rel="stylesheet">
    <!-- Font Awesome 6 (latest) -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='tasks.min.css') }}" rel="stylesheet">

    <!-- jQuery 3.7.1 (latest stable) -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js" integrity="sha256-/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo=" crossorigin="anonymous"></script>
    <!-- jQuery Migrate 3.4.1 (latest stable) -->
    <script src="https://code.jquery.com/jquery-migrate-3.4.1.min.js" integrity="sha256-UnTxHm+zKuDPLfufgEMnKGXDl6fEIjtM+n1Q6lL73ok=" crossorigin="anonymous"></script>
    <!-- Bootstrap 3.4.1 JS (latest stable for Bootstrap 3) -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@3.4.1/dist/js/bootstrap.min.js" integrity="sha384-aJ21OjlMXNL5UyIl/XNwTMqvzeRMZH2w8c5cRVpzpU8Y5bApTppSuUkhZXN0VxHd" crossorigin="anonymous"></script>
    <!-- jQuery UI 1.13.2 (latest stable) -->
    <script src="https://code.jquery.com/ui/1.13.2/jquery-ui.min.js" integrity="sha256-lSjKY0/srUM9BE3dPm+c4fBo1dky2v27Gdjm2uoZaL0=" crossorigin="anonymous"></script>
  </head>

  <body>
    <ol class=tasks>
      {% for task in tasks | sort(reverse=True, attribute='updatetime') %}
      <li class=task>
        {% if task.status %}
          <span class="status status-{{ task.status }}">{{ status_to_string(task.status) }}</span>
        {% elif task.track %}
        <span class="status status-3">
          {% set fetchok = task.track.fetch and task.track.fetch.ok %}
          {% set processok = task.track.process and task.track.process.ok %}
          {%- if not fetchok -%}
          FETCH_ERROR
          {%- elif not processok -%}
          PROCESS_ERROR
          {%- endif -%}
        </span>
        {% else %}
          <span class="status status-4 }}">ERROR</span>
        {% endif %}

        <a class=callback href="/debug/{{ task.project }}?taskid={{ task.taskid }}" target=_blank>{{ task.project }}</a>
        &gt;
        <a class=url href="/task/{{ task.project }}:{{ task.taskid }}" title="{{ task.url }}" target=_blank>{{ task.url }}</a>

        <span class=update-time>{{ task.updatetime | format_date }}</span>

        {% if task.track and task.track.fetch %}
        <span span=use-time>
          {{- '%.1f' | format(task.track.fetch.time * 1000) }}+{{ '%.2f' | format(task.track.process.time * 1000 if task.track and task.track.process else 0) }}ms
        </span>
        {% endif %}

        <span span=follows>
        {% if task.track and task.track.process %}
        +{{ task.track.process.follows | int }}
        {% endif %}
        </span>
      </li>
      {% endfor %}
    </ol>
  </body>
</html>
<!-- vim: set et sw=2 ts=2 sts=2 ff=unix fenc=utf8: -->

