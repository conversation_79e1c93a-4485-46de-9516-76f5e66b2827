<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PySpider アラートダッシュボード</title>
    <link href="//cdn.jsdelivr.net/npm/vuetify@3.4.0/dist/vuetify.min.css" rel="stylesheet">
    <link href="//cdn.jsdelivr.net/npm/@mdi/font@7.2.96/css/materialdesignicons.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
        }
        .timestamp {
            text-align: center;
            color: #7f8c8d;
            margin-top: 20px;
        }
    </style>
    <script src="//cdn.jsdelivr.net/npm/vue@3.3.4/dist/vue.global.prod.js"></script>
    <script src="//cdn.jsdelivr.net/npm/vuetify@3.4.0/dist/vuetify.min.js"></script>
</head>
<body>
    <div id="app">
        <v-app>
            <v-app-bar color="primary" density="compact">
                <v-app-bar-title>PySpider アラートダッシュボード</v-app-bar-title>
                <v-spacer></v-spacer>
                <v-btn variant="text" href="/metrics-dashboard">
                    <v-icon>mdi-chart-line</v-icon> メトリクス
                </v-btn>
                <v-btn variant="text" href="/index-v2">
                    <v-icon>mdi-view-list</v-icon> プロジェクト
                </v-btn>
                <v-btn variant="text" href="/">
                    <v-icon>mdi-home</v-icon> ホーム
                </v-btn>
            </v-app-bar>

            <v-main>
                <v-container>
                    <v-row>
                        <v-col cols="12">
                            <!-- アラートルール一覧 -->
                            <v-card class="mb-4">
                                <v-card-title class="text-h5">
                                    アラートルール
                                    <v-spacer></v-spacer>
                                    <v-btn color="primary" @click="fetchAlerts" icon>
                                        <v-icon>mdi-refresh</v-icon>
                                    </v-btn>
                                </v-card-title>
                                <v-card-text>
                                    <v-table>
                                        <thead>
                                            <tr>
                                                <th>名前</th>
                                                <th>説明</th>
                                                <th>条件</th>
                                                <th>重要度</th>
                                                <th>持続時間</th>
                                                <th>状態</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr v-for="rule in alertRules" :key="rule.name">
                                                <td>[[ rule.name ]]</td>
                                                <td>[[ rule.description ]]</td>
                                                <td><code>[[ rule.condition ]]</code></td>
                                                <td>
                                                    <v-chip :color="getSeverityColor(rule.severity)" size="small">
                                                        [[ rule.severity ]]
                                                    </v-chip>
                                                </td>
                                                <td>[[ formatDuration(rule.duration) ]]</td>
                                                <td>
                                                    <v-chip :color="getStatusColor(rule.status)" size="small">
                                                        [[ rule.status || '正常' ]]
                                                    </v-chip>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </v-table>
                                </v-card-text>
                            </v-card>

                            <!-- アクティブなアラート一覧 -->
                            <v-card class="mb-4">
                                <v-card-title class="text-h5">
                                    アクティブなアラート
                                    <v-spacer></v-spacer>
                                    <v-btn color="primary" @click="fetchAlerts" icon>
                                        <v-icon>mdi-refresh</v-icon>
                                    </v-btn>
                                </v-card-title>
                                <v-card-text>
                                    <div v-if="activeAlerts.length === 0" class="text-center pa-4">
                                        <v-icon color="success" size="large">mdi-check-circle</v-icon>
                                        <div class="text-h6 mt-2">アクティブなアラートはありません</div>
                                    </div>
                                    <v-list v-else>
                                        <v-list-item v-for="alert in activeAlerts" :key="alert.id" :prepend-icon="getAlertIcon(alert.severity)">
                                            <v-list-item-title>
                                                <v-chip :color="getSeverityColor(alert.severity)" size="small" class="mr-2">
                                                    [[ alert.severity ]]
                                                </v-chip>
                                                [[ alert.name ]]
                                            </v-list-item-title>
                                            <v-list-item-subtitle>[[ alert.description ]]</v-list-item-subtitle>
                                            <v-list-item-subtitle>
                                                <small>開始時間: [[ formatTimestamp(alert.start_time) ]]</small>
                                            </v-list-item-subtitle>
                                        </v-list-item>
                                    </v-list>
                                </v-card-text>
                            </v-card>

                            <!-- 最近解決したアラート -->
                            <v-card>
                                <v-card-title class="text-h5">
                                    最近解決したアラート
                                    <v-spacer></v-spacer>
                                    <v-btn color="primary" @click="fetchAlerts" icon>
                                        <v-icon>mdi-refresh</v-icon>
                                    </v-btn>
                                </v-card-title>
                                <v-card-text>
                                    <div v-if="resolvedAlerts.length === 0" class="text-center pa-4">
                                        <v-icon color="info" size="large">mdi-information</v-icon>
                                        <div class="text-h6 mt-2">最近解決したアラートはありません</div>
                                    </div>
                                    <v-list v-else>
                                        <v-list-item v-for="alert in resolvedAlerts" :key="alert.id" :prepend-icon="getAlertIcon(alert.severity)">
                                            <v-list-item-title>
                                                <v-chip :color="getSeverityColor(alert.severity)" size="small" class="mr-2">
                                                    [[ alert.severity ]]
                                                </v-chip>
                                                [[ alert.name ]]
                                            </v-list-item-title>
                                            <v-list-item-subtitle>[[ alert.description ]]</v-list-item-subtitle>
                                            <v-list-item-subtitle>
                                                <small>解決時間: [[ formatTimestamp(alert.end_time) ]]</small>
                                            </v-list-item-subtitle>
                                        </v-list-item>
                                    </v-list>
                                </v-card-text>
                            </v-card>

                            <div class="timestamp" id="timestamp">
                                最終更新: [[ lastUpdated ]]
                            </div>
                        </v-col>
                    </v-row>
                </v-container>
            </v-main>
        </v-app>
    </div>

    <script>
        const { createApp } = Vue;
        const vuetify = Vuetify.createVuetify({
            theme: {
                defaultTheme: 'light'
            }
        });

        const app = createApp({
            delimiters: ['[[', ']]'],
            data() {
                return {
                    alertRules: [],
                    activeAlerts: [],
                    resolvedAlerts: [],
                    lastUpdated: '読み込み中...',
                    refreshInterval: null
                }
            },
            mounted() {
                this.fetchAlerts();
                this.startAutoRefresh();
            },
            beforeUnmount() {
                this.stopAutoRefresh();
            },
            methods: {
                async fetchAlerts() {
                    try {
                        const response = await fetch('/api/v2/alerts');
                        const data = await response.json();
                        
                        this.alertRules = data.rules || [];
                        this.activeAlerts = data.active_alerts || [];
                        this.resolvedAlerts = data.resolved_alerts || [];
                        
                        // タイムスタンプを表示
                        const date = new Date();
                        this.lastUpdated = date.toLocaleString();
                    } catch (error) {
                        console.error('アラート情報の取得に失敗しました:', error);
                    }
                },
                
                startAutoRefresh() {
                    this.refreshInterval = setInterval(() => {
                        this.fetchAlerts();
                    }, 30000); // 30秒ごとに更新
                },
                
                stopAutoRefresh() {
                    if (this.refreshInterval) {
                        clearInterval(this.refreshInterval);
                        this.refreshInterval = null;
                    }
                },
                
                // 重要度に応じた色を返す
                getSeverityColor(severity) {
                    if (!severity) return 'grey';
                    switch (severity.toLowerCase()) {
                        case 'critical': return 'error';
                        case 'warning': return 'warning';
                        case 'info': return 'info';
                        default: return 'grey';
                    }
                },
                
                // アラート状態に応じた色を返す
                getStatusColor(status) {
                    if (!status) return 'success';
                    switch (status.toLowerCase()) {
                        case 'firing': return 'error';
                        case 'pending': return 'warning';
                        case 'resolved': return 'success';
                        default: return 'grey';
                    }
                },
                
                // アラートの重要度に応じたアイコンを返す
                getAlertIcon(severity) {
                    if (!severity) return 'mdi-alert';
                    switch (severity.toLowerCase()) {
                        case 'critical': return 'mdi-alert-circle';
                        case 'warning': return 'mdi-alert';
                        case 'info': return 'mdi-information';
                        default: return 'mdi-alert';
                    }
                },
                
                // 持続時間を人間が読みやすい形式にフォーマット
                formatDuration(seconds) {
                    if (!seconds) return '0秒';
                    if (seconds < 60) return `${seconds}秒`;
                    if (seconds < 3600) return `${Math.floor(seconds / 60)}分`;
                    if (seconds < 86400) return `${Math.floor(seconds / 3600)}時間`;
                    return `${Math.floor(seconds / 86400)}日`;
                },
                
                // タイムスタンプをフォーマット
                formatTimestamp(timestamp) {
                    if (!timestamp) return '不明';
                    return new Date(timestamp * 1000).toLocaleString();
                }
            }
        });

        app.use(vuetify);
        app.mount('#app');
    </script>
</body>
</html>
