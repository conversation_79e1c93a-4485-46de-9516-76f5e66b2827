<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PySpider Prometheusメトリクス</title>
    <link href="//cdn.jsdelivr.net/npm/vuetify@3.4.0/dist/vuetify.min.css" rel="stylesheet">
    <link href="//cdn.jsdelivr.net/npm/@mdi/font@7.2.96/css/materialdesignicons.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
        }
        .timestamp {
            text-align: center;
            color: #7f8c8d;
            margin-top: 20px;
        }
        pre {
            white-space: pre-wrap;
            word-wrap: break-word;
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
        }
        .metric-name {
            font-weight: bold;
            color: #2c3e50;
        }
        .metric-help {
            color: #7f8c8d;
            font-style: italic;
        }
        .metric-type {
            color: #3498db;
            font-weight: bold;
        }
        .metric-value {
            color: #e74c3c;
            font-weight: bold;
        }
    </style>
    <script src="//cdn.jsdelivr.net/npm/vue@3.3.4/dist/vue.global.prod.js"></script>
    <script src="//cdn.jsdelivr.net/npm/vuetify@3.4.0/dist/vuetify.min.js"></script>
</head>
<body>
    <div id="app">
        <v-app>
            <v-app-bar color="primary" density="compact">
                <v-app-bar-title>PySpider Prometheusメトリクス</v-app-bar-title>
                <v-spacer></v-spacer>
                <v-btn variant="text" href="/metrics-dashboard">
                    <v-icon>mdi-chart-line</v-icon> メトリクス
                </v-btn>
                <v-btn variant="text" href="/alerts-dashboard">
                    <v-icon>mdi-bell</v-icon> アラート
                </v-btn>
                <v-btn variant="text" href="/index-v2">
                    <v-icon>mdi-view-list</v-icon> プロジェクト
                </v-btn>
                <v-btn variant="text" href="/">
                    <v-icon>mdi-home</v-icon> ホーム
                </v-btn>
            </v-app-bar>

            <v-main>
                <v-container>
                    <v-row>
                        <v-col cols="12">
                            <v-card>
                                <v-card-title class="text-h5">
                                    Prometheusメトリクス
                                    <v-spacer></v-spacer>
                                    <v-btn color="primary" @click="fetchMetrics" icon>
                                        <v-icon>mdi-refresh</v-icon>
                                    </v-btn>
                                    <v-btn color="secondary" href="/prometheus/metrics" target="_blank" icon>
                                        <v-icon>mdi-open-in-new</v-icon>
                                    </v-btn>
                                </v-card-title>
                                <v-card-text>
                                    <v-text-field
                                        v-model="search"
                                        label="メトリクスを検索"
                                        prepend-icon="mdi-magnify"
                                        clearable
                                    ></v-text-field>

                                    <div v-if="loading" class="text-center pa-4">
                                        <v-progress-circular indeterminate color="primary"></v-progress-circular>
                                        <div class="mt-2">メトリクスを読み込み中...</div>
                                    </div>

                                    <div v-else-if="error" class="text-center pa-4">
                                        <v-icon color="error" size="large">mdi-alert-circle</v-icon>
                                        <div class="text-h6 mt-2">エラーが発生しました</div>
                                        <div class="text-body-1">[[ error ]]</div>
                                    </div>

                                    <div v-else>
                                        <div v-if="metrics.length === 0" class="text-center pa-4">
                                            <v-icon color="info" size="large">mdi-information</v-icon>
                                            <div class="text-h6 mt-2">メトリクスを読み込み中...</div>
                                            <div class="text-body-1">しばらくお待ちください</div>
                                        </div>
                                        <div v-else v-for="(metric, index) in filteredMetrics" :key="index" class="mb-4">
                                            <div class="metric-name"># [[ metric.name ]]</div>
                                            <div v-if="metric.help" class="metric-help">[[ metric.help ]]</div>
                                            <div class="metric-type">TYPE [[ metric.name ]] [[ metric.type ]]</div>
                                            <div v-for="(sample, sampleIndex) in metric.samples" :key="sampleIndex">
                                                [[ sample.name ]]
                                                <span v-if="Object.keys(sample.labels).length > 0">
                                                    {
                                                    <span v-for="(value, label) in sample.labels" :key="label">
                                                        [[ label ]]="[[ value ]]",
                                                    </span>
                                                    }
                                                </span>
                                                <span class="metric-value">[[ sample.value ]]</span>
                                            </div>
                                        </div>
                                    </div>
                                </v-card-text>
                            </v-card>

                            <div class="timestamp" id="timestamp">
                                最終更新: [[ lastUpdated ]]
                            </div>
                        </v-col>
                    </v-row>
                </v-container>
            </v-main>
        </v-app>
    </div>

    <script>
        const { createApp } = Vue;
        const vuetify = Vuetify.createVuetify({
            theme: {
                defaultTheme: 'light'
            }
        });

        const app = createApp({
            delimiters: ['[[', ']]'],
            data() {
                return {
                    metrics: [],
                    loading: true,
                    error: null,
                    lastUpdated: '読み込み中...',
                    refreshInterval: null,
                    search: '',
                    rawMetrics: ''
                }
            },
            computed: {
                filteredMetrics() {
                    if (!this.search) return this.metrics;
                    const searchLower = this.search.toLowerCase();
                    return this.metrics.filter(metric =>
                        metric.name.toLowerCase().includes(searchLower) ||
                        (metric.help && metric.help.toLowerCase().includes(searchLower))
                    );
                }
            },
            mounted() {
                this.fetchMetrics();
                this.startAutoRefresh();
            },
            beforeUnmount() {
                this.stopAutoRefresh();
            },
            methods: {
                async fetchMetrics() {
                    this.loading = true;
                    this.error = null;

                    try {
                        const response = await fetch('/prometheus/metrics');
                        if (!response.ok) {
                            throw new Error(`HTTP error! status: ${response.status}`);
                        }

                        const text = await response.text();
                        this.rawMetrics = text;

                        // メトリクスをパースする
                        this.metrics = this.parsePrometheusMetrics(text);

                        // タイムスタンプを表示
                        const date = new Date();
                        this.lastUpdated = date.toLocaleString();
                    } catch (error) {
                        console.error('メトリクスの取得に失敗しました:', error);
                        this.error = error.message;
                    } finally {
                        this.loading = false;
                    }
                },

                parsePrometheusMetrics(text) {
                    const lines = text.split('\n');
                    const metrics = [];
                    let currentMetric = null;

                    for (const line of lines) {
                        if (line.startsWith('# HELP ')) {
                            const parts = line.substring(7).split(' ');
                            const name = parts[0];
                            const help = parts.slice(1).join(' ');

                            currentMetric = { name, help, type: '', samples: [] };
                            metrics.push(currentMetric);
                        } else if (line.startsWith('# TYPE ')) {
                            const parts = line.substring(7).split(' ');
                            const name = parts[0];
                            const type = parts[1];

                            if (currentMetric && currentMetric.name === name) {
                                currentMetric.type = type;
                            } else {
                                currentMetric = { name, help: '', type, samples: [] };
                                metrics.push(currentMetric);
                            }
                        } else if (line && !line.startsWith('#') && currentMetric) {
                            // メトリクスの値を解析
                            const sample = this.parseSample(line);
                            if (sample) {
                                currentMetric.samples.push(sample);
                            }
                        }
                    }

                    return metrics;
                },

                parseSample(line) {
                    // 基本的なメトリクス行のパース
                    const labelRegex = /([a-zA-Z0-9_]+)(?:{([^}]*)})?(?:\s+([0-9.eE+-]+))?/;
                    const match = line.match(labelRegex);

                    if (!match) return null;

                    const name = match[1];
                    const labelsStr = match[2] || '';
                    const value = match[3] ? parseFloat(match[3]) : 0;

                    // ラベルをパース
                    const labels = {};
                    if (labelsStr) {
                        const labelParts = labelsStr.split(',');
                        for (const part of labelParts) {
                            const [key, val] = part.split('=');
                            if (key && val) {
                                // 引用符を削除
                                labels[key.trim()] = val.trim().replace(/^"|"$/g, '');
                            }
                        }
                    }

                    return { name, labels, value };
                },

                startAutoRefresh() {
                    this.refreshInterval = setInterval(() => {
                        this.fetchMetrics();
                    }, 30000); // 30秒ごとに更新
                },

                stopAutoRefresh() {
                    if (this.refreshInterval) {
                        clearInterval(this.refreshInterval);
                        this.refreshInterval = null;
                    }
                }
            }
        });

        app.use(vuetify);
        app.mount('#app');
    </script>
</body>
</html>
