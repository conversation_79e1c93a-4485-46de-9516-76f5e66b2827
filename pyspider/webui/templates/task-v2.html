<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Task - {{ task.project }}:{{ task.taskid }} - pyspider</title>

    <!-- Vuetify CSS -->
    <link href="https://cdn.jsdelivr.net/npm/vuetify@3.4.0/dist/vuetify.min.css" rel="stylesheet">
    <!-- Material Design Icons -->
    <link href="https://cdn.jsdelivr.net/npm/@mdi/font@7.3.67/css/materialdesignicons.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <style>
        .status-chip {
            min-width: 100px;
            text-align: center;
        }
        .status-1 {
            background-color: #2196F3 !important;
            color: white !important;
        }
        .status-2 {
            background-color: #4CAF50 !important;
            color: white !important;
        }
        .status-3 {
            background-color: #FFC107 !important;
            color: black !important;
        }
        .status-4 {
            background-color: #F44336 !important;
            color: white !important;
        }
        .status-5 {
            background-color: #9E9E9E !important;
            color: white !important;
        }
        .url-link {
            text-decoration: none;
            color: inherit;
            word-break: break-all;
        }
        .url-link:hover {
            text-decoration: underline;
        }
        .json-content {
            white-space: pre-wrap;
            font-family: monospace;
            background-color: #f5f5f5;
            padding: 8px;
            border-radius: 4px;
            overflow-x: auto;
        }
        .time-info {
            color: rgba(0, 0, 0, 0.6);
            font-size: 0.875rem;
        }
    </style>

    <!-- Vue.js -->
    <script src="https://cdn.jsdelivr.net/npm/vue@3.3.4/dist/vue.global.prod.js"></script>
    <!-- Vuetify -->
    <script src="https://cdn.jsdelivr.net/npm/vuetify@3.4.0/dist/vuetify.min.js"></script>
</head>

<body>
    <div id="app">
        <v-app>
            <v-app-bar color="primary" density="compact">
                <v-app-bar-title>Task Details</v-app-bar-title>
                <v-spacer></v-spacer>
                <v-btn
                    icon="mdi-open-in-new"
                    variant="text"
                    href="{{ task.url }}"
                    target="_blank"
                    title="Open URL in new tab"
                ></v-btn>
                <v-btn
                    icon="mdi-code-tags"
                    variant="text"
                    href="/debug/{{ task.project }}?taskid={{ task.taskid }}"
                    target="_blank"
                    title="Debug this task"
                ></v-btn>
            </v-app-bar>

            <v-main>
                <v-container>
                    <v-card class="mb-4">
                        <v-card-title>
                            <v-chip class="status-chip status-{{ task.status }} mr-2">{{ status_to_string(task.status) }}</v-chip>
                            <a class="url-link" href="/debug/{{ task.project }}?taskid={{ task.taskid }}">{{ task.project }}.{{ task.process.callback }}</a>
                            &gt;
                            <a class="url-link" href="{{ task.url }}" target="_blank">{{ task.url }}</a>
                        </v-card-title>
                        <v-card-subtitle>
                            {% if task.status in (2, 3, 4) %}
                            Last crawled: <span class="time-info">{{ task.lastcrawltime | format_date }}</span>
                            {% else %}
                            Last updated: <span class="time-info">{{ task.updatetime | format_date }}</span>
                            {% endif %}
                        </v-card-subtitle>
                    </v-card>

                    <v-expansion-panels>
                        <v-expansion-panel>
                            <v-expansion-panel-title>Basic Information</v-expansion-panel-title>
                            <v-expansion-panel-text>
                                <v-list>
                                    <v-list-item>
                                        <v-list-item-title>taskid</v-list-item-title>
                                        <v-list-item-subtitle>{{ task.taskid }}</v-list-item-subtitle>
                                    </v-list-item>
                                    <v-list-item>
                                        <v-list-item-title>lastcrawltime</v-list-item-title>
                                        <v-list-item-subtitle>{{ task.lastcrawltime }} ({{ task.lastcrawltime | format_date }})</v-list-item-subtitle>
                                    </v-list-item>
                                    <v-list-item>
                                        <v-list-item-title>updatetime</v-list-item-title>
                                        <v-list-item-subtitle>{{ task.updatetime }} ({{ task.updatetime | format_date }})</v-list-item-subtitle>
                                    </v-list-item>
                                    {% if task.schedule and task.schedule.exetime %}
                                    <v-list-item>
                                        <v-list-item-title>exetime</v-list-item-title>
                                        <v-list-item-subtitle>{{ task.schedule.exetime }} ({{ task.schedule.exetime | format_date }})</v-list-item-subtitle>
                                    </v-list-item>
                                    {% endif %}
                                </v-list>
                            </v-expansion-panel-text>
                        </v-expansion-panel>

                        {% if task.track and task.track.fetch %}
                        <v-expansion-panel>
                            <v-expansion-panel-title>
                                track.fetch
                                <v-icon color="{{ 'success' if task.track.fetch.ok else 'error' }}" class="ml-2">
                                    {{ 'mdi-check-circle' if task.track.fetch.ok else 'mdi-alert-circle' }}
                                </v-icon>
                                <span class="ml-2">{{ (task.track.fetch.time * 1000) | round(2) }}ms</span>
                            </v-expansion-panel-title>
                            <v-expansion-panel-text>
                                <div class="json-content">{{ json.dumps(task.track.fetch, indent=2, ensure_ascii=False) }}</div>
                            </v-expansion-panel-text>
                        </v-expansion-panel>
                        {% endif %}

                        {% if task.track and task.track.process %}
                        <v-expansion-panel>
                            <v-expansion-panel-title>
                                track.process
                                <v-icon color="{{ 'success' if task.track.process.ok else 'error' }}" class="ml-2">
                                    {{ 'mdi-check-circle' if task.track.process.ok else 'mdi-alert-circle' }}
                                </v-icon>
                                <span class="ml-2">{{ (task.track.process.time * 1000) | round(2) }}ms</span>
                                {% if task.track.process.follows %}
                                <v-chip size="x-small" color="primary" class="ml-2">+{{ task.track.process.follows | int }}</v-chip>
                                {% endif %}
                            </v-expansion-panel-title>
                            <v-expansion-panel-text>
                                {% if task.track.process.exception %}
                                <div class="json-content mb-2" style="color: #F44336;">{{ task.track.process.exception }}</div>
                                {% endif %}
                                {% if task.track.process.logs %}
                                <div class="json-content mb-2">{{ task.track.process.logs }}</div>
                                {% endif %}
                                <div class="json-content">{{ json.dumps(task.track.process, indent=2, ensure_ascii=False) }}</div>
                            </v-expansion-panel-text>
                        </v-expansion-panel>
                        {% endif %}

                        {% set not_shown_keys = ('status', 'url', 'project', 'taskid', 'lastcrawltime', 'updatetime', 'track', ) %}
                        {% for key, value in task.items() if key not in not_shown_keys %}
                        <v-expansion-panel>
                            <v-expansion-panel-title>{{ key }}</v-expansion-panel-title>
                            <v-expansion-panel-text>
                                <div class="json-content">{{ json.dumps(value, indent=2, ensure_ascii=False) if value is mapping else value }}</div>
                            </v-expansion-panel-text>
                        </v-expansion-panel>
                        {% endfor %}

                        {% if result and result.get('result') %}
                        <v-expansion-panel>
                            <v-expansion-panel-title>result</v-expansion-panel-title>
                            <v-expansion-panel-text>
                                <div class="json-content">{{ json.dumps(result['result'], indent=2, ensure_ascii=False) }}</div>
                            </v-expansion-panel-text>
                        </v-expansion-panel>
                        {% endif %}
                    </v-expansion-panels>
                </v-container>
            </v-main>
        </v-app>
    </div>

    <script>
        const app = Vue.createApp({
            data() {
                return {
                    // Any reactive data can be added here
                }
            }
        });

        const vuetify = Vuetify.createVuetify({
            theme: {
                defaultTheme: 'light'
            }
        });

        app.use(vuetify);
        app.mount('#app');
    </script>
</body>
</html>
