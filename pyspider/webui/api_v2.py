#!/usr/bin/env python
# -*- encoding: utf-8 -*-
# vim: set et sw=4 ts=4 sts=4 ff=unix fenc=utf8:
# Author: Binux<<EMAIL>>
#         http://binux.me
# Created on 2024-06-20 10:00:00

import time
import json
import logging
import socket
import traceback
from flask import request, Response, jsonify

from .app import app
from pyspider.libs.base_handler import BaseHandler
from pyspider.libs.time_series_store import time_series_store
from pyspider.libs.cache import cache_result
from pyspider.webui.metrics import get_scheduler_stats, get_system_stats
from pyspider.webui.components_status import get_components_status
from pyspider.webui.login import need_auth
from pyspider.webui.csrf_protection import exempt_csrf

logger = logging.getLogger('webui.api_v2')

def json_response(data, status=200):
    """Return a JSON response with the given data and status code."""
    response = Response(json.dumps(data), mimetype='application/json')
    response.status_code = status
    return response


def handle_api_error(e, context="API", status_code=500):
    """共通のエラーハンドリング関数

    Args:
        e: 例外オブジェクト
        context: エラーが発生したコンテキスト（API名など）
        status_code: HTTPステータスコード

    Returns:
        JSON形式のエラーレスポンス
    """
    error_info = {
        'error': str(e),
        'type': e.__class__.__name__,
        'context': context,
        'timestamp': time.time()
    }

    # 詳細なエラー情報をログに記録
    logger.error('%s error: %s', context, e)
    logger.error(traceback.format_exc())

    # ソケットエラーの場合は特別なメッセージを返す
    if isinstance(e, socket.error):
        error_info['error'] = 'Connection error: Unable to connect to the service'
        logger.error('Socket error in %s: %r', context, e)

    return json_response(error_info, status_code)

@app.route('/api/v2/test', methods=['GET', 'POST', 'OPTIONS'])
@exempt_csrf
def api_v2_test():
    """API v2テスト用エンドポイント"""
    if request.method == 'OPTIONS':
        response = Response()
        response.headers['Access-Control-Allow-Origin'] = '*'
        response.headers['Access-Control-Allow-Methods'] = 'GET, POST, OPTIONS'
        response.headers['Access-Control-Allow-Headers'] = 'Content-Type'
        return response

    if request.method == 'POST':
        try:
            data = request.get_json(force=True) if request.content_type and 'json' in request.content_type else {}
            return json_response({
                'status': 'success',
                'message': 'API v2 is working!',
                'data': data,
                'timestamp': time.time()
            })
        except Exception as e:
            return json_response({
                'status': 'error',
                'message': str(e),
                'timestamp': time.time()
            }, 400)

    return json_response({
        'status': 'success',
        'message': 'API v2 test endpoint is working!',
        'timestamp': time.time()
    })

@app.route('/api/v2/projects', methods=['POST', 'OPTIONS'])
@exempt_csrf
# 認証なし、CSRF保護なし
def create_project_v2():
    """Create a new project."""
    # Handle CORS preflight request
    if request.method == 'OPTIONS':
        response = Response()
        response.headers['Access-Control-Allow-Origin'] = '*'
        response.headers['Access-Control-Allow-Methods'] = 'POST, OPTIONS'
        response.headers['Access-Control-Allow-Headers'] = 'Content-Type, Authorization, X-CSRF-Token'
        return response

    projectdb = app.config.get('projectdb')
    if not projectdb:
        return json_response({'error': 'projectdb not initialized'}, 500)

    try:
        # Initialize variables
        project_name = None
        script = None
        group = 'default'
        status = 'RUNNING'

        # Check Content-Type and handle different request formats
        content_type = request.content_type or ''

        if 'application/json' in content_type:
            # Handle JSON data
            try:
                data = request.get_json(force=True)
                if data:
                    project_name = data.get('name')
                    script = data.get('script')
                    group = data.get('group', 'default')
                    status = data.get('status', 'RUNNING')
            except Exception as e:
                logger.error('Failed to parse JSON data: %s', e)
                return json_response({'error': 'Invalid JSON data'}, 400)

        elif 'application/x-www-form-urlencoded' in content_type or 'multipart/form-data' in content_type:
            # Handle form data
            project_name = request.form.get('project') or request.form.get('project-name') or request.form.get('name')
            script = request.form.get('script')

            if not script:
                # Create project template if script not provided
                start_urls = request.form.get('start-urls', '')
                script_mode = request.form.get('script-mode', 'script')

                # Create a basic project template
                if script_mode == 'script':
                    script = BaseHandler.get_template(project_name, start_urls)
                else:
                    script = BaseHandler.get_slime_template(project_name, start_urls)

            group = request.form.get('group', 'default')
            status = request.form.get('status', 'RUNNING')

        else:
            # Try to handle as JSON anyway (for cases where Content-Type is missing)
            try:
                data = request.get_json(force=True)
                if data:
                    project_name = data.get('name')
                    script = data.get('script')
                    group = data.get('group', 'default')
                    status = data.get('status', 'RUNNING')
                else:
                    # If no JSON data, try form data
                    project_name = request.form.get('project') or request.form.get('project-name') or request.form.get('name')
                    script = request.form.get('script')
                    group = request.form.get('group', 'default')
                    status = request.form.get('status', 'RUNNING')
            except Exception:
                # Last resort: try form data
                project_name = request.form.get('project') or request.form.get('project-name') or request.form.get('name')
                script = request.form.get('script')
                group = request.form.get('group', 'default')
                status = request.form.get('status', 'RUNNING')

        # Validate project name
        if not project_name:
            return json_response({'error': 'project name is required'}, 400)

        # Validate project name format using input validation
        try:
            from pyspider.webui.input_validation import validate_input
            if not validate_input('project_name', project_name):
                return json_response({'error': 'invalid project name format'}, 400)
        except ImportError:
            # Fallback validation if input_validation module is not available
            import re
            if not re.match(r'^[a-zA-Z0-9_-]+$', project_name):
                return json_response({'error': 'project name can only contain letters, numbers, underscores, and hyphens'}, 400)

        # Generate default script if not provided
        if not script:
            try:
                script = BaseHandler.get_template(project_name, 'https://example.com')
            except Exception as e:
                logger.error('Failed to generate default script: %s', e)
                return json_response({'error': 'script is required'}, 400)

        # Check if project already exists
        if projectdb.get(project_name):
            return json_response({'error': 'project already exists'}, 409)

        # Create project with individual parameters
        projectdb.insert(
            project_name,
            {
                'group': group,
                'status': status,
                'script': script,
                'comments': None,
                'rate': 1,
                'burst': 10,
                'updatetime': time.time(),
            }
        )

        # Return success response with CORS headers
        response = json_response({'status': 'ok', 'project': project_name})
        response.headers['Access-Control-Allow-Origin'] = '*'
        return response

    except Exception as e:
        logger.error('Failed to create project: %s', e)
        error_response = json_response({'error': str(e)}, 500)
        error_response.headers['Access-Control-Allow-Origin'] = '*'
        return error_response

# 古い実装 - 新しい実装（api_v2/projects.py）に置き換えられました
# @app.route('/api/v2/projects/<name>', methods=['GET'])
# @cache_result(expire=60, key_prefix='get_project_v2')
def get_project_v2_old_disabled_completely_removed(name):
    """Get a project by name."""
    projectdb = app.config.get('projectdb')
    if not projectdb:
        return json_response({'error': 'projectdb not initialized'}, 500)

    project = projectdb.get(name)
    if not project:
        return json_response({'error': 'project not found'}, 404)

    return json_response(project)

# 古い実装 - 新しい実装（api_v2/projects.py）に置き換えられました
# @app.route('/api/v2/projects/<name>', methods=['PUT'])
# @need_auth
def update_project_v2_old_disabled(name):
    """Update a project."""
    projectdb = app.config.get('projectdb')
    if not projectdb:
        return json_response({'error': 'projectdb not initialized'}, 500)

    try:
        project = projectdb.get(name)
        if not project:
            return json_response({'error': 'project not found'}, 404)

        data = request.get_json()
        if not data:
            return json_response({'error': 'invalid project data'}, 400)

        # Update project data
        project_data = {
            'name': name,
            'group': data.get('group', project.get('group', 'default')),
            'status': data.get('status', project.get('status', 'RUNNING')),
            'script': data.get('script', project.get('script', '')),
            'comments': project.get('comments'),
            'rate': float(data.get('rate', project.get('rate', 1))),
            'burst': int(data.get('burst', project.get('burst', 10))),
            'updatetime': time.time(),
        }

        # Update project
        projectdb.update(name, project_data)
        return json_response({'status': 'ok'})
    except Exception as e:
        logger.error('Failed to update project: %s', e)
        return json_response({'error': str(e)}, 500)

@app.route('/api/v2/projects', methods=['GET'])
@need_auth
def get_projects_v2():
    """Get all projects."""
    # キャッシュキーを生成
    cache_key = 'get_projects_v2'

    # キャッシュから結果を取得
    from pyspider.libs.cache import default_cache
    cached_result = default_cache.get(cache_key)
    if cached_result:
        return json_response(cached_result)

    projectdb = app.config.get('projectdb')
    if not projectdb:
        return json_response({'error': 'projectdb not initialized'}, 500)

    projects = list(projectdb.get_all())

    # 結果をキャッシュに保存（60秒間）
    default_cache.set(cache_key, projects, 60)

    return json_response(projects)

@app.route('/api/v2/projects/<name>', methods=['DELETE'])
def delete_project_v2_old(name):
    """Delete a project."""
    projectdb = app.config.get('projectdb')
    if not projectdb:
        return json_response({'error': 'projectdb not initialized'}, 500)

    try:
        if not projectdb.get(name):
            return json_response({'error': 'project not found'}, 404)

        projectdb.drop(name)
        return json_response({'status': 'ok'})
    except Exception as e:
        return handle_api_error(e, context=f"Delete project '{name}'", status_code=500)

@app.route('/api/v2/projects/<name>/run', methods=['POST'])
def run_specific_project_v2_old(name):
    """Run a specific project."""
    projectdb = app.config.get('projectdb')
    if not projectdb:
        return json_response({'error': 'projectdb not available'}, 503)

    try:
        project = projectdb.get(name)
        if not project:
            return json_response({'error': 'project not found'}, 404)

        # プロジェクトのステータスを確認
        if project.get('status') not in ('RUNNING', 'DEBUG'):
            # プロジェクトのステータスを RUNNING に変更
            project['status'] = 'RUNNING'
            project['updatetime'] = time.time()
            projectdb.update(name, project)

            # スケジューラに更新を通知
            rpc = app.config.get('scheduler_rpc')
            if rpc:
                try:
                    rpc.update_project()
                except Exception as e:
                    logger.warning('Failed to notify scheduler: %s', e)

            # ステータスが変更されたことを通知
            logger.info('Project %s status changed to RUNNING', name)

        # タスクを実行
        rpc = app.config.get('scheduler_rpc')
        if not rpc:
            return json_response({'error': 'scheduler not available'}, 503)

        # on_startタスクを作成
        newtask = {
            "project": name,
            "taskid": "on_start",
            "url": "data:,on_start",
            "process": {
                "callback": "on_start",
            },
            "schedule": {
                "age": 0,
                "priority": 9,
                "force_update": True,
            },
        }

        logger.info('Creating newtask for project: %s', name)
        logger.info('Newtask details: %s', newtask)

        try:
            # スケジューラにタスクを送信
            ret = rpc.newtask(newtask)
            logger.info('Task executed for project %s, result: %s', name, ret)
            return json_response({'status': 'ok', 'result': ret})
        except socket.error as e:
            logger.error('Connect to scheduler rpc error for project %s: %r', name, e)
            return json_response({'error': 'Connect to scheduler rpc error'}, 500)
        except Exception as e:
            logger.error('Task execution failed for project %s: %r', name, e)
            return json_response({'error': str(e)}, 500)
    except Exception as e:
        logger.error('Error running project: %s', e)
        return json_response({'error': str(e)}, 500)

@app.route('/api/v2/time_series', methods=['GET'])
def get_time_series_v2():
    """Get time series data for projects"""
    project = request.args.get('project')
    metric = request.args.get('metric')
    start_time = request.args.get('start_time')
    end_time = request.args.get('end_time')

    if start_time:
        try:
            start_time = float(start_time)
        except ValueError:
            return jsonify({
                'error': 'Invalid start_time parameter',
            }), 400

    if end_time:
        try:
            end_time = float(end_time)
        except ValueError:
            return jsonify({
                'error': 'Invalid end_time parameter',
            }), 400

    if project and metric:
        # Get data for a specific project and metric
        data = time_series_store.get_data(project, metric, start_time, end_time)
        return jsonify(data)
    elif project:
        # Get all metrics for a specific project
        data = time_series_store.get_all_data(project, start_time, end_time)
        return jsonify(data)
    else:
        # Get data for all projects
        data = time_series_store.get_all_data(None, start_time, end_time)
        return jsonify(data)

@app.route('/api/v2/time_series/metrics', methods=['GET'])
def get_available_metrics_v2():
    """Get available metrics for time series data"""
    # Get all data
    all_data = time_series_store.get_all_data()

    # Extract unique metrics
    metrics = set()
    for project_data in all_data.values():
        metrics.update(project_data.keys())

    return jsonify(list(metrics))

@app.route('/api/v2/project-groups', methods=['GET'])
@cache_result(expire=60, key_prefix='get_project_groups_v2')
def get_project_groups_v2():
    """Get all project groups."""
    projectdb = app.config.get('projectdb')
    if not projectdb:
        return json_response({'error': 'projectdb not initialized'}, 500)

    try:
        # Get all projects
        projects = list(projectdb.get_all())

        # Extract unique groups
        groups = {}
        for project in projects:
            group_name = project.get('group', 'default')
            if not group_name:
                group_name = 'default'

            if group_name not in groups:
                groups[group_name] = {
                    'name': group_name,
                    'projects': [],
                    'project_count': 0
                }

            groups[group_name]['projects'].append(project['name'])
            groups[group_name]['project_count'] += 1

        return json_response(list(groups.values()))
    except Exception as e:
        logger.error('Failed to get project groups: %s', e)
        return json_response({'error': str(e)}, 500)

@app.route('/api/v2/project-groups', methods=['POST'])
def create_project_group_v2():
    """Create a new project group by updating projects."""
    projectdb = app.config.get('projectdb')
    if not projectdb:
        return json_response({'error': 'projectdb not initialized'}, 500)

    try:
        data = request.get_json()
        if not data or 'name' not in data or 'projects' not in data:
            return json_response({'error': 'group name and projects are required'}, 400)

        group_name = data['name']
        project_names = data['projects']

        # Validate group name
        if not group_name or not isinstance(group_name, str):
            return json_response({'error': 'invalid group name'}, 400)

        # Validate projects list
        if not isinstance(project_names, list):
            return json_response({'error': 'projects must be a list'}, 400)

        # Update each project with the new group
        updated_projects = []
        for project_name in project_names:
            project = projectdb.get(project_name)
            if project:
                project['group'] = group_name
                project['updatetime'] = time.time()
                projectdb.update(project_name, project)
                updated_projects.append(project_name)

        return json_response({
            'status': 'ok',
            'group': group_name,
            'updated_projects': updated_projects
        })
    except Exception as e:
        logger.error('Failed to create project group: %s', e)
        return json_response({'error': str(e)}, 500)

@app.route('/api/v2/project-groups/<name>', methods=['GET'])
@cache_result(expire=60, key_prefix='get_project_group_v2')
def get_project_group_v2(name):
    """Get a project group by name."""
    projectdb = app.config.get('projectdb')
    if not projectdb:
        return json_response({'error': 'projectdb not initialized'}, 500)

    try:
        # Get all projects
        projects = list(projectdb.get_all())

        # Filter projects by group
        group_projects = [p for p in projects if p.get('group', 'default') == name]

        if not group_projects:
            return json_response({'error': 'group not found'}, 404)

        return json_response({
            'name': name,
            'projects': [p['name'] for p in group_projects],
            'project_count': len(group_projects)
        })
    except Exception as e:
        logger.error('Failed to get project group: %s', e)
        return json_response({'error': str(e)}, 500)

@app.route('/api/v2/project-groups/<name>', methods=['DELETE'])
def delete_project_group_v2(name):
    """Delete a project group by setting group to default for all projects in the group."""
    projectdb = app.config.get('projectdb')
    if not projectdb:
        return json_response({'error': 'projectdb not initialized'}, 500)

    try:
        # Get all projects
        projects = list(projectdb.get_all())

        # Filter projects by group
        group_projects = [p for p in projects if p.get('group', 'default') == name]

        if not group_projects:
            return json_response({'error': 'group not found'}, 404)

        # Update each project to remove the group
        updated_projects = []
        for project in group_projects:
            project['group'] = 'default'
            project['updatetime'] = time.time()
            projectdb.update(project['name'], project)
            updated_projects.append(project['name'])

        return json_response({
            'status': 'ok',
            'updated_projects': updated_projects
        })
    except Exception as e:
        logger.error('Failed to delete project group: %s', e)
        return json_response({'error': str(e)}, 500)

@app.route('/api/v2/counter', methods=['GET', 'OPTIONS'])
def get_project_task_count_v2():
    """Get task count for projects.

    If project parameter is provided, returns counter data for that specific project.
    Otherwise, returns counter data for all projects.
    """
    # CORSヘッダーを追加
    headers = {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type'
    }

    # OPTIONSリクエストに対応
    if request.method == 'OPTIONS':
        return '', 200, headers

    # Get project parameter from query string
    project = request.args.get('project')
    logger.debug('Project parameter from "project": %s', project)

    # Also support projectname parameter
    projectname = request.args.get('projectname')
    logger.debug('Project parameter from "projectname": %s', projectname)

    if not project and projectname:
        project = projectname
        logger.debug('Using projectname parameter: %s', project)

    # キャッシュキーを生成
    cache_key = f'counter_{project}' if project else 'counter_all'

    # キャッシュから結果を取得
    from pyspider.libs.cache import default_cache
    cached_result = default_cache.get(cache_key)
    if cached_result:
        return json.dumps(cached_result), 200, headers

    rpc = app.config.get('scheduler_rpc')
    if rpc is None:
        logger.warning('scheduler_rpc is None, returning empty counter data')
        return json.dumps({}), 200, headers

    # Always use webui_update to get detailed counter information
    # We don't use the direct counter method anymore

    # Get counter data from webui_update
    result = {}
    try:
        logger.debug('Fetching webui_update from scheduler')
        data = rpc.webui_update()
        logger.debug('webui_update data: %s', data)

        for type, counters in data['counter'].items():
            logger.debug('Processing counter type: %s', type)
            for proj, counter in counters.items():
                # Skip if project parameter is provided and doesn't match
                if project and proj != project:
                    continue

                logger.debug('Project: %s, Counter: %s', proj, counter)

                # Ensure all required properties exist
                complete_counter = {
                    'pending': counter.get('pending', 0),
                    'success': counter.get('success', 0),
                    'retry': counter.get('retry', 0),
                    'failed': counter.get('failed', 0),
                }

                # Calculate task as the sum of all counters
                complete_counter['task'] = (
                    complete_counter['pending'] +
                    complete_counter['success'] +
                    complete_counter['retry'] +
                    complete_counter['failed']
                )

                # Add title
                complete_counter['title'] = 'pending: {pending}, success: {success}, retry: {retry}, failed: {failed}'.format(**complete_counter)

                result.setdefault(proj, {})[type] = complete_counter

        for proj, paused in data['pause_status'].items():
            # Skip if project parameter is provided and doesn't match
            if project and proj != project:
                continue

            logger.debug('Project: %s, Paused: %s', proj, paused)
            result.setdefault(proj, {})['paused'] = paused

        # Ensure all projects have all time types
        for proj in result:
            for time_type in ['5m', '1h', '1d', 'all']:
                if time_type not in result[proj]:
                    logger.debug('Adding missing time type %s for project %s', time_type, proj)
                    result[proj][time_type] = {
                        'pending': 0,
                        'success': 0,
                        'retry': 0,
                        'failed': 0,
                        'task': 0,
                        'title': 'pending: 0, success: 0, retry: 0, failed: 0'
                    }

            # Add avg time data if not present
            if 'time' not in result[proj]:
                logger.debug('Adding time data for project %s', proj)
                result[proj]['time'] = {
                    'fetch_time': 0.1,  # Default fetch time in seconds
                    'process_time': 0.05  # Default process time in seconds
                }
            else:
                logger.debug('Time data already exists for project %s: %s', proj, result[proj]['time'])
                # Ensure fetch_time and process_time are present
                if 'fetch_time' not in result[proj]['time']:
                    result[proj]['time']['fetch_time'] = 0.1
                if 'process_time' not in result[proj]['time']:
                    result[proj]['time']['process_time'] = 0.05

        logger.debug('Final counter result: %s', result)

        # If project parameter is provided but no data was found, return empty object
        if project and not result:
            logger.warning('No counter data found for project: %s', project)
            return json.dumps({}), 200, headers

        # If project parameter is provided and data was found, return only that project's data
        if project and project in result:
            return json.dumps(result[project]), 200, headers

    except socket.error as e:
        logger.warning('connect to scheduler rpc error: %r', e)
        return json.dumps({}), 200, headers
    except Exception as e:
        logger.error('Error in counter endpoint: %r', e)
        return json.dumps({}), 200, headers

    # 結果をキャッシュに保存（10秒間）
    default_cache.set(cache_key, result, 10)

    return json.dumps(result), 200, headers

@app.route('/api/v2/run', methods=['POST'])
def run_project_v2():
    """Run a project."""
    data = request.get_json()
    if not data or 'project' not in data:
        return json_response({'error': 'project parameter is required'}, 400)

    project_name = data['project']
    projectdb = app.config.get('projectdb')
    if not projectdb:
        return json_response({'error': 'projectdb not available'}, 503)

    try:
        project = projectdb.get(project_name)
        if not project:
            return json_response({'error': 'project not found'}, 404)

        # プロジェクトのステータスを確認
        if project.get('status') not in ('RUNNING', 'DEBUG'):
            # プロジェクトのステータスを RUNNING に変更
            project['status'] = 'RUNNING'
            project['updatetime'] = time.time()
            projectdb.update(project_name, project)

            # スケジューラに更新を通知
            rpc = app.config.get('scheduler_rpc')
            if rpc:
                try:
                    rpc.update_project()
                except Exception as e:
                    logger.warning('Failed to notify scheduler: %s', e)
                    # 通知失敗はクリティカルではないので続行

            # ステータスが変更されたことを通知
            logger.info('Project %s status changed to RUNNING', project_name)

        # タスクを実行
        rpc = app.config.get('scheduler_rpc')
        if not rpc:
            return json_response({'error': 'scheduler not available'}, 503)

        # on_startタスクを作成
        newtask = {
            "project": project_name,
            "taskid": "on_start",
            "url": "data:,on_start",
            "process": {
                "callback": "on_start",
            },
            "schedule": {
                "age": 0,
                "priority": 9,
                "force_update": True,
            },
        }

        logger.info('Creating newtask for project: %s', project_name)
        logger.info('Newtask details: %s', newtask)

        try:
            # スケジューラにタスクを送信
            ret = rpc.newtask(newtask)
            logger.info('Task executed for project %s, result: %s', project_name, ret)
            return json_response({'status': 'ok', 'result': ret})
        except socket.error as e:
            # ソケットエラーは特別に処理
            context = f"Run project '{project_name}' - scheduler connection"
            return handle_api_error(e, context=context, status_code=503)
        except Exception as e:
            # その他の例外
            context = f"Run project '{project_name}' - task execution"
            return handle_api_error(e, context=context, status_code=500)
    except Exception as e:
        # 全体的な例外処理
        return handle_api_error(e, context=f"Run project '{project_name}'", status_code=500)

@app.route('/api/v2/stop', methods=['POST'])
def stop_project_v2():
    """Stop a project."""
    data = request.get_json()
    if not data or 'project' not in data:
        return json_response({'error': 'project parameter is required'}, 400)

    project_name = data['project']
    projectdb = app.config.get('projectdb')
    if not projectdb:
        return json_response({'error': 'projectdb not available'}, 503)

    try:
        project = projectdb.get(project_name)
        if not project:
            return json_response({'error': 'project not found'}, 404)

        # プロジェクトのステータスを STOPPED に変更
        project['status'] = 'STOPPED'
        project['updatetime'] = time.time()
        projectdb.update(project_name, project)

        # スケジューラに更新を通知
        rpc = app.config.get('scheduler_rpc')
        if rpc:
            try:
                rpc.update_project()
            except Exception as e:
                logger.warning('Failed to notify scheduler: %s', e)

        return json_response({'status': 'ok'})
    except Exception as e:
        logger.error('Error stopping project: %s', e)
        return json_response({'error': str(e)}, 500)

@app.route('/api/v2/pause', methods=['POST'])
def pause_project_v2():
    """Pause a project."""
    data = request.get_json()
    if not data or 'project' not in data:
        return json_response({'error': 'project parameter is required'}, 400)

    project_name = data['project']
    projectdb = app.config.get('projectdb')
    if not projectdb:
        return json_response({'error': 'projectdb not available'}, 503)

    try:
        project = projectdb.get(project_name)
        if not project:
            return json_response({'error': 'project not found'}, 404)

        # プロジェクトのステータスを PAUSED に変更
        project['status'] = 'PAUSED'
        project['updatetime'] = time.time()
        projectdb.update(project_name, project)

        # スケジューラに更新を通知
        rpc = app.config.get('scheduler_rpc')
        if rpc:
            try:
                rpc.update_project()
            except Exception as e:
                logger.warning('Failed to notify scheduler: %s', e)

        return json_response({'status': 'ok'})
    except Exception as e:
        logger.error('Error pausing project: %s', e)
        return json_response({'error': str(e)}, 500)

@app.route('/api/v2/counters', methods=['GET'])
def get_project_counters_v2():
    """Get counters for all projects."""
    rpc = app.config.get('scheduler_rpc')
    if not rpc:
        return json_response({'error': 'scheduler not available'}, 503)

    try:
        # webui_updateメソッドを使用して詳細なカウンター情報を取得
        try:
            data = rpc.webui_update()
            logger.debug('webui_update data: %s', data)

            result = {}

            # カウンターデータを処理
            for type, counters in data['counter'].items():
                for proj, counter in counters.items():
                    # 完全なカウンターデータを確保
                    complete_counter = {
                        'pending': counter.get('pending', 0),
                        'success': counter.get('success', 0),
                        'retry': counter.get('retry', 0),
                        'failed': counter.get('failed', 0),
                    }

                    # アクティブタスク数を計算
                    complete_counter['active'] = complete_counter['pending']

                    # 合計タスク数を計算
                    complete_counter['task'] = (
                        complete_counter['pending'] +
                        complete_counter['success'] +
                        complete_counter['retry'] +
                        complete_counter['failed']
                    )

                    # タイトルを追加
                    complete_counter['title'] = 'pending: {pending}, success: {success}, retry: {retry}, failed: {failed}'.format(**complete_counter)

                    # プロジェクトごとにデータを整理
                    result.setdefault(proj, {})[type] = complete_counter

            # 一時停止状態を追加
            for proj, paused in data['pause_status'].items():
                result.setdefault(proj, {})['paused'] = paused

            # すべてのプロジェクトに時間タイプを確保
            projectdb = app.config.get('projectdb')
            if projectdb:
                for project in projectdb.get_all():
                    proj = project['name']
                    if proj not in result:
                        result[proj] = {}

                    # すべての時間タイプを確保
                    for time_type in ['5m', '1h', '1d', 'all']:
                        if time_type not in result[proj]:
                            result[proj][time_type] = {
                                'pending': 0,
                                'success': 0,
                                'retry': 0,
                                'failed': 0,
                                'active': 0,
                                'task': 0,
                                'title': 'pending: 0, success: 0, retry: 0, failed: 0'
                            }

            return json_response(result)
        except Exception as e:
            logger.error('Error getting counters from webui_update: %s', e)

            # フォールバック: 従来のcounterメソッドを使用
            result = {}
            projectdb = app.config.get('projectdb')
            if not projectdb:
                return json_response({'error': 'projectdb not initialized'}, 500)

            projects = projectdb.get_all()
            for project in projects:
                try:
                    # counterメソッドは_typeパラメータを必要とする
                    try:
                        counter = rpc.counter(project['name'], 'all')
                        result[project['name']] = {
                            'all': {
                                'active': counter.get('active', 0),
                                'success': counter.get('success', 0),
                                'failed': counter.get('failed', 0),
                                'pending': counter.get('pending', 0),
                                'retry': counter.get('retry', 0),
                                'task': counter.get('active', 0) + counter.get('success', 0) + counter.get('failed', 0) + counter.get('pending', 0),
                                'title': 'pending: {}, success: {}, retry: {}, failed: {}'.format(
                                    counter.get('pending', 0),
                                    counter.get('success', 0),
                                    counter.get('retry', 0),
                                    counter.get('failed', 0)
                                )
                            }
                        }
                    except Exception:
                        # 失敗した場合はデフォルト値を提供
                        result[project['name']] = {
                            'all': {
                                'active': 0,
                                'success': 0,
                                'failed': 0,
                                'pending': 0,
                                'retry': 0,
                                'task': 0,
                                'title': 'pending: 0, success: 0, retry: 0, failed: 0'
                            }
                        }
                except Exception as e:
                    logger.error('Failed to get counter for project %s: %s', project['name'], e)
                    result[project['name']] = {
                        'all': {
                            'active': 0,
                            'success': 0,
                            'failed': 0,
                            'pending': 0,
                            'retry': 0,
                            'task': 0,
                            'title': 'pending: 0, success: 0, retry: 0, failed: 0'
                        }
                    }

            return json_response(result)
    except Exception as e:
        logger.error('RPC error: %s', e)
        return json_response({'error': str(e)}, 500)

@app.route('/api/v2/time_stats', methods=['GET'])
def get_avg_time_data_v2():
    """Get average time data for all projects."""
    rpc = app.config.get('scheduler_rpc')
    if not rpc:
        return json_response({'error': 'scheduler not available'}, 503)

    try:
        result = {}
        projectdb = app.config.get('projectdb')
        if not projectdb:
            return json_response({'error': 'projectdb not initialized'}, 500)

        projects = projectdb.get_all()
        for project in projects:
            # Since get_time_stats is not supported, we'll provide static time stats
            # This is a workaround until the scheduler implements the method
            result[project['name']] = {
                'total_time': 0.0,
                'avg_time': 0.0,
                'min_time': 0.0,
                'max_time': 0.0,
                'count': 0
            }

        return json_response(result)
    except Exception as e:
        logger.error('RPC error: %s', e)
        return json_response({'error': str(e)}, 500)

@app.route('/api/v2/queues', methods=['GET'])
def get_queue_info_v2():
    """Get queue information."""
    rpc = app.config.get('scheduler_rpc')
    if not rpc:
        return json_response({'error': 'scheduler not available'}, 503)

    try:
        # Since get_queue_info is not supported, we'll provide static queue information
        # This is a workaround until the scheduler implements the method
        result = {
            'scheduler2fetcher': 0,
            'fetcher2processor': 0,
            'processor2result': 0,
            'newtask_queue': 0,
            'status_queue': 0
        }
        return json_response(result)
    except Exception as e:
        logger.error('RPC error: %s', e)
        return json_response({'error': str(e)}, 500)

@app.route('/api/v2/metrics', methods=['GET'])
def get_metrics_v2():
    """Get metrics data including system stats, scheduler stats, and component status."""
    try:
        # 統計情報を収集
        metrics = {
            'timestamp': time.time(),
            'scheduler': get_scheduler_stats(),
            'system': get_system_stats(),
            'components': get_components_status()
        }

        return json_response(metrics)
    except Exception as e:
        logger.error('Error in metrics endpoint: %s', e)
        return json_response({
            'error': str(e),
            'timestamp': time.time()
        }, 500)

@app.route('/api/v2/components/status', methods=['GET'])
def get_components_status_v2():
    """Get status of all components."""
    try:
        # キャッシュキーを生成
        cache_key = 'get_components_status_v2'

        # キャッシュから結果を取得
        from pyspider.libs.cache import default_cache
        cached_result = default_cache.get(cache_key)

        # キャッシュの有効期限を短くするため、キャッシュを使用しない
        # if cached_result:
        #     return json_response(cached_result)

        components = get_components_status()

        # 結果をキャッシュに保存（10秒間）- 短い時間にして頻繁に更新されるようにする
        default_cache.set(cache_key, components, 10)

        return json_response(components)
    except Exception as e:
        return handle_api_error(e, context="Get components status", status_code=500)

@app.route('/api/v2/debug/new', methods=['POST'])
def create_project_debug_v2():
    """Create a new project."""
    projectdb = app.config.get('projectdb')
    if not projectdb:
        return json_response({'error': 'projectdb not initialized'}, 500)

    try:
        project_name = request.form.get('project-name')
        start_urls = request.form.get('start-urls', '')
        script_mode = request.form.get('script-mode', 'script')

        if not project_name:
            return json_response({'error': 'project name is required'}, 400)

        # Check if project already exists
        if projectdb.get(project_name):
            return json_response({'error': 'project already exists'}, 409)

        # Create project template
        from pyspider.libs.base_handler import BaseHandler

        # Create a basic project template
        if script_mode == 'script':
            script = BaseHandler.get_template(project_name, start_urls)
        else:
            script = BaseHandler.get_slime_template(project_name, start_urls)

        # Create project
        project = {
            'name': project_name,
            'group': None,
            'status': 'RUNNING',
            'script': script,
            'comments': None,
            'rate': 1,
            'burst': 10,
            'updatetime': time.time(),
        }

        projectdb.insert(project)
        return json_response({'status': 'ok'})
    except Exception as e:
        logger.error('Failed to create project: %s', e)
        return json_response({'error': str(e)}, 500)

# Results endpoints moved to api_v2/results.py to avoid conflicts

# Project-specific results endpoint moved to api_v2/results.py

# Export results endpoint moved to api_v2/results.py

@app.route('/api/v2/tasks', methods=['GET'])
def get_tasks_v2():
    """Get tasks with the given status."""
    status = request.args.get('status', 'all')
    project = request.args.get('project')
    limit = request.args.get('limit', default=10, type=int)
    offset = request.args.get('offset', default=0, type=int)

    rpc = app.config.get('scheduler_rpc')
    if rpc is None:
        return json_response({'error': 'scheduler_rpc is None'}, 500)

    try:
        tasks = []

        # アクティブなタスクを取得
        if status == 'active' or status == 'all':
            try:
                # webui_updateメソッドを使用してアクティブなタスクを取得
                data = rpc.webui_update()
                logger.debug('webui_update data: %s', data)

                if 'tasks' in data and data['tasks']:
                    for updatetime, task in data['tasks']:
                        # プロジェクトフィルタリング
                        if project and task.get('project') != project:
                            continue

                        task['updatetime'] = updatetime
                        task['status'] = 'ACTIVE'
                        tasks.append(task)

                    # 更新日時の降順でソート（最新のタスクが先頭に来るようにする）
                    tasks.sort(key=lambda x: x.get('updatetime', 0), reverse=True)

                    # 指定された数だけ返す
                    tasks = tasks[offset:offset+limit]
            except Exception as e:
                logger.error('Error getting active tasks: %s', e)

                # フォールバック: get_active_tasksメソッドを試す
                try:
                    if hasattr(rpc, 'get_active_tasks'):
                        active_tasks = rpc.get_active_tasks(project, limit)
                        for updatetime, task in active_tasks:
                            task['updatetime'] = updatetime
                            task['status'] = 'ACTIVE'
                            tasks.append(task)
                except Exception as e2:
                    logger.error('Error getting active tasks (fallback): %s', e2)

        # タスクDBからタスクを取得
        if status != 'active':
            try:
                taskdb = app.config.get('taskdb')
                if taskdb:
                    # タスクDBからタスクを取得
                    task_status = {
                        'pending': taskdb.ACTIVE,
                        'success': taskdb.SUCCESS,
                        'failed': taskdb.FAILED,
                        'all': None
                    }.get(status)

                    logger.debug('Getting tasks from taskdb with status: %s', task_status)

                    # プロジェクトが指定されている場合は、そのプロジェクトのタスクのみを取得
                    if project:
                        if task_status is None:
                            # すべてのステータスのタスクを取得
                            for status_code in [taskdb.SUCCESS, taskdb.FAILED, taskdb.ACTIVE]:
                                for task in taskdb.load_tasks(status_code, project):
                                    task['status_string'] = taskdb.status_to_string(task['status'])
                                    tasks.append(task)
                                    if len(tasks) >= limit:
                                        break
                                if len(tasks) >= limit:
                                    break
                        else:
                            # 指定されたステータスのタスクを取得
                            for task in taskdb.load_tasks(task_status, project):
                                task['status_string'] = taskdb.status_to_string(task['status'])
                                tasks.append(task)
                                if len(tasks) >= limit:
                                    break
                    else:
                        # すべてのプロジェクトのタスクを取得
                        projectdb = app.config.get('projectdb')
                        if projectdb:
                            for project_info in projectdb.get_all():
                                project_name = project_info['name']
                                if task_status is None:
                                    # すべてのステータスのタスクを取得
                                    for status_code in [taskdb.SUCCESS, taskdb.FAILED, taskdb.ACTIVE]:
                                        for task in taskdb.load_tasks(status_code, project_name):
                                            task['status_string'] = taskdb.status_to_string(task['status'])
                                            tasks.append(task)
                                            if len(tasks) >= limit:
                                                break
                                        if len(tasks) >= limit:
                                            break
                                else:
                                    # 指定されたステータスのタスクを取得
                                    for task in taskdb.load_tasks(task_status, project_name):
                                        task['status_string'] = taskdb.status_to_string(task['status'])
                                        tasks.append(task)
                                        if len(tasks) >= limit:
                                            break

                                # 指定された数に達したら終了
                                if len(tasks) >= limit:
                                    break

                    # 更新日時の降順でソート（最新のタスクが先頭に来るようにする）
                    tasks.sort(key=lambda x: x.get('updatetime', 0), reverse=True)

                    # 指定された数だけ返す
                    tasks = tasks[offset:offset+limit]
            except Exception as e:
                logger.error('Error getting tasks from taskdb: %s', e)
                import traceback
                logger.error(traceback.format_exc())

        # 結果を返す
        return json_response({
            'tasks': tasks,
            'total': len(tasks)
        })
    except Exception as e:
        logger.error('Error getting tasks: %s', e)
        import traceback
        logger.error(traceback.format_exc())
        return json_response({'error': str(e)}, 500)

@app.route('/api/v2/task/<taskid>', methods=['GET'])
@app.route('/api/v2/task/<taskid>.json', methods=['GET'])
def get_task_v2(taskid):
    """Get a task by taskid."""
    try:
        # キャッシュキーを生成
        cache_key = f'get_task_v2_{taskid}'

        # キャッシュから結果を取得
        from pyspider.libs.cache import default_cache
        cached_result = default_cache.get(cache_key)
        if cached_result:
            return json_response(cached_result)

        if ':' not in taskid:
            return json_response({'error': 'bad project:task_id format'}, 400)

        project, task_id = taskid.split(':', 1)

        taskdb = app.config.get('taskdb')
        if not taskdb:
            return json_response({'error': 'taskdb not initialized'}, 500)

        task = taskdb.get_task(project, task_id)
        if not task:
            return json_response({'error': 'task not found'}, 404)

        # タスクのステータス文字列を追加
        task['status_string'] = taskdb.status_to_string(task['status'])

        # 結果を取得
        resultdb = app.config.get('resultdb')
        if resultdb:
            try:
                result = resultdb.get(project, task_id)
                if result:
                    task['result'] = result.get('result')
            except Exception as e:
                logger.warning(f"Failed to get result for task {taskid}: {e}")
                # 結果の取得に失敗しても、タスク情報は返す

        # 結果をキャッシュに保存（30秒間）
        default_cache.set(cache_key, task, 30)

        return json_response(task)
    except Exception as e:
        return handle_api_error(e, context=f"Get task '{taskid}'", status_code=500)

@app.route('/api/v2/task/<taskid>/rerun', methods=['POST'])
def rerun_task_v2(taskid):
    """Rerun a task."""
    if ':' not in taskid:
        return json_response({'error': 'bad project:task_id format'}, 400)

    project, taskid = taskid.split(':', 1)

    taskdb = app.config.get('taskdb')
    if not taskdb:
        return json_response({'error': 'taskdb not initialized'}, 500)

    # タスクを取得
    task = taskdb.get_task(project, taskid)
    if not task:
        return json_response({'error': 'task not found'}, 404)

    # スケジューラーRPCを取得
    rpc = app.config.get('scheduler_rpc')
    if not rpc:
        return json_response({'error': 'scheduler not available'}, 503)

    try:
        # タスクを再実行
        ret = rpc.send_task({
            'taskid': taskid,
            'project': project,
            'url': task.get('url'),
            'fetch': task.get('fetch', {}),
            'process': task.get('process', {}),
            'schedule': {
                'age': 0,
                'priority': 9,
                'force_update': True,
            },
        })
        logger.info('Task %s:%s rerun, result: %s', project, taskid, ret)
        return json_response({'status': 'ok', 'result': ret})
    except Exception as e:
        logger.error('Failed to rerun task %s:%s: %s', project, taskid, e)
        return json_response({'error': str(e)}, 500)

@app.route('/api/v2/tasks/bulk', methods=['POST'])
def bulk_task_operations_v2():
    """Perform bulk operations on tasks (rerun, delete, etc.)."""
    data = request.get_json()
    if not data or 'operation' not in data or 'tasks' not in data:
        return json_response({'error': 'operation and tasks are required'}, 400)

    operation = data['operation']
    tasks = data['tasks']

    if not isinstance(tasks, list) or not tasks:
        return json_response({'error': 'tasks must be a non-empty list'}, 400)

    # Validate operation
    valid_operations = ['rerun', 'delete']
    if operation not in valid_operations:
        return json_response({'error': f'Invalid operation. Must be one of: {", ".join(valid_operations)}'}, 400)

    taskdb = app.config.get('taskdb')
    if not taskdb:
        return json_response({'error': 'taskdb not initialized'}, 500)

    rpc = app.config.get('scheduler_rpc')
    if not rpc:
        return json_response({'error': 'scheduler not available'}, 503)

    results = {
        'success': [],
        'failed': []
    }

    try:
        for task_id in tasks:
            if ':' not in task_id:
                results['failed'].append({
                    'taskid': task_id,
                    'error': 'bad project:task_id format'
                })
                continue

            project, taskid = task_id.split(':', 1)

            # Get task
            task = taskdb.get_task(project, taskid)
            if not task:
                results['failed'].append({
                    'taskid': task_id,
                    'error': 'task not found'
                })
                continue

            if operation == 'rerun':
                try:
                    # Rerun task
                    ret = rpc.send_task({
                        'taskid': taskid,
                        'project': project,
                        'url': task.get('url'),
                        'fetch': task.get('fetch', {}),
                        'process': task.get('process', {}),
                        'schedule': {
                            'age': 0,
                            'priority': 9,
                            'force_update': True,
                        },
                    })
                    logger.info('Task %s:%s rerun, result: %s', project, taskid, ret)
                    results['success'].append({
                        'taskid': task_id,
                        'result': ret
                    })
                except Exception as e:
                    logger.error('Failed to rerun task %s:%s: %s', project, taskid, e)
                    results['failed'].append({
                        'taskid': task_id,
                        'error': str(e)
                    })

            elif operation == 'delete':
                try:
                    # Delete task
                    taskdb.delete(project, taskid)
                    logger.info('Task %s:%s deleted', project, taskid)
                    results['success'].append({
                        'taskid': task_id,
                        'result': 'deleted'
                    })
                except Exception as e:
                    logger.error('Failed to delete task %s:%s: %s', project, taskid, e)
                    results['failed'].append({
                        'taskid': task_id,
                        'error': str(e)
                    })

        return json_response({
            'status': 'ok',
            'operation': operation,
            'total': len(tasks),
            'success_count': len(results['success']),
            'failed_count': len(results['failed']),
            'results': results
        })
    except Exception as e:
        logger.error('Error in bulk task operation: %s', e)
        return json_response({'error': str(e)}, 500)

@app.route('/api/v2/scheduler/config', methods=['GET', 'PUT'])
def scheduler_config_v2():
    """Get or update scheduler configuration."""
    rpc = app.config.get('scheduler_rpc')
    if not rpc:
        return json_response({'error': 'scheduler not available'}, 503)

    if request.method == 'GET':
        try:
            # Get scheduler configuration
            config = {}

            # Get scheduler information
            scheduler_info = rpc.get_info()
            if scheduler_info:
                config['info'] = scheduler_info

            # Get scheduler status
            status = get_components_status()
            if 'scheduler' in status:
                config['status'] = status['scheduler']

            # Get scheduler statistics
            stats = get_scheduler_stats()
            if stats:
                config['stats'] = stats

            # Get queue information
            try:
                queue_info = rpc.get_queue_info()
                if queue_info:
                    config['queue'] = queue_info
            except Exception as e:
                logger.warning('Failed to get queue info: %s', e)

            return json_response(config)
        except Exception as e:
            logger.error('Failed to get scheduler config: %s', e)
            return json_response({'error': str(e)}, 500)

    elif request.method == 'PUT':
        try:
            data = request.get_json()
            if not data:
                return json_response({'error': 'invalid configuration data'}, 400)

            # Update scheduler configuration
            result = {}

            # Update scheduler max rate
            if 'max_rate' in data:
                try:
                    max_rate = float(data['max_rate'])
                    if max_rate <= 0:
                        return json_response({'error': 'max_rate must be positive'}, 400)

                    rpc.set_max_rate(max_rate)
                    result['max_rate'] = max_rate
                except ValueError:
                    return json_response({'error': 'invalid max_rate value'}, 400)

            # Update scheduler max burst
            if 'max_burst' in data:
                try:
                    max_burst = int(data['max_burst'])
                    if max_burst <= 0:
                        return json_response({'error': 'max_burst must be positive'}, 400)

                    rpc.set_max_burst(max_burst)
                    result['max_burst'] = max_burst
                except ValueError:
                    return json_response({'error': 'invalid max_burst value'}, 400)

            # Update scheduler concurrency
            if 'concurrency' in data:
                try:
                    concurrency = int(data['concurrency'])
                    if concurrency <= 0:
                        return json_response({'error': 'concurrency must be positive'}, 400)

                    rpc.set_concurrency(concurrency)
                    result['concurrency'] = concurrency
                except ValueError:
                    return json_response({'error': 'invalid concurrency value'}, 400)

            # Update scheduler pause status
            if 'pause' in data:
                try:
                    pause = bool(data['pause'])
                    rpc.set_pause(pause)
                    result['pause'] = pause
                except Exception as e:
                    return json_response({'error': f'failed to set pause status: {str(e)}'}, 400)

            return json_response({'status': 'ok', 'updated': result})
        except Exception as e:
            logger.error('Failed to update scheduler config: %s', e)
            return json_response({'error': str(e)}, 500)

@app.route('/api/v2/stats', methods=['GET'])
@cache_result(expire=15, key_prefix='system_stats_v2')
def system_stats_v2():
    """Get system statistics (memory usage, CPU, etc.)."""
    try:
        # Get system statistics
        stats = get_system_stats()

        # Get scheduler statistics
        scheduler_stats = get_scheduler_stats()
        if scheduler_stats:
            stats['scheduler'] = scheduler_stats

        # Get component status
        components = get_components_status()
        if components:
            stats['components'] = components

        # Get queue information
        rpc = app.config.get('scheduler_rpc')
        if rpc:
            try:
                queue_info = rpc.get_queue_info()
                if queue_info:
                    stats['queue'] = queue_info
            except Exception as e:
                logger.warning('Failed to get queue info: %s', e)

        return json_response(stats)
    except Exception as e:
        logger.error('Failed to get system stats: %s', e)
        return json_response({'error': str(e)}, 500)

@app.route('/api/tasks', methods=['GET'])
@need_auth
def get_tasks():
    """Get tasks with the given status (legacy API endpoint)."""
    status = request.args.get('status', 'all')
    project = request.args.get('project')
    limit = request.args.get('limit', default=10, type=int)
    offset = request.args.get('offset', default=0, type=int)

    rpc = app.config.get('scheduler_rpc')
    if rpc is None:
        return json_response({'error': 'scheduler_rpc is None'}, 500)

    try:
        tasks = []

        # アクティブなタスクを取得
        if status == 'active' or status == 'all':
            try:
                # webui_updateメソッドを使用してアクティブなタスクを取得
                data = rpc.webui_update()
                logger.debug('webui_update data: %s', data)

                if 'tasks' in data and data['tasks']:
                    for updatetime, task in data['tasks']:
                        # プロジェクトフィルタリング
                        if project and task.get('project') != project:
                            continue

                        task['updatetime'] = updatetime
                        task['status'] = 'ACTIVE'
                        tasks.append(task)

                    # 更新日時の降順でソート（最新のタスクが先頭に来るようにする）
                    tasks.sort(key=lambda x: x.get('updatetime', 0), reverse=True)

                    # 指定された数だけ返す
                    tasks = tasks[offset:offset+limit]
            except Exception as e:
                logger.error('Error getting active tasks: %s', e)

        # 他のステータスのタスクを取得する場合は、taskdbから取得
        if status != 'active':
            try:
                taskdb = app.config.get('taskdb')
                if taskdb:
                    # taskdbから指定されたステータスのタスクを取得
                    db_tasks = []
                    if project:
                        # プロジェクト指定の場合
                        for task in taskdb.get_tasks(project, status=status, limit=limit, offset=offset):
                            if 'status' in task:
                                task['status_string'] = taskdb.status_to_string(task['status'])
                            db_tasks.append(task)
                    else:
                        # 全プロジェクトの場合
                        projectdb = app.config.get('projectdb')
                        if projectdb:
                            for proj in projectdb.get_all():
                                for task in taskdb.get_tasks(proj['name'], status=status, limit=limit//10, offset=offset//10):
                                    if 'status' in task:
                                        task['status_string'] = taskdb.status_to_string(task['status'])
                                    db_tasks.append(task)

                    if status == 'all':
                        tasks.extend(db_tasks)
                    else:
                        tasks = db_tasks
            except Exception as e:
                logger.error('Error getting tasks from taskdb: %s', e)

        return json_response(tasks)
    except Exception as e:
        return handle_api_error(e, context="Get tasks", status_code=500)

@app.route('/api/results', methods=['GET'])
@need_auth
def get_results():
    """Get results (legacy API endpoint)."""
    project = request.args.get('project')
    limit = request.args.get('limit', default=20, type=int)
    offset = request.args.get('offset', default=0, type=int)

    resultdb = app.config.get('resultdb')
    if not resultdb:
        return json_response({'error': 'resultdb not initialized'}, 500)

    try:
        results = []
        if project:
            # プロジェクト指定の場合
            for result in resultdb.select(project, offset=offset, limit=limit):
                # Convert ObjectId to string for JSON serialization
                if '_id' in result and hasattr(result['_id'], '__str__'):
                    result['_id'] = str(result['_id'])
                results.append(result)
        else:
            # 全プロジェクトの場合
            projectdb = app.config.get('projectdb')
            if projectdb:
                for proj in projectdb.get_all():
                    for result in resultdb.select(proj['name'], offset=offset//10, limit=limit//10):
                        # Convert ObjectId to string for JSON serialization
                        if '_id' in result and hasattr(result['_id'], '__str__'):
                            result['_id'] = str(result['_id'])
                        results.append(result)

        return json_response(results)
    except Exception as e:
        return handle_api_error(e, context="Get results", status_code=500)

@app.route('/api/v2/debug/run', methods=['POST'])
def run_task_v2():
    """Run a task with the given script."""
    data = request.get_json()
    if not data:
        return json_response({'error': 'Invalid request data'}, 400)

    project_name = data.get('project')
    task = data.get('task')
    script = data.get('script')

    if not project_name or not task or not script:
        return json_response({'error': 'Missing required parameters'}, 400)

    try:
        from pyspider.processor.project_module import ProjectManager
        from pyspider.libs.response import rebuild_response
        from pyspider.libs.utils import pretty_unicode
        import traceback
        import sys
        import time

        project = {
            'name': project_name,
            'script': script,
        }

        # Build module from script
        try:
            project_data = ProjectManager.build_module(project, {})
            module = project_data['module']
            instance = project_data['instance']
        except Exception as e:
            return json_response({
                'result': None,
                'follows': [],
                'messages': [],
                'logs': [str(e)],
                'error': 'Script error',
                'time': 0,
                'fetch_result': {
                    'content': 'Script error',
                    'cookies': {},
                    'headers': {},
                    'orig_url': 'data:,Script error',
                    'save': None,
                    'status_code': 500,
                    'time': 0,
                    'url': 'data:,Script error'
                }
            }, 200)

        # Prepare task
        if not isinstance(task, dict):
            try:
                task = json.loads(task)
            except Exception as e:
                return json_response({'error': 'Invalid task format'}, 400)

        # Execute task
        start_time = time.time()
        try:
            result = {}
            logs = []
            follows = []
            messages = []

            # Get callback function
            callback = task.get('process', {}).get('callback', 'on_start')
            if not hasattr(module, callback):
                return json_response({
                    'result': None,
                    'follows': [],
                    'messages': [],
                    'logs': [f'Callback {callback} not found in script'],
                    'error': 'Callback not found',
                    'time': 0,
                    'fetch_result': {
                        'content': f'Callback {callback} not found',
                        'cookies': {},
                        'headers': {},
                        'orig_url': f'data:,Callback {callback} not found',
                        'save': None,
                        'status_code': 400,
                        'time': 0,
                        'url': f'data:,Callback {callback} not found'
                    }
                }, 200)

            # Create a custom logger to capture logs
            class LogCapture(object):
                def __init__(self):
                    self.lines = []

                def debug(self, msg, *args, **kwargs):
                    # kwargsパラメータは無視して、メッセージと引数のみを処理
                    self.lines.append(pretty_unicode(msg) % args if args else pretty_unicode(msg))

                def info(self, msg, *args, **kwargs):
                    # kwargsパラメータは無視して、メッセージと引数のみを処理
                    self.lines.append(pretty_unicode(msg) % args if args else pretty_unicode(msg))

                def warning(self, msg, *args, **kwargs):
                    # kwargsパラメータは無視して、メッセージと引数のみを処理
                    self.lines.append(pretty_unicode(msg) % args if args else pretty_unicode(msg))

                def error(self, msg, *args, **kwargs):
                    # kwargsパラメータは無視して、メッセージと引数のみを処理
                    self.lines.append(pretty_unicode(msg) % args if args else pretty_unicode(msg))

            log_capture = LogCapture()
            module.logger = log_capture

            # Execute callback
            func = getattr(module, callback)

            # If task has a response, rebuild it
            if 'response' in task:
                response = rebuild_response(task['response'])
                ret = func(task, response)
            else:
                ret = func(task)

            # Process result
            if ret is not None:
                result = ret

            # Get logs
            logs = log_capture.lines
            if not logs:
                logs = []

            # Get follows
            if hasattr(instance, 'follows') and instance.follows:
                follows = instance.follows

            # Get messages
            if hasattr(instance, 'messages') and instance.messages:
                messages = instance.messages

            return json_response({
                'result': result,
                'follows': follows,
                'messages': messages,
                'logs': logs,
                'time': time.time() - start_time,
                'fetch_result': {
                    'content': callback,
                    'cookies': {},
                    'headers': {},
                    'orig_url': f"data:,{callback}",
                    'save': None,
                    'status_code': 200,
                    'time': 0,
                    'url': f"data:,{callback}"
                }
            }, 200)
        except Exception as e:
            exc_type, exc_value, exc_traceback = sys.exc_info()
            tb = traceback.format_exception(exc_type, exc_value, exc_traceback)
            logs = [line.rstrip('\n') for line in tb]
            return json_response({
                'result': None,
                'follows': [],
                'messages': [],
                'logs': logs,
                'error': str(e),
                'time': time.time() - start_time,
                'fetch_result': {
                    'content': 'Error',
                    'cookies': {},
                    'headers': {},
                    'orig_url': 'data:,Error',
                    'save': None,
                    'status_code': 500,
                    'time': 0,
                    'url': 'data:,Error'
                }
            }, 200)
    except Exception as e:
        logger.error('Failed to run task: %s', e)
        return json_response({'error': str(e)}, 500)
