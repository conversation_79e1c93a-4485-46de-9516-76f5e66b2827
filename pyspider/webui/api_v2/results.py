#!/usr/bin/env python
# -*- encoding: utf-8 -*-
# vim: set et sw=4 ts=4 sts=4 ff=unix fenc=utf8:
# Author: Binux<<EMAIL>>
#         http://binux.me
# Created on 2024-06-20 10:00:00

import time
import json
import logging
import csv
import io
import xml.etree.ElementTree as ET
from flask import request, Response, jsonify, current_app

from ..app import app
from ..api_utils import (
    json_response, api_error, api_success, require_params, require_auth, require_admin,
    handle_api_exception, paginate, get_project_name, get_task_id, get_db_or_error, get_rpc_or_error
)

logger = logging.getLogger('webui.api_v2.results')

@app.route('/api/v2/results', methods=['GET'])
@handle_api_exception
def get_all_results_v2():
    """
    Get results from all projects or a specific project.

    Query Parameters:
        project (str, optional): Filter by project name
        offset (int, optional): Pagination offset
        limit (int, optional): Pagination limit

    Returns:
        JSON: List of results
    """
    resultdb = get_db_or_error('resultdb')

    # Get query parameters
    project = request.args.get('project')
    offset = request.args.get('offset', default=0, type=int)
    limit = request.args.get('limit', default=20, type=int)

    results = []
    total = 0

    if project:
        # Get results for specific project
        for result in resultdb.select(project, offset=offset, limit=limit):
            # Convert ObjectId to string for JSON serialization
            if '_id' in result and hasattr(result['_id'], '__str__'):
                result['_id'] = str(result['_id'])
            results.append(result)

        # Count total if available
        if hasattr(resultdb, 'count'):
            total = resultdb.count(project)
    else:
        # Get results from all projects
        projectdb = get_db_or_error('projectdb')
        all_projects = list(projectdb.get_all())  # Convert generator to list

        if all_projects:
            # Calculate per-project limits for pagination
            per_project_limit = max(1, limit // max(1, len(all_projects)))
            per_project_offset = offset // max(1, len(all_projects))

            for proj in all_projects:
                project_name = proj['name']
                for result in resultdb.select(project_name, offset=per_project_offset, limit=per_project_limit):
                    # Convert ObjectId to string for JSON serialization
                    if '_id' in result and hasattr(result['_id'], '__str__'):
                        result['_id'] = str(result['_id'])
                    result['project'] = project_name  # Add project name to result
                    results.append(result)

                    if len(results) >= limit:
                        break

                if len(results) >= limit:
                    break

        total = len(results)

    return api_success({
        'results': results,
        'total': total,
        'offset': offset,
        'limit': limit,
        'project': project
    })

@app.route('/api/v2/results/<project_name>', methods=['GET'])
@handle_api_exception
def get_results_v2(project_name):
    """
    Get results for a project.

    Args:
        project_name (str): Project name

    Query Parameters:
        offset (int, optional): Pagination offset
        limit (int, optional): Pagination limit

    Returns:
        JSON: List of results
    """
    resultdb = get_db_or_error('resultdb')

    # Check if project exists
    projectdb = get_db_or_error('projectdb')
    if not projectdb.get(project_name):
        return api_error(f"Project {project_name} not found", 404)

    # Get query parameters
    offset = request.args.get('offset', default=0, type=int)
    limit = request.args.get('limit', default=20, type=int)

    # Get results
    results = []
    for result in resultdb.select(project_name, offset=offset, limit=limit):
        # Convert ObjectId to string for JSON serialization
        if '_id' in result and hasattr(result['_id'], '__str__'):
            result['_id'] = str(result['_id'])
        results.append(result)

    # Count total if available
    total = None
    if hasattr(resultdb, 'count'):
        total = resultdb.count(project_name)

    return api_success({
        'results': results,
        'total': total,
        'offset': offset,
        'limit': limit
    })

@app.route('/api/v2/results/<project_name>/<task_id>', methods=['GET'])
@handle_api_exception
def get_result_v2(project_name, task_id):
    """
    Get a result by project name and task ID.

    Args:
        project_name (str): Project name
        task_id (str): Task ID

    Returns:
        JSON: Result details
    """
    resultdb = get_db_or_error('resultdb')

    result = resultdb.get(project_name, task_id)
    if not result:
        return api_error(f"Result for task {project_name}:{task_id} not found", 404)

    # Convert ObjectId to string for JSON serialization
    if '_id' in result and hasattr(result['_id'], '__str__'):
        result['_id'] = str(result['_id'])

    return api_success(result)

@app.route('/api/v2/results/<project_name>/export', methods=['GET'])
@handle_api_exception
def export_results_v2(project_name):
    """
    Export results for a project in various formats.

    Args:
        project_name (str): Project name

    Query Parameters:
        format (str, optional): Export format (json, csv, xml)
        limit (int, optional): Maximum number of results to export

    Returns:
        File: Exported results
    """
    resultdb = get_db_or_error('resultdb')

    # Check if project exists
    projectdb = get_db_or_error('projectdb')
    if not projectdb.get(project_name):
        return api_error(f"Project {project_name} not found", 404)

    # Get query parameters
    format_type = request.args.get('format', default='json')
    limit = request.args.get('limit', default=1000, type=int)

    # Get results
    results = []
    for result in resultdb.select(project_name, limit=limit):
        # Convert ObjectId to string for JSON serialization
        if '_id' in result and hasattr(result['_id'], '__str__'):
            result['_id'] = str(result['_id'])
        results.append(result)

    # Export in requested format
    if format_type == 'json':
        return Response(
            json.dumps(results, indent=2),
            mimetype='application/json',
            headers={
                'Content-Disposition': f'attachment; filename={project_name}_results.json'
            }
        )

    elif format_type == 'jsonl':
        # JSONL format: Try to read from file first, fallback to database
        from pathlib import Path

        # Check if file output is enabled and file exists
        try:
            from pyspider.run import g
            result_worker_config = g.config.get('result_worker', {})
            file_output_config = result_worker_config.get('file_output', {})

            if (file_output_config.get('enabled', False) and
                result_worker_config.get('type') == 'file_output'):

                output_dir = Path(file_output_config.get('output_dir', 'results'))
                # プロジェクト名をファイル名に安全な形式に変換
                safe_project_name = "".join(c for c in project_name if c.isalnum() or c in ('-', '_')).rstrip()
                if not safe_project_name:
                    safe_project_name = "unknown_project"

                file_path = output_dir / f"{safe_project_name}_results.jsonl"

                if file_path.exists():
                    # ファイルから直接読み取り
                    def generate_jsonl_from_file():
                        with open(file_path, 'r', encoding='utf-8') as f:
                            for line in f:
                                line = line.strip()
                                if line:
                                    yield line + '\n'

                    return Response(
                        generate_jsonl_from_file(),
                        mimetype='application/x-jsonlines',
                        headers={
                            'Content-Disposition': f'attachment; filename={project_name}_results.jsonl'
                        }
                    )
        except Exception as e:
            logger.warning(f"Failed to read from file, falling back to database: {e}")

        # Fallback: Generate JSONL from database
        def generate_jsonl_from_db():
            for result in results:
                yield json.dumps(result, ensure_ascii=False) + '\n'

        return Response(
            generate_jsonl_from_db(),
            mimetype='application/x-jsonlines',
            headers={
                'Content-Disposition': f'attachment; filename={project_name}_results.jsonl'
            }
        )

    elif format_type == 'csv':
        # Prepare CSV data
        output = io.StringIO()

        # Get all field names from all results
        fieldnames = set()
        for result in results:
            if 'result' in result and isinstance(result['result'], dict):
                fieldnames.update(result['result'].keys())

        # Add standard fields
        standard_fields = ['taskid', 'url', 'updatetime']
        fieldnames = standard_fields + sorted(fieldnames)

        # Write CSV
        writer = csv.DictWriter(output, fieldnames=fieldnames)
        writer.writeheader()

        for result in results:
            row = {field: '' for field in fieldnames}

            # Add standard fields
            for field in standard_fields:
                if field in result:
                    row[field] = result[field]

            # Add result fields
            if 'result' in result and isinstance(result['result'], dict):
                for field, value in result['result'].items():
                    row[field] = value

            writer.writerow(row)

        return Response(
            output.getvalue(),
            mimetype='text/csv',
            headers={
                'Content-Disposition': f'attachment; filename={project_name}_results.csv'
            }
        )

    elif format_type == 'xml':
        # Prepare XML data
        root = ET.Element('results')
        root.set('project', project_name)

        for result in results:
            result_elem = ET.SubElement(root, 'result')

            # Add standard fields
            for field in ['taskid', 'url', 'updatetime']:
                if field in result:
                    field_elem = ET.SubElement(result_elem, field)
                    field_elem.text = str(result[field])

            # Add result fields
            if 'result' in result and isinstance(result['result'], dict):
                result_data = ET.SubElement(result_elem, 'data')
                for field, value in result['result'].items():
                    field_elem = ET.SubElement(result_data, field)
                    field_elem.text = str(value)

        # Convert to string
        xml_str = ET.tostring(root, encoding='utf-8', method='xml')

        return Response(
            xml_str,
            mimetype='application/xml',
            headers={
                'Content-Disposition': f'attachment; filename={project_name}_results.xml'
            }
        )

    else:
        return api_error(f"Unsupported format: {format_type}. Supported formats: json, jsonl, csv, xml", 400)

@app.route('/api/v2/results/<project_name>/<task_id>', methods=['DELETE'])
@require_auth
@handle_api_exception
def delete_result_v2(project_name, task_id):
    """
    Delete a result.

    Args:
        project_name (str): Project name
        task_id (str): Task ID

    Returns:
        JSON: Success message
    """
    resultdb = get_db_or_error('resultdb')

    # Check if result exists
    result = resultdb.get(project_name, task_id)
    if not result:
        return api_error(f"Result for task {project_name}:{task_id} not found", 404)

    # Delete result
    resultdb.delete(project_name, task_id)

    return api_success(None, f"Result for task {project_name}:{task_id} deleted successfully")

@app.route('/api/v2/results/<project_name>/clear', methods=['POST'])
@require_admin
@handle_api_exception
def clear_results_v2(project_name):
    """
    Clear all results for a project.

    Args:
        project_name (str): Project name

    Returns:
        JSON: Success message
    """
    resultdb = get_db_or_error('resultdb')

    # Check if project exists
    projectdb = get_db_or_error('projectdb')
    if not projectdb.get(project_name):
        return api_error(f"Project {project_name} not found", 404)

    # Clear results
    try:
        if hasattr(resultdb, 'drop'):
            resultdb.drop(project_name)
        else:
            # Fallback to deleting each result
            for result in resultdb.select(project_name):
                resultdb.delete(project_name, result['taskid'])

        return api_success(None, f"Results for project {project_name} cleared successfully")
    except Exception as e:
        logger.error(f"Failed to clear results for project {project_name}: {e}")
        return api_error(f"Failed to clear results: {e}", 500)

@app.route('/api/v2/results/search', methods=['GET'])
@handle_api_exception
def search_results_v2():
    """
    Search results across all projects.

    Query Parameters:
        q (str): Search query
        project (str, optional): Filter by project
        offset (int, optional): Pagination offset
        limit (int, optional): Pagination limit

    Returns:
        JSON: Search results
    """
    resultdb = get_db_or_error('resultdb')

    # Get query parameters
    query = request.args.get('q')
    project = request.args.get('project')
    offset = request.args.get('offset', default=0, type=int)
    limit = request.args.get('limit', default=20, type=int)

    if not query:
        return api_error("Search query is required", 400)

    # Check if search is supported
    if not hasattr(resultdb, 'search'):
        return api_error("Search is not supported by the current result database", 501)

    # Search results
    try:
        results = resultdb.search(query, project=project, offset=offset, limit=limit)

        # Convert ObjectId to string for JSON serialization
        for result in results:
            if '_id' in result and hasattr(result['_id'], '__str__'):
                result['_id'] = str(result['_id'])

        # Count total if available
        total = None
        if hasattr(resultdb, 'count_search'):
            total = resultdb.count_search(query, project=project)

        return api_success({
            'results': results,
            'query': query,
            'project': project,
            'total': total,
            'offset': offset,
            'limit': limit
        })
    except Exception as e:
        logger.error(f"Failed to search results: {e}")
        return api_error(f"Failed to search results: {e}", 500)
