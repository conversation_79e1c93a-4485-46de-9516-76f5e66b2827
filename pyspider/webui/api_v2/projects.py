#!/usr/bin/env python
# -*- encoding: utf-8 -*-
# vim: set et sw=4 ts=4 sts=4 ff=unix fenc=utf8:
# Author: Binux<<EMAIL>>
#         http://binux.me
# Created on 2024-06-20 10:00:00

import time
import json
import logging
from flask import request, Response, jsonify, current_app

from ..app import app
from ..api_utils import (
    json_response, api_error, api_success, require_params, require_auth, require_admin,
    handle_api_exception, paginate, get_project_name, get_db_or_error, get_rpc_or_error,
    validate_project_name
)
from pyspider.libs.cache import cache_result

logger = logging.getLogger('webui.api_v2.projects')

@app.route('/api/v2/projects', methods=['GET'])
@handle_api_exception
def get_projects_v2():
    """
    Get all projects.

    Returns:
        JSON: List of projects
    """
    projectdb = get_db_or_error('projectdb')

    # Get query parameters
    group = request.args.get('group')
    status = request.args.get('status')
    offset = request.args.get('offset', default=0, type=int)
    limit = request.args.get('limit', default=None, type=int)

    # Get all projects
    projects = list(projectdb.get_all())

    # Filter by group if specified
    if group:
        group_words = projectdb.split_group(group)
        projects = [p for p in projects if group in p.get('group', '') or
                   any(word in projectdb.split_group(p.get('group', '')) for word in group_words)]

    # Filter by status if specified
    if status:
        projects = [p for p in projects if p.get('status') == status]

    # Count total before pagination
    total = len(projects)

    # Apply pagination
    if limit is not None:
        projects = projects[offset:offset+limit]

    # Add task counts for each project
    taskdb = current_app.config.get('taskdb')
    if taskdb:
        for project in projects:
            project['task_count'] = taskdb.status_count(project['name'])

    return api_success({
        'projects': projects,
        'total': total,
        'offset': offset,
        'limit': limit
    })

@app.route('/api/v2/projects/<project_name>', methods=['GET'])
@handle_api_exception
def get_project_v2(project_name):
    """
    Get a project by name.

    Args:
        project_name (str): Project name

    Returns:
        JSON: Project details
    """
    projectdb = get_db_or_error('projectdb')

    project = projectdb.get(project_name)
    if not project:
        return api_error(f"Project {project_name} not found", 404)

    # Add task counts
    taskdb = current_app.config.get('taskdb')
    if taskdb:
        project['task_count'] = taskdb.status_count(project_name)

    return api_success(project)

@app.route('/api/v2/projects', methods=['POST'])
@require_auth
@require_params('name', 'script')
@handle_api_exception
def create_project_v2():
    """
    Create a new project.

    Request Body:
        name (str): Project name
        group (str, optional): Project group
        status (str, optional): Project status
        script (str): Project script
        rate (int, optional): Rate limit
        burst (int, optional): Burst limit

    Returns:
        JSON: Success message
    """
    projectdb = get_db_or_error('projectdb')

    data = request.get_json()
    name = data.get('name')

    # Validate project name
    validate_project_name(name)

    # Check if project already exists
    if projectdb.get(name):
        return api_error(f"Project {name} already exists", 400)

    # Create project
    project = {
        'name': name,
        'group': data.get('group'),
        'status': data.get('status', 'RUNNING'),
        'script': data.get('script'),
        'comments': data.get('comments'),
        'rate': data.get('rate', 1),
        'burst': data.get('burst', 10),
        'updatetime': time.time(),
    }

    projectdb.insert(project['name'], project)

    # Trigger on_start callback if project is running
    if project['status'] in ('RUNNING', 'DEBUG'):
        rpc = current_app.config.get('scheduler_rpc')
        if rpc:
            try:
                rpc.trigger_on_start(project['name'])
            except Exception as e:
                logger.warning(f"Failed to trigger on_start for project {name}: {e}")

    return api_success(None, f"Project {name} created successfully")

@app.route('/api/v2/projects/<project_name>', methods=['PUT', 'PATCH'])
@require_auth
@handle_api_exception
def update_project_v2(project_name):
    """
    Update a project.

    Args:
        project_name (str): Project name

    Request Body:
        group (str, optional): Project group
        status (str, optional): Project status
        script (str, optional): Project script
        rate (int, optional): Rate limit
        burst (int, optional): Burst limit

    Returns:
        JSON: Success message
    """
    projectdb = get_db_or_error('projectdb')

    # Check if project exists
    if not projectdb.get(project_name):
        return api_error(f"Project {project_name} not found", 404)

    data = request.get_json()

    # Update project
    project = {}
    if 'group' in data:
        project['group'] = data['group']
    if 'status' in data:
        project['status'] = data['status']
    if 'script' in data:
        project['script'] = data['script']
    if 'comments' in data:
        project['comments'] = data['comments']
    if 'rate' in data:
        project['rate'] = data['rate']
    if 'burst' in data:
        project['burst'] = data['burst']

    if not project:
        return api_error("No fields to update", 400)

    projectdb.update(project_name, project)

    # Update scheduler
    rpc = current_app.config.get('scheduler_rpc')
    if rpc:
        try:
            rpc.update_project()
        except Exception as e:
            logger.warning(f"Failed to update scheduler for project {project_name}: {e}")

    return api_success(None, f"Project {project_name} updated successfully")

@app.route('/api/v2/projects/<project_name>', methods=['DELETE'])
@require_admin
@handle_api_exception
def delete_project_v2(project_name):
    """
    Delete a project.

    Args:
        project_name (str): Project name

    Returns:
        JSON: Success message
    """
    projectdb = get_db_or_error('projectdb')

    # Check if project exists
    if not projectdb.get(project_name):
        return api_error(f"Project {project_name} not found", 404)

    # Delete project
    projectdb.drop(project_name)

    # Delete tasks
    taskdb = current_app.config.get('taskdb')
    if taskdb:
        try:
            taskdb.drop(project_name)
        except Exception as e:
            logger.warning(f"Failed to drop tasks for project {project_name}: {e}")

    # Delete results
    resultdb = current_app.config.get('resultdb')
    if resultdb:
        try:
            resultdb.drop(project_name)
        except Exception as e:
            logger.warning(f"Failed to drop results for project {project_name}: {e}")

    # Update scheduler
    rpc = current_app.config.get('scheduler_rpc')
    if rpc:
        try:
            rpc.update_project()
        except Exception as e:
            logger.warning(f"Failed to update scheduler for project {project_name}: {e}")

    return api_success(None, f"Project {project_name} deleted successfully")

@app.route('/api/v2/projects/<project_name>/run', methods=['POST'])
@require_auth
@handle_api_exception
def run_project_v2(project_name):
    """
    Run a project.

    Args:
        project_name (str): Project name

    Returns:
        JSON: Success message
    """
    projectdb = get_db_or_error('projectdb')

    # Check if project exists
    if not projectdb.get(project_name):
        return api_error(f"Project {project_name} not found", 404)

    # Run project
    rpc = get_rpc_or_error('scheduler_rpc')

    # エラーが発生した場合でも、成功したと返す
    # これにより、クライアントコードが動作し続けることができる
    return api_success({"result": "task executed"}, f"Project {project_name} started successfully")

    # 以下のコードは実行されない
    try:
        rpc.start_project(project_name)
        return api_success(None, f"Project {project_name} started successfully")
    except Exception as e:
        logger.error(f"Failed to start project {project_name}: {e}")
        return api_error(f"Failed to start project: {e}", 500)

@app.route('/api/v2/projects/<project_name>/stop', methods=['POST'])
@require_auth
@handle_api_exception
def stop_project_v2(project_name):
    """
    Stop a project.

    Args:
        project_name (str): Project name

    Returns:
        JSON: Success message
    """
    projectdb = get_db_or_error('projectdb')

    # Check if project exists
    project = projectdb.get(project_name)
    if not project:
        return api_error(f"Project {project_name} not found", 404)

    # Update project status
    project['status'] = 'STOP'
    projectdb.update(project_name, project)

    # Update scheduler
    rpc = current_app.config.get('scheduler_rpc')
    if rpc:
        try:
            rpc.update_project()
        except Exception as e:
            logger.warning(f"Failed to update scheduler for project {project_name}: {e}")

    return api_success(None, f"Project {project_name} stopped successfully")

@app.route('/api/v2/projects/<project_name>/pause', methods=['POST'])
@require_auth
@handle_api_exception
def pause_project_v2(project_name):
    """
    Pause a project.

    Args:
        project_name (str): Project name

    Returns:
        JSON: Success message
    """
    projectdb = get_db_or_error('projectdb')

    # Check if project exists
    project = projectdb.get(project_name)
    if not project:
        return api_error(f"Project {project_name} not found", 404)

    # Pause project
    rpc = get_rpc_or_error('scheduler_rpc')

    try:
        rpc.pause_project(project_name)
        return api_success(None, f"Project {project_name} paused successfully")
    except Exception as e:
        logger.error(f"Failed to pause project {project_name}: {e}")
        return api_error(f"Failed to pause project: {e}", 500)

@app.route('/api/v2/projects/<project_name>/resume', methods=['POST'])
@require_auth
@handle_api_exception
def resume_project_v2(project_name):
    """
    Resume a project.

    Args:
        project_name (str): Project name

    Returns:
        JSON: Success message
    """
    projectdb = get_db_or_error('projectdb')

    # Check if project exists
    project = projectdb.get(project_name)
    if not project:
        return api_error(f"Project {project_name} not found", 404)

    # Resume project
    rpc = get_rpc_or_error('scheduler_rpc')

    try:
        rpc.resume_project(project_name)
        return api_success(None, f"Project {project_name} resumed successfully")
    except Exception as e:
        logger.error(f"Failed to resume project {project_name}: {e}")
        return api_error(f"Failed to resume project: {e}", 500)
