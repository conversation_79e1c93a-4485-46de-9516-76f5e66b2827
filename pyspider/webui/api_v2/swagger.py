#!/usr/bin/env python
# -*- encoding: utf-8 -*-
# vim: set et sw=4 ts=4 sts=4 ff=unix fenc=utf8:
# Author: Binux<<EMAIL>>
#         http://binux.me
# Created on 2024-06-20 10:00:00

import os
import json
from flask import request, Response, jsonify, current_app, send_from_directory

from ..app import app
from ..api_utils import json_response, handle_api_exception

@app.route('/api/v2/swagger.json', methods=['GET'])
@handle_api_exception
def get_swagger_json():
    """Get Swagger JSON specification."""
    swagger_spec = {
        "openapi": "3.0.0",
        "info": {
            "title": "pyspiderNX2 API",
            "description": "RESTful API for pyspiderNX2",
            "version": "2.0.0"
        },
        "servers": [
            {
                "url": "/api/v2",
                "description": "API v2"
            }
        ],
        "components": {
            "securitySchemes": {
                "bearerAuth": {
                    "type": "http",
                    "scheme": "bearer",
                    "bearerFormat": "JWT"
                },
                "apiKeyAuth": {
                    "type": "apiKey",
                    "in": "header",
                    "name": "X-API-Key"
                }
            },
            "schemas": {
                "Error": {
                    "type": "object",
                    "properties": {
                        "error": {
                            "type": "string"
                        },
                        "status": {
                            "type": "string",
                            "enum": ["error"]
                        },
                        "timestamp": {
                            "type": "number"
                        }
                    }
                },
                "Success": {
                    "type": "object",
                    "properties": {
                        "data": {
                            "type": "object"
                        },
                        "message": {
                            "type": "string"
                        },
                        "status": {
                            "type": "string",
                            "enum": ["success"]
                        },
                        "timestamp": {
                            "type": "number"
                        }
                    }
                },
                "Project": {
                    "type": "object",
                    "properties": {
                        "name": {
                            "type": "string"
                        },
                        "group": {
                            "type": "string"
                        },
                        "status": {
                            "type": "string",
                            "enum": ["RUNNING", "STOP", "CHECKING", "DEBUG", "PAUSED"]
                        },
                        "script": {
                            "type": "string"
                        },
                        "comments": {
                            "type": "string"
                        },
                        "rate": {
                            "type": "number"
                        },
                        "burst": {
                            "type": "number"
                        },
                        "updatetime": {
                            "type": "number"
                        }
                    }
                },
                "Task": {
                    "type": "object",
                    "properties": {
                        "taskid": {
                            "type": "string"
                        },
                        "project": {
                            "type": "string"
                        },
                        "url": {
                            "type": "string"
                        },
                        "status": {
                            "type": "integer"
                        },
                        "status_string": {
                            "type": "string",
                            "enum": ["ACTIVE", "SUCCESS", "FAILED", "BAD"]
                        },
                        "schedule": {
                            "type": "object"
                        },
                        "fetch": {
                            "type": "object"
                        },
                        "process": {
                            "type": "object"
                        },
                        "track": {
                            "type": "object"
                        },
                        "lastcrawltime": {
                            "type": "number"
                        },
                        "updatetime": {
                            "type": "number"
                        }
                    }
                },
                "Result": {
                    "type": "object",
                    "properties": {
                        "taskid": {
                            "type": "string"
                        },
                        "url": {
                            "type": "string"
                        },
                        "result": {
                            "type": "object"
                        },
                        "updatetime": {
                            "type": "number"
                        }
                    }
                }
            }
        },
        "security": [
            {
                "bearerAuth": []
            },
            {
                "apiKeyAuth": []
            }
        ],
        "paths": {
            # Projects
            "/projects": {
                "get": {
                    "summary": "Get all projects",
                    "description": "Get a list of all projects",
                    "responses": {
                        "200": {
                            "description": "List of projects",
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "$ref": "#/components/schemas/Success"
                                    }
                                }
                            }
                        }
                    }
                },
                "post": {
                    "summary": "Create a new project",
                    "description": "Create a new project",
                    "requestBody": {
                        "required": True,
                        "content": {
                            "application/json": {
                                "schema": {
                                    "$ref": "#/components/schemas/Project"
                                }
                            }
                        }
                    },
                    "responses": {
                        "200": {
                            "description": "Project created successfully",
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "$ref": "#/components/schemas/Success"
                                    }
                                }
                            }
                        },
                        "400": {
                            "description": "Invalid request",
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "$ref": "#/components/schemas/Error"
                                    }
                                }
                            }
                        }
                    }
                }
            },
            "/projects/{project_name}": {
                "get": {
                    "summary": "Get a project",
                    "description": "Get a project by name",
                    "parameters": [
                        {
                            "name": "project_name",
                            "in": "path",
                            "required": True,
                            "schema": {
                                "type": "string"
                            }
                        }
                    ],
                    "responses": {
                        "200": {
                            "description": "Project details",
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "$ref": "#/components/schemas/Success"
                                    }
                                }
                            }
                        },
                        "404": {
                            "description": "Project not found",
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "$ref": "#/components/schemas/Error"
                                    }
                                }
                            }
                        }
                    }
                },
                "put": {
                    "summary": "Update a project",
                    "description": "Update a project by name",
                    "parameters": [
                        {
                            "name": "project_name",
                            "in": "path",
                            "required": True,
                            "schema": {
                                "type": "string"
                            }
                        }
                    ],
                    "requestBody": {
                        "required": True,
                        "content": {
                            "application/json": {
                                "schema": {
                                    "$ref": "#/components/schemas/Project"
                                }
                            }
                        }
                    },
                    "responses": {
                        "200": {
                            "description": "Project updated successfully",
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "$ref": "#/components/schemas/Success"
                                    }
                                }
                            }
                        },
                        "404": {
                            "description": "Project not found",
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "$ref": "#/components/schemas/Error"
                                    }
                                }
                            }
                        }
                    }
                },
                "delete": {
                    "summary": "Delete a project",
                    "description": "Delete a project by name",
                    "parameters": [
                        {
                            "name": "project_name",
                            "in": "path",
                            "required": True,
                            "schema": {
                                "type": "string"
                            }
                        }
                    ],
                    "responses": {
                        "200": {
                            "description": "Project deleted successfully",
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "$ref": "#/components/schemas/Success"
                                    }
                                }
                            }
                        },
                        "404": {
                            "description": "Project not found",
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "$ref": "#/components/schemas/Error"
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
    
    return Response(json.dumps(swagger_spec), mimetype='application/json')

@app.route('/api/v2/docs', methods=['GET'])
def swagger_ui():
    """Serve Swagger UI."""
    return """
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <title>pyspiderNX2 API Documentation</title>
        <link rel="stylesheet" type="text/css" href="https://unpkg.com/swagger-ui-dist@3/swagger-ui.css">
    </head>
    <body>
        <div id="swagger-ui"></div>
        <script src="https://unpkg.com/swagger-ui-dist@3/swagger-ui-bundle.js"></script>
        <script>
            window.onload = function() {
                const ui = SwaggerUIBundle({
                    url: "/api/v2/swagger.json",
                    dom_id: '#swagger-ui',
                    deepLinking: true,
                    presets: [
                        SwaggerUIBundle.presets.apis,
                        SwaggerUIBundle.SwaggerUIStandalonePreset
                    ],
                    layout: "BaseLayout",
                    docExpansion: "list",
                    defaultModelsExpandDepth: 1,
                    defaultModelExpandDepth: 1
                });
                window.ui = ui;
            };
        </script>
    </body>
    </html>
    """
