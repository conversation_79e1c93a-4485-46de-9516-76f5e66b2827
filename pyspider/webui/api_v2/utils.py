#!/usr/bin/env python
# -*- encoding: utf-8 -*-
# vim: set et sw=4 ts=4 sts=4 ff=unix fenc=utf8:
"""
API v2 Utilities - API v2で使用される共通ユーティリティ関数
"""

# Import common API utilities from the main api_utils module
from pyspider.webui.api_utils import (
    json_response,
    json_encoder,
    api_error,
    api_success,
    require_params,
    require_auth,
    require_admin,
    handle_api_exception,
    paginate,
    get_project_name,
    get_task_id,
    get_db_or_error,
    get_rpc_or_error
)

# Re-export all functions for backward compatibility
__all__ = [
    'json_response',
    'json_encoder',
    'api_error',
    'api_success',
    'require_params',
    'require_auth',
    'require_admin',
    'handle_api_exception',
    'paginate',
    'get_project_name',
    'get_task_id',
    'get_db_or_error',
    'get_rpc_or_error'
]
