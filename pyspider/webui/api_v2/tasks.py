#!/usr/bin/env python
# -*- encoding: utf-8 -*-
# vim: set et sw=4 ts=4 sts=4 ff=unix fenc=utf8:
# Author: Binux<<EMAIL>>
#         http://binux.me
# Created on 2024-06-20 10:00:00

import time
import json
import logging
from flask import request, Response, jsonify, current_app

from ..app import app
from ..api_utils import (
    json_response, api_error, api_success, require_params, require_auth, require_admin,
    handle_api_exception, paginate, get_project_name, get_task_id, get_db_or_error, get_rpc_or_error
)

logger = logging.getLogger('webui.api_v2.tasks')

@app.route('/api/v2/tasks', methods=['GET'])
@handle_api_exception
def get_tasks_v2():
    """
    Get tasks.
    
    Query Parameters:
        project (str, optional): Filter by project
        status (str, optional): Filter by status
        offset (int, optional): Pagination offset
        limit (int, optional): Pagination limit
        
    Returns:
        JSON: List of tasks
    """
    taskdb = get_db_or_error('taskdb')
    
    # Get query parameters
    project = request.args.get('project')
    status = request.args.get('status')
    offset = request.args.get('offset', default=0, type=int)
    limit = request.args.get('limit', default=20, type=int)
    
    # Convert status string to int if provided
    status_int = None
    if status:
        if status.isdigit():
            status_int = int(status)
        else:
            status_int = taskdb.status_to_int(status)
    
    # Get tasks
    tasks = []
    if project and status_int:
        for task in taskdb.load_tasks(status_int, project=project):
            tasks.append(task)
    elif project:
        for status in (taskdb.ACTIVE, taskdb.SUCCESS, taskdb.FAILED, taskdb.BAD):
            for task in taskdb.load_tasks(status, project=project):
                tasks.append(task)
    elif status_int:
        for task in taskdb.load_tasks(status_int):
            tasks.append(task)
    else:
        # Default to active tasks
        for task in taskdb.load_tasks(taskdb.ACTIVE):
            tasks.append(task)
    
    # Sort tasks by updatetime
    tasks.sort(key=lambda x: x.get('updatetime', 0), reverse=True)
    
    # Count total before pagination
    total = len(tasks)
    
    # Apply pagination
    tasks = tasks[offset:offset+limit]
    
    # Convert status int to string
    for task in tasks:
        if 'status' in task:
            task['status_string'] = taskdb.status_to_string(task['status'])
    
    return api_success({
        'tasks': tasks,
        'total': total,
        'offset': offset,
        'limit': limit
    })

@app.route('/api/v2/tasks/<project_name>/<task_id>', methods=['GET'])
@handle_api_exception
def get_task_v2(project_name, task_id):
    """
    Get a task by project name and task ID.
    
    Args:
        project_name (str): Project name
        task_id (str): Task ID
        
    Returns:
        JSON: Task details
    """
    taskdb = get_db_or_error('taskdb')
    
    task = taskdb.get_task(project_name, task_id)
    if not task:
        return api_error(f"Task {project_name}:{task_id} not found", 404)
    
    # Convert status int to string
    if 'status' in task:
        task['status_string'] = taskdb.status_to_string(task['status'])
    
    # Get result if available
    resultdb = current_app.config.get('resultdb')
    if resultdb:
        result = resultdb.get(project_name, task_id)
        if result:
            task['result'] = result
    
    return api_success(task)

@app.route('/api/v2/tasks', methods=['POST'])
@require_auth
@require_params('project', 'url')
@handle_api_exception
def create_task_v2():
    """
    Create a new task.
    
    Request Body:
        project (str): Project name
        url (str): Task URL
        taskid (str, optional): Task ID
        fetch (dict, optional): Fetch configuration
        process (dict, optional): Process configuration
        schedule (dict, optional): Schedule configuration
        
    Returns:
        JSON: Success message
    """
    data = request.get_json()
    
    project = data.get('project')
    url = data.get('url')
    taskid = data.get('taskid')
    
    # Check if project exists
    projectdb = get_db_or_error('projectdb')
    if not projectdb.get(project):
        return api_error(f"Project {project} not found", 404)
    
    # Create task
    task = {
        'project': project,
        'url': url,
        'taskid': taskid,
        'fetch': data.get('fetch', {}),
        'process': data.get('process', {}),
        'schedule': data.get('schedule', {})
    }
    
    # Send task to scheduler
    rpc = get_rpc_or_error('scheduler_rpc')
    
    try:
        rpc.send_task(task)
        return api_success(None, f"Task created successfully")
    except Exception as e:
        logger.error(f"Failed to create task: {e}")
        return api_error(f"Failed to create task: {e}", 500)

@app.route('/api/v2/tasks/<project_name>/<task_id>/rerun', methods=['POST'])
@require_auth
@handle_api_exception
def rerun_task_v2(project_name, task_id):
    """
    Rerun a task.
    
    Args:
        project_name (str): Project name
        task_id (str): Task ID
        
    Returns:
        JSON: Success message
    """
    taskdb = get_db_or_error('taskdb')
    
    task = taskdb.get_task(project_name, task_id)
    if not task:
        return api_error(f"Task {project_name}:{task_id} not found", 404)
    
    # Reset task status
    task['status'] = taskdb.ACTIVE
    task['schedule'] = task.get('schedule', {})
    task['schedule']['retries'] = 0
    task['schedule']['retried'] = 0
    task['schedule']['exetime'] = 0
    task['lastcrawltime'] = 0
    task['updatetime'] = time.time()
    
    # Update task in database
    taskdb.update(project_name, task_id, task)
    
    # Send task to scheduler
    rpc = get_rpc_or_error('scheduler_rpc')
    
    try:
        rpc.update_project()
        return api_success(None, f"Task {project_name}:{task_id} rerun successfully")
    except Exception as e:
        logger.error(f"Failed to rerun task {project_name}:{task_id}: {e}")
        return api_error(f"Failed to rerun task: {e}", 500)

@app.route('/api/v2/tasks/<project_name>/<task_id>', methods=['DELETE'])
@require_auth
@handle_api_exception
def delete_task_v2(project_name, task_id):
    """
    Delete a task.
    
    Args:
        project_name (str): Project name
        task_id (str): Task ID
        
    Returns:
        JSON: Success message
    """
    taskdb = get_db_or_error('taskdb')
    
    task = taskdb.get_task(project_name, task_id)
    if not task:
        return api_error(f"Task {project_name}:{task_id} not found", 404)
    
    # Delete task
    taskdb.delete(project_name, task_id)
    
    # Delete result if available
    resultdb = current_app.config.get('resultdb')
    if resultdb:
        try:
            resultdb.delete(project_name, task_id)
        except Exception as e:
            logger.warning(f"Failed to delete result for task {project_name}:{task_id}: {e}")
    
    return api_success(None, f"Task {project_name}:{task_id} deleted successfully")

@app.route('/api/v2/tasks/bulk', methods=['POST'])
@require_auth
@require_params('operation', 'tasks')
@handle_api_exception
def bulk_task_operations_v2():
    """
    Perform bulk operations on tasks.
    
    Request Body:
        operation (str): Operation to perform (rerun, delete)
        tasks (list): List of task IDs in format "project:taskid"
        
    Returns:
        JSON: Success message
    """
    data = request.get_json()
    
    operation = data.get('operation')
    tasks = data.get('tasks')
    
    if not isinstance(tasks, list) or not tasks:
        return api_error("tasks must be a non-empty list", 400)
    
    # Validate operation
    valid_operations = ['rerun', 'delete']
    if operation not in valid_operations:
        return api_error(f"Invalid operation. Must be one of: {', '.join(valid_operations)}", 400)
    
    taskdb = get_db_or_error('taskdb')
    resultdb = current_app.config.get('resultdb')
    
    results = {
        'success': [],
        'failed': []
    }
    
    for task_id in tasks:
        try:
            if ':' not in task_id:
                results['failed'].append({
                    'id': task_id,
                    'error': 'Invalid task ID format. Must be "project:taskid"'
                })
                continue
            
            project, tid = task_id.split(':', 1)
            
            task = taskdb.get_task(project, tid)
            if not task:
                results['failed'].append({
                    'id': task_id,
                    'error': 'Task not found'
                })
                continue
            
            if operation == 'rerun':
                # Reset task status
                task['status'] = taskdb.ACTIVE
                task['schedule'] = task.get('schedule', {})
                task['schedule']['retries'] = 0
                task['schedule']['retried'] = 0
                task['schedule']['exetime'] = 0
                task['lastcrawltime'] = 0
                task['updatetime'] = time.time()
                
                # Update task in database
                taskdb.update(project, tid, task)
                
                results['success'].append(task_id)
            
            elif operation == 'delete':
                # Delete task
                taskdb.delete(project, tid)
                
                # Delete result if available
                if resultdb:
                    try:
                        resultdb.delete(project, tid)
                    except Exception as e:
                        logger.warning(f"Failed to delete result for task {task_id}: {e}")
                
                results['success'].append(task_id)
        
        except Exception as e:
            logger.error(f"Failed to {operation} task {task_id}: {e}")
            results['failed'].append({
                'id': task_id,
                'error': str(e)
            })
    
    # Update scheduler
    rpc = current_app.config.get('scheduler_rpc')
    if rpc and operation == 'rerun':
        try:
            rpc.update_project()
        except Exception as e:
            logger.warning(f"Failed to update scheduler: {e}")
    
    return api_success({
        'operation': operation,
        'results': results,
        'success_count': len(results['success']),
        'failed_count': len(results['failed'])
    })

@app.route('/api/v2/tasks/status_count', methods=['GET'])
@handle_api_exception
def task_status_count_v2():
    """
    Get task count by status.
    
    Query Parameters:
        project (str, optional): Filter by project
        
    Returns:
        JSON: Task count by status
    """
    taskdb = get_db_or_error('taskdb')
    
    # Get query parameters
    project = request.args.get('project')
    
    if project:
        # Get task count for a specific project
        status_count = taskdb.status_count(project)
    else:
        # Get task count for all projects
        status_count = {}
        for project in taskdb.projects:
            project_count = taskdb.status_count(project)
            for status, count in project_count.items():
                status_count[status] = status_count.get(status, 0) + count
    
    # Convert status int to string
    result = {}
    for status, count in status_count.items():
        result[taskdb.status_to_string(status)] = count
    
    return api_success(result)
