#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
File Output API v2 - ファイル出力機能のAPI
"""

import os
import json
import logging
from pathlib import Path
from datetime import datetime
from flask import Blueprint, request, jsonify, Response

from pyspider.webui.api_v2.utils import api_success, api_error, handle_api_exception

logger = logging.getLogger(__name__)

app = Blueprint('file_output_api_v2', __name__)


@app.route('/api/v2/file-output/stats', methods=['GET'])
@handle_api_exception
def get_file_output_stats():
    """
    ファイル出力の統計情報を取得
    
    Returns:
        JSON: ファイル出力統計情報
    """
    try:
        # 設定からファイル出力ディレクトリを取得
        from pyspider.run import g
        result_worker_config = g.config.get('result_worker', {})
        file_output_config = result_worker_config.get('file_output', {})
        
        if not file_output_config.get('enabled', False):
            return api_success({
                "enabled": False,
                "message": "File output is disabled"
            })
        
        output_dir = Path(file_output_config.get('output_dir', 'results'))
        
        stats = {
            "enabled": True,
            "output_directory": str(output_dir),
            "max_file_size": file_output_config.get('max_file_size', 104857600),
            "rotation_enabled": file_output_config.get('enable_rotation', True),
            "rotation_count": file_output_config.get('rotation_count', 10),
            "files": [],
            "total_files": 0,
            "total_size": 0,
            "total_lines": 0
        }
        
        # 出力ディレクトリが存在する場合、ファイル情報を収集
        if output_dir.exists():
            for file_path in output_dir.glob("*.jsonl"):
                try:
                    file_stat = file_path.stat()
                    line_count = _count_lines(file_path)
                    
                    file_info = {
                        "name": file_path.name,
                        "size": file_stat.st_size,
                        "size_human": _format_size(file_stat.st_size),
                        "modified": datetime.fromtimestamp(file_stat.st_mtime).isoformat(),
                        "lines": line_count,
                        "project": _extract_project_name(file_path.name)
                    }
                    
                    stats["files"].append(file_info)
                    stats["total_size"] += file_stat.st_size
                    stats["total_lines"] += line_count
                    
                except Exception as e:
                    logger.error(f"Error getting stats for {file_path}: {e}")
            
            # ローテーションファイルも含める
            for file_path in output_dir.glob("*.*.jsonl"):
                try:
                    file_stat = file_path.stat()
                    line_count = _count_lines(file_path)
                    
                    file_info = {
                        "name": file_path.name,
                        "size": file_stat.st_size,
                        "size_human": _format_size(file_stat.st_size),
                        "modified": datetime.fromtimestamp(file_stat.st_mtime).isoformat(),
                        "lines": line_count,
                        "project": _extract_project_name(file_path.name),
                        "is_rotated": True
                    }
                    
                    stats["files"].append(file_info)
                    stats["total_size"] += file_stat.st_size
                    stats["total_lines"] += line_count
                    
                except Exception as e:
                    logger.error(f"Error getting stats for rotated file {file_path}: {e}")
        
        stats["total_files"] = len(stats["files"])
        stats["total_size_human"] = _format_size(stats["total_size"])
        
        # ファイルを更新日時でソート
        stats["files"].sort(key=lambda x: x["modified"], reverse=True)
        
        return api_success(stats)
        
    except Exception as e:
        logger.error(f"Error getting file output stats: {e}")
        return api_error(f"Failed to get file output stats: {str(e)}", 500)


@app.route('/api/v2/file-output/download/<filename>', methods=['GET'])
@handle_api_exception
def download_file(filename):
    """
    ファイルをダウンロード
    
    Args:
        filename (str): ダウンロードするファイル名
        
    Returns:
        File: ファイルデータ
    """
    try:
        # 設定からファイル出力ディレクトリを取得
        from pyspider.run import g
        result_worker_config = g.config.get('result_worker', {})
        file_output_config = result_worker_config.get('file_output', {})
        
        if not file_output_config.get('enabled', False):
            return api_error("File output is disabled", 400)
        
        output_dir = Path(file_output_config.get('output_dir', 'results'))
        file_path = output_dir / filename
        
        # セキュリティチェック: ディレクトリトラバーサル攻撃を防ぐ
        if not str(file_path.resolve()).startswith(str(output_dir.resolve())):
            return api_error("Invalid file path", 400)
        
        # ファイルの存在確認
        if not file_path.exists():
            return api_error(f"File {filename} not found", 404)
        
        # ファイルタイプの確認
        if not filename.endswith('.jsonl'):
            return api_error("Only JSONL files can be downloaded", 400)
        
        # ファイルを読み込んでレスポンスとして返す
        def generate():
            with open(file_path, 'r', encoding='utf-8') as f:
                for line in f:
                    yield line
        
        return Response(
            generate(),
            mimetype='application/x-jsonlines',
            headers={
                'Content-Disposition': f'attachment; filename="{filename}"'
            }
        )
        
    except Exception as e:
        logger.error(f"Error downloading file {filename}: {e}")
        return api_error(f"Failed to download file: {str(e)}", 500)


def _count_lines(file_path: Path) -> int:
    """
    ファイルの行数をカウント
    
    Args:
        file_path (Path): ファイルパス
        
    Returns:
        int: 行数
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return sum(1 for _ in f)
    except Exception:
        return 0


def _format_size(size_bytes: int) -> str:
    """
    バイト数を人間が読みやすい形式に変換
    
    Args:
        size_bytes (int): バイト数
        
    Returns:
        str: フォーマットされたサイズ
    """
    if size_bytes == 0:
        return "0 B"
    
    size_names = ["B", "KB", "MB", "GB", "TB"]
    i = 0
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1
    
    return f"{size_bytes:.1f} {size_names[i]}"


def _extract_project_name(filename: str) -> str:
    """
    ファイル名からプロジェクト名を抽出
    
    Args:
        filename (str): ファイル名
        
    Returns:
        str: プロジェクト名
    """
    # ファイル名の形式: {project_name}_results.jsonl または {project_name}_results.{n}.jsonl
    if filename.endswith('_results.jsonl'):
        return filename[:-14]  # '_results.jsonl' を除去
    elif '_results.' in filename and filename.endswith('.jsonl'):
        # ローテーションファイルの場合
        parts = filename.split('_results.')
        if len(parts) >= 2:
            return parts[0]
    
    return "unknown"
