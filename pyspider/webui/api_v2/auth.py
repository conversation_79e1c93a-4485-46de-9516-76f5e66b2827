#!/usr/bin/env python
# -*- encoding: utf-8 -*-
# vim: set et sw=4 ts=4 sts=4 ff=unix fenc=utf8:
# Author: Binux<<EMAIL>>
#         http://binux.me
# Created on 2024-06-20 10:00:00

import time
import json
import logging
import os
import uuid
import hashlib
import hmac
import base64
from datetime import datetime, timedelta
from flask import request, Response, jsonify, current_app, g, session

from ..app import app
from ..api_utils import (
    json_response, api_error, api_success, require_params, require_auth, require_admin,
    handle_api_exception
)

logger = logging.getLogger('webui.api_v2.auth')

# API key storage
api_keys = {}

def generate_token(username, expiration=24):
    """Generate a token for the given username."""
    payload = {
        'username': username,
        'exp': int((datetime.now() + timedelta(hours=expiration)).timestamp()),
        'iat': int(datetime.now().timestamp()),
        'jti': str(uuid.uuid4())
    }

    # Add admin flag if user is admin
    if username == current_app.config.get('webui_username'):
        payload['is_admin'] = True

    # Convert payload to JSON string
    payload_str = json.dumps(payload)

    # Encode payload
    payload_b64 = base64.b64encode(payload_str.encode()).decode()

    # Generate strong secret key if not provided
    secret = current_app.config.get('webui_password')
    if not secret or secret == 'pyspider':
        # Generate a strong random secret key
        secret = base64.b64encode(os.urandom(32)).decode()
        logger.warning("Using weak or default secret key. Please set a strong webui_password in config.")

    signature = hmac.new(
        secret.encode(),
        payload_b64.encode(),
        hashlib.sha256
    ).hexdigest()

    # Return token
    return f"{payload_b64}.{signature}"

def verify_token(token):
    """Verify a token and return the payload if valid."""
    try:
        # Split token
        payload_b64, signature = token.split('.')

        # Verify signature
        secret = current_app.config.get('webui_password')
        if not secret or secret == 'pyspider':
            # Generate a strong random secret key
            secret = base64.b64encode(os.urandom(32)).decode()
            logger.warning("Using weak or default secret key. Please set a strong webui_password in config.")

        expected_signature = hmac.new(
            secret.encode(),
            payload_b64.encode(),
            hashlib.sha256
        ).hexdigest()

        if not hmac.compare_digest(signature, expected_signature):
            return None

        # Decode payload
        payload_str = base64.b64decode(payload_b64).decode()
        payload = json.loads(payload_str)

        # Check expiration
        if payload.get('exp', 0) < int(datetime.now().timestamp()):
            return None

        return payload
    except Exception as e:
        logger.warning(f"Failed to verify token: {e}")
        return None

# 既存のlogin.pyのbefore_requestと競合するため、この関数は無効化
# @app.before_request
# def check_auth():
#     """Check authentication before each request."""
#     # Skip authentication for non-API routes
#     if not request.path.startswith('/api/'):
#         return
#
#     # Skip authentication for login route
#     if request.path == '/api/v2/auth/login':
#         return
#
#     # Check if authentication is enabled
#     if not current_app.config.get('webui_username'):
#         return
#
#     # Check for token in Authorization header
#     auth_header = request.headers.get('Authorization')
#     if auth_header and auth_header.startswith('Bearer '):
#         token = auth_header[7:]
#         payload = verify_token(token)
#         if payload:
#             g.user = payload
#             return
#
#     # Check for API key in X-API-Key header
#     api_key = request.headers.get('X-API-Key')
#     if api_key and api_key in api_keys:
#         g.user = api_keys[api_key]
#         return
#
#     # Check for session authentication
#     if 'user' in session:
#         g.user = session['user']
#         return

@app.route('/api/v2/auth/login', methods=['POST'])
@require_params('username', 'password')
@handle_api_exception
def login_v2():
    """
    Login and get authentication token.

    Request Body:
        username (str): Username
        password (str): Password

    Returns:
        JSON: Authentication token
    """
    data = request.get_json()
    username = data.get('username')
    password = data.get('password')

    # Check if authentication is enabled
    if not current_app.config.get('webui_username'):
        return api_error("Authentication is not enabled", 403)

    # Check credentials
    if username != current_app.config.get('webui_username') or password != current_app.config.get('webui_password'):
        return api_error("Invalid credentials", 401)

    # Generate token
    token = generate_token(username)

    # Set session
    session['user'] = {
        'username': username,
        'is_admin': True
    }

    return api_success({
        'token': token,
        'username': username,
        'is_admin': True
    })

@app.route('/api/v2/auth/logout', methods=['POST'])
@handle_api_exception
def logout_v2():
    """
    Logout and invalidate session.

    Returns:
        JSON: Success message
    """
    # Clear session
    session.pop('user', None)

    return api_success(None, "Logged out successfully")

@app.route('/api/v2/auth/me', methods=['GET'])
@require_auth
@handle_api_exception
def get_current_user_v2():
    """
    Get current user information.

    Returns:
        JSON: User information
    """
    return api_success(g.user)

@app.route('/api/v2/auth/api_keys', methods=['GET'])
@require_admin
@handle_api_exception
def get_api_keys_v2():
    """
    Get all API keys.

    Returns:
        JSON: List of API keys
    """
    keys = []
    for key, user in api_keys.items():
        keys.append({
            'key': key,
            'username': user.get('username'),
            'created_at': user.get('created_at'),
            'description': user.get('description')
        })

    return api_success(keys)

@app.route('/api/v2/auth/api_keys', methods=['POST'])
@require_admin
@require_params('description')
@handle_api_exception
def create_api_key_v2():
    """
    Create a new API key.

    Request Body:
        description (str): API key description

    Returns:
        JSON: API key
    """
    data = request.get_json()
    description = data.get('description')

    # Generate API key
    api_key = str(uuid.uuid4())

    # Store API key
    api_keys[api_key] = {
        'username': current_app.config.get('webui_username'),
        'is_admin': True,
        'created_at': int(time.time()),
        'description': description
    }

    return api_success({
        'key': api_key,
        'username': current_app.config.get('webui_username'),
        'created_at': api_keys[api_key]['created_at'],
        'description': description
    })

@app.route('/api/v2/auth/api_keys/<api_key>', methods=['DELETE'])
@require_admin
@handle_api_exception
def delete_api_key_v2(api_key):
    """
    Delete an API key.

    Args:
        api_key (str): API key

    Returns:
        JSON: Success message
    """
    if api_key not in api_keys:
        return api_error(f"API key not found", 404)

    # Delete API key
    del api_keys[api_key]

    return api_success(None, f"API key deleted successfully")

@app.route('/api/v2/auth/change_password', methods=['POST'])
@require_admin
@require_params('old_password', 'new_password')
@handle_api_exception
def change_password_v2():
    """
    Change password.

    Request Body:
        old_password (str): Old password
        new_password (str): New password

    Returns:
        JSON: Success message
    """
    data = request.get_json()
    old_password = data.get('old_password')
    new_password = data.get('new_password')

    # Check if authentication is enabled
    if not current_app.config.get('webui_username'):
        return api_error("Authentication is not enabled", 403)

    # Check old password
    if old_password != current_app.config.get('webui_password'):
        return api_error("Invalid old password", 401)

    # Validate new password
    if not new_password or len(new_password) < 8:
        return api_error("New password must be at least 8 characters long", 400)

    # Update password
    current_app.config['webui_password'] = new_password

    # Save config if possible
    config_path = current_app.config.get('config_path')
    if config_path:
        try:
            with open(config_path, 'r') as f:
                config = json.load(f)

            config['webui_password'] = new_password

            with open(config_path, 'w') as f:
                json.dump(config, f, indent=2)
        except Exception as e:
            logger.warning(f"Failed to save config: {e}")

    return api_success(None, "Password changed successfully")
