#!/usr/bin/env python
# -*- encoding: utf-8 -*-
# vim: set et sw=4 ts=4 sts=4 ff=unix fenc=utf8:
# Author: Binux<<EMAIL>>
#         http://binux.me
# Created on 2024-06-20 10:00:00

import time
import json
import logging
import os
import sys
import platform
import psutil
from flask import request, Response, jsonify, current_app

from ..app import app
from ..api_utils import (
    json_response, api_error, api_success, require_params, require_auth, require_admin,
    handle_api_exception, paginate, get_project_name, get_task_id, get_db_or_error, get_rpc_or_error
)
from pyspider.libs.cache import cache_result
from pyspider.webui.metrics import get_scheduler_stats, get_system_stats
from pyspider.webui.components_status import get_components_status

logger = logging.getLogger('webui.api_v2.system')

@app.route('/api/v2/system/info', methods=['GET'])
@handle_api_exception
def get_system_info_v2():
    """
    Get system information.
    
    Returns:
        JSON: System information
    """
    # Get system information
    system_info = {
        'platform': platform.platform(),
        'python_version': platform.python_version(),
        'python_implementation': platform.python_implementation(),
        'cpu_count': psutil.cpu_count(),
        'memory_total': psutil.virtual_memory().total,
        'disk_total': psutil.disk_usage('/').total,
        'hostname': platform.node(),
        'uptime': int(time.time() - psutil.boot_time()),
        'pyspider_version': getattr(sys.modules['pyspider'], '__version__', 'unknown')
    }
    
    return api_success(system_info)

@app.route('/api/v2/system/stats', methods=['GET'])
@cache_result(expire=15, key_prefix='system_stats_v2')
@handle_api_exception
def get_system_stats_v2():
    """
    Get system statistics.
    
    Returns:
        JSON: System statistics
    """
    try:
        # Get system statistics
        stats = get_system_stats()
        
        # Get scheduler statistics
        scheduler_stats = get_scheduler_stats()
        if scheduler_stats:
            stats['scheduler'] = scheduler_stats
        
        # Get component status
        components = get_components_status()
        if components:
            stats['components'] = components
        
        # Get queue information
        rpc = current_app.config.get('scheduler_rpc')
        if rpc:
            try:
                queue_info = rpc.get_queue_info()
                if queue_info:
                    stats['queue'] = queue_info
            except Exception as e:
                logger.warning(f"Failed to get queue info: {e}")
        
        return api_success(stats)
    except Exception as e:
        logger.error(f"Failed to get system stats: {e}")
        return api_error(f"Failed to get system stats: {e}", 500)

@app.route('/api/v2/system/components', methods=['GET'])
@handle_api_exception
def get_components_status_v2():
    """
    Get components status.
    
    Returns:
        JSON: Components status
    """
    try:
        components = get_components_status()
        return api_success(components)
    except Exception as e:
        logger.error(f"Failed to get components status: {e}")
        return api_error(f"Failed to get components status: {e}", 500)

@app.route('/api/v2/system/metrics', methods=['GET'])
@handle_api_exception
def get_metrics_v2():
    """
    Get metrics data.
    
    Returns:
        JSON: Metrics data
    """
    try:
        # Collect metrics
        metrics = {
            'timestamp': time.time(),
            'scheduler': get_scheduler_stats(),
            'system': get_system_stats(),
            'components': get_components_status()
        }
        
        return api_success(metrics)
    except Exception as e:
        logger.error(f"Failed to get metrics: {e}")
        return api_error(f"Failed to get metrics: {e}", 500)

@app.route('/api/v2/system/logs', methods=['GET'])
@require_admin
@handle_api_exception
def get_logs_v2():
    """
    Get system logs.
    
    Query Parameters:
        lines (int, optional): Number of lines to return
        
    Returns:
        JSON: System logs
    """
    try:
        lines = request.args.get('lines', default=100, type=int)
        
        # Get log file path
        log_path = current_app.config.get('log_path')
        if not log_path:
            return api_error("Log path not configured", 404)
        
        # Check if log file exists
        if not os.path.exists(log_path):
            return api_error(f"Log file not found: {log_path}", 404)
        
        # Read log file
        logs = []
        with open(log_path, 'r') as f:
            logs = f.readlines()
        
        # Get last N lines
        logs = logs[-lines:]
        
        return api_success({
            'logs': logs,
            'lines': len(logs),
            'log_path': log_path
        })
    except Exception as e:
        logger.error(f"Failed to get logs: {e}")
        return api_error(f"Failed to get logs: {e}", 500)

@app.route('/api/v2/system/processes', methods=['GET'])
@require_admin
@handle_api_exception
def get_processes_v2():
    """
    Get system processes.
    
    Returns:
        JSON: System processes
    """
    try:
        processes = []
        for proc in psutil.process_iter(['pid', 'name', 'username', 'cpu_percent', 'memory_percent', 'create_time']):
            try:
                pinfo = proc.as_dict()
                processes.append(pinfo)
            except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                pass
        
        # Sort by CPU usage
        processes.sort(key=lambda x: x.get('cpu_percent', 0), reverse=True)
        
        return api_success({
            'processes': processes,
            'count': len(processes)
        })
    except Exception as e:
        logger.error(f"Failed to get processes: {e}")
        return api_error(f"Failed to get processes: {e}", 500)

@app.route('/api/v2/system/databases', methods=['GET'])
@require_admin
@handle_api_exception
def get_databases_info_v2():
    """
    Get databases information.
    
    Returns:
        JSON: Databases information
    """
    try:
        databases = {}
        
        # Get taskdb info
        taskdb = current_app.config.get('taskdb')
        if taskdb:
            databases['taskdb'] = {
                'type': type(taskdb).__name__,
                'projects': len(taskdb.projects)
            }
        
        # Get projectdb info
        projectdb = current_app.config.get('projectdb')
        if projectdb:
            projects = list(projectdb.get_all())
            databases['projectdb'] = {
                'type': type(projectdb).__name__,
                'projects': len(projects)
            }
        
        # Get resultdb info
        resultdb = current_app.config.get('resultdb')
        if resultdb:
            databases['resultdb'] = {
                'type': type(resultdb).__name__
            }
        
        return api_success(databases)
    except Exception as e:
        logger.error(f"Failed to get databases info: {e}")
        return api_error(f"Failed to get databases info: {e}", 500)

@app.route('/api/v2/system/version', methods=['GET'])
@handle_api_exception
def get_version_v2():
    """
    Get pyspider version.
    
    Returns:
        JSON: Version information
    """
    try:
        version = getattr(sys.modules['pyspider'], '__version__', 'unknown')
        return api_success({
            'version': version,
            'python_version': platform.python_version(),
            'platform': platform.platform()
        })
    except Exception as e:
        logger.error(f"Failed to get version: {e}")
        return api_error(f"Failed to get version: {e}", 500)
