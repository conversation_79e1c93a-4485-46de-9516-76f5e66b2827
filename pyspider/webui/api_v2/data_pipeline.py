#!/usr/bin/env python
# -*- encoding: utf-8 -*-
# vim: set et sw=4 ts=4 sts=4 ff=unix fenc=utf8:
# Author: Binux<<EMAIL>>
#         http://binux.me
# Created on 2024-06-20 10:00:00

import time
import json
import logging
import os
from flask import request, Response, jsonify, current_app

from ..app import app
from ..api_utils import (
    json_response, api_error, api_success, require_params, require_auth, require_admin,
    handle_api_exception, paginate, get_project_name, get_task_id, get_db_or_error, get_rpc_or_error
)

logger = logging.getLogger('webui.api_v2.data_pipeline')

# Import data pipeline modules if available
try:
    from pyspider.libs.data_pipeline import DataPipeline
    from pyspider.libs.data_transformers import DataTransformer
    from pyspider.libs.data_quality import DataQualityChecker
    data_pipeline_available = True
except ImportError:
    data_pipeline_available = False
    logger.warning("Data pipeline modules not available")

@app.route('/api/v2/data_pipeline/status', methods=['GET'])
@handle_api_exception
def get_data_pipeline_status_v2():
    """
    Get data pipeline status.
    
    Returns:
        JSON: Data pipeline status
    """
    if not data_pipeline_available:
        return api_error("Data pipeline modules not available", 501)
    
    return api_success({
        'available': True,
        'modules': {
            'DataPipeline': True,
            'DataTransformer': True,
            'DataQualityChecker': True
        }
    })

@app.route('/api/v2/data_pipeline/transformers', methods=['GET'])
@handle_api_exception
def get_transformers_v2():
    """
    Get available data transformers.
    
    Returns:
        JSON: Available transformers
    """
    if not data_pipeline_available:
        return api_error("Data pipeline modules not available", 501)
    
    try:
        transformer = DataTransformer()
        transformers = {}
        
        for name, func in transformer.transformers.items():
            transformers[name] = {
                'name': name,
                'description': func.__doc__ or '',
                'type': 'transformer'
            }
        
        return api_success(transformers)
    except Exception as e:
        logger.error(f"Failed to get transformers: {e}")
        return api_error(f"Failed to get transformers: {e}", 500)

@app.route('/api/v2/data_pipeline/validators', methods=['GET'])
@handle_api_exception
def get_validators_v2():
    """
    Get available data validators.
    
    Returns:
        JSON: Available validators
    """
    if not data_pipeline_available:
        return api_error("Data pipeline modules not available", 501)
    
    try:
        checker = DataQualityChecker()
        validators = {}
        
        for name, func in checker.validators.items():
            validators[name] = {
                'name': name,
                'description': func.__doc__ or '',
                'type': 'validator'
            }
        
        return api_success(validators)
    except Exception as e:
        logger.error(f"Failed to get validators: {e}")
        return api_error(f"Failed to get validators: {e}", 500)

@app.route('/api/v2/data_pipeline/configs', methods=['GET'])
@handle_api_exception
def get_pipeline_configs_v2():
    """
    Get available pipeline configurations.
    
    Returns:
        JSON: Available pipeline configurations
    """
    if not data_pipeline_available:
        return api_error("Data pipeline modules not available", 501)
    
    try:
        # Get pipeline configs directory
        configs_dir = os.path.join(current_app.config.get('data_path', 'data'), 'pipeline_configs')
        
        # Create directory if it doesn't exist
        if not os.path.exists(configs_dir):
            os.makedirs(configs_dir)
        
        # Get all JSON files in the directory
        configs = []
        for filename in os.listdir(configs_dir):
            if filename.endswith('.json'):
                config_path = os.path.join(configs_dir, filename)
                try:
                    with open(config_path, 'r') as f:
                        config = json.load(f)
                        configs.append({
                            'name': filename[:-5],  # Remove .json extension
                            'path': config_path,
                            'config': config
                        })
                except Exception as e:
                    logger.warning(f"Failed to load pipeline config {filename}: {e}")
        
        return api_success(configs)
    except Exception as e:
        logger.error(f"Failed to get pipeline configs: {e}")
        return api_error(f"Failed to get pipeline configs: {e}", 500)

@app.route('/api/v2/data_pipeline/configs/<config_name>', methods=['GET'])
@handle_api_exception
def get_pipeline_config_v2(config_name):
    """
    Get a pipeline configuration.
    
    Args:
        config_name (str): Configuration name
        
    Returns:
        JSON: Pipeline configuration
    """
    if not data_pipeline_available:
        return api_error("Data pipeline modules not available", 501)
    
    try:
        # Get pipeline configs directory
        configs_dir = os.path.join(current_app.config.get('data_path', 'data'), 'pipeline_configs')
        
        # Check if config exists
        config_path = os.path.join(configs_dir, f"{config_name}.json")
        if not os.path.exists(config_path):
            return api_error(f"Pipeline configuration {config_name} not found", 404)
        
        # Load config
        with open(config_path, 'r') as f:
            config = json.load(f)
        
        return api_success({
            'name': config_name,
            'path': config_path,
            'config': config
        })
    except Exception as e:
        logger.error(f"Failed to get pipeline config {config_name}: {e}")
        return api_error(f"Failed to get pipeline config: {e}", 500)

@app.route('/api/v2/data_pipeline/configs', methods=['POST'])
@require_auth
@require_params('name', 'config')
@handle_api_exception
def create_pipeline_config_v2():
    """
    Create a pipeline configuration.
    
    Request Body:
        name (str): Configuration name
        config (dict): Pipeline configuration
        
    Returns:
        JSON: Success message
    """
    if not data_pipeline_available:
        return api_error("Data pipeline modules not available", 501)
    
    try:
        data = request.get_json()
        name = data.get('name')
        config = data.get('config')
        
        # Validate name
        if not name or not name.isalnum():
            return api_error("Invalid configuration name. Use alphanumeric characters only.", 400)
        
        # Get pipeline configs directory
        configs_dir = os.path.join(current_app.config.get('data_path', 'data'), 'pipeline_configs')
        
        # Create directory if it doesn't exist
        if not os.path.exists(configs_dir):
            os.makedirs(configs_dir)
        
        # Check if config already exists
        config_path = os.path.join(configs_dir, f"{name}.json")
        if os.path.exists(config_path):
            return api_error(f"Pipeline configuration {name} already exists", 400)
        
        # Save config
        with open(config_path, 'w') as f:
            json.dump(config, f, indent=2)
        
        return api_success({
            'name': name,
            'path': config_path,
            'config': config
        }, f"Pipeline configuration {name} created successfully")
    except Exception as e:
        logger.error(f"Failed to create pipeline config: {e}")
        return api_error(f"Failed to create pipeline config: {e}", 500)

@app.route('/api/v2/data_pipeline/configs/<config_name>', methods=['PUT', 'PATCH'])
@require_auth
@require_params('config')
@handle_api_exception
def update_pipeline_config_v2(config_name):
    """
    Update a pipeline configuration.
    
    Args:
        config_name (str): Configuration name
        
    Request Body:
        config (dict): Pipeline configuration
        
    Returns:
        JSON: Success message
    """
    if not data_pipeline_available:
        return api_error("Data pipeline modules not available", 501)
    
    try:
        data = request.get_json()
        config = data.get('config')
        
        # Get pipeline configs directory
        configs_dir = os.path.join(current_app.config.get('data_path', 'data'), 'pipeline_configs')
        
        # Check if config exists
        config_path = os.path.join(configs_dir, f"{config_name}.json")
        if not os.path.exists(config_path):
            return api_error(f"Pipeline configuration {config_name} not found", 404)
        
        # Save config
        with open(config_path, 'w') as f:
            json.dump(config, f, indent=2)
        
        return api_success({
            'name': config_name,
            'path': config_path,
            'config': config
        }, f"Pipeline configuration {config_name} updated successfully")
    except Exception as e:
        logger.error(f"Failed to update pipeline config {config_name}: {e}")
        return api_error(f"Failed to update pipeline config: {e}", 500)

@app.route('/api/v2/data_pipeline/configs/<config_name>', methods=['DELETE'])
@require_auth
@handle_api_exception
def delete_pipeline_config_v2(config_name):
    """
    Delete a pipeline configuration.
    
    Args:
        config_name (str): Configuration name
        
    Returns:
        JSON: Success message
    """
    if not data_pipeline_available:
        return api_error("Data pipeline modules not available", 501)
    
    try:
        # Get pipeline configs directory
        configs_dir = os.path.join(current_app.config.get('data_path', 'data'), 'pipeline_configs')
        
        # Check if config exists
        config_path = os.path.join(configs_dir, f"{config_name}.json")
        if not os.path.exists(config_path):
            return api_error(f"Pipeline configuration {config_name} not found", 404)
        
        # Delete config
        os.remove(config_path)
        
        return api_success(None, f"Pipeline configuration {config_name} deleted successfully")
    except Exception as e:
        logger.error(f"Failed to delete pipeline config {config_name}: {e}")
        return api_error(f"Failed to delete pipeline config: {e}", 500)

@app.route('/api/v2/data_pipeline/transform', methods=['POST'])
@require_params('data', 'transformers')
@handle_api_exception
def transform_data_v2():
    """
    Transform data using specified transformers.
    
    Request Body:
        data (any): Data to transform
        transformers (list): List of transformers to apply
        
    Returns:
        JSON: Transformed data
    """
    if not data_pipeline_available:
        return api_error("Data pipeline modules not available", 501)
    
    try:
        data = request.get_json()
        input_data = data.get('data')
        transformers = data.get('transformers')
        
        # Create transformer
        transformer = DataTransformer()
        
        # Apply transformers
        result = transformer.transform(input_data, transformers)
        
        return api_success({
            'input': input_data,
            'transformers': transformers,
            'output': result
        })
    except Exception as e:
        logger.error(f"Failed to transform data: {e}")
        return api_error(f"Failed to transform data: {e}", 500)

@app.route('/api/v2/data_pipeline/validate', methods=['POST'])
@require_params('data', 'validators')
@handle_api_exception
def validate_data_v2():
    """
    Validate data using specified validators.
    
    Request Body:
        data (any): Data to validate
        validators (list): List of validators to apply
        
    Returns:
        JSON: Validation results
    """
    if not data_pipeline_available:
        return api_error("Data pipeline modules not available", 501)
    
    try:
        data = request.get_json()
        input_data = data.get('data')
        validators = data.get('validators')
        
        # Create validator
        checker = DataQualityChecker()
        
        # Apply validators
        valid, errors = checker.validate(input_data, validators)
        
        return api_success({
            'input': input_data,
            'validators': validators,
            'valid': valid,
            'errors': errors
        })
    except Exception as e:
        logger.error(f"Failed to validate data: {e}")
        return api_error(f"Failed to validate data: {e}", 500)

@app.route('/api/v2/data_pipeline/validate_schema', methods=['POST'])
@require_params('data', 'schema')
@handle_api_exception
def validate_schema_v2():
    """
    Validate data against a schema.
    
    Request Body:
        data (dict): Data to validate
        schema (dict): Schema to validate against
        
    Returns:
        JSON: Validation results
    """
    if not data_pipeline_available:
        return api_error("Data pipeline modules not available", 501)
    
    try:
        data = request.get_json()
        input_data = data.get('data')
        schema = data.get('schema')
        
        # Create validator
        checker = DataQualityChecker()
        
        # Validate schema
        valid, errors = checker.validate_schema(input_data, schema)
        
        return api_success({
            'input': input_data,
            'schema': schema,
            'valid': valid,
            'errors': errors
        })
    except Exception as e:
        logger.error(f"Failed to validate schema: {e}")
        return api_error(f"Failed to validate schema: {e}", 500)
