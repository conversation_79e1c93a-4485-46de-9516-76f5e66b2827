#!/usr/bin/env python
# -*- encoding: utf-8 -*-
# vim: set et sw=4 ts=4 sts=4 ff=unix fenc=utf8:
# Author: Binux<<EMAIL>>
#         http://binux.me
# Created on 2024-06-20 10:00:00

import logging
import os
import importlib
import pkgutil

logger = logging.getLogger('webui.api_v2')

# Import API modules explicitly
try:
    from . import projects
    logger.debug("Imported API v2 module: projects")
except ImportError as e:
    logger.error(f"Failed to import API v2 module projects: {e}")

try:
    from . import tasks
    logger.debug("Imported API v2 module: tasks")
except ImportError as e:
    logger.error(f"Failed to import API v2 module tasks: {e}")

try:
    from . import results
    logger.debug("Imported API v2 module: results")
except ImportError as e:
    logger.error(f"Failed to import API v2 module results: {e}")

try:
    from . import scheduler
    logger.debug("Imported API v2 module: scheduler")
except ImportError as e:
    logger.error(f"Failed to import API v2 module scheduler: {e}")

try:
    from . import system
    logger.debug("Imported API v2 module: system")
except ImportError as e:
    logger.error(f"Failed to import API v2 module system: {e}")

try:
    from . import data_pipeline
    logger.debug("Imported API v2 module: data_pipeline")
except ImportError as e:
    logger.error(f"Failed to import API v2 module data_pipeline: {e}")

try:
    from . import auth
    logger.debug("Imported API v2 module: auth")
except ImportError as e:
    logger.error(f"Failed to import API v2 module auth: {e}")

# Import Swagger documentation
try:
    from . import swagger
    logger.debug("Imported API v2 module: swagger")
except ImportError as e:
    logger.warning(f"Failed to import Swagger documentation: {e}")
