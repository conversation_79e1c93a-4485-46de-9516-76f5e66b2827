#!/usr/bin/env python
# -*- encoding: utf-8 -*-
# vim: set et sw=4 ts=4 sts=4 ff=unix fenc=utf8:
# Author: Binux<<EMAIL>>
#         http://binux.me
# Created on 2024-06-20 10:00:00

import time
import json
import logging
from flask import request, Response, jsonify, current_app

from ..app import app
from ..api_utils import (
    json_response, api_error, api_success, require_params, require_auth, require_admin,
    handle_api_exception, paginate, get_project_name, get_task_id, get_db_or_error, get_rpc_or_error
)

logger = logging.getLogger('webui.api_v2.scheduler')

@app.route('/api/v2/scheduler/status', methods=['GET'])
@handle_api_exception
def get_scheduler_status_v2():
    """
    Get scheduler status.
    
    Returns:
        JSON: Scheduler status
    """
    rpc = current_app.config.get('scheduler_rpc')
    if not rpc:
        return api_error("Scheduler not available", 503)
    
    try:
        # Get scheduler size
        size = rpc.size()
        
        # Get queue info
        queue_info = None
        try:
            queue_info = rpc.get_queue_info()
        except:
            pass
        
        # Get active tasks
        active_tasks = None
        try:
            active_tasks = rpc.get_active_tasks()
        except:
            pass
        
        # Get scheduler config
        config = None
        try:
            config = {
                'INQUEUE_LIMIT': rpc.get_config('INQUEUE_LIMIT'),
                'DELETE_TIME': rpc.get_config('DELETE_TIME'),
                'ACTIVE_TASKS': rpc.get_config('ACTIVE_TASKS'),
                'LOOP_LIMIT': rpc.get_config('LOOP_LIMIT'),
                'FAIL_PAUSE_NUM': rpc.get_config('FAIL_PAUSE_NUM')
            }
        except:
            pass
        
        return api_success({
            'size': size,
            'queue_info': queue_info,
            'active_tasks': active_tasks,
            'config': config
        })
    except Exception as e:
        logger.error(f"Failed to get scheduler status: {e}")
        return api_error(f"Failed to get scheduler status: {e}", 500)

@app.route('/api/v2/scheduler/config', methods=['GET'])
@handle_api_exception
def get_scheduler_config_v2():
    """
    Get scheduler configuration.
    
    Returns:
        JSON: Scheduler configuration
    """
    rpc = current_app.config.get('scheduler_rpc')
    if not rpc:
        return api_error("Scheduler not available", 503)
    
    try:
        config = {
            'INQUEUE_LIMIT': rpc.get_config('INQUEUE_LIMIT'),
            'DELETE_TIME': rpc.get_config('DELETE_TIME'),
            'ACTIVE_TASKS': rpc.get_config('ACTIVE_TASKS'),
            'LOOP_LIMIT': rpc.get_config('LOOP_LIMIT'),
            'FAIL_PAUSE_NUM': rpc.get_config('FAIL_PAUSE_NUM')
        }
        
        return api_success(config)
    except Exception as e:
        logger.error(f"Failed to get scheduler config: {e}")
        return api_error(f"Failed to get scheduler config: {e}", 500)

@app.route('/api/v2/scheduler/config', methods=['PUT', 'PATCH'])
@require_admin
@handle_api_exception
def update_scheduler_config_v2():
    """
    Update scheduler configuration.
    
    Request Body:
        INQUEUE_LIMIT (int, optional): Maximum number of tasks in queue
        DELETE_TIME (int, optional): Time in seconds to delete finished tasks
        ACTIVE_TASKS (int, optional): Maximum number of active tasks
        LOOP_LIMIT (int, optional): Maximum number of tasks to process in one loop
        FAIL_PAUSE_NUM (int, optional): Number of consecutive failures to pause a project
        
    Returns:
        JSON: Success message
    """
    rpc = get_rpc_or_error('scheduler_rpc')
    
    data = request.get_json()
    if not data:
        return api_error("No data provided", 400)
    
    try:
        # Update config
        for key, value in data.items():
            if key in ('INQUEUE_LIMIT', 'DELETE_TIME', 'ACTIVE_TASKS', 'LOOP_LIMIT', 'FAIL_PAUSE_NUM'):
                rpc.set_config(key, value)
        
        # Get updated config
        config = {
            'INQUEUE_LIMIT': rpc.get_config('INQUEUE_LIMIT'),
            'DELETE_TIME': rpc.get_config('DELETE_TIME'),
            'ACTIVE_TASKS': rpc.get_config('ACTIVE_TASKS'),
            'LOOP_LIMIT': rpc.get_config('LOOP_LIMIT'),
            'FAIL_PAUSE_NUM': rpc.get_config('FAIL_PAUSE_NUM')
        }
        
        return api_success(config, "Scheduler configuration updated successfully")
    except Exception as e:
        logger.error(f"Failed to update scheduler config: {e}")
        return api_error(f"Failed to update scheduler config: {e}", 500)

@app.route('/api/v2/scheduler/queue', methods=['GET'])
@handle_api_exception
def get_queue_info_v2():
    """
    Get queue information.
    
    Returns:
        JSON: Queue information
    """
    rpc = current_app.config.get('scheduler_rpc')
    if not rpc:
        return api_error("Scheduler not available", 503)
    
    try:
        queue_info = rpc.get_queue_info()
        return api_success(queue_info)
    except Exception as e:
        logger.error(f"Failed to get queue info: {e}")
        return api_error(f"Failed to get queue info: {e}", 500)

@app.route('/api/v2/scheduler/active_tasks', methods=['GET'])
@handle_api_exception
def get_active_tasks_v2():
    """
    Get active tasks.
    
    Returns:
        JSON: Active tasks
    """
    rpc = current_app.config.get('scheduler_rpc')
    if not rpc:
        return api_error("Scheduler not available", 503)
    
    try:
        active_tasks = rpc.get_active_tasks()
        return api_success(active_tasks)
    except Exception as e:
        logger.error(f"Failed to get active tasks: {e}")
        return api_error(f"Failed to get active tasks: {e}", 500)

@app.route('/api/v2/scheduler/task_queue/<project_name>', methods=['GET'])
@handle_api_exception
def get_project_task_queue_v2(project_name):
    """
    Get task queue for a project.
    
    Args:
        project_name (str): Project name
        
    Returns:
        JSON: Task queue information
    """
    rpc = current_app.config.get('scheduler_rpc')
    if not rpc:
        return api_error("Scheduler not available", 503)
    
    try:
        task_queue = rpc.get_project_task_queue(project_name)
        return api_success(task_queue)
    except Exception as e:
        logger.error(f"Failed to get task queue for project {project_name}: {e}")
        return api_error(f"Failed to get task queue: {e}", 500)

@app.route('/api/v2/scheduler/update_project', methods=['POST'])
@require_auth
@handle_api_exception
def update_project_in_scheduler_v2():
    """
    Update project in scheduler.
    
    Returns:
        JSON: Success message
    """
    rpc = get_rpc_or_error('scheduler_rpc')
    
    try:
        rpc.update_project()
        return api_success(None, "Project updated in scheduler successfully")
    except Exception as e:
        logger.error(f"Failed to update project in scheduler: {e}")
        return api_error(f"Failed to update project in scheduler: {e}", 500)

@app.route('/api/v2/scheduler/counter', methods=['GET'])
@handle_api_exception
def get_scheduler_counter_v2():
    """
    Get scheduler counter.
    
    Query Parameters:
        time (str, optional): Time period (5m, 1h, 1d, all)
        type (str, optional): Counter type (sum, avg, max, min)
        
    Returns:
        JSON: Scheduler counter
    """
    rpc = current_app.config.get('scheduler_rpc')
    if not rpc:
        return api_error("Scheduler not available", 503)
    
    try:
        time_period = request.args.get('time', default='1h')
        counter_type = request.args.get('type', default='sum')
        
        counter = rpc.counter(time_period, counter_type)
        return api_success(counter)
    except Exception as e:
        logger.error(f"Failed to get scheduler counter: {e}")
        return api_error(f"Failed to get scheduler counter: {e}", 500)

@app.route('/api/v2/scheduler/send_task', methods=['POST'])
@require_auth
@require_params('task')
@handle_api_exception
def send_task_to_scheduler_v2():
    """
    Send a task to scheduler.
    
    Request Body:
        task (dict): Task to send
        
    Returns:
        JSON: Success message
    """
    rpc = get_rpc_or_error('scheduler_rpc')
    
    data = request.get_json()
    task = data.get('task')
    
    try:
        result = rpc.send_task(task)
        return api_success({'result': result}, "Task sent to scheduler successfully")
    except Exception as e:
        logger.error(f"Failed to send task to scheduler: {e}")
        return api_error(f"Failed to send task to scheduler: {e}", 500)
