#!/usr/bin/env python
# -*- encoding: utf-8 -*-
# vim: set et sw=4 ts=4 sts=4 ff=unix fenc=utf8:
# Author: PySpiderNX2 Team
# Created on 2025-01-XX

import re
import html
import logging
import functools
from urllib.parse import urlparse
from flask import request, abort, jsonify

logger = logging.getLogger(__name__)

# 入力検証パターン
VALIDATION_PATTERNS = {
    'project_name': re.compile(r'^[a-zA-Z0-9_-]{1,50}$'),
    'task_id': re.compile(r'^[a-zA-Z0-9_:-]{1,100}$'),
    'url': re.compile(r'^https?://[^\s/$.?#].[^\s]*$'),
    'email': re.compile(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'),
    'username': re.compile(r'^[a-zA-Z0-9_]{3,30}$'),
    'safe_string': re.compile(r'^[a-zA-Z0-9\s\-_.,:;!?()]{0,500}$'),
}

def validate_input(input_type, value):
    """
    入力値を検証する
    
    Args:
        input_type: 検証タイプ
        value: 検証する値
        
    Returns:
        bool: 検証結果
    """
    if not value:
        return False
    
    if input_type not in VALIDATION_PATTERNS:
        logger.warning(f"Unknown validation type: {input_type}")
        return False
    
    pattern = VALIDATION_PATTERNS[input_type]
    return bool(pattern.match(str(value)))

def sanitize_html(text):
    """
    HTMLエスケープを行う
    
    Args:
        text: エスケープするテキスト
        
    Returns:
        str: エスケープされたテキスト
    """
    if not text:
        return ""
    return html.escape(str(text))

def validate_url(url):
    """
    URLの妥当性を検証する
    
    Args:
        url: 検証するURL
        
    Returns:
        bool: 検証結果
    """
    try:
        parsed = urlparse(url)
        return all([
            parsed.scheme in ['http', 'https'],
            parsed.netloc,
            len(url) <= 2048,  # URL長制限
            not any(char in url for char in ['<', '>', '"', "'", '`'])  # 危険な文字チェック
        ])
    except Exception:
        return False

def validate_json_input(required_fields=None, optional_fields=None):
    """
    JSON入力を検証するデコレータ
    
    Args:
        required_fields: 必須フィールドのリスト
        optional_fields: オプションフィールドのリスト
    """
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            try:
                data = request.get_json()
                if not data:
                    return jsonify({'error': 'Invalid JSON data'}), 400
                
                # 必須フィールドのチェック
                if required_fields:
                    for field in required_fields:
                        if field not in data:
                            return jsonify({'error': f'Missing required field: {field}'}), 400
                
                # フィールドの検証
                all_fields = (required_fields or []) + (optional_fields or [])
                for field in data:
                    if field not in all_fields:
                        return jsonify({'error': f'Unknown field: {field}'}), 400
                
                # データのサニタイズ
                for field, value in data.items():
                    if isinstance(value, str):
                        data[field] = sanitize_html(value)
                
                return func(*args, **kwargs)
            except Exception as e:
                logger.error(f"Input validation error: {e}")
                return jsonify({'error': 'Input validation failed'}), 400
        return wrapper
    return decorator

def validate_project_name(func):
    """
    プロジェクト名を検証するデコレータ
    """
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        project_name = kwargs.get('project') or request.view_args.get('project')
        if project_name and not validate_input('project_name', project_name):
            logger.warning(f"Invalid project name: {project_name}")
            abort(400, description="Invalid project name")
        return func(*args, **kwargs)
    return wrapper

def validate_task_id(func):
    """
    タスクIDを検証するデコレータ
    """
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        task_id = kwargs.get('taskid') or request.view_args.get('taskid')
        if task_id and not validate_input('task_id', task_id):
            logger.warning(f"Invalid task ID: {task_id}")
            abort(400, description="Invalid task ID")
        return func(*args, **kwargs)
    return wrapper

def validate_url_param(func):
    """
    URLパラメータを検証するデコレータ
    """
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        url = request.args.get('url') or request.json.get('url') if request.json else None
        if url and not validate_url(url):
            logger.warning(f"Invalid URL: {url}")
            abort(400, description="Invalid URL")
        return func(*args, **kwargs)
    return wrapper

# SQLインジェクション対策のためのエスケープ関数
def escape_sql_like(value):
    """
    SQL LIKE句で使用する値をエスケープする
    
    Args:
        value: エスケープする値
        
    Returns:
        str: エスケープされた値
    """
    if not value:
        return ""
    
    # LIKE句で特別な意味を持つ文字をエスケープ
    value = str(value).replace('\\', '\\\\')
    value = value.replace('%', '\\%')
    value = value.replace('_', '\\_')
    value = value.replace('[', '\\[')
    value = value.replace(']', '\\]')
    
    return value

def validate_pagination_params():
    """
    ページネーションパラメータを検証する
    
    Returns:
        tuple: (offset, limit)
    """
    try:
        offset = int(request.args.get('offset', 0))
        limit = int(request.args.get('limit', 20))
        
        # 範囲チェック
        offset = max(0, min(offset, 100000))  # 最大オフセット制限
        limit = max(1, min(limit, 1000))      # 最大リミット制限
        
        return offset, limit
    except (ValueError, TypeError):
        return 0, 20

def rate_limit_key():
    """
    レート制限のキーを生成する
    
    Returns:
        str: レート制限キー
    """
    from .security import get_real_ip
    client_ip = get_real_ip()
    user_agent = request.headers.get('User-Agent', '')[:100]  # User-Agentを制限
    return f"rate_limit:{client_ip}:{hash(user_agent)}"
