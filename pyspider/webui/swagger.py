#!/usr/bin/env python
# -*- encoding: utf-8 -*-
# vim: set et sw=4 ts=4 sts=4 ff=unix fenc=utf8:
# Author: Binux<<EMAIL>>
#         http://binux.me
# Created on 2025-05-17 16:00:00

"""
Swagger UI設定モジュール
"""

import os
import json
from flask_swagger_ui import get_swaggerui_blueprint

from .app import app

# Swagger UI設定
SWAGGER_URL = '/api/docs'  # Swagger UIのURL
API_URL = '/api/swagger.json'  # Swagger仕様ファイルのURL

# Swagger UI Blueprintの作成
swagger_ui_blueprint = get_swaggerui_blueprint(
    SWAGGER_URL,
    API_URL,
    config={
        'app_name': "pyspiderNX2 API",
        'dom_id': '#swagger-ui',
        'deepLinking': True,
        'layout': 'BaseLayout',
        'supportedSubmitMethods': ['get', 'post', 'put', 'delete', 'patch'],
        'validatorUrl': None,
    }
)

# Swagger UIをアプリケーションに登録
app.register_blueprint(swagger_ui_blueprint, url_prefix=SWAGGER_URL)

# Swagger仕様を提供するエンドポイント
@app.route('/api/swagger.json')
def get_swagger_spec():
    """Swagger仕様を提供するエンドポイント"""
    swagger_path = os.path.join(os.path.dirname(__file__), 'static', 'swagger.json')
    with open(swagger_path, 'r') as f:
        return json.loads(f.read())
