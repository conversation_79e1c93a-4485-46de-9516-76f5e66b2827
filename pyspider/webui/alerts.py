#!/usr/bin/env python
# -*- encoding: utf-8 -*-
# vim: set et sw=4 ts=4 sts=4 ff=unix fenc=utf8:
# Author: PySpider Team
# Created on 2025-05-18

import time
import logging
from flask import render_template, jsonify

from pyspider.libs.alert_manager import alert_manager
from pyspider.webui.app import app

logger = logging.getLogger(__name__)

@app.route('/alerts-dashboard')
def alerts_dashboard():
    """アラートダッシュボードページ"""
    return render_template("alerts-dashboard.html")

@app.route('/api/v2/alerts')
def api_alerts():
    """アラート情報を取得するAPIエンドポイント"""
    try:
        # アラートルールを取得
        rules = alert_manager.get_rules()
        
        # アクティブなアラートを取得
        active_alerts = []
        with alert_manager._alerts_lock:
            for alert in alert_manager.alerts:
                active_alerts.append({
                    'id': f"{alert.get('rule')}-{alert.get('timestamp')}",
                    'name': alert.get('rule'),
                    'description': alert.get('description'),
                    'severity': alert.get('severity', 'warning'),
                    'start_time': alert.get('timestamp'),
                    'metrics': alert.get('metrics')
                })
        
        # 最近解決したアラートを取得（最新10件）
        resolved_alerts = []
        with alert_manager._alert_history_lock:
            for alert in reversed(alert_manager.alert_history):
                if alert.get('resolved', False):
                    resolved_alerts.append({
                        'id': f"{alert.get('rule')}-{alert.get('timestamp')}-{alert.get('resolved_timestamp')}",
                        'name': alert.get('rule'),
                        'description': alert.get('description'),
                        'severity': alert.get('severity', 'warning'),
                        'start_time': alert.get('timestamp'),
                        'end_time': alert.get('resolved_timestamp'),
                        'duration': alert.get('resolved_timestamp', 0) - alert.get('timestamp', 0)
                    })
                    if len(resolved_alerts) >= 10:
                        break
        
        # アラートルールの状態を更新
        for rule in rules:
            rule_name = rule.get('name')
            rule['status'] = 'normal'
            
            # アクティブなアラートがあるか確認
            for alert in active_alerts:
                if alert['name'] == rule_name:
                    rule['status'] = 'firing'
                    break
        
        return jsonify({
            'rules': rules,
            'active_alerts': active_alerts,
            'resolved_alerts': resolved_alerts,
            'timestamp': time.time()
        })
    except Exception as e:
        logger.error(f"Error getting alerts: {e}")
        return jsonify({
            'error': str(e),
            'timestamp': time.time()
        }), 500
