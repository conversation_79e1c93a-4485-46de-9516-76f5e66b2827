#!/usr/bin/env python
# -*- encoding: utf-8 -*-
# vim: set et sw=4 ts=4 sts=4 ff=unix fenc=utf8:
# Author: PySpider Team
# Created on 2023-05-18 15:00:00

import os
import logging
import threading
import time
from typing import Dict, Any, Optional

from pyspider.libs.prometheus_metrics import prometheus_metrics
from pyspider.libs.performance_metrics import performance_metrics
from pyspider.libs.alert_manager import alert_manager
from pyspider.webui.app import app

logger = logging.getLogger('enhanced_features')

class EnhancedFeatures:
    """
    Enhanced features for PySpider
    """

    def __init__(self,
                 enable_prometheus: bool = True,
                 enable_performance_metrics: bool = True,
                 enable_alerts: bool = True,
                 prometheus_push_gateway: Optional[str] = None,
                 alert_config_file: Optional[str] = None):
        """
        Initialize EnhancedFeatures

        Args:
            enable_prometheus: Whether to enable Prometheus metrics
            enable_performance_metrics: Whether to enable performance metrics
            enable_alerts: Whether to enable alerts
            prometheus_push_gateway: Prometheus push gateway URL
            alert_config_file: Path to alert config file
        """
        self.enable_prometheus = enable_prometheus
        self.enable_performance_metrics = enable_performance_metrics
        self.enable_alerts = enable_alerts
        self.prometheus_push_gateway = prometheus_push_gateway
        self.alert_config_file = alert_config_file

        # Initialize
        self._init()

        logger.info(f"Enhanced features initialized (prometheus: {enable_prometheus}, "
                   f"performance_metrics: {enable_performance_metrics}, "
                   f"alerts: {enable_alerts})")

    def _init(self):
        """Initialize enhanced features"""
        # Initialize Prometheus metrics
        if self.enable_prometheus:
            self._init_prometheus()

        # Initialize performance metrics
        if self.enable_performance_metrics:
            self._init_performance_metrics()

        # Initialize alerts
        if self.enable_alerts:
            self._init_alerts()

    def _init_prometheus(self):
        """Initialize Prometheus metrics"""
        try:
            # Check if prometheus_client is installed
            from pyspider.libs.prometheus_metrics import has_prometheus

            if not has_prometheus:
                logger.warning("prometheus_client is not installed. Prometheus metrics will not be available.")
                return

            # Set push gateway
            if self.prometheus_push_gateway:
                prometheus_metrics.push_gateway = self.prometheus_push_gateway
                prometheus_metrics.start_push_thread()

            logger.info("Prometheus metrics initialized")
        except Exception as e:
            logger.error(f"Error initializing Prometheus metrics: {e}")

    def _init_performance_metrics(self):
        """Initialize performance metrics"""
        try:
            # Start performance metrics collection
            performance_metrics.start_collection()

            logger.info("Performance metrics initialized")
        except Exception as e:
            logger.error(f"Error initializing performance metrics: {e}")

    def _init_alerts(self):
        """Initialize alerts"""
        try:
            # Set config file
            if self.alert_config_file:
                alert_manager.config_file = self.alert_config_file
                alert_manager.load_config(self.alert_config_file)

            # アラート通知の設定
            try:
                # Slack通知の設定例
                # alert_manager.add_channel('slack', {
                #     'type': 'slack',
                #     'webhook_url': 'https://hooks.slack.com/services/XXXXXXXXX/XXXXXXXXX/XXXXXXXXXXXXXXXXXXXXXXXX',
                #     'enabled': True,
                #     'severities': ['warning', 'critical'],
                #     'send_resolved': True
                # })

                # Webhook通知の設定例
                # alert_manager.add_channel('webhook', {
                #     'type': 'webhook',
                #     'url': 'https://example.com/webhook',
                #     'enabled': True,
                #     'severities': ['warning', 'critical'],
                #     'send_resolved': True
                # })

                # メール通知の設定例
                # alert_manager.add_channel('email', {
                #     'type': 'email',
                #     'smtp_server': 'smtp.example.com',
                #     'smtp_port': 587,
                #     'smtp_username': '<EMAIL>',
                #     'smtp_password': 'password',
                #     'from_addr': '<EMAIL>',
                #     'to_addrs': ['<EMAIL>'],
                #     'enabled': True,
                #     'severities': ['warning', 'critical'],
                #     'send_resolved': True
                # })

                logger.info("Alert channels configured")
            except Exception as e:
                logger.error(f"Error configuring alert channels: {e}")

            # Start alert check thread
            alert_manager.start_check_thread()

            logger.info("Alerts initialized")
        except Exception as e:
            logger.error(f"Error initializing alerts: {e}")

    def shutdown(self):
        """Shutdown enhanced features"""
        # Shutdown Prometheus metrics
        if self.enable_prometheus:
            try:
                prometheus_metrics.stop_push_thread()
            except Exception as e:
                logger.error(f"Error shutting down Prometheus metrics: {e}")

        # Shutdown performance metrics
        if self.enable_performance_metrics:
            try:
                performance_metrics.stop_collection()
            except Exception as e:
                logger.error(f"Error shutting down performance metrics: {e}")

        # Shutdown alerts
        if self.enable_alerts:
            try:
                alert_manager.stop_check_thread()
            except Exception as e:
                logger.error(f"Error shutting down alerts: {e}")

        logger.info("Enhanced features shutdown")


# Initialize enhanced features
def init_enhanced_features():
    """Initialize enhanced features"""
    try:
        # Get configuration from environment variables
        enable_prometheus = os.environ.get('PYSPIDER_ENABLE_PROMETHEUS', 'true').lower() == 'true'
        enable_performance_metrics = os.environ.get('PYSPIDER_ENABLE_PERFORMANCE_METRICS', 'true').lower() == 'true'
        enable_alerts = os.environ.get('PYSPIDER_ENABLE_ALERTS', 'true').lower() == 'true'
        prometheus_push_gateway = os.environ.get('PYSPIDER_PROMETHEUS_PUSH_GATEWAY')
        alert_config_file = os.environ.get('PYSPIDER_ALERT_CONFIG_FILE')

        # Create enhanced features
        enhanced_features = EnhancedFeatures(
            enable_prometheus=enable_prometheus,
            enable_performance_metrics=enable_performance_metrics,
            enable_alerts=enable_alerts,
            prometheus_push_gateway=prometheus_push_gateway,
            alert_config_file=alert_config_file
        )

        # Store enhanced features in app
        app.enhanced_features = enhanced_features

        # Register shutdown function
        import atexit
        atexit.register(enhanced_features.shutdown)

        logger.info("Enhanced features initialized")
    except Exception as e:
        logger.error(f"Error initializing enhanced features: {e}")


# Initialize enhanced features
init_enhanced_features()
