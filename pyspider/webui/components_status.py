#!/usr/bin/env python
# -*- encoding: utf-8 -*-
# vim: set et sw=4 ts=4 sts=4 ff=unix fenc=utf8:
# Author: Augment Agent
# Created on 2024-05-05

import time
import socket
import logging
import os
import psutil
import glob
from flask import jsonify

from .app import app

logger = logging.getLogger(__name__)

def check_component_status(host, port, timeout=1):
    """指定されたホストとポートに接続してコンポーネントの状態を確認する"""
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(timeout)
        result = sock.connect_ex((host, port))
        sock.close()
        return result == 0
    except Exception as e:
        logger.error(f"Error checking component status: {str(e)}")
        return False

def check_process_running(process_name):
    """指定された名前のプロセスが実行中かどうかを確認する"""
    try:
        # psutilを使用してプロセスを検索
        logger.debug(f"Checking for process: {process_name}")
        for proc in psutil.process_iter(['name', 'cmdline']):
            try:
                # コマンドラインを確認
                if proc.info['cmdline'] and len(proc.info['cmdline']) > 1:
                    cmdline = ' '.join(proc.info['cmdline'])
                    # リザルトワーカーの場合は特別な処理
                    if process_name == 'result-worker':
                        if ('result-worker' in cmdline or 'result_worker' in cmdline) and 'pyspider' in cmdline:
                            logger.debug(f"Found result worker process: {cmdline}")
                            return True
                    # 他のプロセスの場合は通常の処理
                    elif process_name in cmdline and 'pyspider' in cmdline:
                        logger.debug(f"Found process {process_name}: {cmdline}")
                        return True
            except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                pass
        logger.debug(f"Process {process_name} not found")
        return False
    except Exception as e:
        logger.error(f"Error checking process {process_name}: {str(e)}")
        return False

def check_process_by_pid_file(component_name):
    """PIDファイルを使用してプロセスが実行中かどうかを確認する"""
    try:
        # PIDファイルのパスを生成
        pid_file = f"/tmp/pyspider_{component_name}.pid"

        # PIDファイルが存在するか確認
        if not os.path.exists(pid_file):
            logger.debug(f"PID file for {component_name} not found")
            return False

        # PIDファイルからPIDを読み取る
        with open(pid_file, 'r') as f:
            pid = int(f.read().strip())

        # プロセスが実行中かどうかを確認
        if psutil.pid_exists(pid):
            try:
                process = psutil.Process(pid)
                # プロセス名を確認
                cmdline = ' '.join(process.cmdline())
                if component_name in cmdline and 'pyspider' in cmdline:
                    logger.debug(f"Process {component_name} is running with PID {pid}")
                    return True
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                pass

        logger.debug(f"Process {component_name} with PID {pid} is not running")
        return False
    except Exception as e:
        logger.error(f"Error checking PID file for {component_name}: {str(e)}")
        return False

def get_components_status():
    """各コンポーネントの状態を取得する"""
    try:
        # デフォルトのポート設定
        scheduler_host, scheduler_port = 'localhost', 23333
        fetcher_host, fetcher_port = 'localhost', 24444
        puppeteer_host, puppeteer_port = 'localhost', 22223

        # 各コンポーネントの状態を確認
        scheduler_status = check_component_status(scheduler_host, scheduler_port)
        fetcher_status = check_component_status(fetcher_host, fetcher_port)
        puppeteer_status = check_component_status(puppeteer_host, puppeteer_port)

        # プロセッサとリザルトワーカーの状態を直接確認
        processor_status = check_process_running('processor')
        result_worker_status = check_process_running('result-worker')

        # バックアップ方法として、プロセスの存在を確認
        if not processor_status:
            processor_status = check_process_by_pid_file('processor')

        if not result_worker_status:
            result_worker_status = check_process_by_pid_file('result_worker')
            # ハイフン版も試す
            if not result_worker_status:
                result_worker_status = check_process_by_pid_file('result-worker')

        # スケジューラーが実行中でない場合、他のコンポーネントの状態も確認
        if not scheduler_status:
            scheduler_status = check_process_running('scheduler')
            if not scheduler_status:
                scheduler_status = check_process_by_pid_file('scheduler')

        # フェッチャーが実行中でない場合、プロセスも確認
        if not fetcher_status:
            fetcher_status = check_process_running('fetcher')
            if not fetcher_status:
                fetcher_status = check_process_by_pid_file('fetcher')

        # ログ出力
        if scheduler_status:
            logger.debug("Scheduler is running")
        if processor_status:
            logger.debug("Processor is running")
        if result_worker_status:
            logger.debug("Result worker is running")
        if fetcher_status:
            logger.debug("Fetcher is running")
        if puppeteer_status:
            logger.debug("Puppeteer fetcher is running")

        return {
            'scheduler': {
                'status': 'running' if scheduler_status else 'stopped',
                'host': scheduler_host,
                'port': scheduler_port
            },
            'fetcher': {
                'status': 'running' if fetcher_status else 'stopped',
                'host': fetcher_host,
                'port': fetcher_port
            },
            'processor': {
                'status': 'running' if processor_status else 'stopped',
            },
            'result_worker': {
                'status': 'running' if result_worker_status else 'stopped',
            },
            'puppeteer_fetcher': {
                'status': 'running' if puppeteer_status else 'stopped',
                'host': puppeteer_host,
                'port': puppeteer_port
            },
            'timestamp': time.time()
        }
    except Exception as e:
        logger.error(f"Error getting components status: {str(e)}")
        return {
            'error': str(e),
            'timestamp': time.time()
        }

@app.route('/api/components/status')
@app.route('/api/v2/components/status')
def components_status_api():
    """コンポーネントの状態を取得するAPIエンドポイント"""
    return jsonify(get_components_status())
