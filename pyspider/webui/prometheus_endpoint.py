#!/usr/bin/env python
# -*- encoding: utf-8 -*-
# vim: set et sw=4 ts=4 sts=4 ff=unix fenc=utf8:
# Author: PySpider Team
# Created on 2023-05-18 11:30:00

import time
import logging
import psutil
from flask import Response, Blueprint, current_app, render_template

try:
    from prometheus_client import Counter, Gauge, Histogram, Summary, generate_latest, REGISTRY
    from prometheus_client.exposition import MetricsHandler
    has_prometheus = True
except ImportError:
    has_prometheus = False

from pyspider.libs.prometheus_metrics import prometheus_metrics
from pyspider.webui.metrics import get_scheduler_stats, get_system_stats
from pyspider.webui.components_status import get_components_status

logger = logging.getLogger('prometheus_endpoint')

# Create blueprint
prometheus_bp = Blueprint('prometheus', __name__)

# Initialize metrics
def init_metrics():
    """Initialize metrics"""
    if not has_prometheus:
        logger.warning("prometheus_client is not installed. Please install it with 'pip install prometheus_client'")
        return

    # System metrics
    system_cpu_percent = prometheus_metrics.gauge('system_cpu_percent', 'System CPU usage percent')
    system_memory_percent = prometheus_metrics.gauge('system_memory_percent', 'System memory usage percent')
    system_disk_percent = prometheus_metrics.gauge('system_disk_percent', 'System disk usage percent')

    process_cpu_percent = prometheus_metrics.gauge('process_cpu_percent', 'Process CPU usage percent')
    process_memory_percent = prometheus_metrics.gauge('process_memory_percent', 'Process memory usage percent')
    process_memory_mb = prometheus_metrics.gauge('process_memory_mb', 'Process memory usage in MB')
    process_threads = prometheus_metrics.gauge('process_threads', 'Number of threads in the process')
    process_open_files = prometheus_metrics.gauge('process_open_files', 'Number of open files by the process')
    process_connections = prometheus_metrics.gauge('process_connections', 'Number of network connections by the process')

    # Scheduler metrics
    scheduler_queue_size = prometheus_metrics.gauge('scheduler_queue_size', 'Scheduler queue size')
    scheduler_processing_tasks = prometheus_metrics.gauge('scheduler_processing_tasks', 'Number of tasks being processed')
    scheduler_total_tasks_24h = prometheus_metrics.gauge('scheduler_total_tasks_24h', 'Total number of tasks in the last 24 hours')
    scheduler_failed_tasks_24h = prometheus_metrics.gauge('scheduler_failed_tasks_24h', 'Number of failed tasks in the last 24 hours')
    scheduler_success_tasks_24h = prometheus_metrics.gauge('scheduler_success_tasks_24h', 'Number of successful tasks in the last 24 hours')
    scheduler_pending_tasks = prometheus_metrics.gauge('scheduler_pending_tasks', 'Number of pending tasks')

    # Component metrics
    component_status = prometheus_metrics.gauge('component_status', 'Component status (1=running, 0=stopped)', ['component'])

    logger.info("Prometheus metrics initialized")

# Initialize metrics
init_metrics()

# Update metrics function (called manually to avoid circular imports)
def update_metrics():
    """Update metrics before each request"""
    if not has_prometheus:
        return

    try:
        # Get stats
        system_stats = get_system_stats()
        scheduler_stats = get_scheduler_stats()
        components_status = get_components_status()

        # Update system metrics
        prometheus_metrics.gauge('system_cpu_percent', 'System CPU usage percent').set(system_stats.get('cpu_percent', 0))
        prometheus_metrics.gauge('system_memory_percent', 'System memory usage percent').set(system_stats.get('memory_percent', 0))
        prometheus_metrics.gauge('system_disk_percent', 'System disk usage percent').set(system_stats.get('disk_percent', 0))

        prometheus_metrics.gauge('process_cpu_percent', 'Process CPU usage percent').set(system_stats.get('process_cpu_percent', 0))
        prometheus_metrics.gauge('process_memory_percent', 'Process memory usage percent').set(system_stats.get('process_memory_percent', 0))
        prometheus_metrics.gauge('process_memory_mb', 'Process memory usage in MB').set(system_stats.get('process_memory_mb', 0))
        prometheus_metrics.gauge('process_threads', 'Number of threads in the process').set(system_stats.get('process_threads', 0))
        prometheus_metrics.gauge('process_open_files', 'Number of open files by the process').set(system_stats.get('process_open_files', 0))
        prometheus_metrics.gauge('process_connections', 'Number of network connections by the process').set(system_stats.get('process_connections', 0))

        # Update scheduler metrics
        prometheus_metrics.gauge('scheduler_queue_size', 'Scheduler queue size').set(scheduler_stats.get('queue_size', 0))
        prometheus_metrics.gauge('scheduler_processing_tasks', 'Number of tasks being processed').set(scheduler_stats.get('processing_tasks', 0))
        prometheus_metrics.gauge('scheduler_total_tasks_24h', 'Total number of tasks in the last 24 hours').set(scheduler_stats.get('total_tasks_24h', 0))
        prometheus_metrics.gauge('scheduler_failed_tasks_24h', 'Number of failed tasks in the last 24 hours').set(scheduler_stats.get('failed_tasks_24h', 0))
        prometheus_metrics.gauge('scheduler_success_tasks_24h', 'Number of successful tasks in the last 24 hours').set(scheduler_stats.get('success_tasks_24h', 0))
        prometheus_metrics.gauge('scheduler_pending_tasks', 'Number of pending tasks').set(scheduler_stats.get('pending_tasks', 0))

        # Update component metrics
        for component, status_info in components_status.items():
            if component != 'timestamp' and isinstance(status_info, dict):
                status_value = status_info.get('status', '')
                prometheus_metrics.gauge('component_status', 'Component status (1=running, 0=stopped)', ['component']).labels(component=component).set(1 if status_value == 'running' else 0)

    except Exception as e:
        logger.error(f"Error updating metrics: {e}")

# Prometheus metrics endpoint
@prometheus_bp.route('/metrics')
def metrics():
    """Prometheus metrics endpoint"""
    if not has_prometheus:
        return Response("prometheus_client is not installed", mimetype='text/plain')

    try:
        # Update metrics
        update_metrics()

        # Generate metrics directly from the registry
        if hasattr(prometheus_metrics, 'registry') and prometheus_metrics.registry:
            metrics_data = generate_latest(prometheus_metrics.registry)
        else:
            # Fallback to default registry
            metrics_data = generate_latest(REGISTRY)

        return Response(metrics_data, mimetype='text/plain')
    except Exception as e:
        logger.error(f"Error generating metrics: {e}")
        return Response(f"Error generating metrics: {e}", mimetype='text/plain', status=500)

# Prometheus metrics dashboard
@prometheus_bp.route('/dashboard')
def prometheus_dashboard():
    """Prometheus metrics dashboard"""
    return render_template("prometheus-metrics.html")

# Blueprint registration is handled in app.py to avoid circular imports
