#!/usr/bin/env python
# -*- encoding: utf-8 -*-
# vim: set et sw=4 ts=4 sts=4 ff=unix fenc=utf8:
# Author: Binux<<EMAIL>>
#         http://binux.me
# Created on 2024-06-20 10:00:00

import time
import json
import logging
import functools
import traceback
from flask import request, Response, current_app, g
try:
    from flask import jsonify
except ImportError:
    # Fallback for older Flask versions
    def jsonify(*args, **kwargs):
        return Response(json.dumps(*args, **kwargs), mimetype='application/json')

logger = logging.getLogger('webui.api_utils')

def json_response(data, status=200):
    """Return a JSON response with the given data and status code."""
    response = Response(json.dumps(data, default=json_encoder), mimetype='application/json')
    response.status_code = status
    return response

def json_encoder(obj):
    """JSON encoder for objects not serializable by default json code"""
    if hasattr(obj, 'isoformat'):
        return obj.isoformat()
    elif hasattr(obj, '__str__'):
        return str(obj)
    else:
        return repr(obj)

def api_error(message, status=400):
    """Return a JSON error response with the given message and status code."""
    return json_response({
        'error': message,
        'status': 'error',
        'timestamp': time.time()
    }, status)

def api_success(data=None, message=None):
    """Return a JSON success response with the given data and message."""
    response = {
        'status': 'success',
        'timestamp': time.time()
    }
    if data is not None:
        response['data'] = data
    if message is not None:
        response['message'] = message
    return json_response(response)

def require_params(*params):
    """Decorator to require parameters in the request."""
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            if request.method == 'GET':
                req_data = request.args
            else:
                req_data = request.get_json() or {}

            missing = [p for p in params if p not in req_data]
            if missing:
                return api_error(f"Missing required parameters: {', '.join(missing)}")

            return func(*args, **kwargs)
        return wrapper
    return decorator

def require_auth(func):
    """Decorator to require authentication."""
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        # Check if authentication is enabled
        if not current_app.config.get('need_auth', False):
            logger.info(f"Authentication disabled, skipping auth check for {func.__name__}")
            return func(*args, **kwargs)

        logger.info(f"Authentication enabled, checking auth for {func.__name__}")

        # Check Authorization header for Basic auth
        auth_header = request.headers.get('Authorization')
        if auth_header and auth_header.startswith('Basic '):
            try:
                import base64
                encoded_credentials = auth_header[6:]  # Remove "Basic "
                decoded_credentials = base64.b64decode(encoded_credentials).decode('utf-8')
                username, password = decoded_credentials.split(':', 1)

                # Check credentials against config
                config_username = current_app.config.get('webui_username')
                config_password = current_app.config.get('webui_password')

                if username == config_username and password == config_password:
                    logger.info(f"Authentication successful for {username}")
                    return func(*args, **kwargs)
                else:
                    logger.warning(f"Authentication failed for {username}")
                    return api_error("Invalid credentials", 401)

            except Exception as e:
                logger.error(f"Authentication error: {e}")
                return api_error("Authentication failed", 401)

        # Check if user is authenticated via login.py system
        from flask_login import current_user
        if current_user and current_user.is_authenticated and current_user.is_active():
            logger.info(f"User authenticated via flask-login: {current_user}")
            return func(*args, **kwargs)

        # Check if user is authenticated via g.user (set by login.py)
        if getattr(g, 'user', None):
            logger.info(f"User authenticated via g.user: {g.user}")
            return func(*args, **kwargs)

        logger.warning(f"No valid authentication found for {func.__name__}")
        logger.warning(f"current_user: {current_user}, is_authenticated: {current_user.is_authenticated if current_user else 'N/A'}, is_active: {current_user.is_active() if current_user else 'N/A'}")
        return api_error("Authentication required", 401)
    return wrapper

def require_admin(func):
    """Decorator to require admin privileges."""
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        # Check if authentication is enabled
        if not current_app.config.get('webui_username'):
            return func(*args, **kwargs)

        # Check if user is authenticated and is admin
        if not getattr(g, 'user', None):
            return api_error("Authentication required", 401)

        if not g.user.get('is_admin', False):
            return api_error("Admin privileges required", 403)

        return func(*args, **kwargs)
    return wrapper

def handle_api_exception(func):
    """Decorator to handle exceptions in API endpoints."""
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except Exception as e:
            logger.exception(f"API error: {e}")
            error_message = str(e)
            error_traceback = traceback.format_exc() if current_app.debug else None

            response = {
                'error': error_message,
                'status': 'error',
                'timestamp': time.time()
            }

            if error_traceback:
                response['traceback'] = error_traceback

            return json_response(response, 500)
    return wrapper

def paginate(query_func):
    """Helper function to paginate results."""
    offset = request.args.get('offset', default=0, type=int)
    limit = request.args.get('limit', default=20, type=int)

    # Limit the maximum number of items per page
    if limit > 100:
        limit = 100

    items = query_func(offset=offset, limit=limit)

    # Get total count if available
    total = None
    if hasattr(query_func, 'count'):
        total = query_func.count()

    return {
        'items': items,
        'offset': offset,
        'limit': limit,
        'total': total
    }

def parse_date_range():
    """Parse date range from request arguments."""
    start_time = request.args.get('start_time', default=None, type=float)
    end_time = request.args.get('end_time', default=None, type=float)

    if start_time is None:
        # Default to 24 hours ago
        start_time = time.time() - 24 * 60 * 60

    if end_time is None:
        # Default to now
        end_time = time.time()

    return start_time, end_time

def get_project_name(project_name=None):
    """Get project name from request or URL parameters."""
    if project_name:
        return project_name

    # Try to get from URL parameters
    if request.view_args and 'project_name' in request.view_args:
        return request.view_args['project_name']

    # Try to get from query parameters
    return request.args.get('project') or request.args.get('project_name')

def get_task_id(task_id=None):
    """Get task ID from request or URL parameters."""
    if task_id:
        return task_id

    # Try to get from URL parameters
    if request.view_args and 'task_id' in request.view_args:
        return request.view_args['task_id']

    # Try to get from query parameters
    return request.args.get('taskid') or request.args.get('task_id')

def get_db_or_error(db_name):
    """Get database from app config or return error."""
    db = current_app.config.get(db_name)
    if not db:
        raise ValueError(f"{db_name} not initialized")
    return db

def get_rpc_or_error(rpc_name):
    """Get RPC from app config or return error."""
    rpc = current_app.config.get(rpc_name)
    if not rpc:
        raise ValueError(f"{rpc_name} not available")
    return rpc

def validate_project_name(name):
    """Validate project name."""
    if not name:
        raise ValueError("Project name is required")

    projectdb = get_db_or_error('projectdb')
    if not projectdb.verify_project_name(name):
        raise ValueError("Invalid project name. Only alphanumeric characters and underscore are allowed, and length should be less than 64 characters.")

    return name
