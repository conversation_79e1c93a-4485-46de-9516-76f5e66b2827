#!/usr/bin/env python
# -*- encoding: utf-8 -*-
# vim: set et sw=4 ts=4 sts=4 ff=unix fenc=utf8:
# Author: PySpider Team
# Created on 2025-04-26

import json
import time
import psutil
import logging
from flask import jsonify, render_template
from .app import app
from pyspider.webui.components_status import get_components_status

logger = logging.getLogger(__name__)

def get_scheduler_stats():
    """スケジューラの統計情報を取得する"""
    try:
        # スケジューラRPCが設定されているか確認
        if hasattr(app, 'config') and 'scheduler_rpc' in app.config and app.config['scheduler_rpc'] is not None:
            logger.debug("Using scheduler RPC for stats")
            scheduler_rpc = app.config['scheduler_rpc']

            # DummySchedulerRPCかどうかを確認
            if hasattr(scheduler_rpc.__class__, '__name__') and scheduler_rpc.__class__.__name__ == 'DummySchedulerRPC':
                logger.debug("Using DummySchedulerRPC for stats")
                # ダミーのスケジューラRPCの場合は、デフォルト値を返す
                return {
                    'initialized': True,  # 初期化されたとみなす
                    'queue_size': 0,
                    'processing_tasks': 0,
                    'total_tasks_24h': 0,
                    'failed_tasks_24h': 0,
                    'success_tasks_24h': 0,
                    'pending_tasks': 0
                }

            # スケジューラRPCからカウンター情報を取得
            try:
                # webui_updateメソッドを使用して、すべての情報を一度に取得
                try:
                    webui_data = scheduler_rpc.webui_update()
                    if isinstance(webui_data, dict) and 'counter' in webui_data:
                        counter_data = webui_data['counter']

                        # 1日のカウンター情報
                        counter_1d = counter_data.get('1d', {})
                        if not isinstance(counter_1d, dict):
                            counter_1d = {}

                        # 全期間のカウンター情報
                        counter_all = counter_data.get('all', {})
                        if not isinstance(counter_all, dict):
                            counter_all = {}

                        # キュー情報
                        queue_stats = {}

                        # 合計値を計算（数値のみを合計）
                        total_tasks_24h = 0
                        for k, v in counter_1d.items():
                            if k != 'time' and isinstance(v, (int, float)):
                                total_tasks_24h += v

                        return {
                            'initialized': True,
                            'queue_size': sum(queue_stats.values()) if isinstance(queue_stats, dict) else 0,
                            'processing_tasks': counter_all.get('active', 0),
                            'total_tasks_24h': total_tasks_24h,
                            'failed_tasks_24h': counter_1d.get('failed', 0),
                            'success_tasks_24h': counter_1d.get('success', 0),
                            'pending_tasks': counter_all.get('pending', 0)
                        }
                except Exception as e:
                    logger.error(f"Error getting stats from webui_update: {str(e)}")

                # 従来の方法でカウンター情報を取得
                counter_1d = {}
                counter_all = {}
                queue_stats = {}

                try:
                    counter_1d = scheduler_rpc.counter('1d', None)
                    if not isinstance(counter_1d, dict):
                        counter_1d = {}
                except Exception as e:
                    logger.error(f"Error getting 1d counter: {str(e)}")

                try:
                    counter_all = scheduler_rpc.counter('all', None)
                    if not isinstance(counter_all, dict):
                        counter_all = {}
                except Exception as e:
                    logger.error(f"Error getting all counter: {str(e)}")

                try:
                    queue_stats = scheduler_rpc.get_queue_stats()
                    if not isinstance(queue_stats, dict):
                        queue_stats = {}
                except Exception as e:
                    logger.error(f"Error getting queue stats: {str(e)}")

                # 合計値を計算（数値のみを合計）
                total_tasks_24h = 0
                for k, v in counter_1d.items():
                    if k != 'time' and isinstance(v, (int, float)):
                        total_tasks_24h += v

                return {
                    'initialized': True,
                    'queue_size': sum(queue_stats.values()) if isinstance(queue_stats, dict) else 0,
                    'processing_tasks': counter_all.get('active', 0),
                    'total_tasks_24h': total_tasks_24h,
                    'failed_tasks_24h': counter_1d.get('failed', 0),
                    'success_tasks_24h': counter_1d.get('success', 0),
                    'pending_tasks': counter_all.get('pending', 0)
                }
            except Exception as e:
                logger.error(f"Error getting stats from scheduler RPC: {str(e)}")
                # エラーが発生した場合はデフォルト値を返す
                return {
                    'initialized': True,  # 初期化されたとみなす
                    'queue_size': 0,
                    'processing_tasks': 0,
                    'total_tasks_24h': 0,
                    'failed_tasks_24h': 0,
                    'success_tasks_24h': 0,
                    'pending_tasks': 0
                }

        # スケジューラが直接アクセス可能か確認
        if not hasattr(app, 'scheduler'):
            logger.info("Scheduler is not directly accessible. Using default values.")
            return {
                'initialized': True,  # 初期化されたとみなす
                'queue_size': 0,
                'processing_tasks': 0,
                'total_tasks_24h': 0,
                'failed_tasks_24h': 0,
                'success_tasks_24h': 0,
                'pending_tasks': 0
            }

        logger.debug("Scheduler is initialized. Getting scheduler stats.")
        scheduler = app.scheduler
        stats = {
            'initialized': True,
            'queue_size': 0,
            'processing_tasks': 0,
            'total_tasks_24h': 0,
            'failed_tasks_24h': 0,
            'success_tasks_24h': 0,
            'pending_tasks': 0
        }

        # キューサイズ
        if hasattr(scheduler, 'queue') and hasattr(scheduler.queue, 'qsize'):
            stats['queue_size'] = scheduler.queue.qsize()

        # 処理中のタスク
        if hasattr(scheduler, 'processing'):
            stats['processing_tasks'] = len(scheduler.processing)

        # カウンター
        if hasattr(scheduler, 'counter'):
            try:
                stats['total_tasks_24h'] = scheduler.counter('1d', 'sum')
                stats['failed_tasks_24h'] = scheduler.counter('1d', 'failed')
                stats['success_tasks_24h'] = scheduler.counter('1d', 'success')
                stats['pending_tasks'] = scheduler.counter('all', 'pending')
                logger.debug(f"Counter stats: total_24h={stats['total_tasks_24h']}, failed_24h={stats['failed_tasks_24h']}, success_24h={stats['success_tasks_24h']}, pending={stats['pending_tasks']}")
            except Exception as e:
                logger.error(f"Error getting counter stats: {str(e)}")
        else:
            logger.warning("Scheduler does not have counter method. No counter stats available.")

        return stats
    except Exception as e:
        logger.error(f"Error getting scheduler stats: {str(e)}")
        return {}

def get_system_stats():
    """システムの統計情報を取得する"""
    try:
        stats = {
            'cpu_percent': psutil.cpu_percent(interval=0.1),
            'memory_percent': psutil.virtual_memory().percent,
            'disk_percent': psutil.disk_usage('/').percent,
            'uptime': time.time() - psutil.boot_time()
        }

        # プロセス情報
        process = psutil.Process()
        # CPU使用率の計算を修正（interval=Noneにして前回の呼び出しからの差分を計算）
        stats['process_cpu_percent'] = process.cpu_percent(interval=None)
        # 2回目の呼び出しで正確な値を取得（最初の呼び出しは常に0を返す）
        time.sleep(0.1)
        stats['process_cpu_percent'] = process.cpu_percent(interval=None)

        # メモリ使用率は100倍しない（テンプレート側で調整）
        stats['process_memory_percent'] = process.memory_percent() / 100
        stats['process_memory_mb'] = process.memory_info().rss / (1024 * 1024)
        stats['process_threads'] = process.num_threads()

        # エラー処理を追加
        try:
            stats['process_open_files'] = len(process.open_files())
        except Exception:
            stats['process_open_files'] = 0

        try:
            # 非推奨のconnections()の代わりにnet_connections()を使用
            stats['process_connections'] = len(process.net_connections())
        except Exception:
            stats['process_connections'] = 0

        return stats
    except Exception as e:
        logger.error(f"Error getting system stats: {str(e)}")
        return {}

@app.route('/metrics')
@app.route('/api/metrics')
@app.route('/api/v2/metrics')
def metrics():
    """メトリクスエンドポイント"""
    try:
        # 統計情報を収集
        metrics = {
            'timestamp': time.time(),
            'scheduler': get_scheduler_stats(),
            'system': get_system_stats(),
            'components': get_components_status()
        }

        return jsonify(metrics)
    except Exception as e:
        logger.error(f"Error in metrics endpoint: {str(e)}")
        return jsonify({
            'error': str(e),
            'timestamp': time.time()
        }), 500

@app.route('/metrics-dashboard')
def metrics_dashboard():
    """メトリクスダッシュボードページ"""
    return render_template("metrics-dashboard.html")


@app.route('/metrics/health')
@app.route('/api/metrics/health')
@app.route('/api/v2/metrics/health')
def health():
    """ヘルスチェックエンドポイント"""
    try:
        # 基本的なヘルスチェック
        system_stats = get_system_stats()
        scheduler_stats = get_scheduler_stats()

        # ヘルスステータスを判断
        health_status = 'healthy'
        issues = []

        # システムリソースのチェック
        if system_stats.get('cpu_percent', 0) > 90:
            health_status = 'warning'
            issues.append('High CPU usage')

        if system_stats.get('memory_percent', 0) > 90:
            health_status = 'warning'
            issues.append('High memory usage')

        if system_stats.get('disk_percent', 0) > 90:
            health_status = 'warning'
            issues.append('High disk usage')

        # スケジューラのチェック
        if scheduler_stats.get('queue_size', 0) > 10000:
            health_status = 'warning'
            issues.append('Large queue size')

        if scheduler_stats.get('processing_tasks', 0) > 1000:
            health_status = 'warning'
            issues.append('Many processing tasks')

        # レスポンスを返す
        return jsonify({
            'status': health_status,
            'issues': issues,
            'timestamp': time.time()
        })
    except Exception as e:
        logger.error(f"Error in health endpoint: {str(e)}")
        return jsonify({
            'status': 'error',
            'error': str(e),
            'timestamp': time.time()
        }), 500
