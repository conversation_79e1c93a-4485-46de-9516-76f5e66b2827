{"openapi": "3.0.0", "info": {"title": "pyspiderNX2 API", "description": "pyspiderNX2のRESTful API", "version": "2.0.0", "contact": {"name": "pyspiderNX2 Team", "url": "https://github.com/your-username/pyspiderNX2"}, "license": {"name": "Apache 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.html"}}, "servers": [{"url": "http://localhost:5000", "description": "開発サーバー"}], "tags": [{"name": "projects", "description": "プロジェクト管理"}, {"name": "project-groups", "description": "プロジェクトグループ管理"}, {"name": "tasks", "description": "タスク管理"}, {"name": "results", "description": "結果管理"}, {"name": "system", "description": "システム情報"}], "paths": {"/api/v2/projects": {"get": {"tags": ["projects"], "summary": "プロジェクト一覧の取得", "description": "すべてのプロジェクトの一覧を取得します。", "operationId": "getProjects", "responses": {"200": {"description": "成功", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Project"}}}}}, "500": {"description": "サーバーエラー", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}, "post": {"tags": ["projects"], "summary": "プロジェクトの作成", "description": "新しいプロジェクトを作成します。", "operationId": "createProject", "requestBody": {"description": "プロジェクト情報", "required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProjectInput"}}}}, "responses": {"200": {"description": "成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "400": {"description": "不正なリクエスト", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "サーバーエラー", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/api/v2/projects/{name}": {"get": {"tags": ["projects"], "summary": "プロジェクトの取得", "description": "指定されたプロジェクトの詳細情報を取得します。", "operationId": "getProject", "parameters": [{"name": "name", "in": "path", "description": "プロジェクト名", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Project"}}}}, "404": {"description": "プロジェクトが見つかりません", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "サーバーエラー", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}, "put": {"tags": ["projects"], "summary": "プロジェクトの更新", "description": "指定されたプロジェクトを更新します。", "operationId": "updateProject", "parameters": [{"name": "name", "in": "path", "description": "プロジェクト名", "required": true, "schema": {"type": "string"}}], "requestBody": {"description": "プロジェクト情報", "required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProjectUpdate"}}}}, "responses": {"200": {"description": "成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "404": {"description": "プロジェクトが見つかりません", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "サーバーエラー", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}, "delete": {"tags": ["projects"], "summary": "プロジェクトの削除", "description": "指定されたプロジェクトを削除します。", "operationId": "deleteProject", "parameters": [{"name": "name", "in": "path", "description": "プロジェクト名", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "404": {"description": "プロジェクトが見つかりません", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "サーバーエラー", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/api/v2/projects/{name}/run": {"post": {"tags": ["projects"], "summary": "プロジェクトの実行", "description": "指定されたプロジェクトを実行します。", "operationId": "runProject", "parameters": [{"name": "name", "in": "path", "description": "プロジェクト名", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}, "404": {"description": "プロジェクトが見つかりません", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "サーバーエラー", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/api/v2/project-groups": {"get": {"tags": ["project-groups"], "summary": "プロジェクトグループ一覧の取得", "description": "すべてのプロジェクトグループの一覧を取得します。", "operationId": "getProjectGroups", "responses": {"200": {"description": "成功", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProjectGroup"}}}}}, "500": {"description": "サーバーエラー", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}, "post": {"tags": ["project-groups"], "summary": "プロジェクトグループの作成", "description": "新しいプロジェクトグループを作成します。", "operationId": "createProjectGroup", "requestBody": {"description": "プロジェクトグループ情報", "required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProjectGroupInput"}}}}, "responses": {"200": {"description": "成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProjectGroupResponse"}}}}, "400": {"description": "不正なリクエスト", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "500": {"description": "サーバーエラー", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}}, "components": {"schemas": {"Project": {"type": "object", "properties": {"name": {"type": "string", "description": "プロジェクト名"}, "group": {"type": "string", "description": "プロジェクトグループ"}, "status": {"type": "string", "description": "プロジェクトのステータス", "enum": ["RUNNING", "PAUSED", "STOPPED", "CHECKING", "TODO", "DEBUG"]}, "script": {"type": "string", "description": "プロジェクトスクリプト"}, "rate": {"type": "number", "format": "float", "description": "レート制限"}, "burst": {"type": "number", "format": "float", "description": "バースト値"}, "updatetime": {"type": "number", "format": "float", "description": "更新日時"}}}, "ProjectInput": {"type": "object", "required": ["name", "script"], "properties": {"name": {"type": "string", "description": "プロジェクト名"}, "group": {"type": "string", "description": "プロジェクトグループ"}, "status": {"type": "string", "description": "プロジェクトのステータス", "enum": ["RUNNING", "PAUSED", "STOPPED", "CHECKING", "TODO", "DEBUG"], "default": "TODO"}, "script": {"type": "string", "description": "プロジェクトスクリプト"}, "rate": {"type": "number", "format": "float", "description": "レート制限", "default": 1.0}, "burst": {"type": "number", "format": "float", "description": "バースト値", "default": 10.0}}}, "ProjectUpdate": {"type": "object", "properties": {"group": {"type": "string", "description": "プロジェクトグループ"}, "status": {"type": "string", "description": "プロジェクトのステータス", "enum": ["RUNNING", "PAUSED", "STOPPED", "CHECKING", "TODO", "DEBUG"]}, "script": {"type": "string", "description": "プロジェクトスクリプト"}, "rate": {"type": "number", "format": "float", "description": "レート制限"}, "burst": {"type": "number", "format": "float", "description": "バースト値"}}}, "ProjectGroup": {"type": "object", "properties": {"name": {"type": "string", "description": "グループ名"}, "projects": {"type": "array", "items": {"type": "string"}, "description": "グループ内のプロジェクト名一覧"}, "project_count": {"type": "integer", "description": "グループ内のプロジェクト数"}}}, "ProjectGroupInput": {"type": "object", "required": ["name", "projects"], "properties": {"name": {"type": "string", "description": "グループ名"}, "projects": {"type": "array", "items": {"type": "string"}, "description": "グループに追加するプロジェクト名一覧"}}}, "ProjectGroupResponse": {"type": "object", "properties": {"status": {"type": "string", "enum": ["ok"], "description": "ステータス"}, "group": {"type": "string", "description": "グループ名"}, "updated_projects": {"type": "array", "items": {"type": "string"}, "description": "更新されたプロジェクト名一覧"}}}, "SuccessResponse": {"type": "object", "properties": {"status": {"type": "string", "enum": ["ok"], "description": "ステータス"}}}, "Error": {"type": "object", "properties": {"error": {"type": "string", "description": "エラーメッセージ"}}}}}}