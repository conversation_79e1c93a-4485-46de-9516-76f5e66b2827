!function(e){function t(n){if(r[n])return r[n].exports;var a=r[n]={exports:{},id:n,loaded:!1};return e[n].call(a.exports,a,a.exports,t),a.loaded=!0,a.exports}var r={};return t.m=e,t.c=r,t.p="",t(0)}([function(e,t){"use strict";function r(e,t){function r(e,t){if(!e||!t)return!1;if(e.length!=t.length)return!1;for(var r=0,n=e.length;r<n;r++)if(e[r]!==t[r])return!1;return!0}function n(t){return e.evaluate(t,e,null,XPathResult.FIRST_ORDERED_NODE_TYPE,null).singleNodeValue}function a(e){var t=0,r=0;do isNaN(e.offsetLeft)||(r+=e.offsetLeft),isNaN(e.offsetTop)||(t+=e.offsetTop);while(e=e.offsetParent);return{top:t,left:r}}function o(e){var t="";return e.forEach(function(e){e.selected&&(t+=e.name)}),t}function l(e,t){var r="",n=null;return e.forEach(function(e,a){if(!(t>=0&&a>t))if(e.invalid)n=null;else if(e.selected){n&&(r+=" >");var o="";e.features.forEach(function(e){e.selected&&(o+=e.pattern)}),""===o&&(o="*"),r+=" "+o,n=e}else n=null}),""===r&&(r="*"),r}function i(t){var n=[];do{var a=[];if(a.push({name:t.tagName.toLowerCase(),pattern:t.tagName.toLowerCase(),selected:!0}),t.getAttribute("id")&&a.push({name:"#"+t.getAttribute("id"),pattern:"#"+t.getAttribute("id"),selected:!0}),t.classList.length>0)for(var i=0;i<t.classList.length;i++){var s=t.classList[i];a.push({name:"."+s,pattern:"."+s,selected:!0})}for(var f="itemprop",i=0,u=t.attributes;i<u.length;i++)f.indexOf(u[i].nodeName)!=-1&&a.push({name:"["+u[i].nodeName+"="+JSON.stringify(u[i].nodeValue)+"]",pattern:"["+u[i].nodeName+"="+JSON.stringify(u[i].nodeValue)+"]",selected:!0});for(var c=t.parentNode.childNodes,d=t.tagName.toLowerCase(),i=0,p=0;c.length>1&&i<c.length;i++){var h=c[i];if(h===t){d+="["+(p+1)+"]";break}h.tagName==t.tagName&&p++}n.push({tag:t.tagName.toLowerCase(),name:o(a),xpath:d,selected:!0,invalid:"tbody"===t.tagName.toLowerCase(),features:a})}while(t=t.parentElement);n.reverse();var v=e.querySelectorAll(l(n));return n.forEach(function(t,a){if(!t.invalid){var i=e.querySelectorAll(l(n,a));t.features.forEach(function(t,o){t.selected=!1,r(i,e.querySelectorAll(l(n,a)))||(t.selected=!0)}),t.features.every(function(e){return!e.selected})&&(t.features[0].selected=!0),t.name=o(t.features)}}),n.forEach(function(t,a){return t.selected=!1,r(v,e.querySelectorAll(l(n)))?void(t.name=t.tag):void(t.selected=!0)}),n}function s(t){t instanceof Element&&(t=[t]),Array.prototype.forEach.call(e.querySelectorAll(".pyspider_overlay"),function(e){e.remove()}),Array.prototype.forEach.call(t,function(t){var r=e.createElement("div");r.className="pyspider_overlay";var n=a(t);r.setAttribute("style","z-index: 999999;background-color: rgba(255, 165, 0, 0.3);position: absolute;pointer-events: none;top: "+n.top+"px;left:"+n.left+"px;width: "+t.offsetWidth+"px;height: "+t.offsetHeight+"px;"),e.body.appendChild(r)})}function f(t){t instanceof Element&&(t=[t]),Array.prototype.forEach.call(e.querySelectorAll(".pyspider_highlight"),function(e){e.remove()}),Array.prototype.forEach.call(t,function(t){var r=e.createElement("div");r.className="pyspider_highlight";var n=a(t);r.setAttribute("style","z-index: 888888;border: 2px solid #c00;position: absolute;pointer-events: none;top: "+(n.top-2)+"px;left:"+(n.left-2)+"px;width: "+t.offsetWidth+"px;height: "+t.offsetHeight+"px;"),e.body.appendChild(r)})}window.addEventListener("message",function(t){"overlay"==t.data.type?s(n(t.data.xpath)):"heightlight"==t.data.type&&f(e.querySelectorAll(t.data.css_selector))}),e.addEventListener("mouseover",function(e){s(event.target)}),e.addEventListener("click",function(e){e.preventDefault(),e.stopPropagation(),t(i(e.target))})}Object.defineProperty(t,"__esModule",{value:!0}),t["default"]=r}]);
//# sourceMappingURL=css_selector_helper.min.js.map