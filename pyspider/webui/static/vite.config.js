import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [vue()],

  // 開発サーバー設定
  server: {
    port: 8080,
    host: '0.0.0.0',
    open: false,
    cors: true,
    proxy: {
      // pyspider APIプロキシ
      '/api': {
        target: 'http://localhost:5000',
        changeOrigin: true,
        secure: false
      },
      '/counter': {
        target: 'http://localhost:5000',
        changeOrigin: true,
        secure: false
      },
      '/queues': {
        target: 'http://localhost:5000',
        changeOrigin: true,
        secure: false
      },
      '/update': {
        target: 'http://localhost:5000',
        changeOrigin: true,
        secure: false
      },
      '/run': {
        target: 'http://localhost:5000',
        changeOrigin: true,
        secure: false
      }
    }
  },

  // ビルド設定
  build: {
    outDir: 'dist',
    assetsDir: 'assets',
    sourcemap: true,
    minify: 'terser',
    rollupOptions: {
      input: {
        main: resolve(__dirname, 'index.html')
      },
      output: {
        manualChunks: {
          vue: ['vue'],
          vuetify: ['vuetify'],
          pinia: ['pinia']
        }
      }
    },
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true
      }
    }
  },

  // 依存関係の最適化
  optimizeDeps: {
    include: [
      'vue',
      'vuetify',
      'pinia',
      'vee-validate',
      '@mdi/font'
    ]
  },

  // CSS設定
  css: {
    preprocessorOptions: {
      less: {
        javascriptEnabled: true,
        additionalData: `@import "${resolve(__dirname, 'src/variable.less')}";`
      }
    }
  },

  // 解決設定
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
      '~': resolve(__dirname, 'node_modules')
    },
    extensions: ['.js', '.vue', '.json', '.less', '.css']
  },

  // 環境変数
  define: {
    __VUE_OPTIONS_API__: true,
    __VUE_PROD_DEVTOOLS__: false,
    __VUE_PROD_HYDRATION_MISMATCH_DETAILS__: false
  }
})
