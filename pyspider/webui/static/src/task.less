/* vim: set et sw=2 ts=2 sts=2 ff=unix fenc=utf8: */
/* Author: Binux<<EMAIL>> */
/*         http://binux.me */
/* Created on 2014-07-16 19:20:30 */

@import "variable";

.base-info {
  padding: 10px 15px 2px 15px;
  background-color: #f5f5f5;
  border-bottom: 1px solid #ddd;
}

.more-info {
  padding: 10px 15px;
}

.more-info dd {
  display: block;
  font-family: monospace;
  white-space: pre;
  word-break: break-all;
  word-wrap: break-word;
  margin: 1em 0px;
}

.status_mix(@color: lighten(black, 50%)) {
  border: solid 1px darken(@color, 10%);
  padding: 1px 5px 0 5px;
  background: @color;
  color: white;
}
.status {
  &-1 {
    .status_mix(@blue);
  }
  &-2 {
    .status_mix(@green);
  }
  &-3 {
    .status_mix(@red);
  }
  &-4 {
    .status_mix;
  }
}

.url {
  font-size: 120%;
  text-decoration: underline;
}

.callback {
  color: @orange;
  font-weight: bold;

  &:hover, &:focus {
    color: darken(@orange, 10%);
  }
}

dt .glyphicon-ok {
  color: @green;
}
dt .glyphicon-remove {
  color: @red;
}
