/* vim: set et sw=2 ts=2 sts=2 ff=unix fenc=utf8: */
/* Author: Binux<<EMAIL>> */
/*         http://binux.me */
/* Created on 2014-10-22 22:38:45 */

@import "variable";

.top-bar {
  padding: 10px 15px 2px 15px;
  height: 46px;
  background-color: #f5f5f5;
  border-bottom: 1px solid #ddd;
  position: relative;
  
  h1 {
    margin: 0 0 10px 0;
    font-size: 18px;
  }

  .btn-group {
    margin: 8px 10px 0 0;
    position: absolute;
    right: 0;
    top: 0;

    a.btn {
    }
  }
}

.pagination-wrap {
  text-align: right;
  padding-right: 15px;
}

table {
  border-bottom: 1px solid #ddd;

  td {
    word-break: break-all;
  }
}
