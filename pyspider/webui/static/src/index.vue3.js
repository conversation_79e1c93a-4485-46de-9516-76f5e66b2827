// Vite環境でのVue.js 3アプリケーション
console.log('🚀 pyspider WebUI (Vite版) 起動中...');

// Vue.jsがグローバルに読み込まれているかチェック
if (typeof window.Vue === 'undefined') {
  console.error('Vue.js が読み込まれていません');
  throw new Error('Vue.js が読み込まれていません');
}

if (typeof window.Vuetify === 'undefined') {
  console.error('Vuetify が読み込まれていません');
  throw new Error('Vuetify が読み込まれていません');
}

const { createApp, ref, computed, onMounted, nextTick } = Vue;
const { createVuetify } = Vuetify;

// Vuetifyの設定
const vuetify = createVuetify({
  theme: {
    defaultTheme: 'light',
    themes: {
      light: {
        colors: {
          primary: '#1976d2',
          secondary: '#424242',
          accent: '#82b1ff',
          error: '#ff5252',
          info: '#2196f3',
          success: '#4caf50',
          warning: '#ffc107'
        }
      },
      dark: {
        colors: {
          primary: '#2196f3',
          secondary: '#424242',
          accent: '#ff4081',
          error: '#ff5252',
          info: '#2196f3',
          success: '#4caf50',
          warning: '#ffc107'
        }
      }
    }
  }
});

// メインアプリケーション
const app = createApp({
  setup() {
    // リアクティブデータ
    const loading = ref(true);
    const refreshing = ref(false);
    const projects = ref([]);
    const showCreateDialog = ref(false);
    const createFormValid = ref(false);
    const creating = ref(false);
    const isDark = ref(false);

    // 新規プロジェクトデータ
    const newProject = ref({
      name: '',
      group: '',
      script: ''
    });

    // テーブルヘッダー
    const headers = ref([
      { title: 'プロジェクト名', key: 'name', sortable: true },
      { title: 'グループ', key: 'group', sortable: true },
      { title: 'レート/バースト', key: 'rate', sortable: false },
      { title: '進行状況', key: 'progress', sortable: false },
      { title: 'アクション', key: 'actions', sortable: false }
    ]);

    // バリデーションルール
    const nameRules = ref([
      v => !!v || 'プロジェクト名は必須です',
      v => /^[a-zA-Z0-9_]+$/.test(v) || 'プロジェクト名は英数字とアンダースコアのみ使用可能です'
    ]);

    // デフォルトスクリプト
    const defaultScript = ref(`#!/usr/bin/env python
# -*- encoding: utf-8 -*-

from pyspider.libs.base_handler import *

class Handler(BaseHandler):
    crawl_config = {}

    @every(minutes=24 * 60)
    def on_start(self):
        self.crawl('http://example.com/', callback=self.index_page)

    @config(age=10 * 24 * 60 * 60)
    def index_page(self, response):
        for each in response.doc('a[href^="http"]').items():
            self.crawl(each.attr.href, callback=self.detail_page)

    @config(priority=2)
    def detail_page(self, response):
        return {
            'url': response.url,
            'title': response.doc('title').text(),
        }`);

    // 計算プロパティ
    const runningProjects = computed(() =>
      projects.value.filter(p => p.status === 'RUNNING').length
    );

    const stoppedProjects = computed(() =>
      projects.value.filter(p => p.status === 'STOP').length
    );

    const debugProjects = computed(() =>
      projects.value.filter(p => p.status === 'DEBUG').length
    );

    // メソッド
    const fetchProjects = async () => {
      try {
        const response = await fetch('/api/v2/projects');
        const data = await response.json();
        projects.value = data.projects || [];
      } catch (error) {
        console.error('プロジェクト取得エラー:', error);
        // モックデータ
        projects.value = [
          {
            name: 'example_project',
            group: 'default',
            status: 'RUNNING',
            rate: 1,
            burst: 3,
            pending: 10,
            success: 150,
            retry: 5,
            failed: 2
          }
        ];
      } finally {
        loading.value = false;
      }
    };

    const refreshData = async () => {
      refreshing.value = true;
      await fetchProjects();
      refreshing.value = false;
    };

    const getStatusColor = (status) => {
      const colors = {
        'RUNNING': 'success',
        'STOP': 'error',
        'CHECKING': 'warning',
        'DEBUG': 'info',
        'TODO': 'grey'
      };
      return colors[status] || 'grey';
    };

    const getProgressWidth = (item, type) => {
      const total = (item.pending || 0) + (item.success || 0) +
                   (item.retry || 0) + (item.failed || 0);
      if (total === 0) return 0;
      return ((item[type] || 0) / total) * 100;
    };

    const toggleProject = async (project) => {
      project.loading = true;
      const action = project.status === 'RUNNING' ? 'stop' : 'start';

      try {
        const response = await fetch(`/api/v2/projects/${project.name}/${action}`, {
          method: 'POST'
        });

        if (response.ok) {
          await fetchProjects();
        }
      } catch (error) {
        console.error('プロジェクト操作エラー:', error);
      } finally {
        project.loading = false;
      }
    };

    const editProject = (project) => {
      window.open(`/debug/${project.name}`, '_blank');
    };

    const createProject = async () => {
      if (!createFormValid.value) return;

      creating.value = true;

      try {
        const response = await fetch('/api/v2/projects', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(newProject.value)
        });

        if (response.ok) {
          showCreateDialog.value = false;
          newProject.value = { name: '', group: '', script: '' };
          await fetchProjects();
        }
      } catch (error) {
        console.error('プロジェクト作成エラー:', error);
      } finally {
        creating.value = false;
      }
    };

    const toggleTheme = () => {
      isDark.value = !isDark.value;
      vuetify.theme.global.name.value = isDark.value ? 'dark' : 'light';
    };

    // 初期化
    onMounted(() => {
      fetchProjects();

      // 定期更新
      setInterval(() => {
        if (!refreshing.value) {
          fetchProjects();
        }
      }, 5000);
    });

    return {
      loading,
      refreshing,
      projects,
      showCreateDialog,
      createFormValid,
      creating,
      isDark,
      newProject,
      headers,
      nameRules,
      defaultScript,
      runningProjects,
      stoppedProjects,
      debugProjects,
      refreshData,
      getStatusColor,
      getProgressWidth,
      toggleProject,
      editProject,
      createProject,
      toggleTheme
    };
  }
});

// Vuetifyプラグインを使用
app.use(vuetify);

// アプリケーションをマウント
app.mount('#app');

console.log('✅ pyspider WebUI (Vite版) 起動完了');
