!function(e){function t(o){if(n[o])return n[o].exports;var r=n[o]={exports:{},id:o,loaded:!1};return e[o].call(r.exports,r,r.exports,t),r.loaded=!0,r.exports}var n={};return t.m=e,t.c=n,t.p="",t(0)}([function(e,t,n){"use strict";function o(e){return e&&e.__esModule?e:{"default":e}}n(3),n(7);var r=n(8),i=o(r);window.SelectorHelper=function(){function e(e){var t=e.features,n="";return t.forEach(function(e){e.selected&&(n+=e.name)}),""===n?e.tag:n}function t(e,t){var n="",o=null;return e.forEach(function(e,r){if(!(t>=0&&r>t))if(e.invalid)o=null;else if(e.selected){o&&(n+=" >");var i="";e.features.forEach(function(e){e.selected&&(i+=e.pattern)}),""===i&&(i="*"),n+=" "+i,o=e}else o=null}),""===n&&(n="*"),n.trim()}function n(e){l=e,a.heightlight(t(e))}function o(t){s.find(".element").remove();var o=[];$.each(t,function(r,i){var s=$("<span>").addClass("element").data("info",i);$('<span class="element-name">').text(i.name).appendTo(s),i.selected&&s.addClass("selected"),i.invalid&&s.addClass("invalid");var l=$("<ul>");$.each(i.features,function(o,r){var s=$("<li>").text(r.name).data("feature",r);r.selected&&s.addClass("selected"),s.appendTo(l),s.on("click",function(o){o.stopPropagation();var r=$(this),s=r.data("feature");s.selected?(s.selected=!1,r.removeClass("selected")):(s.selected=!0,r.addClass("selected"));var a=r.parents(".element");i.selected||(i.selected=!0,a.addClass("selected")),a.find(".element-name").text(e(i)),n(t)})}),l.appendTo(s),s.on("mouseover",function(e){var n=[];$.each(t,function(e,t){if(n.push(t.xpath),t===i)return!1}),a.overlay(a.getElementByXpath("/"+n.join("/")))}),s.on("click",function(o){o.stopPropagation();var r=$(this),i=r.data("info");i.selected?(i.selected=!1,r.removeClass("selected")):(i.selected=!0,r.addClass("selected")),r.find(".element-name").text(e(r.data("info"))),n(t)}),o.push(s)}),s.prepend(o),r(),n(t)}function r(){for(;s[0].scrollWidth>s.width();){var e=s.find(".element:visible:first");if(0==e.length)return;e.addClass("invalid").data("info").invalid=!0}}var s=$("#css-selector-helper"),a=null,l=null,c=$("#tab-web");return{init:function(){var e=this,n=this;n.clear(),$("#J-enable-css-selector-helper").on("click",function(t){e.clear(),a=new i["default"]($("#tab-web iframe")[0].contentWindow),a.on("selector_helper_click",function(e){o(e)}),e.enable()}),$("#task-panel").on("scroll",function(e){s.is(":visible")&&($("#debug-tabs").position().top<0?(s.addClass("fixed"),c.addClass("fixed")):(s.removeClass("fixed"),c.removeClass("fixed")))});var r=s.find(".copy-selector-input");r.on("focus",function(e){$(this).select()}),s.find(".copy-selector").on("click",function(e){l&&(r.is(":visible")?(r.hide(),s.find(".element").show()):(s.find(".element").hide(),r.val(t(l)).show()))}),s.find(".add-to-editor").on("click",function(e){Debugger.python_editor_replace_selection(t(l))})},clear:function(){l=null,s.hide(),s.removeClass("fixed"),c.removeClass("fixed"),s.find(".element").remove()},enable:function(){s.show(),s.find(".copy-selector-input").hide(),$("#debug-tabs").position().top<0?(s.addClass("fixed"),c.addClass("fixed")):(s.removeClass("fixed"),c.removeClass("fixed"))}}}(),window.Debugger=function(){function e(e){return t.text(e).html()}var t=$("<div>");return{init:function(){this.splitter=$(".debug-panel:not(:first)").splitter().data("splitter").trigger("init").on("resize-start",function(){$("#left-area .overlay").show()}).on("resize-end",function(){$("#left-area .overlay").hide()}),CodeMirror.keyMap.basic.Tab="indentMore",this.init_python_editor($("#python-editor")),this.init_task_editor($("#task-editor")),this.bind_debug_tabs(),this.bind_run(),this.bind_save(),this.bind_others(),SelectorHelper.init()},not_saved:!1,init_python_editor:function(e){var t=this;this.python_editor_elem=e;var n=this.python_editor=CodeMirror(e[0],{value:script_content,mode:"python",lineNumbers:!0,indentUnit:4,lineWrapping:!0,styleActiveLine:!0,autofocus:!0});n.on("focus",function(){e.addClass("focus")}),n.on("blur",function(){e.removeClass("focus")}),n.on("change",function(){t.not_saved=!0}),window.addEventListener("beforeunload",function(e){if(t.not_saved){var n="You have not saved changes.";return(e||window.event).returnValue=n,n}})},python_editor_replace_selection:function(e){this.python_editor.getDoc().replaceSelection(e)},auto_format:function(e){var t=e.getCursor(!0);CodeMirror.commands.selectAll(e),e.autoFormatRange(e.getCursor(!0),e.getCursor(!1)),e.setCursor(t)},format_string:function(e,t){var n=document.createElement("div"),o=CodeMirror(n,{value:e,mode:t});return this.auto_format(o),o.getDoc().getValue()},init_task_editor:function(e){var t=this.task_editor=CodeMirror(e[0],{value:task_content,mode:"application/json",indentUnit:2,lineWrapping:!0,styleActiveLine:!0,lint:!0});this.auto_format(t),t.getDoc().clearHistory(),t.on("focus",function(){e.addClass("focus")}),t.on("blur",function(){e.removeClass("focus")})},bind_debug_tabs:function(){var t=this;$("#tab-control > li[data-id]").on("click",function(){$("#tab-control > li[data-id]").removeClass("active");var e=$(this).addClass("active").data("id");$("#debug-tabs .tab").hide(),$("#debug-tabs #"+e).show()}),$("#tab-control li[data-id=tab-html]").on("click",function(){if(!$("#tab-html").data("format")){var n="";CodeMirror.runMode(t.format_string($("#tab-html pre").text(),"text/html"),"text/html",function(t,o){n+=o?'<span class="cm-'+o+'">'+e(t)+"</span>":e(t)}),$("#tab-html pre").html(n),$("#tab-html").data("format",!0)}})},bind_run:function(){var e=this;$("#run-task-btn").on("click",function(){e.run()}),$("#undo-btn").on("click",function(t){e.task_editor.execCommand("undo")}),$("#redo-btn").on("click",function(t){e.task_editor.execCommand("redo")})},bind_save:function(){var e=this;$("#save-task-btn").on("click",function(){var t=e.python_editor.getDoc().getValue();$("#right-area .overlay").show(),$.ajax({type:"POST",url:location.pathname+"/save",data:{script:t},success:function(t){console.log(t),e.python_log(""),e.python_log("saved!"),e.not_saved=!1,$("#right-area .overlay").hide()},error:function(t,n,o){console.log(t,n,o),e.python_log("save error!\n"+t.responseText),$("#right-area .overlay").hide()}})})},bind_follows:function(){var e=this;$(".newtask").on("click",function(){if($(this).next().hasClass("task-show"))return void $(this).next().remove();var e=$(this).after('<div class="task-show"><pre class="cm-s-default"></pre></div>').data("task");e=JSON.stringify(window.newtasks[e],null,"  "),CodeMirror.runMode(e,"application/json",$(this).next().find("pre")[0])}),$(".newtask .task-run").on("click",function(t){t.preventDefault(),t.stopPropagation();var n=$(this).parents(".newtask").data("task"),o=window.newtasks[n];e.task_editor.setValue(JSON.stringify(o,null,"  ")),e.task_updated(o),e.run()})},task_updated:function(e){$("#history-wrap").hide(),e.project&&e.taskid&&$.ajax({url:"/task/"+e.project+":"+e.taskid+".json",success:function(t){t.code||t.error||($("#history-link").attr("href","/task/"+e.project+":"+e.taskid).text("status: "+t.status_string),$("#history-wrap").show())}})},bind_others:function(){var e=this;$("#python-log-show").on("click",function(){$("#python-log pre").is(":visible")?($("#python-log pre").hide(),$(this).height(8)):($("#python-log pre").show(),$(this).height(0))}),$(".webdav-btn").on("click",function(){e.toggle_webdav_mode(this)})},render_html:function(e,t){var n=arguments.length<=2||void 0===arguments[2]||arguments[2],o=arguments.length<=3||void 0===arguments[3]||arguments[3];void 0===e&&(e="");var r=(new DOMParser).parseFromString(e,"text/html");return $(r).find("base").remove(),$(r).find("head").prepend("<base>"),$(r).find("base").attr("href",t),n&&$(r).find("script").attr("type","text/plain"),o&&$(r).find("iframe[src]").each(function(e,t){t=$(t),t.attr("__src",t.attr("src")),t.attr("src",encodeURI("data:text/html;,<h1>iframe blocked</h1>"))}),r.documentElement.innerHTML},run:function(){var e=this.python_editor.getDoc().getValue(),t=this.task_editor.getDoc().getValue(),n=this;SelectorHelper.clear(),$("#tab-web .iframe-box").html(""),$("#tab-html pre").html(""),$("#tab-follows").html(""),$("#tab-control li[data-id=tab-follows] .num").hide(),$("#python-log").hide(),$("#left-area .overlay").show(),$.ajax({type:"POST",url:location.pathname+"/run",data:{webdav_mode:n.webdav_mode,script:n.webdav_mode?"":e,task:t},success:function(e){console.log(e),$("#left-area .overlay").hide(),$("#tab-web .iframe-box").html('<iframe src="/blank.html" sandbox="allow-same-origin allow-scripts" height="50%"></iframe>');var t=$("#tab-web iframe")[0],o=e.fetch_result.headers&&e.fetch_result.headers["Content-Type"]&&e.fetch_result.headers["Content-Type"]||"text/plain";$("#tab-html pre").text(e.fetch_result.content),$("#tab-html").data("format",!0);var r=null;if(0==o.indexOf("application/json"))try{var i=JSON.parse(e.fetch_result.content);i=JSON.stringify(i,null,"  "),i="<html><pre>"+i+"</pre></html>",r=n.render_html(i,e.fetch_result.url,!0,!0,!1)}catch(s){r="data:,Content-Type:"+o+" parse error."}else 0==o.indexOf("text/html")?($("#tab-html").data("format",!1),r=n.render_html(e.fetch_result.content,e.fetch_result.url,!0,!0,!1)):r=0==o.indexOf("text")?"data:"+o+","+e.fetch_result.content:e.fetch_result.dataurl?e.fetch_result.dataurl:"data:,Content-Type:"+o;var a=t.contentDocument;a.open("text/html","replace"),a.write(r),a.close(),a.onreadystatechange=function(){"complete"===a.readyState&&$("#tab-web iframe").height(a.body.scrollHeight+60)},$("#tab-follows").html("");var l=$("#tab-control li[data-id=tab-follows] .num"),c='<div class="newtask" data-task="__task__"><span class="task-callback">__callback__</span> &gt; <span class="task-url">__url__</span><div class="task-run"><i class="fa fa-play"></i></div><div class="task-more"> <i class="fa fa-ellipsis-h"></i> </div></div>';if(e.follows.length>0){l.text(e.follows.length).show();var d="";window.newtasks={},$.each(e.follows,function(e,t){var n=t.process;n=n&&n.callback||"__call__";var o=c.replace("__callback__",n);o=o.replace("__url__",t.url||'<span class="error">no_url!</span>'),d+=o.replace("__task__",e),window.newtasks[e]=t}),$("#tab-follows").append(d),n.bind_follows()}else l.hide();if($("#tab-messages pre").html(""),e.messages.length>0){$("#tab-control li[data-id=tab-messages] .num").text(e.messages.length).show();var u=JSON.stringify(e.messages,null,"  ");CodeMirror.runMode(u,"application/json",$("#tab-messages pre")[0]),$("#tab-messages")[0]}else $("#tab-control li[data-id=tab-messages] .num").hide();$("#tab-control li.active").click(),n.python_log(e.logs)},error:function(e,t,o){console.log(e,t,o),n.python_log("error: "+t),$("#left-area .overlay").hide()}})},python_log:function(e){e?($("#python-log pre").text(e),$("#python-log pre, #python-log").show(),$("#python-log-show").height(0)):$("#python-log pre, #python-log").hide()},webdav_mode:!1,toggle_webdav_mode:function(e){if(this.webdav_mode){var t=this;$.ajax({type:"GET",url:location.pathname+"/get",success:function(n){t.splitter.trigger("init"),t.python_editor_elem.show(),t.python_editor.setValue(n.script),t.not_saved=!1,$(e).removeClass("active"),t.webdav_mode=!t.webdav_mode},error:function(){alert("Loading script from database error. Script may out-of-date."),t.python_editor_elem.show(),t.splitter.trigger("init"),$(e).removeClass("active"),t.webdav_mode=!t.webdav_mode}})}else{if(this.not_saved){if(!confirm("You have not saved changes. Ignore changes and switch to WebDav mode."))return;this.not_saved=!1}this.python_editor_elem.hide(),this.splitter.trigger("fullsize","prev"),$(e).addClass("active"),this.webdav_mode=!this.webdav_mode}}}}(),Debugger.init()},,,function(e,t){},,,,function(e,t){"use strict";$.fn.splitter=function(e){var t=$(document),n=$('<div class="block"></div>'),o=$("body"),r=JSON.parse(localStorage.getItem("splitterSettings")||"[]");return this.each(function(){function i(e){"y"===u&&(e-=m);var n=e-g[u].currentPos,o=100/g[u].size*n,s=(e-_[u])*g[u].multiplier,l=f[g[u].sizeProp](),d=a[g[u].sizeProp]();if("y"===u&&(o=100-o),l<100&&s<0);else if(d<100&&s>0);else{a.css(g[u].cssProp,o+"%"),f.css(g[u].otherCssProp,100-o+"%");var p={};p[g[u].cssProp]=o+"%",h.css(p),_[u]=e,r[c]=_,localStorage.setItem("splitterSettings",JSON.stringify(r)),i.timer&&clearTimeout(i.timer),i.timer=setTimeout(function(){t.trigger("sizeeditors")},120)}}function s(){f="x"===u?h.prevAll(":visible:first"):h.nextAll(":visible:first")}var a=$(this),l=$(this),c=$.fn.splitter.guid++,d=a.parent(),u=e||"x",f="x"===u?a.prevAll(":visible:first"):a.nextAll(":visible:first"),h=$('<div class="resize"></div>'),p=!1,v=(d.width(),d.offset()),m=(v.left,v.top),g={x:{display:"block",currentPos:d.offset().left,multiplier:1,cssProp:"left",otherCssProp:"right",size:d.width(),sizeProp:"width",moveProp:"pageX",init:{top:0,bottom:0,width:8,"margin-left":"-4px",height:"100%",left:"auto",right:"auto",opacity:0,position:"absolute",cursor:"ew-resize","border-left":"1px solid rgba(218, 218, 218, 0.5)","z-index":99999}},y:{display:"block",currentPos:d.offset().top,multiplier:-1,size:d.height(),cssProp:"bottom",otherCssProp:"top",sizeProp:"height",moveProp:"pageY",init:{top:"auto",cursor:"ns-resize",bottom:"auto",height:8,width:"100%",left:0,right:0,opacity:0,position:"absolute",border:0,"z-index":99999}}},_=r[c]||{},b={down:{x:null,y:null},delta:{x:null,y:null},track:!1,timer:null};h.bind("mousedown",function(e){b.down.x=e.pageX,b.down.y=e.pageY,b.delta={x:null,y:null},b.target=.25*h["x"==u?"height":"width"]()}),t.bind("mousemove",function(e){p&&(b.delta.x=b.down.x-e.pageX,b.delta.y=b.down.y-e.pageY,clearTimeout(b.timer),b.timer=setTimeout(function(){b.down.x=e.pageX,b.down.y=e.pageY},250))}),t.bind("mouseup touchend",function(){p&&(p=!1,h.trigger("resize-end"),n.remove(),o.removeClass("dragging"))}).bind("mousemove touchmove",function(e){p&&i(e[g[u].moveProp]||e.originalEvent.touches[0][g[u].moveProp])}),n.bind("mousemove touchmove",function(e){p&&i(e[g[u].moveProp]||e.originalEvent.touches[0][g[u].moveProp])}),h.bind("mousedown touchstart",function(e){p=!0,h.trigger("resize-start"),o.append(n).addClass("dragging"),g[u].size=d[g[u].sizeProp](),g[u].currentPos=0,s(),e.preventDefault()}),h.bind("fullsize",function(e,t){void 0===t&&(t="prev");var n=0;"prev"===t&&(n=100),a.css(g[u].cssProp,n+"%"),f.css(g[u].otherCssProp,100-n+"%"),h.hide()}),h.bind("init",function(e,t){h.css(g[u].init),g[u].size=d[g[u].sizeProp](),s(),m=d.offset().top,n.css("cursor","x"==u?"ew-resize":"ns-resize"),"y"==u?(a.css("border-right",0),f.css("border-left",0),f.css("border-top","2px solid #ccc")):a.css("border-top",0),a.is(":hidden")?h.hide():(f.length?a.css("border-"+g[u].cssProp,"1px solid #ccc"):a.css("border-"+g[u].cssProp,"0"),i(void 0!==t?t:_[u]||a.offset()[g[u].cssProp]))}),h.bind("change",function(e,t,n){a.css(g[u].cssProp,"0"),f.css(g[u].otherCssProp,"0"),a.css("border-"+g[u].cssProp,"0"),"y"===t?(a=a.find("> *"),h.appendTo(f),a.appendTo(f),f.css("height","100%"),l.hide(),h.css("margin-left",0),h.css("margin-top",5),h.addClass("vertical"),delete _.x,l.nextAll(":visible:first").trigger("init")):(a=f,f=o,a.appendTo(l),h.insertBefore(l),h.removeClass("vertical"),a.css("border-top",0),a=l,l.show(),h.css("margin-top",0),h.css("margin-left",-4),delete _.y,setTimeout(function(){l.nextAll(":visible:first").trigger("init")},0)),s(),u=t;var o=a;if(a=f,f=o,a.css(g[u].otherCssProp,"0"),f.css(g[u].cssProp,"0"),a.is(":visible")){if("y"===u){var r=a.find(".resize");r.each(function(e){var t=$(this);this===h[0]||t.trigger("init",100/(r-e-1))})}h.trigger("init",n||a.offset()[g[u].cssProp]||g[u].size/2)}}),f.css("width","auto"),f.css("height","auto"),a.data("splitter",h),a.before(h)})},$.fn.splitter.guid=0},function(e,t,n){"use strict";function o(e){return e&&e.__esModule?e:{"default":e}}function r(e){if(Array.isArray(e)){for(var t=0,n=Array(e.length);t<e.length;t++)n[t]=e[t];return n}return Array.from(e)}function i(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function s(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}function a(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}function l(e,t){if(!e||!t)return!1;if(e.length!=t.length)return!1;for(var n=0,o=e.length;n<o;n++)if(e[n]!==t[n])return!1;return!0}function c(e){var t=0,n=0;do isNaN(e.offsetLeft)||(n+=e.offsetLeft),isNaN(e.offsetTop)||(t+=e.offsetTop);while(e=e.offsetParent);return{top:t,left:n}}function d(e){var t="";return e.forEach(function(e){e.selected&&(t+=e.name)}),t}function u(e,t){var n="",o=null;return e.forEach(function(e,r){if(!(t>=0&&r>t))if(e.invalid)o=null;else if(e.selected){o&&(n+=" >");var i="";e.features.forEach(function(e){e.selected&&(i+=e.pattern)}),""===i&&(i="*"),n+=" "+i,o=e}else o=null}),""===n&&(n="*"),n}function f(e,t){var n=[];do{var o=[];if(o.push({name:t.tagName.toLowerCase(),pattern:t.tagName.toLowerCase(),selected:!0}),t.getAttribute("id")&&o.push({name:"#"+t.getAttribute("id"),pattern:"#"+t.getAttribute("id"),selected:!0}),t.classList.length>0)for(var r=0;r<t.classList.length;r++){var i=t.classList[r];o.push({name:"."+i,pattern:"."+i,selected:!0})}for(var s="itemprop",r=0,a=t.attributes;r<a.length;r++)s.indexOf(a[r].nodeName)!=-1&&o.push({name:"["+a[r].nodeName+"="+JSON.stringify(a[r].nodeValue)+"]",pattern:"["+a[r].nodeName+"="+JSON.stringify(a[r].nodeValue)+"]",selected:!0});for(var c=t.parentNode.childNodes,f=t.tagName.toLowerCase(),r=0,h=0;c.length>1&&r<c.length;r++){var p=c[r];if(p===t){f+="["+(h+1)+"]";break}p.tagName==t.tagName&&h++}n.push({tag:t.tagName.toLowerCase(),name:d(o),xpath:f,selected:!0,invalid:"tbody"===t.tagName.toLowerCase(),features:o})}while(t=t.parentElement);n.reverse();var v=e.querySelectorAll(u(n));return n.forEach(function(t,o){if(!t.invalid){var r=e.querySelectorAll(u(n,o));t.features.forEach(function(t,i){t.selected=!1,l(r,e.querySelectorAll(u(n,o)))||(t.selected=!0)}),t.features.every(function(e){return!e.selected})&&(t.features[0].selected=!0),t.name=d(t.features)}}),n.forEach(function(t,o){return t.selected=!1,l(v,e.querySelectorAll(u(n)))?void(t.name=t.tag):void(t.selected=!0)}),n}Object.defineProperty(t,"__esModule",{value:!0});var h=function(){function e(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(t,n,o){return n&&e(t.prototype,n),o&&e(t,o),t}}(),p=n(9),v=o(p),m=function(e){function t(e){i(this,t);var n=s(this,(t.__proto__||Object.getPrototypeOf(t)).call(this));return n.window=e,n.document=e.document,n.document.addEventListener("mouseover",function(e){n.overlay(e.target)}),n.document.addEventListener("click",function(e){e.preventDefault(),e.stopPropagation(),n.emit("selector_helper_click",f(n.document,e.target))}),n}return a(t,e),h(t,[{key:"overlay",value:function(e){var t=this;"string"==typeof e&&(e=this.document.querySelectorAll(e)),e instanceof this.window.Element&&(e=[e]),[].concat(r(this.document.querySelectorAll(".pyspider_overlay"))).forEach(function(e){e.remove()}),[].concat(r(e)).forEach(function(e){var n=c(e),o=t.document.createElement("div");o.className="pyspider_overlay",o.setAttribute("style","z-index: 999999;background-color: rgba(255, 165, 0, 0.3);position: absolute;pointer-events: none;top: "+n.top+"px;left:"+n.left+"px;width: "+e.offsetWidth+"px;height: "+e.offsetHeight+"px;"),t.document.body.appendChild(o)})}},{key:"heightlight",value:function(e){var t=this;"string"==typeof e&&(e=this.document.querySelectorAll(e)),console.log(e),e instanceof this.window.Element&&(e=[e]),[].concat(r(this.document.querySelectorAll(".pyspider_highlight"))).forEach(function(e){e.remove()}),[].concat(r(e)).forEach(function(e){var n=c(e),o=t.document.createElement("div");o.className="pyspider_highlight",o.setAttribute("style","z-index: 888888;border: 2px solid #c00;position: absolute;pointer-events: none;top: "+(n.top-2)+"px;left:"+(n.left-2)+"px;width: "+e.offsetWidth+"px;height: "+e.offsetHeight+"px;"),t.document.body.appendChild(o)})}},{key:"getElementByXpath",value:function(e){return this.document.evaluate(e,this.document,null,this.window.XPathResult.FIRST_ORDERED_NODE_TYPE,null).singleNodeValue}}]),t}(v["default"]);t["default"]=m},function(e,t){"use strict";function n(){this._events=this._events||{},this._maxListeners=this._maxListeners||void 0}function o(e){return"function"==typeof e}function r(e){return"number"==typeof e}function i(e){return"object"===("undefined"==typeof e?"undefined":a(e))&&null!==e}function s(e){return void 0===e}var a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol?"symbol":typeof e};e.exports=n,n.EventEmitter=n,n.prototype._events=void 0,n.prototype._maxListeners=void 0,n.defaultMaxListeners=10,n.prototype.setMaxListeners=function(e){if(!r(e)||e<0||isNaN(e))throw TypeError("n must be a positive number");return this._maxListeners=e,this},n.prototype.emit=function(e){var t,n,r,a,l,c;if(this._events||(this._events={}),"error"===e&&(!this._events.error||i(this._events.error)&&!this._events.error.length)){if(t=arguments[1],t instanceof Error)throw t;var d=new Error('Uncaught, unspecified "error" event. ('+t+")");throw d.context=t,d}if(n=this._events[e],s(n))return!1;if(o(n))switch(arguments.length){case 1:n.call(this);break;case 2:n.call(this,arguments[1]);break;case 3:n.call(this,arguments[1],arguments[2]);break;default:a=Array.prototype.slice.call(arguments,1),n.apply(this,a)}else if(i(n))for(a=Array.prototype.slice.call(arguments,1),c=n.slice(),r=c.length,l=0;l<r;l++)c[l].apply(this,a);return!0},n.prototype.addListener=function(e,t){var r;if(!o(t))throw TypeError("listener must be a function");return this._events||(this._events={}),this._events.newListener&&this.emit("newListener",e,o(t.listener)?t.listener:t),this._events[e]?i(this._events[e])?this._events[e].push(t):this._events[e]=[this._events[e],t]:this._events[e]=t,i(this._events[e])&&!this._events[e].warned&&(r=s(this._maxListeners)?n.defaultMaxListeners:this._maxListeners,r&&r>0&&this._events[e].length>r&&(this._events[e].warned=!0,console.error("(node) warning: possible EventEmitter memory leak detected. %d listeners added. Use emitter.setMaxListeners() to increase limit.",this._events[e].length),"function"==typeof console.trace&&console.trace())),this},n.prototype.on=n.prototype.addListener,n.prototype.once=function(e,t){function n(){this.removeListener(e,n),r||(r=!0,t.apply(this,arguments))}if(!o(t))throw TypeError("listener must be a function");var r=!1;return n.listener=t,this.on(e,n),this},n.prototype.removeListener=function(e,t){var n,r,s,a;if(!o(t))throw TypeError("listener must be a function");if(!this._events||!this._events[e])return this;if(n=this._events[e],s=n.length,r=-1,n===t||o(n.listener)&&n.listener===t)delete this._events[e],this._events.removeListener&&this.emit("removeListener",e,t);else if(i(n)){for(a=s;a-- >0;)if(n[a]===t||n[a].listener&&n[a].listener===t){r=a;break}if(r<0)return this;1===n.length?(n.length=0,delete this._events[e]):n.splice(r,1),this._events.removeListener&&this.emit("removeListener",e,t)}return this},n.prototype.removeAllListeners=function(e){var t,n;if(!this._events)return this;if(!this._events.removeListener)return 0===arguments.length?this._events={}:this._events[e]&&delete this._events[e],this;if(0===arguments.length){for(t in this._events)"removeListener"!==t&&this.removeAllListeners(t);return this.removeAllListeners("removeListener"),this._events={},this}if(n=this._events[e],o(n))this.removeListener(e,n);else if(n)for(;n.length;)this.removeListener(e,n[n.length-1]);return delete this._events[e],this},n.prototype.listeners=function(e){var t;return t=this._events&&this._events[e]?o(this._events[e])?[this._events[e]]:this._events[e].slice():[]},n.prototype.listenerCount=function(e){if(this._events){var t=this._events[e];if(o(t))return 1;if(t)return t.length}return 0},n.listenerCount=function(e,t){return e.listenerCount(t)}}]);
//# sourceMappingURL=debug.min.js.map