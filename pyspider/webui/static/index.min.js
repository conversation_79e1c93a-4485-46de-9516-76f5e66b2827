!function(t){function e(r){if(a[r])return a[r].exports;var n=a[r]={exports:{},id:r,loaded:!1};return t[r].call(n.exports,n,n.exports,e),n.loaded=!0,n.exports}var a={};return e.m=t,e.c=a,e.p="",e(0)}({0:function(t,e,a){"use strict";a(10),$(function(){function t(t){$(".project-group>span").editable({name:"group",pk:function(t){return $(this).parents("tr").data("name")},emptytext:"[group]",placement:"right",url:"/update",success:function(e,a){var r=$(this).parents("tr").data("name");t.projects[r].group=a,$(this).attr("style","")}}),$(".project-status>span").editable({type:"select",name:"status",source:[{value:"TODO",text:"TODO"},{value:"STOP",text:"STOP"},{value:"CHECKING",text:"CHECKING"},{value:"DEBUG",text:"DEBUG"},{value:"RUNNING",text:"RUNNING"}],pk:function(t){return $(this).parents("tr").data("name")},emptytext:"[status]",placement:"right",url:"/update",success:function(e,a){var r=$(this).parents("tr").data("name");t.projects[r].status=a,$(this).removeClass("status-"+$(this).attr("data-value")).addClass("status-"+a).attr("data-value",a).attr("style","")}}),$(".project-rate>span").editable({name:"rate",pk:function(t){return $(this).parents("tr").data("name")},validate:function(t){var e=t.split("/");return 2!=e.length?"format error: rate/burst":$.isNumeric(e[0])&&$.isNumeric(e[1])?void 0:"format error: rate/burst"},highlight:!1,emptytext:"0/0",placement:"right",url:"/update",success:function(e,a){var r=$(this).parents("tr").data("name"),n=a.split("/");t.projects[r].rate=parseFloat(n[0]),t.projects[r].burst=parseFloat(n[1]),$(this).attr("style","")}})}function e(){Sortable.getColumnType=function(t,e){var a=$($(t).find("th").get(e)).data("type");return"num"==a?Sortable.types.numeric:"date"==a?Sortable.types.date:Sortable.types.alpha},$("table.projects").attr("data-sortable",!0),Sortable.init()}function a(){$.get("/counter",function(t){for(var e in t){var a=t[e];if(void 0!==s.projects[e]){var r="5m,1h,1d,all".split(","),n=!0,o=!1,i=void 0;try{for(var u,c=r[Symbol.iterator]();!(n=(u=c.next()).done);n=!0){var l=u.value,p=a[l];if(void 0!==p){var d=p.pending||0,f=p.success||0,m=p.retry||0,v=p.failed||0,h=p.task||d+f+m+v;p.task=h,p.title=""+l+" of "+h+" tasks:\n"+("all"==l?"pending("+(d/h*100).toFixed(1)+"%): \t"+d+"\n":"new("+(d/h*100).toFixed(1)+"%): \t\t"+d+"\n")+"success("+(f/h*100).toFixed(1)+"%): \t"+f+"\nretry("+(m/h*100).toFixed(1)+"%): \t"+m+"\nfailed("+(v/h*100).toFixed(1)+"%): \t"+v}}}catch($){o=!0,i=$}finally{try{!n&&c["return"]&&c["return"]()}finally{if(o)throw i}}s.projects[e].paused=a.paused,s.projects[e].time=a["5m_time"],s.projects[e].progress=a}}})}function r(){$.get("/queues",function(t){$(".queue_value").each(function(e,a){var r=$(a).attr("title");void 0!==t[r]?$(a).text(t[r]):$(a).text("???")})})}$("#create-project-modal form").on("submit",function(t){var e=$(this),a=e.find("[name=project-name]").val();return 0==a.length||a.search(/[^\w]/)!=-1?(e.find("[name=project-name]").parents(".form-group").addClass("has-error"),e.find("[name=project-name] ~ .help-block").show(),!1):(e.find("[name=script-mode]:checked").val(),e.attr("action","/debug/"+a),!0)});var n={};projects.forEach(function(t){t.paused=!1,t.time={},t.progress={},n[t.name]=t});var s=new Vue({el:".projects",data:{projects:n},ready:function(){t(this),e(this),a(),window.setInterval(a,15e3),r(),window.setInterval(r,15e3)},methods:{project_run:function(t,e){$("#need-set-status-alert").hide(),"RUNNING"!=t.status&&"DEBUG"!=t.status&&$("#need-set-status-alert").show();var a=e.target;$(a).addClass("btn-warning"),$.ajax({type:"POST",url:"/run",data:{project:t.name},success:function(t){$(a).removeClass("btn-warning"),t.result||$(a).addClass("btn-danger")},error:function(){$(a).removeClass("btn-warning").addClass("btn-danger")}})}}})})},10:function(t,e){}});
//# sourceMappingURL=index.min.js.map