<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>pyspider WebUI (CDN版)</title>
    
    <!-- Vue 3 CDN -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    
    <!-- Vuetify CDN -->
    <link href="https://cdn.jsdelivr.net/npm/vuetify@3.4.0/dist/vuetify.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/vuetify@3.4.0/dist/vuetify.min.js"></script>
    
    <!-- Material Design Icons -->
    <link href="https://cdn.jsdelivr.net/npm/@mdi/font@7.0.96/css/materialdesignicons.min.css" rel="stylesheet">
    
    <!-- Pinia CDN -->
    <script src="https://unpkg.com/pinia@2.1.7/dist/pinia.iife.js"></script>
    
    <style>
        .project-card {
            margin: 16px;
            transition: transform 0.2s;
        }
        .project-card:hover {
            transform: translateY(-2px);
        }
        .status-chip {
            margin: 4px;
        }
    </style>
</head>
<body>
    <div id="app">
        <v-app>
            <v-app-bar color="primary" dark>
                <v-app-bar-title>pyspider WebUI</v-app-bar-title>
                <v-spacer></v-spacer>
                <v-btn icon @click="refreshData">
                    <v-icon>mdi-refresh</v-icon>
                </v-btn>
            </v-app-bar>
            
            <v-main>
                <v-container>
                    <!-- プロジェクト一覧 -->
                    <v-row>
                        <v-col cols="12">
                            <h2>プロジェクト一覧</h2>
                        </v-col>
                    </v-row>
                    
                    <v-row>
                        <v-col 
                            v-for="project in projects" 
                            :key="project.name"
                            cols="12" 
                            md="6" 
                            lg="4"
                        >
                            <v-card class="project-card">
                                <v-card-title>{{ project.name }}</v-card-title>
                                <v-card-subtitle>{{ project.group || 'デフォルト' }}</v-card-subtitle>
                                
                                <v-card-text>
                                    <v-chip 
                                        :color="getStatusColor(project.status)"
                                        class="status-chip"
                                        small
                                    >
                                        {{ project.status }}
                                    </v-chip>
                                    
                                    <div class="mt-2">
                                        <small>Rate: {{ project.rate || '1/s' }}</small><br>
                                        <small>Burst: {{ project.burst || '3' }}</small>
                                    </div>
                                    
                                    <!-- プログレスバー -->
                                    <div class="mt-3">
                                        <div class="d-flex justify-space-between">
                                            <span>進行状況</span>
                                            <span>{{ getProgressText(project) }}</span>
                                        </div>
                                        <v-progress-linear
                                            :value="getProgressValue(project)"
                                            :color="getProgressColor(project)"
                                            height="8"
                                            rounded
                                        ></v-progress-linear>
                                    </div>
                                </v-card-text>
                                
                                <v-card-actions>
                                    <v-btn 
                                        color="primary" 
                                        variant="text"
                                        @click="editProject(project)"
                                    >
                                        編集
                                    </v-btn>
                                    <v-btn 
                                        :color="project.status === 'RUNNING' ? 'error' : 'success'"
                                        variant="text"
                                        @click="toggleProject(project)"
                                        :disabled="project.status === 'CHECKING'"
                                    >
                                        {{ project.status === 'RUNNING' ? '停止' : '開始' }}
                                    </v-btn>
                                </v-card-actions>
                            </v-card>
                        </v-col>
                    </v-row>
                    
                    <!-- 新規プロジェクト作成 -->
                    <v-row>
                        <v-col cols="12">
                            <v-card>
                                <v-card-title>新規プロジェクト作成</v-card-title>
                                <v-card-text>
                                    <v-form @submit.prevent="createProject">
                                        <v-text-field
                                            v-model="newProject.name"
                                            label="プロジェクト名"
                                            required
                                            :rules="[v => !!v || 'プロジェクト名は必須です']"
                                        ></v-text-field>
                                        
                                        <v-text-field
                                            v-model="newProject.group"
                                            label="グループ名"
                                        ></v-text-field>
                                        
                                        <v-textarea
                                            v-model="newProject.script"
                                            label="スクリプト"
                                            rows="10"
                                            placeholder="#!/usr/bin/env python
# -*- encoding: utf-8 -*-

from pyspider.libs.base_handler import *

class Handler(BaseHandler):
    crawl_config = {}

    @every(minutes=24 * 60)
    def on_start(self):
        self.crawl('http://scrapy.org/', callback=self.index_page)

    @config(age=10 * 24 * 60 * 60)
    def index_page(self, response):
        for each in response.doc('a[href^=\"http\"]').items():
            self.crawl(each.attr.href, callback=self.detail_page)

    @config(priority=2)
    def detail_page(self, response):
        return {
            'url': response.url,
            'title': response.doc('title').text(),
        }"
                                        ></v-textarea>
                                        
                                        <v-btn 
                                            type="submit" 
                                            color="primary"
                                            :loading="creating"
                                        >
                                            作成
                                        </v-btn>
                                    </v-form>
                                </v-card-text>
                            </v-card>
                        </v-col>
                    </v-row>
                </v-container>
            </v-main>
        </v-app>
    </div>

    <script>
        const { createApp } = Vue;
        const { createVuetify } = Vuetify;
        const { createPinia, defineStore } = Pinia;

        // Vuetify設定
        const vuetify = createVuetify({
            theme: {
                defaultTheme: 'light'
            }
        });

        // Pinia Store
        const useProjectStore = defineStore('project', {
            state: () => ({
                projects: [],
                loading: false
            }),
            
            actions: {
                async fetchProjects() {
                    this.loading = true;
                    try {
                        const response = await fetch('/api/v2/projects');
                        const data = await response.json();
                        this.projects = data.projects || [];
                    } catch (error) {
                        console.error('プロジェクト取得エラー:', error);
                        // モックデータ
                        this.projects = [
                            {
                                name: 'example_project',
                                group: 'default',
                                status: 'RUNNING',
                                rate: '1/s',
                                burst: '3',
                                pending: 10,
                                active: 2,
                                success: 150,
                                error: 1
                            }
                        ];
                    } finally {
                        this.loading = false;
                    }
                },
                
                async toggleProject(project) {
                    const action = project.status === 'RUNNING' ? 'stop' : 'start';
                    try {
                        const response = await fetch(`/api/v2/projects/${project.name}/${action}`, {
                            method: 'POST'
                        });
                        if (response.ok) {
                            await this.fetchProjects();
                        }
                    } catch (error) {
                        console.error('プロジェクト操作エラー:', error);
                    }
                },
                
                async createProject(projectData) {
                    try {
                        const response = await fetch('/api/v2/projects', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify(projectData)
                        });
                        if (response.ok) {
                            await this.fetchProjects();
                            return true;
                        }
                    } catch (error) {
                        console.error('プロジェクト作成エラー:', error);
                    }
                    return false;
                }
            }
        });

        // Vue アプリケーション
        const app = createApp({
            setup() {
                const store = useProjectStore();
                
                const newProject = Vue.ref({
                    name: '',
                    group: '',
                    script: ''
                });
                
                const creating = Vue.ref(false);
                
                // メソッド
                const refreshData = () => {
                    store.fetchProjects();
                };
                
                const getStatusColor = (status) => {
                    const colors = {
                        'RUNNING': 'success',
                        'STOPPED': 'error',
                        'CHECKING': 'warning',
                        'DEBUG': 'info'
                    };
                    return colors[status] || 'grey';
                };
                
                const getProgressValue = (project) => {
                    const total = (project.pending || 0) + (project.active || 0) + 
                                 (project.success || 0) + (project.error || 0);
                    if (total === 0) return 0;
                    return ((project.success || 0) / total) * 100;
                };
                
                const getProgressColor = (project) => {
                    const errorRate = (project.error || 0) / 
                        ((project.success || 0) + (project.error || 0) || 1);
                    if (errorRate > 0.1) return 'error';
                    if (errorRate > 0.05) return 'warning';
                    return 'success';
                };
                
                const getProgressText = (project) => {
                    return `${project.success || 0}/${(project.success || 0) + (project.error || 0)}`;
                };
                
                const editProject = (project) => {
                    window.open(`/debug/${project.name}`, '_blank');
                };
                
                const toggleProject = (project) => {
                    store.toggleProject(project);
                };
                
                const createProject = async () => {
                    if (!newProject.value.name) return;
                    
                    creating.value = true;
                    const success = await store.createProject(newProject.value);
                    if (success) {
                        newProject.value = { name: '', group: '', script: '' };
                    }
                    creating.value = false;
                };
                
                // 初期化
                Vue.onMounted(() => {
                    store.fetchProjects();
                });
                
                return {
                    projects: Vue.computed(() => store.projects),
                    newProject,
                    creating,
                    refreshData,
                    getStatusColor,
                    getProgressValue,
                    getProgressColor,
                    getProgressText,
                    editProject,
                    toggleProject,
                    createProject
                };
            }
        });

        // Pinia使用
        app.use(createPinia());
        app.use(vuetify);
        app.mount('#app');
    </script>
</body>
</html>
