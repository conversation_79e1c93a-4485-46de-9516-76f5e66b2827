<!DOCTYPE html>
<html lang="ja">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>pyspider WebUI</title>
  
  <!-- Material Design Icons -->
  <link href="https://cdn.jsdelivr.net/npm/@mdi/font@7.0.96/css/materialdesignicons.min.css" rel="stylesheet">
  
  <!-- Vuetify CSS -->
  <link href="https://cdn.jsdelivr.net/npm/vuetify@3.4.0/dist/vuetify.min.css" rel="stylesheet">
  
  <style>
    body {
      margin: 0;
      font-family: 'Roboto', sans-serif;
    }
    
    .project-card {
      margin: 16px 0;
      transition: transform 0.2s ease;
    }
    
    .project-card:hover {
      transform: translateY(-2px);
    }
    
    .status-chip {
      margin: 4px;
    }
    
    .progress-container {
      margin: 8px 0;
    }
    
    .progress-bar {
      height: 8px;
      border-radius: 4px;
      overflow: hidden;
      background-color: #e0e0e0;
      position: relative;
    }
    
    .progress-segment {
      height: 100%;
      float: left;
      transition: width 0.3s ease;
    }
    
    .progress-pending { background-color: #ff9800; }
    .progress-success { background-color: #4caf50; }
    .progress-retry { background-color: #ffeb3b; }
    .progress-failed { background-color: #f44336; }
    
    .loading-overlay {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(255, 255, 255, 0.8);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 9999;
    }
  </style>
</head>
<body>
  <div id="app">
    <!-- ローディング表示 -->
    <div v-if="loading" class="loading-overlay">
      <v-progress-circular
        indeterminate
        color="primary"
        size="64"
      ></v-progress-circular>
    </div>
    
    <v-app>
      <!-- アプリバー -->
      <v-app-bar color="primary" dark>
        <v-app-bar-title>
          <v-icon class="mr-2">mdi-spider</v-icon>
          pyspider WebUI
        </v-app-bar-title>
        
        <v-spacer></v-spacer>
        
        <v-btn icon @click="refreshData" :loading="refreshing">
          <v-icon>mdi-refresh</v-icon>
        </v-btn>
        
        <v-btn icon @click="toggleTheme">
          <v-icon>{{ isDark ? 'mdi-brightness-7' : 'mdi-brightness-4' }}</v-icon>
        </v-btn>
      </v-app-bar>
      
      <!-- メインコンテンツ -->
      <v-main>
        <v-container fluid>
          <!-- プロジェクト統計 -->
          <v-row class="mb-4">
            <v-col cols="12" md="3">
              <v-card>
                <v-card-text>
                  <div class="text-h6">総プロジェクト数</div>
                  <div class="text-h4 primary--text">{{ projects.length }}</div>
                </v-card-text>
              </v-card>
            </v-col>
            <v-col cols="12" md="3">
              <v-card>
                <v-card-text>
                  <div class="text-h6">実行中</div>
                  <div class="text-h4 success--text">{{ runningProjects }}</div>
                </v-card-text>
              </v-card>
            </v-col>
            <v-col cols="12" md="3">
              <v-card>
                <v-card-text>
                  <div class="text-h6">停止中</div>
                  <div class="text-h4 error--text">{{ stoppedProjects }}</div>
                </v-card-text>
              </v-card>
            </v-col>
            <v-col cols="12" md="3">
              <v-card>
                <v-card-text>
                  <div class="text-h6">デバッグ中</div>
                  <div class="text-h4 warning--text">{{ debugProjects }}</div>
                </v-card-text>
              </v-card>
            </v-col>
          </v-row>
          
          <!-- プロジェクト一覧 -->
          <v-row>
            <v-col cols="12">
              <v-card>
                <v-card-title class="d-flex align-center">
                  <v-icon class="mr-2">mdi-folder-multiple</v-icon>
                  プロジェクト一覧
                  <v-spacer></v-spacer>
                  <v-btn color="primary" @click="showCreateDialog = true">
                    <v-icon class="mr-2">mdi-plus</v-icon>
                    新規作成
                  </v-btn>
                </v-card-title>
                
                <v-card-text>
                  <v-data-table
                    :headers="headers"
                    :items="projects"
                    :loading="loading"
                    class="elevation-1"
                  >
                    <!-- プロジェクト名 -->
                    <template v-slot:item.name="{ item }">
                      <v-chip
                        :color="getStatusColor(item.status)"
                        text-color="white"
                        small
                        class="mr-2"
                      >
                        {{ item.status }}
                      </v-chip>
                      <strong>{{ item.name }}</strong>
                    </template>
                    
                    <!-- グループ -->
                    <template v-slot:item.group="{ item }">
                      <v-chip variant="outlined" small>
                        {{ item.group || 'default' }}
                      </v-chip>
                    </template>
                    
                    <!-- レート/バースト -->
                    <template v-slot:item.rate="{ item }">
                      {{ item.rate || '1' }}/{{ item.burst || '3' }}
                    </template>
                    
                    <!-- 進行状況 -->
                    <template v-slot:item.progress="{ item }">
                      <div class="progress-container">
                        <div class="progress-bar">
                          <div 
                            class="progress-segment progress-pending"
                            :style="{ width: getProgressWidth(item, 'pending') + '%' }"
                          ></div>
                          <div 
                            class="progress-segment progress-success"
                            :style="{ width: getProgressWidth(item, 'success') + '%' }"
                          ></div>
                          <div 
                            class="progress-segment progress-retry"
                            :style="{ width: getProgressWidth(item, 'retry') + '%' }"
                          ></div>
                          <div 
                            class="progress-segment progress-failed"
                            :style="{ width: getProgressWidth(item, 'failed') + '%' }"
                          ></div>
                        </div>
                        <div class="text-caption mt-1">
                          P:{{ item.pending || 0 }} 
                          S:{{ item.success || 0 }} 
                          R:{{ item.retry || 0 }} 
                          F:{{ item.failed || 0 }}
                        </div>
                      </div>
                    </template>
                    
                    <!-- アクション -->
                    <template v-slot:item.actions="{ item }">
                      <v-btn
                        :color="item.status === 'RUNNING' ? 'error' : 'success'"
                        size="small"
                        @click="toggleProject(item)"
                        :loading="item.loading"
                        class="mr-2"
                      >
                        {{ item.status === 'RUNNING' ? '停止' : '開始' }}
                      </v-btn>
                      
                      <v-btn
                        color="primary"
                        size="small"
                        @click="editProject(item)"
                      >
                        編集
                      </v-btn>
                    </template>
                  </v-data-table>
                </v-card-text>
              </v-card>
            </v-col>
          </v-row>
        </v-container>
      </v-main>
      
      <!-- プロジェクト作成ダイアログ -->
      <v-dialog v-model="showCreateDialog" max-width="800px">
        <v-card>
          <v-card-title>新規プロジェクト作成</v-card-title>
          <v-card-text>
            <v-form ref="createForm" v-model="createFormValid">
              <v-text-field
                v-model="newProject.name"
                label="プロジェクト名"
                :rules="nameRules"
                required
              ></v-text-field>
              
              <v-text-field
                v-model="newProject.group"
                label="グループ名"
                placeholder="default"
              ></v-text-field>
              
              <v-textarea
                v-model="newProject.script"
                label="スクリプト"
                rows="15"
                :placeholder="defaultScript"
              ></v-textarea>
            </v-form>
          </v-card-text>
          <v-card-actions>
            <v-spacer></v-spacer>
            <v-btn @click="showCreateDialog = false">キャンセル</v-btn>
            <v-btn 
              color="primary" 
              @click="createProject"
              :loading="creating"
              :disabled="!createFormValid"
            >
              作成
            </v-btn>
          </v-card-actions>
        </v-card>
      </v-dialog>
    </v-app>
  </div>

  <!-- Vue.js 3 -->
  <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
  <!-- Vuetify -->
  <script src="https://cdn.jsdelivr.net/npm/vuetify@3.4.0/dist/vuetify.min.js"></script>
  
  <script type="module" src="/src/index.vue3.js"></script>
</body>
</html>
