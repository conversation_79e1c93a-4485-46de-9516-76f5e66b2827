#!/usr/bin/env python
# -*- encoding: utf-8 -*-
# vim: set et sw=4 ts=4 sts=4 ff=unix fenc=utf8:
# Author: PySpiderNX2 Team
# Created on 2025-01-XX

import os
import hmac
import hashlib
import logging
import functools
from datetime import datetime, timedelta
from flask import request, abort, session, current_app

logger = logging.getLogger(__name__)

class CSRFProtection:
    """CSRF保護クラス"""

    def __init__(self, app=None):
        self.app = app
        if app:
            self.init_app(app)

    def init_app(self, app):
        """Flaskアプリケーションを初期化"""
        app.config.setdefault('CSRF_SECRET_KEY', os.urandom(32))
        app.config.setdefault('CSRF_TOKEN_EXPIRY', 3600)  # 1時間

        # CSRFトークン生成関数をテンプレートで使用可能にする
        app.jinja_env.globals['csrf_token'] = self.generate_token

    def generate_token(self):
        """CSRFトークンを生成"""
        if 'csrf_token' not in session or self._is_token_expired():
            session['csrf_token'] = self._create_token()
            session['csrf_token_time'] = datetime.now().timestamp()

        return session['csrf_token']

    def _create_token(self):
        """新しいCSRFトークンを作成"""
        secret_key = current_app.config['CSRF_SECRET_KEY']
        timestamp = str(datetime.now().timestamp())
        random_data = os.urandom(16).hex()

        # HMAC-SHA256でトークンを生成
        message = f"{timestamp}:{random_data}"
        signature = hmac.new(
            secret_key,
            message.encode(),
            hashlib.sha256
        ).hexdigest()

        return f"{message}:{signature}"

    def _is_token_expired(self):
        """トークンが期限切れかチェック"""
        if 'csrf_token_time' not in session:
            return True

        token_time = session['csrf_token_time']
        expiry_time = current_app.config['CSRF_TOKEN_EXPIRY']

        return (datetime.now().timestamp() - token_time) > expiry_time

    def validate_token(self, token):
        """CSRFトークンを検証"""
        if not token:
            return False

        try:
            parts = token.split(':')
            if len(parts) != 3:
                return False

            timestamp, random_data, signature = parts

            # 署名を検証
            secret_key = current_app.config['CSRF_SECRET_KEY']
            message = f"{timestamp}:{random_data}"
            expected_signature = hmac.new(
                secret_key,
                message.encode(),
                hashlib.sha256
            ).hexdigest()

            if not hmac.compare_digest(signature, expected_signature):
                return False

            # タイムスタンプを検証
            token_timestamp = float(timestamp)
            expiry_time = current_app.config['CSRF_TOKEN_EXPIRY']

            if (datetime.now().timestamp() - token_timestamp) > expiry_time:
                return False

            return True

        except (ValueError, TypeError) as e:
            logger.warning(f"CSRF token validation error: {e}")
            return False

    def protect(self, func):
        """CSRF保護デコレータ"""
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            if request.method in ['POST', 'PUT', 'DELETE', 'PATCH']:
                # CSRFトークンを取得
                token = (
                    request.form.get('csrf_token') or
                    request.headers.get('X-CSRF-Token') or
                    request.json.get('csrf_token') if request.json else None
                )

                # セッションのトークンと比較
                session_token = session.get('csrf_token')

                if not token or not session_token or not self.validate_token(token):
                    logger.warning(f"CSRF token validation failed for {request.endpoint}")
                    abort(403, description="CSRF token validation failed")

            return func(*args, **kwargs)
        return wrapper

# グローバルインスタンス
csrf = CSRFProtection()

def csrf_protect(func):
    """CSRF保護デコレータ（関数用）"""
    return csrf.protect(func)

def exempt_csrf(func):
    """CSRF保護を免除するデコレータ"""
    func._csrf_exempt = True
    return func

def init_csrf_protection(app):
    """CSRF保護を初期化"""
    csrf.init_app(app)

    @app.before_request
    def check_csrf():
        """リクエスト前にCSRF保護をチェック"""
        # API エンドポイントや特定のパスは除外
        if (request.endpoint and
            (request.endpoint.startswith('api.') or
             request.path.startswith('/api/') or
             request.path.startswith('/api/v2/') or
             hasattr(app.view_functions.get(request.endpoint), '_csrf_exempt'))):
            return

        # GETリクエストは除外
        if request.method in ['GET', 'HEAD', 'OPTIONS']:
            return

        # CSRFトークンを検証
        token = (
            request.form.get('csrf_token') or
            request.headers.get('X-CSRF-Token') or
            request.json.get('csrf_token') if request.json else None
        )

        session_token = session.get('csrf_token')

        if not token or not session_token or not csrf.validate_token(token):
            logger.warning(f"CSRF protection triggered for {request.path}")
            abort(403, description="CSRF token validation failed")

def get_csrf_token():
    """現在のCSRFトークンを取得"""
    return csrf.generate_token()
