#!/usr/bin/env python
# -*- encoding: utf-8 -*-
# vim: set et sw=4 ts=4 sts=4 ff=unix fenc=utf8:
# Author: Binux<<EMAIL>>
#         http://binux.me
# Created on 2014-02-22 23:17:13

import os
import sys
import logging
import builtins
logger = logging.getLogger("webui")


from urllib.parse import urljoin
# Python 3.13 compatibility: no need for six.reraise
from flask import Flask
from flask_cors import CORS
from pyspider.fetcher import tornado_fetcher
from pyspider.libs.memory_optimizer import memory_optimizer

if os.name == 'nt':
    import mimetypes
    mimetypes.add_type("text/css", ".css", True)


class QuitableFlask(Flask):
    """Add quit() method to Flask object"""

    @property
    def logger(self):
        return logger

    def run(self, host=None, port=None, debug=None, **options):
        import tornado.wsgi
        import tornado.ioloop
        import tornado.httpserver
        import tornado.web

        if host is None:
            host = '0.0.0.0'
        if port is None:
            server_name = self.config['SERVER_NAME']
            if server_name and ':' in server_name:
                port = int(server_name.rsplit(':', 1)[1])
            else:
                port = 5000
        if debug is not None:
            self.debug = bool(debug)

        hostname = host
        port = port
        application = self
        use_reloader = self.debug
        use_debugger = self.debug

        if use_debugger:
            from werkzeug.debug import DebuggedApplication
            application = DebuggedApplication(application, True)

        # WebDAV mode has been removed
        logger.info('WebDAV mode has been removed')

        container = tornado.wsgi.WSGIContainer(application)
        self.http_server = tornado.httpserver.HTTPServer(container)
        self.http_server.listen(port, hostname)
        if use_reloader:
            from tornado import autoreload
            autoreload.start()

        self.logger.info('webui running on %s:%s', hostname, port)
        self.ioloop = tornado.ioloop.IOLoop.current()
        self.ioloop.start()

    def quit(self):
        if hasattr(self, 'ioloop'):
            self.ioloop.add_callback(self.http_server.stop)
            self.ioloop.add_callback(self.ioloop.stop)

        # Stop memory optimizer
        if 'memory_optimizer' in globals():
            memory_optimizer.stop()

        # Stop enhanced features
        if hasattr(self, 'enhanced_features'):
            try:
                self.enhanced_features.shutdown()
                self.logger.info('Enhanced features stopped')
            except Exception as e:
                self.logger.error(f'Error stopping enhanced features: {e}')

        self.logger.info('webui exiting...')


app = QuitableFlask('webui',
                    static_folder=os.path.join(os.path.dirname(__file__), 'static'),
                    template_folder=os.path.join(os.path.dirname(__file__), 'templates'))
# セキュリティ強化
try:
    from pyspider.config.unified_config import get_config
    config = get_config()

    # セッションキーの設定
    session_key = config.get('security.session_secret_key')
    if not session_key:
        session_key = os.urandom(32)
        config.set('security.session_secret_key', session_key.hex())
        config.save()
    else:
        session_key = bytes.fromhex(session_key)

    app.secret_key = session_key

    # CSRF保護の初期化（設定で有効な場合のみ）
    webui_config = config.get_component_config('webui')
    if webui_config.get('csrf_protection', True):
        from pyspider.webui.csrf_protection import init_csrf_protection
        init_csrf_protection(app)

except ImportError:
    app.secret_key = os.urandom(24)
app.jinja_env.line_statement_prefix = '#'

# Initialize memory optimizer
memory_optimizer.start()

# Vue.jsテンプレートとの競合を避けるために、index-v2.htmlテンプレート用に特別なJinja2環境を作成
from jinja2 import Environment, FileSystemLoader
vue_jinja_env = Environment(
    loader=FileSystemLoader(os.path.join(os.path.dirname(__file__), 'templates')),
    variable_start_string='[[',
    variable_end_string=']]'
)
app.jinja_env.globals.update(builtins.__dict__)

# デバッグモードを有効化
app.debug = True

# Enable CORS with secure settings
CORS(app, resources={
    r"/api/*": {
        "origins": ["http://localhost:3000", "http://localhost:3001", "http://127.0.0.1:3000", "http://127.0.0.1:3001"],
        "methods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
        "allow_headers": ["Content-Type", "Authorization", "X-CSRF-Token"],
        "supports_credentials": True
    },
    r"/dashboard-active-tasks": {
        "origins": ["http://localhost:3000", "http://localhost:3001", "http://127.0.0.1:3000", "http://127.0.0.1:3001"],
        "methods": ["GET", "POST", "OPTIONS"],
        "allow_headers": ["Content-Type", "Authorization", "X-CSRF-Token"],
        "supports_credentials": True
    },
    r"/counter": {
        "origins": ["http://localhost:3000", "http://localhost:3001", "http://127.0.0.1:3000", "http://127.0.0.1:3001"],
        "methods": ["GET", "POST", "OPTIONS"],
        "allow_headers": ["Content-Type", "Authorization", "X-CSRF-Token"],
        "supports_credentials": True
    },
    r"/*": {
        "origins": ["http://localhost:3000", "http://localhost:3001", "http://127.0.0.1:3000", "http://127.0.0.1:3001"],
        "methods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
        "allow_headers": ["Content-Type", "Authorization", "X-CSRF-Token"],
        "supports_credentials": True
    }
})
logger.info("CORS enabled with secure settings")

# 統一設定から認証設定を読み込み
try:
    from pyspider.config.unified_config import get_config
    unified_config = get_config()
    webui_config = unified_config.get_component_config('webui')

    auth_config = {
        'need_auth': webui_config.get('need-auth', True),
        'webui_username': webui_config.get('username', 'admin'),
        'webui_password': webui_config.get('password', 'PySpider2024!SecurePass#'),
    }
    logger.info(f"Authentication config loaded: need_auth={auth_config['need_auth']}, username={auth_config['webui_username']}")
except Exception as e:
    logger.warning(f"Failed to load unified config: {e}")
    auth_config = {
        'need_auth': True,
        'webui_username': 'admin',
        'webui_password': 'PySpider2024!SecurePass#',
    }

app.config.update({
    'fetch': lambda x: tornado_fetcher.Fetcher(None, None, async_mode=False).fetch(x),
    'taskdb': None,
    'projectdb': None,
    'scheduler_rpc': None,
    'queues': dict(),
    'process_time_limit': 30,
    **auth_config,  # 認証設定を展開
})


def cdn_url_handler(error, endpoint, kwargs):
    if endpoint == 'cdn':
        path = kwargs.pop('path')
        # cdn = app.config.get('cdn', 'http://cdn.staticfile.org/')
        # cdn = app.config.get('cdn', '//cdnjs.cloudflare.com/ajax/libs/')
        cdn = app.config.get('cdn', '//cdnjscn.b0.upaiyun.com/libs/')
        return urljoin(cdn, path)
    else:
        exc_type, exc_value, tb = sys.exc_info()
        if exc_value is error:
            # Python 3.13 compatibility: use raise with traceback
            raise exc_value.with_traceback(tb)
        else:
            raise error
app.handle_url_build_error = cdn_url_handler



# Import views
import pyspider.webui.index
import pyspider.webui.debug
import pyspider.webui.result
import pyspider.webui.task
import pyspider.webui.login
import pyspider.webui.products
import pyspider.webui.selector_tester
import pyspider.webui.api
# 古いAPI v2実装は無効化（新しい実装と競合するため）
# import pyspider.webui.api_v2  # APIv2エンドポイントをインポート（古い実装）
# 新しいAPI v2実装をインポート
import pyspider.webui.api_v2.projects
import pyspider.webui.api_v2.tasks
import pyspider.webui.api_v2.results
import pyspider.webui.api_v2.file_output
import pyspider.webui.components_status
import pyspider.webui.metrics
import pyspider.webui.alerts  # アラートダッシュボードをインポート
import pyspider.webui.swagger  # Swagger UIをインポート

# Enhanced API v2実装をインポートして登録
try:
    from pyspider.webui.api_v2_enhanced import api_v2_enhanced
    app.register_blueprint(api_v2_enhanced, url_prefix='/api/v2/enhanced')
    logger.info("✅ Enhanced API v2エンドポイントを登録しました")
except ImportError as e:
    logger.warning(f"⚠️ Enhanced API v2のインポートに失敗: {e}")
except Exception as e:
    logger.error(f"❌ Enhanced API v2の登録に失敗: {e}")

# Prometheusエンドポイントをインポートして登録
try:
    from pyspider.webui.prometheus_endpoint import prometheus_bp
    app.register_blueprint(prometheus_bp, url_prefix='/prometheus')
    logger.info("✅ Prometheusエンドポイントを登録しました")
except ImportError as e:
    logger.warning(f"⚠️ Prometheusエンドポイントのインポートに失敗: {e}")
except Exception as e:
    logger.error(f"❌ Prometheusエンドポイントの登録に失敗: {e}")

# Redis状態確認APIを追加
try:
    from pyspider.webui.handlers.redis_status_handler import RedisStatusHandler, RedisControlHandler, RedisMonitorHandler
    from flask import request, jsonify

    @app.route('/api/v2/redis/status', methods=['GET'])
    def redis_status_api():
        """Redis状態確認API"""
        handler = RedisStatusHandler()
        handler.initialize(app.config['scheduler_rpc'], app.config['taskdb'], app.config['projectdb'])
        result = handler.get()
        return jsonify(result)

    @app.route('/api/v2/redis/control', methods=['POST'])
    def redis_control_api():
        """Redis制御API"""
        handler = RedisControlHandler()
        handler.initialize(app.config['scheduler_rpc'], app.config['taskdb'], app.config['projectdb'])
        result = handler.post(request.get_data())
        return jsonify(result)

    @app.route('/api/v2/redis/monitor', methods=['GET'])
    def redis_monitor_api():
        """Redis監視API"""
        handler = RedisMonitorHandler()
        handler.initialize(app.config['scheduler_rpc'], app.config['taskdb'], app.config['projectdb'])
        result = handler.get()
        return jsonify(result)

    logger.info("✅ Redis状態確認APIを登録しました")

except ImportError as e:
    logger.warning(f"⚠️ Redis状態確認APIのインポートに失敗: {e}")
except Exception as e:
    logger.error(f"❌ Redis状態確認APIの登録に失敗: {e}")

# ファイル出力APIを登録
try:
    from pyspider.webui.api_v2.file_output import app as file_output_api_v2
    app.register_blueprint(file_output_api_v2)
    logger.info("✅ ファイル出力APIを登録しました")
except ImportError as e:
    logger.warning(f"⚠️ ファイル出力APIのインポートに失敗: {e}")
except Exception as e:
    logger.error(f"❌ ファイル出力APIの登録に失敗: {e}")
