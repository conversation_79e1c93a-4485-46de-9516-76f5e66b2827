#!/usr/bin/env python
# -*- encoding: utf-8 -*-
# vim: set et sw=4 ts=4 sts=4 ff=unix fenc=utf8:
# Author: PySpider Team
# Created on 2023-05-18 14:00:00

import time
import json
import logging
from flask import request, Response, Blueprint, jsonify, current_app

from pyspider.libs.prometheus_metrics import prometheus_metrics
from pyspider.libs.performance_metrics import performance_metrics
from pyspider.libs.alert_manager import alert_manager
from pyspider.fetcher.enhanced_playwright_fetcher import EnhancedPlaywrightFetcher

logger = logging.getLogger('api_v2_enhanced')

# Create blueprint
api_v2_enhanced = Blueprint('api_v2_enhanced', __name__)

# Initialize enhanced Playwright fetcher
enhanced_playwright_fetcher = None

def get_enhanced_playwright_fetcher():
    """Get or create enhanced Playwright fetcher"""
    global enhanced_playwright_fetcher

    if enhanced_playwright_fetcher is None:
        enhanced_playwright_fetcher = EnhancedPlaywrightFetcher(
            browser_type='chromium',
            headless=True,
            timeout=30,
            max_pages=10
        )

    return enhanced_playwright_fetcher

# Helper function for JSON responses
def json_response(data, status=200):
    """Return JSON response"""
    return Response(
        json.dumps(data, indent=2),
        status=status,
        mimetype='application/json'
    )

# Enhanced Playwright API endpoints
@api_v2_enhanced.route('/fetch', methods=['POST'])
async def fetch():
    """Fetch a URL using enhanced Playwright"""
    try:
        # Get request data
        data = request.get_json()

        if not data:
            return json_response({
                'error': 'No data provided'
            }, 400)

        url = data.get('url')

        if not url:
            return json_response({
                'error': 'URL is required'
            }, 400)

        # Get fetcher
        fetcher = get_enhanced_playwright_fetcher()

        # Fetch URL
        result = await fetcher.fetch(data)

        # Track request
        performance_metrics.track_request(
            url=url,
            status_code=result.get('status_code', 0),
            duration=result.get('time', 0),
            error=result.get('error')
        )

        # Remove screenshot from response if it exists (too large for JSON)
        if 'screenshot' in result:
            result['screenshot_available'] = True
            del result['screenshot']

        return json_response(result)
    except Exception as e:
        logger.error(f"Error fetching URL: {e}")
        return json_response({
            'error': str(e)
        }, 500)

@api_v2_enhanced.route('/execute_actions', methods=['POST'])
async def execute_actions():
    """Execute actions using enhanced Playwright"""
    try:
        # Get request data
        data = request.get_json()

        if not data:
            return json_response({
                'error': 'No data provided'
            }, 400)

        url = data.get('url')
        actions = data.get('actions')

        if not url:
            return json_response({
                'error': 'URL is required'
            }, 400)

        if not actions:
            return json_response({
                'error': 'Actions are required'
            }, 400)

        # Get fetcher
        fetcher = get_enhanced_playwright_fetcher()

        # Get page
        page, is_new_page = await fetcher.get_page()

        try:
            # Navigate to URL
            await page.goto(
                url,
                timeout=data.get('timeout', 30) * 1000,
                wait_until=data.get('wait_until', 'networkidle')
            )

            # Execute actions
            result = await fetcher.execute_actions(page, actions)

            # Get final URL and content
            final_url = page.url
            content = await page.content()

            # Add URL and content to result
            result['url'] = final_url
            result['content'] = content

            return json_response(result)
        finally:
            # Return page to pool
            await fetcher.return_page(page)
    except Exception as e:
        logger.error(f"Error executing actions: {e}")
        return json_response({
            'error': str(e)
        }, 500)

@api_v2_enhanced.route('/extract_data', methods=['POST'])
async def extract_data():
    """Extract data using enhanced Playwright"""
    try:
        # Get request data
        data = request.get_json()

        if not data:
            return json_response({
                'error': 'No data provided'
            }, 400)

        url = data.get('url')
        selectors = data.get('selectors')

        if not url:
            return json_response({
                'error': 'URL is required'
            }, 400)

        if not selectors:
            return json_response({
                'error': 'Selectors are required'
            }, 400)

        # Get fetcher
        fetcher = get_enhanced_playwright_fetcher()

        # Get page
        page, is_new_page = await fetcher.get_page()

        try:
            # Navigate to URL
            await page.goto(
                url,
                timeout=data.get('timeout', 30) * 1000,
                wait_until=data.get('wait_until', 'networkidle')
            )

            # Wait for selector if specified
            if data.get('wait_for'):
                try:
                    await page.wait_for_selector(
                        data.get('wait_for'),
                        timeout=data.get('wait_for_timeout', 10) * 1000
                    )
                except Exception as e:
                    logger.warning(f"Wait for selector failed: {e}")

            # Extract data
            result = await fetcher.extract_data(page, selectors)

            # Add URL to result
            result['url'] = page.url

            return json_response(result)
        finally:
            # Return page to pool
            await fetcher.return_page(page)
    except Exception as e:
        logger.error(f"Error extracting data: {e}")
        return json_response({
            'error': str(e)
        }, 500)

# Performance metrics API endpoints
@api_v2_enhanced.route('/performance/metrics', methods=['GET'])
def get_performance_metrics():
    """Get performance metrics"""
    try:
        # Get metrics
        metrics = performance_metrics.get_all_stats()

        return json_response(metrics)
    except Exception as e:
        logger.error(f"Error getting performance metrics: {e}")
        return json_response({
            'error': str(e)
        }, 500)

@api_v2_enhanced.route('/performance/function_stats', methods=['GET'])
def get_function_stats():
    """Get function stats"""
    try:
        # Get function stats
        stats = performance_metrics.get_function_stats()

        return json_response(stats)
    except Exception as e:
        logger.error(f"Error getting function stats: {e}")
        return json_response({
            'error': str(e)
        }, 500)

@api_v2_enhanced.route('/performance/request_stats', methods=['GET'])
def get_request_stats():
    """Get request stats"""
    try:
        # Get request stats
        stats = performance_metrics.get_request_stats()

        return json_response(stats)
    except Exception as e:
        logger.error(f"Error getting request stats: {e}")
        return json_response({
            'error': str(e)
        }, 500)

@api_v2_enhanced.route('/performance/task_stats', methods=['GET'])
def get_task_stats():
    """Get task stats"""
    try:
        # Get task stats
        stats = performance_metrics.get_task_stats()

        return json_response(stats)
    except Exception as e:
        logger.error(f"Error getting task stats: {e}")
        return json_response({
            'error': str(e)
        }, 500)

@api_v2_enhanced.route('/performance/memory_snapshots', methods=['GET'])
def get_memory_snapshots():
    """Get memory snapshots"""
    try:
        # Get memory snapshots
        snapshots = performance_metrics.get_memory_snapshots()

        return json_response(snapshots)
    except Exception as e:
        logger.error(f"Error getting memory snapshots: {e}")
        return json_response({
            'error': str(e)
        }, 500)

@api_v2_enhanced.route('/performance/take_memory_snapshot', methods=['POST'])
def take_memory_snapshot():
    """Take memory snapshot"""
    try:
        # Take memory snapshot
        snapshot = performance_metrics.take_tracemalloc_snapshot()

        return json_response(snapshot)
    except Exception as e:
        logger.error(f"Error taking memory snapshot: {e}")
        return json_response({
            'error': str(e)
        }, 500)

# Alert manager API endpoints
@api_v2_enhanced.route('/alerts', methods=['GET'])
def get_alerts():
    """Get active alerts"""
    try:
        # Get alerts
        alerts = alert_manager.get_alerts()

        return json_response(alerts)
    except Exception as e:
        logger.error(f"Error getting alerts: {e}")
        return json_response({
            'error': str(e)
        }, 500)

@api_v2_enhanced.route('/alerts/history', methods=['GET'])
def get_alert_history():
    """Get alert history"""
    try:
        # Get alert history
        history = alert_manager.get_alert_history()

        return json_response(history)
    except Exception as e:
        logger.error(f"Error getting alert history: {e}")
        return json_response({
            'error': str(e)
        }, 500)

@api_v2_enhanced.route('/alerts/rules', methods=['GET'])
def get_alert_rules():
    """Get alert rules"""
    try:
        # Get alert rules
        rules = alert_manager.get_rules()

        return json_response(rules)
    except Exception as e:
        logger.error(f"Error getting alert rules: {e}")
        return json_response({
            'error': str(e)
        }, 500)

@api_v2_enhanced.route('/alerts/rules', methods=['POST'])
def add_alert_rule():
    """Add alert rule"""
    try:
        # Get request data
        data = request.get_json()

        if not data:
            return json_response({
                'error': 'No data provided'
            }, 400)

        # Add rule
        success = alert_manager.add_rule(data)

        if success:
            return json_response({
                'success': True,
                'message': f"Added alert rule: {data.get('name')}"
            })
        else:
            return json_response({
                'success': False,
                'error': f"Failed to add alert rule: {data.get('name')}"
            }, 400)
    except Exception as e:
        logger.error(f"Error adding alert rule: {e}")
        return json_response({
            'error': str(e)
        }, 500)

@api_v2_enhanced.route('/alerts/rules/<name>', methods=['DELETE'])
def remove_alert_rule(name):
    """Remove alert rule"""
    try:
        # Remove rule
        success = alert_manager.remove_rule(name)

        if success:
            return json_response({
                'success': True,
                'message': f"Removed alert rule: {name}"
            })
        else:
            return json_response({
                'success': False,
                'error': f"Alert rule not found: {name}"
            }, 404)
    except Exception as e:
        logger.error(f"Error removing alert rule: {e}")
        return json_response({
            'error': str(e)
        }, 500)

@api_v2_enhanced.route('/alerts/channels', methods=['GET'])
def get_alert_channels():
    """Get alert channels"""
    try:
        # Get alert channels
        channels = alert_manager.get_channels()

        return json_response(channels)
    except Exception as e:
        logger.error(f"Error getting alert channels: {e}")
        return json_response({
            'error': str(e)
        }, 500)

@api_v2_enhanced.route('/alerts/channels/<name>', methods=['POST'])
def add_alert_channel(name):
    """Add alert channel"""
    try:
        # Get request data
        data = request.get_json()

        if not data:
            return json_response({
                'error': 'No data provided'
            }, 400)

        # Add channel
        success = alert_manager.add_channel(name, data)

        if success:
            return json_response({
                'success': True,
                'message': f"Added alert channel: {name}"
            })
        else:
            return json_response({
                'success': False,
                'error': f"Failed to add alert channel: {name}"
            }, 400)
    except Exception as e:
        logger.error(f"Error adding alert channel: {e}")
        return json_response({
            'error': str(e)
        }, 500)

@api_v2_enhanced.route('/alerts/channels/<name>', methods=['DELETE'])
def remove_alert_channel(name):
    """Remove alert channel"""
    try:
        # Remove channel
        success = alert_manager.remove_channel(name)

        if success:
            return json_response({
                'success': True,
                'message': f"Removed alert channel: {name}"
            })
        else:
            return json_response({
                'success': False,
                'error': f"Alert channel not found: {name}"
            }, 404)
    except Exception as e:
        logger.error(f"Error removing alert channel: {e}")
        return json_response({
            'error': str(e)
        }, 500)

@api_v2_enhanced.route('/alerts/check', methods=['POST'])
def check_alerts():
    """Check alerts"""
    try:
        # Get request data
        data = request.get_json()

        # Check alerts
        alerts = alert_manager.check_alerts(data)

        return json_response({
            'success': True,
            'alerts': alerts
        })
    except Exception as e:
        logger.error(f"Error checking alerts: {e}")
        return json_response({
            'error': str(e)
        }, 500)

# Blueprint registration is handled in app.py to avoid circular imports
