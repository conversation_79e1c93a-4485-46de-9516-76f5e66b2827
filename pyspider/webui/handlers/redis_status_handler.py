#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
Redis状態確認用WebUIハンドラー
"""

import json
import time
import logging

logger = logging.getLogger(__name__)

class RedisStatusHandler:
    """Redis状態確認ハンドラー"""

    def __init__(self):
        self.scheduler = None
        self.taskdb = None
        self.projectdb = None

    def initialize(self, scheduler, taskdb, projectdb):
        """ハンドラーを初期化"""
        self.scheduler = scheduler
        self.taskdb = taskdb
        self.projectdb = projectdb
    """Redis状態確認ハンドラー"""
    
    def get(self):
        """Redis状態を取得"""
        try:
            # スケジューラからRedis状態を取得
            if hasattr(self.scheduler, 'get_redis_status'):
                redis_status = self.scheduler.get_redis_status()
            else:
                redis_status = {
                    "enabled": False,
                    "status": "not_supported",
                    "message": "スケジューラがRedis状態確認をサポートしていません"
                }

            # 追加情報を取得
            additional_info = self._get_additional_info()

            response = {
                "status": "success",
                "timestamp": time.time(),
                "redis_status": redis_status,
                "additional_info": additional_info
            }

            return response

        except Exception as e:
            logger.error(f"Redis状態確認エラー: {e}")
            return {
                "status": "error",
                "message": f"内部エラー: {str(e)}",
                "timestamp": time.time()
            }
    
    def _get_additional_info(self):
        """追加情報を取得"""
        try:
            # スケジューラカウンター情報
            counter_info = {}
            try:
                counter_result = self.scheduler.counter('5m', 'sum')
                counter_info = {
                    "active_projects": len(counter_result),
                    "projects": list(counter_result.keys()) if counter_result else []
                }
            except Exception as e:
                counter_info = {"error": str(e)}
            
            # システム情報
            import psutil
            system_info = {
                "memory_usage": psutil.virtual_memory().percent,
                "cpu_usage": psutil.cpu_percent(),
                "disk_usage": psutil.disk_usage('/').percent
            }
            
            return {
                "counter_info": counter_info,
                "system_info": system_info,
                "scheduler_info": {
                    "active": hasattr(self.scheduler, 'projects'),
                    "project_count": len(getattr(self.scheduler, 'projects', {}))
                }
            }
            
        except Exception as e:
            logger.warning(f"追加情報取得エラー: {e}")
            return {"error": str(e)}


class RedisControlHandler:
    """Redis制御ハンドラー"""

    def __init__(self):
        self.scheduler = None
        self.taskdb = None
        self.projectdb = None

    def initialize(self, scheduler, taskdb, projectdb):
        """ハンドラーを初期化"""
        self.scheduler = scheduler
        self.taskdb = taskdb
        self.projectdb = projectdb

    def post(self, request_body):
        """Redis制御操作"""
        try:
            data = json.loads(request_body.decode('utf-8'))
            action = data.get('action')

            if not action:
                return {
                    "status": "error",
                    "message": "アクションが指定されていません"
                }

            result = self._execute_action(action, data)

            return {
                "status": "success",
                "action": action,
                "result": result,
                "timestamp": time.time()
            }

        except json.JSONDecodeError:
            return {
                "status": "error",
                "message": "無効なJSONデータです"
            }
        except Exception as e:
            logger.error(f"Redis制御エラー: {e}")
            return {
                "status": "error",
                "message": f"内部エラー: {str(e)}"
            }
    
    def _execute_action(self, action, data):
        """アクションを実行"""
        if action == "force_fallback":
            return self._force_fallback()
        elif action == "check_connection":
            return self._check_connection()
        elif action == "get_queue_status":
            return self._get_queue_status()
        else:
            raise ValueError(f"未知のアクション: {action}")
    
    def _force_fallback(self):
        """強制的にフォールバックモードに切り替え"""
        try:
            if hasattr(self.scheduler, 'redis_manager') and self.scheduler.redis_manager:
                self.scheduler.redis_manager.force_fallback()
                return {
                    "message": "XMLRPCフォールバックモードに切り替えました",
                    "success": True
                }
            else:
                return {
                    "message": "Redis管理機能が利用できません",
                    "success": False
                }
        except Exception as e:
            return {
                "message": f"フォールバック切り替えエラー: {e}",
                "success": False
            }
    
    def _check_connection(self):
        """Redis接続をチェック"""
        try:
            if hasattr(self.scheduler, 'redis_manager') and self.scheduler.redis_manager:
                redis_client = self.scheduler.redis_manager.get_redis_client()
                if redis_client:
                    redis_client.ping()
                    return {
                        "message": "Redis接続正常",
                        "success": True
                    }
                else:
                    return {
                        "message": "Redis接続なし（フォールバックモード）",
                        "success": False
                    }
            else:
                return {
                    "message": "Redis管理機能が利用できません",
                    "success": False
                }
        except Exception as e:
            return {
                "message": f"Redis接続チェックエラー: {e}",
                "success": False
            }
    
    def _get_queue_status(self):
        """キューの状態を取得"""
        try:
            if hasattr(self.scheduler, 'message_queue') and self.scheduler.message_queue:
                # 主要なキューのサイズを確認
                queue_status = {}
                queue_names = ['newtask', 'status', 'scheduler2fetcher']
                
                for queue_name in queue_names:
                    try:
                        size = self.scheduler.message_queue.size(queue_name)
                        queue_status[queue_name] = size
                    except Exception as e:
                        queue_status[queue_name] = f"エラー: {e}"
                
                return {
                    "queue_status": queue_status,
                    "success": True
                }
            else:
                return {
                    "message": "メッセージキュー管理機能が利用できません",
                    "success": False
                }
        except Exception as e:
            return {
                "message": f"キュー状態取得エラー: {e}",
                "success": False
            }


class RedisMonitorHandler:
    """Redis監視ハンドラー（リアルタイム監視用）"""

    def __init__(self):
        self.scheduler = None
        self.taskdb = None
        self.projectdb = None

    def initialize(self, scheduler, taskdb, projectdb):
        """ハンドラーを初期化"""
        self.scheduler = scheduler
        self.taskdb = taskdb
        self.projectdb = projectdb

    def get(self):
        """Redis監視データを取得"""
        try:
            # 過去の状態履歴を取得（実装は簡略化）
            monitoring_data = {
                "current_status": self._get_current_status(),
                "history": self._get_status_history(),
                "alerts": self._get_alerts()
            }

            return {
                "status": "success",
                "monitoring_data": monitoring_data,
                "timestamp": time.time()
            }

        except Exception as e:
            logger.error(f"Redis監視データ取得エラー: {e}")
            return {
                "status": "error",
                "message": f"内部エラー: {str(e)}"
            }
    
    def _get_current_status(self):
        """現在の状態を取得"""
        try:
            if hasattr(self.scheduler, 'get_redis_status'):
                return self.scheduler.get_redis_status()
            else:
                return {"status": "unknown"}
        except Exception as e:
            return {"status": "error", "message": str(e)}
    
    def _get_status_history(self):
        """状態履歴を取得（簡略化実装）"""
        # 実際の実装では、状態履歴をファイルやデータベースに保存する
        return [
            {
                "timestamp": time.time() - 300,
                "status": "connected",
                "fallback_mode": False
            },
            {
                "timestamp": time.time() - 150,
                "status": "connected", 
                "fallback_mode": False
            },
            {
                "timestamp": time.time(),
                "status": "connected",
                "fallback_mode": False
            }
        ]
    
    def _get_alerts(self):
        """アラート情報を取得"""
        alerts = []
        
        try:
            if hasattr(self.scheduler, 'redis_manager') and self.scheduler.redis_manager:
                status = self.scheduler.redis_manager.get_status()
                
                if status.get('fallback_mode'):
                    alerts.append({
                        "level": "warning",
                        "message": "XMLRPCフォールバックモードで動作中",
                        "timestamp": time.time()
                    })
                
                if status.get('connection_attempts', 0) > 0:
                    alerts.append({
                        "level": "info",
                        "message": f"Redis接続試行回数: {status['connection_attempts']}",
                        "timestamp": time.time()
                    })
        
        except Exception as e:
            alerts.append({
                "level": "error",
                "message": f"アラート取得エラー: {e}",
                "timestamp": time.time()
            })
        
        return alerts
