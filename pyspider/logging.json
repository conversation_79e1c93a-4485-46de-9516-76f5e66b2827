{"version": 1, "disable_existing_loggers": false, "formatters": {"default": {"format": "%(asctime)s [%(levelname)s] %(name)s: %(message)s", "datefmt": "%Y-%m-%d %H:%M:%S"}, "detailed": {"format": "%(asctime)s [%(levelname)s] %(name)s.%(funcName)s:%(lineno)d: %(message)s", "datefmt": "%Y-%m-%d %H:%M:%S"}, "json": {"()": "pyspider.libs.enhanced_logging.JSONFormatter", "format": "%(timestamp)s %(level)s %(logger)s %(message)s"}}, "handlers": {"console": {"class": "logging.StreamHandler", "level": "INFO", "formatter": "default", "stream": "ext://sys.stdout"}, "file": {"class": "logging.handlers.RotatingFileHandler", "level": "INFO", "formatter": "json", "filename": "logs/pyspider.log", "maxBytes": 10485760, "backupCount": 5, "encoding": "utf8"}, "error_file": {"class": "logging.handlers.RotatingFileHandler", "level": "ERROR", "formatter": "detailed", "filename": "logs/error.log", "maxBytes": 10485760, "backupCount": 3, "encoding": "utf8"}}, "loggers": {"pyspider": {"level": "INFO", "handlers": ["console", "file"], "propagate": false}, "scheduler": {"level": "INFO", "handlers": ["file"], "propagate": false}, "fetcher": {"level": "INFO", "handlers": ["file"], "propagate": false}, "processor": {"level": "INFO", "handlers": ["file"], "propagate": false}, "webui": {"level": "INFO", "handlers": ["file"], "propagate": false}, "result": {"level": "INFO", "handlers": ["file"], "propagate": false}}, "root": {"level": "INFO", "handlers": ["console", "file", "error_file"]}}