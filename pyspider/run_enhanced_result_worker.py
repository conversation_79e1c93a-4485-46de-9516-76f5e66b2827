#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sys
import time
import logging
import argparse
from pyspider.database import connect_database
from pyspider.result.enhanced_result_worker import EnhancedResultWorker, BatchEnhancedResultWorker
from multiprocessing import Queue

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger('run_enhanced_result_worker')

def get_pipeline_config(config_file=None):
    """パイプライン設定を取得"""
    if config_file:
        import json
        try:
            with open(config_file, 'r') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"Failed to load pipeline config: {e}")
    
    # デフォルト設定
    return {
        'transformers': [
            {'name': 'strip'},
            {'name': 'remove_html'},
            {'name': 'normalize_whitespace'}
        ],
        'validators': [
            {'name': 'not_empty'},
            {'name': 'min_length', 'args': [10]}
        ],
        'filters': [
            {
                'field': 'result',
                'operator': 'ne',
                'value': None
            }
        ],
        'post_processors': [
            {
                'type': 'add_field',
                'field': 'processed_by',
                'value': 'enhanced_result_worker'
            }
        ]
    }

def main():
    parser = argparse.ArgumentParser(description='Run enhanced result worker')
    parser.add_argument('--resultdb', help='result database url')
    parser.add_argument('--config', help='pipeline config file')
    parser.add_argument('--max-workers', type=int, default=4, help='max workers')
    parser.add_argument('--queue-size', type=int, default=100, help='queue size')
    parser.add_argument('--batch', action='store_true', help='use batch processing')
    parser.add_argument('--batch-size', type=int, default=100, help='batch size')
    parser.add_argument('--batch-timeout', type=int, default=60, help='batch timeout in seconds')
    
    args = parser.parse_args()
    
    resultdb = connect_database(args.resultdb)
    inqueue = Queue(args.queue_size)
    
    pipeline_config = get_pipeline_config(args.config)
    
    if args.batch:
        result_worker = BatchEnhancedResultWorker(
            resultdb=resultdb,
            inqueue=inqueue,
            pipeline_config=pipeline_config,
            max_workers=args.max_workers,
            queue_size=args.queue_size,
            batch_size=args.batch_size,
            batch_timeout=args.batch_timeout
        )
        logger.info(f"Starting batch enhanced result worker with batch size {args.batch_size}")
    else:
        result_worker = EnhancedResultWorker(
            resultdb=resultdb,
            inqueue=inqueue,
            pipeline_config=pipeline_config,
            max_workers=args.max_workers,
            queue_size=args.queue_size
        )
        logger.info("Starting enhanced result worker")
    
    try:
        result_worker.run()
    except KeyboardInterrupt:
        pass
    finally:
        result_worker.quit()
        
        # 統計情報を表示
        stats = result_worker.get_stats()
        logger.info(f"Result worker stats: {stats}")

if __name__ == '__main__':
    main()
