#!/usr/bin/env python
# -*- encoding: utf-8 -*-
# vim: set et sw=4 ts=4 sts=4 ff=unix fenc=utf8:
# Author: Binux<<EMAIL>>
#         http://binux.me
# Created on 2014-12-04 23:25:10

import time
import logging
import sqlalchemy.exc

from sqlalchemy import create_engine, MetaData, Table, Column, String, Float, Text, select, text
import sqlalchemy
from sqlalchemy.engine.url import make_url
from pyspider.libs import utils
from pyspider.database.base.projectdb import ProjectDB as BaseProjectDB
from .sqlalchemybase import result2dict

logger = logging.getLogger('database.sqlalchemy.projectdb')


class ProjectDB(BaseProjectDB):
    __tablename__ = 'projectdb'

    def __init__(self, url):
        self.table = Table(self.__tablename__, MetaData(),
                           <PERSON>umn('name', String(64), primary_key=True),
                           <PERSON>umn('group', String(64)),
                           <PERSON><PERSON><PERSON>('status', String(16)),
                           <PERSON><PERSON><PERSON>('script', Text),
                           <PERSON><PERSON><PERSON>('comments', String(1024)),
                           <PERSON><PERSON><PERSON>('rate', Float(11)),
                           <PERSON><PERSON><PERSON>('burst', Float(11)),
                           Column('updatetime', Float(32)),
                           mysql_engine='InnoDB',
                           mysql_charset='utf8'
                           )

        # SQLAlchemy 2.0 compatibility: URL objects are immutable
        self.url = make_url(url)
        if self.url.database:
            database = self.url.database
            # Create a new URL without the database
            url_without_db = self.url.set(database=None)
            try:
                engine = create_engine(url_without_db, pool_recycle=3600)
                conn = engine.connect()
                # SQLAlchemy 2.0 compatibility: execute returns a result object
                with conn.begin():
                    conn.execute(sqlalchemy.text(f"CREATE DATABASE IF NOT EXISTS {database}"))
            except sqlalchemy.exc.SQLAlchemyError as e:
                logger.warning(f"Error creating database: {e}")
                pass
        self.engine = create_engine(url, pool_recycle=3600)
        # SQLAlchemy 2.0 compatibility: use connection with transaction for table creation
        with self.engine.connect() as conn:
            with conn.begin():
                self.table.create(conn, checkfirst=True)

    @staticmethod
    def _parse(data):
        return data

    @staticmethod
    def _stringify(data):
        return data

    def insert(self, name, obj={}):
        obj = dict(obj)
        obj['name'] = name
        obj['updatetime'] = time.time()
        # SQLAlchemy 2.0 compatibility: use connection.execute() with transaction
        with self.engine.connect() as conn:
            with conn.begin():
                return conn.execute(self.table.insert()
                                   .values(**self._stringify(obj)))

    def update(self, name, obj={}, **kwargs):
        obj = dict(obj)
        obj.update(kwargs)
        obj['updatetime'] = time.time()
        # SQLAlchemy 2.0 compatibility: use connection.execute() with transaction
        with self.engine.connect() as conn:
            with conn.begin():
                return conn.execute(self.table.update()
                                   .where(self.table.c.name == name)
                                   .values(**self._stringify(obj)))

    def get_all(self, fields=None):
        columns = [getattr(self.table.c, f, f) for f in fields] if fields else self.table.c
        # SQLAlchemy 2.0 compatibility: use connection.execute() and select()
        with self.engine.connect() as conn:
            stmt = sqlalchemy.select(*columns).select_from(self.table)
            result = conn.execute(stmt)
            for task in result:
                yield self._parse(result2dict(columns, task))

    def get(self, name, fields=None):
        columns = [getattr(self.table.c, f, f) for f in fields] if fields else self.table.c
        # SQLAlchemy 2.0 compatibility: use connection.execute() and select()
        with self.engine.connect() as conn:
            stmt = sqlalchemy.select(*columns).select_from(self.table).where(self.table.c.name == name).limit(1)
            result = conn.execute(stmt)
            for task in result:
                return self._parse(result2dict(columns, task))

    def drop(self, name):
        # SQLAlchemy 2.0 compatibility: use connection.execute() with transaction
        with self.engine.connect() as conn:
            with conn.begin():
                return conn.execute(self.table.delete()
                                   .where(self.table.c.name == name))

    def check_update(self, timestamp, fields=None):
        columns = [getattr(self.table.c, f, f) for f in fields] if fields else self.table.c
        # SQLAlchemy 2.0 compatibility: use connection.execute() and select()
        with self.engine.connect() as conn:
            stmt = sqlalchemy.select(*columns).select_from(self.table).where(self.table.c.updatetime >= timestamp)
            result = conn.execute(stmt)
            for task in result:
                yield self._parse(result2dict(columns, task))
