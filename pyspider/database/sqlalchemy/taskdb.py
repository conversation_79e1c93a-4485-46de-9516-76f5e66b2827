#!/usr/bin/env python
# -*- encoding: utf-8 -*-
# vim: set et sw=4 ts=4 sts=4 ff=unix fenc=utf8:
# Author: Binux<<EMAIL>>
#         http://binux.me
# Created on 2014-12-04 22:33:43

import re
import time
import json
import logging
import sqlalchemy.exc

from sqlalchemy import (create_engine, MetaData, Table, Column, Index,
                        Integer, String, Float, Text, func, select, text)
import sqlalchemy
from sqlalchemy.engine.url import make_url
from pyspider.libs import utils
from pyspider.database.base.taskdb import TaskDB as BaseTaskDB
from .sqlalchemybase import SplitTableMixin, result2dict

logger = logging.getLogger('database.sqlalchemy.taskdb')


class TaskDB(SplitTableMixin, BaseTaskDB):
    __tablename__ = 'taskdb'

    def __init__(self, url):
        self.table = Table('__tablename__', MetaData(),
                           <PERSON>umn('taskid', String(64), primary_key=True, nullable=False),
                           <PERSON>umn('project', String(64)),
                           <PERSON>umn('url', String(1024)),
                           Column('status', Integer),
                           Column('schedule', Text()),
                           Column('fetch', Text()),
                           Column('process', Text()),
                           Column('track', Text()),
                           Column('lastcrawltime', Float(32)),
                           Column('updatetime', Float(32)),
                           mysql_engine='InnoDB',
                           mysql_charset='utf8'
                           )

        # SQLAlchemy 2.0 compatibility: URL objects are immutable
        self.url = make_url(url)
        if self.url.database:
            database = self.url.database
            # Create a new URL without the database
            url_without_db = self.url.set(database=None)
            try:
                engine = create_engine(url_without_db, pool_recycle=3600)
                conn = engine.connect()
                # SQLAlchemy 2.0 compatibility: execute returns a result object
                with conn.begin():
                    conn.execute(sqlalchemy.text(f"CREATE DATABASE IF NOT EXISTS {database}"))
            except sqlalchemy.exc.SQLAlchemyError as e:
                logger.warning(f"Error creating database: {e}")
                pass
        self.engine = create_engine(url, pool_recycle=3600)

        self._list_project()

    def _create_project(self, project):
        assert re.match(r'^\w+$', project) is not None
        if project in self.projects:
            return
        self.table.name = self._tablename(project)
        Index('status_%s_index' % self.table.name, self.table.c.status)
        # SQLAlchemy 2.0 compatibility: use connection with transaction for table creation
        with self.engine.connect() as conn:
            with conn.begin():
                self.table.create(conn, checkfirst=True)
        self.table.indexes.clear()

    @staticmethod
    def _parse(data):
        for key, value in list(data.items()):
            if isinstance(value, bytes):
                data[key] = utils.text(value)
        for each in ('schedule', 'fetch', 'process', 'track'):
            if each in data:
                if data[each]:
                    data[each] = json.loads(data[each])
                else:
                    data[each] = {}
        return data

    @staticmethod
    def _stringify(data):
        for each in ('schedule', 'fetch', 'process', 'track'):
            if each in data:
                if data[each]:
                    data[each] = json.dumps(data[each])
                else:
                    data[each] = json.dumps({})
        return data

    def load_tasks(self, status, project=None, fields=None):
        if project and project not in self.projects:
            return

        if project:
            projects = [project, ]
        else:
            projects = self.projects

        columns = [getattr(self.table.c, f, f) for f in fields] if fields else self.table.c
        for project in projects:
            self.table.name = self._tablename(project)
            # SQLAlchemy 2.0 compatibility: use connection.execute() and select()
            with self.engine.connect() as conn:
                stmt = sqlalchemy.select(*columns).select_from(self.table).where(self.table.c.status == status)
                result = conn.execute(stmt)
                for task in result:
                    yield self._parse(result2dict(columns, task))

    def get_task(self, project, taskid, fields=None):
        if project not in self.projects:
            self._list_project()
        if project not in self.projects:
            return None

        self.table.name = self._tablename(project)
        columns = [getattr(self.table.c, f, f) for f in fields] if fields else self.table.c
        # SQLAlchemy 2.0 compatibility: use connection.execute() and select()
        with self.engine.connect() as conn:
            stmt = sqlalchemy.select(*columns).select_from(self.table).where(self.table.c.taskid == taskid).limit(1)
            result = conn.execute(stmt)
            for each in result:
                return self._parse(result2dict(columns, each))

    def status_count(self, project):
        result = dict()
        if project not in self.projects:
            self._list_project()
        if project not in self.projects:
            return result

        self.table.name = self._tablename(project)
        # SQLAlchemy 2.0 compatibility: use connection.execute() and select()
        with self.engine.connect() as conn:
            stmt = sqlalchemy.select(self.table.c.status, func.count(1)).select_from(self.table).group_by(self.table.c.status)
            result_set = conn.execute(stmt)
            for status, count in result_set:
                result[status] = count
        return result

    def insert(self, project, taskid, obj={}):
        if project not in self.projects:
            self._list_project()
        if project not in self.projects:
            self._create_project(project)
            self._list_project()
        obj = dict(obj)
        obj['taskid'] = taskid
        obj['project'] = project
        obj['updatetime'] = time.time()
        self.table.name = self._tablename(project)
        # SQLAlchemy 2.0 compatibility: use connection.execute() with transaction
        with self.engine.connect() as conn:
            with conn.begin():
                return conn.execute(self.table.insert()
                                   .values(**self._stringify(obj)))

    def update(self, project, taskid, obj={}, **kwargs):
        if project not in self.projects:
            self._list_project()
        if project not in self.projects:
            raise LookupError
        self.table.name = self._tablename(project)
        obj = dict(obj)
        obj.update(kwargs)
        obj['updatetime'] = time.time()
        # SQLAlchemy 2.0 compatibility: use connection.execute() with transaction
        with self.engine.connect() as conn:
            with conn.begin():
                return conn.execute(self.table.update()
                                   .where(self.table.c.taskid == taskid)
                                   .values(**self._stringify(obj)))
