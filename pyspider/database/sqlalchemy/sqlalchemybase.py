#!/usr/bin/env python
# -*- encoding: utf-8 -*-
# vim: set et sw=4 ts=4 sts=4 ff=unix fenc=utf8:
# Author: Binux<<EMAIL>>
#         http://binux.me
# Created on 2014-12-04 18:48:47

import time
import logging
import sqlalchemy
from sqlalchemy import inspect

logger = logging.getLogger('database.sqlalchemy')


def result2dict(columns, task):
    """Convert SQLAlchemy result to dict"""
    try:
        # SQLAlchemy 2.0の場合、taskは既にRow型でdict-likeアクセスが可能
        if hasattr(task, '_mapping'):
            return dict(task._mapping)
        elif hasattr(task, '_asdict'):
            return task._asdict()
        elif hasattr(task, 'keys') and hasattr(task, 'values'):
            # Row型の場合
            return dict(zip(task.keys(), task))
        else:
            # 従来の方法
            return dict(zip(columns, task))
    except Exception as e:
        logger.error(f"Error converting result to dict: {e}, task type: {type(task)}")
        # フォールバック: columnsとtaskを使用
        if hasattr(task, '__iter__') and not isinstance(task, (str, bytes)):
            return dict(zip(columns, task))
        else:
            return {}


def get_table_names(engine):
    """Get table names from SQLAlchemy engine in a version-compatible way"""
    try:
        # SQLAlchemy 1.x
        if hasattr(engine, 'table_names'):
            return engine.table_names()
        # SQLAlchemy 2.x
        else:
            inspector = inspect(engine)
            return inspector.get_table_names()
    except Exception as e:
        logger.error(f"Error getting table names: {e}")
        return []


class SplitTableMixin(object):
    UPDATE_PROJECTS_TIME = 10 * 60

    def _tablename(self, project):
        if self.__tablename__:
            return '%s_%s' % (self.__tablename__, project)
        else:
            return project

    @property
    def projects(self):
        if time.time() - getattr(self, '_last_update_projects', 0) \
                > self.UPDATE_PROJECTS_TIME:
            self._list_project()
        return self._projects

    @projects.setter
    def projects(self, value):
        self._projects = value

    def _list_project(self):
        self._last_update_projects = time.time()
        self.projects = set()
        if self.__tablename__:
            prefix = '%s_' % self.__tablename__
        else:
            prefix = ''

        # SQLAlchemy 2.0互換の方法でテーブル名を取得
        for project in get_table_names(self.engine):
            if project.startswith(prefix):
                project = project[len(prefix):]
                self.projects.add(project)

    def drop(self, project):
        if project not in self.projects:
            self._list_project()
        if project not in self.projects:
            return
        self.table.name = self._tablename(project)
        # SQLAlchemy 2.0 compatibility: use connection with transaction for table drop
        with self.engine.connect() as conn:
            with conn.begin():
                self.table.drop(conn)
        self._list_project()
