#!/usr/bin/env python
# -*- encoding: utf-8 -*-
# vim: set et sw=4 ts=4 sts=4 ff=unix fenc=utf8:
# Author: Binux<<EMAIL>>
#         http://binux.me
# Created on 2014-12-04 18:48:15

import re
import time
import json
import logging
import sqlalchemy.exc

from sqlalchemy import (create_engine, MetaData, Table, Column,
                        String, Float, Text, select, text, func)
import sqlalchemy
from sqlalchemy.engine.url import make_url
from pyspider.database.base.resultdb import ResultDB as BaseResultDB
from pyspider.libs import utils
from .sqlalchemybase import SplitTableMixin, result2dict

logger = logging.getLogger('database.sqlalchemy.resultdb')


class ResultDB(SplitTableMixin, BaseResultDB):
    __tablename__ = ''

    def __init__(self, url):
        self.table = Table('__tablename__', MetaData(),
                           <PERSON>umn('taskid', String(64), primary_key=True, nullable=False),
                           <PERSON><PERSON><PERSON>('url', String(1024)),
                           <PERSON><PERSON><PERSON>('result', Text()),
                           <PERSON>umn('updatetime', Float(32)),
                           mysql_engine='InnoDB',
                           mysql_charset='utf8'
                           )

        # SQLAlchemy 2.0 compatibility: URL objects are immutable
        self.url = make_url(url)
        if self.url.database:
            database = self.url.database
            # Create a new URL without the database
            url_without_db = self.url.set(database=None)
            try:
                engine = create_engine(url_without_db, pool_recycle=3600)
                conn = engine.connect()
                # SQLAlchemy 2.0 compatibility: execute returns a result object
                with conn.begin():
                    conn.execute(sqlalchemy.text(f"CREATE DATABASE IF NOT EXISTS {database}"))
            except sqlalchemy.exc.SQLAlchemyError as e:
                logger.warning(f"Error creating database: {e}")
                pass
        self.engine = create_engine(url, pool_recycle=3600)

        self._list_project()

    def _create_project(self, project):
        assert re.match(r'^\w+$', project) is not None
        if project in self.projects:
            return
        self.table.name = self._tablename(project)
        # SQLAlchemy 2.0 compatibility: use connection with transaction for table creation
        with self.engine.connect() as conn:
            with conn.begin():
                self.table.create(conn, checkfirst=True)

    @staticmethod
    def _parse(data):
        for key, value in list(data.items()):
            if isinstance(value, bytes):
                data[key] = utils.text(value)
        if 'result' in data:
            if data['result']:
                data['result'] = json.loads(data['result'])
            else:
                data['result'] = {}
        return data

    @staticmethod
    def _stringify(data):
        if 'result' in data:
            if data['result']:
                data['result'] = json.dumps(data['result'])
            else:
                data['result'] = json.dumps({})
        return data

    def save(self, project, taskid, url, result):
        if project not in self.projects:
            self._create_project(project)
            self._list_project()
        self.table.name = self._tablename(project)
        obj = {
            'taskid': taskid,
            'url': url,
            'result': result,
            'updatetime': time.time(),
        }
        # SQLAlchemy 2.0 compatibility: use connection.execute() with transaction
        with self.engine.connect() as conn:
            with conn.begin():
                if self.get(project, taskid, ('taskid', )):
                    del obj['taskid']
                    return conn.execute(self.table.update()
                                       .where(self.table.c.taskid == taskid)
                                       .values(**self._stringify(obj)))
                else:
                    return conn.execute(self.table.insert()
                                       .values(**self._stringify(obj)))

    def select(self, project, fields=None, offset=0, limit=None):
        if project not in self.projects:
            self._list_project()
        if project not in self.projects:
            return
        self.table.name = self._tablename(project)

        columns = [getattr(self.table.c, f, f) for f in fields] if fields else self.table.c
        # SQLAlchemy 2.0 compatibility: use connection.execute() and select() instead of with_only_columns()
        with self.engine.connect() as conn:
            stmt = sqlalchemy.select(*columns).select_from(self.table).order_by(self.table.c.updatetime.desc())
            if offset:
                stmt = stmt.offset(offset)
            if limit:
                stmt = stmt.limit(limit)
            result = conn.execute(stmt)
            for task in result:
                yield self._parse(result2dict(columns, task))

    def count(self, project):
        if project not in self.projects:
            self._list_project()
        if project not in self.projects:
            return 0
        self.table.name = self._tablename(project)

        # SQLAlchemy 2.0 compatibility: use connection.execute() and func.count()
        with self.engine.connect() as conn:
            stmt = sqlalchemy.select(sqlalchemy.func.count()).select_from(self.table)
            result = conn.execute(stmt)
            return result.scalar()

    def get(self, project, taskid, fields=None):
        if project not in self.projects:
            self._list_project()
        if project not in self.projects:
            return
        self.table.name = self._tablename(project)

        columns = [getattr(self.table.c, f, f) for f in fields] if fields else self.table.c
        # SQLAlchemy 2.0 compatibility: use connection.execute() and select()
        with self.engine.connect() as conn:
            stmt = sqlalchemy.select(*columns).select_from(self.table).where(self.table.c.taskid == taskid).limit(1)
            result = conn.execute(stmt)
            for task in result:
                return self._parse(result2dict(columns, task))
