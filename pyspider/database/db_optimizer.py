#!/usr/bin/env python
# -*- encoding: utf-8 -*-
# vim: set et sw=4 ts=4 sts=4 ff=unix fenc=utf8:
# Author: PySpiderNX2 Team
# Created on 2025-01-XX

import logging
import time
from typing import Dict, Any, List, Optional

logger = logging.getLogger(__name__)

class DatabaseOptimizer:
    """データベース最適化クラス"""
    
    def __init__(self):
        self.query_cache = {}
        self.cache_ttl = 300  # 5分
        self.max_cache_size = 1000
        
    def optimize_sqlite_database(self, db_connection):
        """SQLiteデータベースを最適化"""
        try:
            cursor = db_connection.cursor()
            
            # WALモードを有効化（パフォーマンス向上）
            cursor.execute("PRAGMA journal_mode=WAL")
            
            # 同期モードを最適化
            cursor.execute("PRAGMA synchronous=NORMAL")
            
            # キャッシュサイズを増加
            cursor.execute("PRAGMA cache_size=10000")
            
            # 一時ファイルをメモリに保存
            cursor.execute("PRAGMA temp_store=MEMORY")
            
            # 自動バキュームを有効化
            cursor.execute("PRAGMA auto_vacuum=INCREMENTAL")
            
            logger.info("SQLite database optimized")
            
        except Exception as e:
            logger.error(f"Failed to optimize SQLite database: {e}")
    
    def create_indexes(self, db_connection, table_name: str, indexes: List[Dict[str, Any]]):
        """インデックスを作成"""
        try:
            cursor = db_connection.cursor()
            
            for index in indexes:
                index_name = index['name']
                columns = index['columns']
                unique = index.get('unique', False)
                
                # インデックスが既に存在するかチェック
                cursor.execute("""
                    SELECT name FROM sqlite_master 
                    WHERE type='index' AND name=?
                """, (index_name,))
                
                if cursor.fetchone():
                    logger.debug(f"Index {index_name} already exists")
                    continue
                
                # インデックスを作成
                unique_clause = "UNIQUE" if unique else ""
                columns_str = ", ".join(columns)
                
                sql = f"""
                    CREATE {unique_clause} INDEX {index_name} 
                    ON {table_name} ({columns_str})
                """
                
                cursor.execute(sql)
                logger.info(f"Created index {index_name} on {table_name}")
                
        except Exception as e:
            logger.error(f"Failed to create indexes: {e}")
    
    def optimize_queries(self, db_connection):
        """クエリを最適化"""
        try:
            cursor = db_connection.cursor()
            
            # 統計情報を更新
            cursor.execute("ANALYZE")
            
            # バキュームを実行（必要に応じて）
            cursor.execute("PRAGMA incremental_vacuum")
            
            logger.info("Database queries optimized")
            
        except Exception as e:
            logger.error(f"Failed to optimize queries: {e}")
    
    def cache_query_result(self, query_key: str, result: Any, ttl: Optional[int] = None):
        """クエリ結果をキャッシュ"""
        if len(self.query_cache) >= self.max_cache_size:
            # 古いエントリを削除
            self._cleanup_cache()
        
        cache_ttl = ttl or self.cache_ttl
        self.query_cache[query_key] = {
            'result': result,
            'timestamp': time.time(),
            'ttl': cache_ttl
        }
    
    def get_cached_result(self, query_key: str) -> Optional[Any]:
        """キャッシュされたクエリ結果を取得"""
        if query_key not in self.query_cache:
            return None
        
        cache_entry = self.query_cache[query_key]
        
        # TTLをチェック
        if time.time() - cache_entry['timestamp'] > cache_entry['ttl']:
            del self.query_cache[query_key]
            return None
        
        return cache_entry['result']
    
    def _cleanup_cache(self):
        """期限切れのキャッシュエントリを削除"""
        current_time = time.time()
        expired_keys = []
        
        for key, entry in self.query_cache.items():
            if current_time - entry['timestamp'] > entry['ttl']:
                expired_keys.append(key)
        
        for key in expired_keys:
            del self.query_cache[key]
        
        # まだサイズが大きい場合は、古いエントリを削除
        if len(self.query_cache) >= self.max_cache_size:
            sorted_entries = sorted(
                self.query_cache.items(),
                key=lambda x: x[1]['timestamp']
            )
            
            # 古い半分を削除
            for key, _ in sorted_entries[:len(sorted_entries)//2]:
                del self.query_cache[key]

# 推奨インデックス設定
RECOMMENDED_INDEXES = {
    'taskdb': [
        {
            'name': 'idx_taskdb_project_status',
            'columns': ['project', 'status'],
            'unique': False
        },
        {
            'name': 'idx_taskdb_updatetime',
            'columns': ['updatetime'],
            'unique': False
        },
        {
            'name': 'idx_taskdb_project_taskid',
            'columns': ['project', 'taskid'],
            'unique': True
        }
    ],
    'projectdb': [
        {
            'name': 'idx_projectdb_name',
            'columns': ['name'],
            'unique': True
        },
        {
            'name': 'idx_projectdb_status',
            'columns': ['status'],
            'unique': False
        },
        {
            'name': 'idx_projectdb_updatetime',
            'columns': ['updatetime'],
            'unique': False
        }
    ],
    'resultdb': [
        {
            'name': 'idx_resultdb_project_taskid',
            'columns': ['project', 'taskid'],
            'unique': True
        },
        {
            'name': 'idx_resultdb_updatetime',
            'columns': ['updatetime'],
            'unique': False
        }
    ]
}

def optimize_database(db_connection, db_type: str):
    """データベースを最適化"""
    optimizer = DatabaseOptimizer()
    
    if 'sqlite' in db_type.lower():
        optimizer.optimize_sqlite_database(db_connection)
        
        # 推奨インデックスを作成
        table_name = db_type.split('+')[1] if '+' in db_type else 'main'
        if table_name in RECOMMENDED_INDEXES:
            optimizer.create_indexes(
                db_connection, 
                table_name, 
                RECOMMENDED_INDEXES[table_name]
            )
        
        optimizer.optimize_queries(db_connection)
    
    return optimizer

# グローバルオプティマイザーインスタンス
db_optimizer = DatabaseOptimizer()
