#!/usr/bin/env python
# -*- encoding: utf-8 -*-
# vim: set et sw=4 ts=4 sts=4 ff=unix fenc=utf8:
# Author: Binux<<EMAIL>>
#         http://binux.me
# Created on 2025-05-17 14:30:00

"""
データベースの最適化スクリプト
"""

import os
import sys
import logging
import sqlite3
import argparse
from datetime import datetime

# ロガーの設定
logging.basicConfig(level=logging.INFO, format='%(asctime)s [%(levelname)s] %(message)s')
logger = logging.getLogger('optimize_db')

def optimize_sqlite_db(db_path, vacuum=True):
    """SQLiteデータベースを最適化する"""
    if not os.path.exists(db_path):
        logger.error(f"Database file not found: {db_path}")
        return False

    try:
        logger.info(f"Optimizing database: {db_path}")
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # テーブル一覧を取得
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [row[0] for row in cursor.fetchall()]
        logger.info(f"Found {len(tables)} tables: {', '.join(tables)}")

        # taskdbテーブルのインデックスを作成
        taskdb_tables = [t for t in tables if t.startswith('taskdb_')]
        for table in taskdb_tables:
            logger.info(f"Creating indexes for table: {table}")
            
            # ステータスのインデックス
            try:
                cursor.execute(f"CREATE INDEX IF NOT EXISTS idx_{table}_status ON {table} (status)")
                logger.info(f"Created status index for {table}")
            except sqlite3.Error as e:
                logger.warning(f"Failed to create status index for {table}: {e}")
            
            # 更新時間のインデックス
            try:
                cursor.execute(f"CREATE INDEX IF NOT EXISTS idx_{table}_updatetime ON {table} (updatetime)")
                logger.info(f"Created updatetime index for {table}")
            except sqlite3.Error as e:
                logger.warning(f"Failed to create updatetime index for {table}: {e}")
            
            # プロジェクトとステータスの複合インデックス
            try:
                cursor.execute(f"CREATE INDEX IF NOT EXISTS idx_{table}_project_status ON {table} (project, status)")
                logger.info(f"Created project_status index for {table}")
            except sqlite3.Error as e:
                logger.warning(f"Failed to create project_status index for {table}: {e}")

        # projectdbテーブルのインデックスを作成
        if 'projectdb' in tables:
            logger.info("Creating indexes for projectdb table")
            
            # グループのインデックス
            try:
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_projectdb_group ON projectdb (`group`)")
                logger.info("Created group index for projectdb")
            except sqlite3.Error as e:
                logger.warning(f"Failed to create group index for projectdb: {e}")
            
            # ステータスのインデックス
            try:
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_projectdb_status ON projectdb (status)")
                logger.info("Created status index for projectdb")
            except sqlite3.Error as e:
                logger.warning(f"Failed to create status index for projectdb: {e}")
            
            # 更新時間のインデックス
            try:
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_projectdb_updatetime ON projectdb (updatetime)")
                logger.info("Created updatetime index for projectdb")
            except sqlite3.Error as e:
                logger.warning(f"Failed to create updatetime index for projectdb: {e}")

        # resultdbテーブルのインデックスを作成
        resultdb_tables = [t for t in tables if t.startswith('resultdb_')]
        for table in resultdb_tables:
            logger.info(f"Creating indexes for table: {table}")
            
            # 更新時間のインデックス
            try:
                cursor.execute(f"CREATE INDEX IF NOT EXISTS idx_{table}_updatetime ON {table} (updatetime)")
                logger.info(f"Created updatetime index for {table}")
            except sqlite3.Error as e:
                logger.warning(f"Failed to create updatetime index for {table}: {e}")

        # VACUUMを実行
        if vacuum:
            logger.info("Running VACUUM to optimize database size")
            cursor.execute("VACUUM")
            logger.info("VACUUM completed")

        conn.commit()
        conn.close()
        logger.info(f"Database optimization completed: {db_path}")
        return True
    except sqlite3.Error as e:
        logger.error(f"SQLite error: {e}")
        return False
    except Exception as e:
        logger.error(f"Error optimizing database: {e}")
        return False

def main():
    parser = argparse.ArgumentParser(description='Optimize pyspider SQLite databases')
    parser.add_argument('--path', '-p', help='Path to the database directory', default='./data')
    parser.add_argument('--no-vacuum', action='store_true', help='Skip VACUUM operation')
    args = parser.parse_args()

    db_dir = args.path
    if not os.path.isdir(db_dir):
        logger.error(f"Database directory not found: {db_dir}")
        return 1

    # データベースファイルを検索
    db_files = [f for f in os.listdir(db_dir) if f.endswith('.db')]
    if not db_files:
        logger.warning(f"No database files found in {db_dir}")
        return 0

    logger.info(f"Found {len(db_files)} database files: {', '.join(db_files)}")
    
    success_count = 0
    for db_file in db_files:
        db_path = os.path.join(db_dir, db_file)
        if optimize_sqlite_db(db_path, not args.no_vacuum):
            success_count += 1
    
    logger.info(f"Optimization completed: {success_count}/{len(db_files)} databases optimized")
    return 0

if __name__ == '__main__':
    sys.exit(main())
