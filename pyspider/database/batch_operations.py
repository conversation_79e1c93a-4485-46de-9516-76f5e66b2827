#!/usr/bin/env python
# -*- encoding: utf-8 -*-
# vim: set et sw=4 ts=4 sts=4 ff=unix fenc=utf8:
# Author: Binux<<EMAIL>>
#         http://binux.me
# Created on 2023-05-17 23:30:56

import time
import logging
import threading
from typing import Dict, Any, List, Optional, Callable, Union

logger = logging.getLogger('batch_operations')

class BatchOperations:
    """
    Batch database operations for pyspider
    
    This class provides batch operations for database operations
    to improve performance.
    """
    
    def __init__(self, 
                 batch_size: int = 100,
                 flush_interval: int = 10,
                 auto_flush: bool = True):
        """
        Initialize BatchOperations
        
        Args:
            batch_size: Maximum number of operations in a batch
            flush_interval: Interval between automatic flushes in seconds
            auto_flush: Whether to automatically flush batches
        """
        self.batch_size = batch_size
        self.flush_interval = flush_interval
        self.auto_flush = auto_flush
        
        self.last_flush_time = time.time()
        self._stop_event = threading.Event()
        self._flush_thread = None
        self._batches = {}
        self._batch_locks = {}
        
        logger.info(f"Batch operations initialized with batch_size={batch_size}, "
                   f"flush_interval={flush_interval}s, auto_flush={auto_flush}")
    
    def start(self):
        """
        Start automatic flushing
        """
        if self.auto_flush and self._flush_thread is None:
            self._stop_event.clear()
            self._flush_thread = threading.Thread(target=self._flush_loop, daemon=True)
            self._flush_thread.start()
            logger.info("Automatic batch flushing started")
    
    def stop(self):
        """
        Stop automatic flushing
        """
        if self._flush_thread is not None:
            self._stop_event.set()
            self._flush_thread.join(timeout=5)
            self._flush_thread = None
            logger.info("Automatic batch flushing stopped")
            
            # Flush all remaining batches
            self.flush_all()
    
    def register_batch(self, name: str, flush_func: Callable[[List[Any]], None]):
        """
        Register a batch for operations
        
        Args:
            name: Batch name
            flush_func: Function to call when flushing the batch
        """
        if name not in self._batches:
            self._batches[name] = []
            self._batch_locks[name] = threading.Lock()
            logger.info(f"Registered batch: {name}")
    
    def add_to_batch(self, name: str, item: Any) -> bool:
        """
        Add an item to a batch
        
        Args:
            name: Batch name
            item: Item to add to the batch
            
        Returns:
            True if the batch was flushed, False otherwise
        """
        if name not in self._batches:
            logger.warning(f"Batch {name} not registered")
            return False
        
        flushed = False
        with self._batch_locks[name]:
            self._batches[name].append(item)
            
            # Flush if batch is full
            if len(self._batches[name]) >= self.batch_size:
                self.flush_batch(name)
                flushed = True
        
        return flushed
    
    def flush_batch(self, name: str) -> int:
        """
        Flush a batch
        
        Args:
            name: Batch name
            
        Returns:
            Number of items flushed
        """
        if name not in self._batches:
            logger.warning(f"Batch {name} not registered")
            return 0
        
        with self._batch_locks[name]:
            if not self._batches[name]:
                return 0
            
            items = self._batches[name]
            self._batches[name] = []
            
            try:
                flush_func = self._batch_flush_funcs.get(name)
                if flush_func:
                    flush_func(items)
                    logger.debug(f"Flushed {len(items)} items from batch {name}")
                else:
                    logger.warning(f"No flush function for batch {name}")
            except Exception as e:
                logger.error(f"Error flushing batch {name}: {e}")
                # Put items back in batch
                self._batches[name] = items + self._batches[name]
                return 0
            
            return len(items)
    
    def flush_all(self) -> Dict[str, int]:
        """
        Flush all batches
        
        Returns:
            Dictionary of batch names and number of items flushed
        """
        result = {}
        for name in list(self._batches.keys()):
            result[name] = self.flush_batch(name)
        
        self.last_flush_time = time.time()
        return result
    
    def _flush_loop(self):
        """
        Automatic flush loop
        """
        while not self._stop_event.is_set():
            try:
                # Check if it's time to flush
                current_time = time.time()
                if current_time - self.last_flush_time >= self.flush_interval:
                    self.flush_all()
            except Exception as e:
                logger.error(f"Error in flush loop: {e}")
            
            # Wait for next check
            self._stop_event.wait(1)  # Check every second

# Create a singleton instance
batch_operations = BatchOperations()
