#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
拡張データパイプラインのサンプルプロジェクト

このサンプルは、拡張されたデータ処理パイプラインを使用して、
クローリングしたデータを効率的に処理する方法を示しています。

このサンプルでは、以下の機能を使用しています：
- ストリーム処理：大量のデータをストリームとして処理
- データ変換：柔軟なデータ変換機能
- データ品質チェック：取得したデータの品質を自動的にチェック
"""

from pyspider.libs.base_handler import *
from pyspider.libs.data_pipeline import DataPipeline
from pyspider.libs.data_transformers import DataTransformer
from pyspider.libs.data_quality import DataQualityChecker

class Handler(BaseHandler):
    crawl_config = {
        'headers': {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        }
    }
    
    def __init__(self, *args, **kwargs):
        super(Handler, self).__init__(*args, **kwargs)
        
        # データ変換器を初期化
        self.transformer = DataTransformer()
        
        # データ品質チェッカーを初期化
        self.quality_checker = DataQualityChecker()
        
        # データパイプラインを初期化
        self.pipeline = DataPipeline(max_workers=4, queue_size=100)
        
        # パイプラインを設定
        self.setup_pipeline()
    
    def setup_pipeline(self):
        """パイプラインを設定"""
        # 変換処理を追加
        self.pipeline.add_transformer({
            'name': 'strip',
        })
        
        self.pipeline.add_transformer({
            'name': 'remove_html',
        })
        
        self.pipeline.add_transformer({
            'name': 'normalize_whitespace',
        })
        
        # 検証処理を追加
        self.pipeline.add_validator({
            'name': 'not_empty',
        })
        
        self.pipeline.add_validator({
            'name': 'min_length',
            'args': [10]
        })
        
        # フィルター処理を追加
        def filter_adult_content(data):
            """成人向けコンテンツをフィルタリング"""
            if not isinstance(data, dict):
                return True
            
            text = data.get('text', '')
            adult_keywords = ['adult', 'xxx', 'sex', 'porn']
            return not any(keyword in text.lower() for keyword in adult_keywords)
        
        self.pipeline.add_filter(filter_adult_content)
        
        # 後処理を追加
        def add_metadata(data):
            """メタデータを追加"""
            if not isinstance(data, dict):
                return data
            
            data['processed'] = True
            data['processor'] = 'enhanced_data_pipeline'
            return data
        
        self.pipeline.add_post_processor(add_metadata)
    
    @every(minutes=24 * 60)
    def on_start(self):
        self.crawl('https://news.ycombinator.com/', callback=self.index_page)
    
    @config(age=10 * 24 * 60 * 60)
    def index_page(self, response):
        """インデックスページを処理"""
        # ニュース記事を抽出
        for item in response.doc('.athing').items():
            title = item.find('.title a').text()
            url = item.find('.title a').attr.href
            item_id = item.attr.id
            
            if url and title:
                self.crawl(url, callback=self.detail_page, save={'title': title, 'item_id': item_id})
        
        # 次のページをクロール
        next_link = response.doc('.morelink').attr.href
        if next_link:
            self.crawl(next_link, callback=self.index_page)
    
    @config(priority=2)
    def detail_page(self, response):
        """詳細ページを処理"""
        # 基本データを抽出
        title = response.save.get('title', '')
        item_id = response.save.get('item_id', '')
        
        # メインコンテンツを抽出
        content = response.doc('body').html()
        
        # メタデータを抽出
        meta_description = response.doc('meta[name="description"]').attr.content or ''
        
        # 抽出したデータを作成
        data = {
            'title': title,
            'url': response.url,
            'item_id': item_id,
            'content': content,
            'meta_description': meta_description,
            'text': response.doc('body').text(),
        }
        
        # データパイプラインで処理
        processed_data = list(self.pipeline.process([data]))
        
        # 処理結果を返す
        if processed_data:
            result = processed_data[0]
            
            # 単語数と文字数を計算
            result['word_count'] = self.transformer.transform(result.get('text', ''), 'word_count')
            result['char_count'] = self.transformer.transform(result.get('text', ''), 'character_count')
            
            # 品質スコアを計算
            quality_score = 0
            
            # タイトルの品質をチェック
            title_valid, _ = self.quality_checker.validate(result.get('title', ''), [
                {'name': 'not_empty'},
                {'name': 'min_length', 'args': [5]},
                {'name': 'max_length', 'args': [200]}
            ])
            if title_valid:
                quality_score += 30
            
            # メタディスクリプションの品質をチェック
            meta_valid, _ = self.quality_checker.validate(result.get('meta_description', ''), [
                {'name': 'not_empty'},
                {'name': 'min_length', 'args': [10]},
                {'name': 'max_length', 'args': [300]}
            ])
            if meta_valid:
                quality_score += 20
            
            # コンテンツの品質をチェック
            content_valid, _ = self.quality_checker.validate(result.get('text', ''), [
                {'name': 'not_empty'},
                {'name': 'min_length', 'args': [100]}
            ])
            if content_valid:
                quality_score += 50
            
            result['quality_score'] = quality_score
            
            return result
        
        return {
            'title': title,
            'url': response.url,
            'error': 'Failed to process data',
        }
    
    def on_result(self, result):
        """結果を処理"""
        if not result:
            return
        
        # 処理統計を表示
        stats = self.pipeline.get_stats()
        self.logger.info(f"Pipeline stats: {stats}")
        
        # 品質スコアに基づいて結果を分類
        quality_score = result.get('quality_score', 0)
        if quality_score >= 80:
            self.logger.info(f"High quality content: {result.get('title')} (Score: {quality_score})")
        elif quality_score >= 50:
            self.logger.info(f"Medium quality content: {result.get('title')} (Score: {quality_score})")
        else:
            self.logger.info(f"Low quality content: {result.get('title')} (Score: {quality_score})")
        
        return result
