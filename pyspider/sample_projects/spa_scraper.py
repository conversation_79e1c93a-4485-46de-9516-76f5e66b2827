#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
SPAサイト（Single Page Application）スクレイピングのサンプルプロジェクト

このサンプルは、SPAサイトからデータを効率的に抽出する方法を示しています。
SPAサイトは、JavaScriptを使用して動的にコンテンツを生成するため、
通常のHTTPリクエストでは完全なコンテンツを取得できません。

このサンプルでは、以下の機能を使用しています：
- SPAフレームワークの自動検出
- フレームワーク固有の待機戦略
- 動的コンテンツの完全な読み込み
"""

from pyspider.libs.base_handler import *

class Handler(BaseHandler):
    crawl_config = {
        'headers': {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        },
        'timeout': 120,  # SPAサイトは読み込みに時間がかかるため、タイムアウトを長めに設定
    }
    
    @every(minutes=24 * 60)
    def on_start(self):
        # Reactベースのサイト
        self.crawl('https://reactjs.org', 
                  callback=self.index_page,
                  fetch_type='spa')  # SPAサイト用のフェッチタイプを指定
        
        # Vueベースのサイト
        self.crawl('https://vuejs.org', 
                  callback=self.index_page,
                  fetch_type='spa')
        
        # Angularベースのサイト
        self.crawl('https://angular.io', 
                  callback=self.index_page,
                  fetch_type='spa')
    
    @config(age=10 * 24 * 60 * 60)
    def index_page(self, response):
        # SPAフレームワークの情報を表示
        self.logger.info("Detected SPA framework: %s", response.get('framework', 'Unknown'))
        
        # ページタイトルを取得
        title = response.doc('title').text()
        self.logger.info("Page title: %s", title)
        
        # メインコンテンツを取得
        main_content = response.doc('main').text() or response.doc('#main').text() or response.doc('.main').text()
        if main_content:
            self.logger.info("Main content length: %d", len(main_content))
        
        # リンクを抽出
        for link in response.doc('a[href^="http"]').items():
            url = link.attr.href
            if url and not url.startswith('javascript:'):
                # 同じドメインのリンクのみをクロール
                if urlparse(url).netloc == urlparse(response.url).netloc:
                    self.crawl(url, 
                              callback=self.detail_page,
                              fetch_type='spa')
    
    @config(priority=2)
    def detail_page(self, response):
        # SPAフレームワークの情報を表示
        framework = response.get('framework', 'Unknown')
        
        # ページタイトルを取得
        title = response.doc('title').text()
        
        # メタディスクリプションを取得
        meta_description = response.doc('meta[name="description"]').attr.content
        
        # メインコンテンツを取得
        main_content = response.doc('main').text() or response.doc('#main').text() or response.doc('.main').text()
        
        # フレームワーク固有の要素を抽出
        framework_specific_content = None
        if framework == 'react':
            # Reactの場合、rootコンテナを探す
            framework_specific_content = response.doc('#root').html() or response.doc('[data-reactroot]').html()
        elif framework == 'vue':
            # Vueの場合、appコンテナを探す
            framework_specific_content = response.doc('#app').html()
        elif framework == 'angular':
            # Angularの場合、app-rootを探す
            framework_specific_content = response.doc('app-root').html()
        
        return {
            "url": response.url,
            "title": title,
            "framework": framework,
            "meta_description": meta_description,
            "main_content_length": len(main_content) if main_content else 0,
            "framework_specific_content_length": len(framework_specific_content) if framework_specific_content else 0,
        }
