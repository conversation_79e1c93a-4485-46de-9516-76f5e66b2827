# webui-nextから認証付きAPIサーバーへの接続方法

## 概要

webui-nextから認証付きのポート5000番のpyspider APIサーバーに接続するための設定方法を説明します。

## 認証設定の方法

### 1. 環境変数による設定（推奨）

`.env.local`ファイルを作成して認証情報を設定：

```bash
# pyspider API認証設定
NEXT_PUBLIC_PYSPIDER_API_URL=http://localhost:5000
NEXT_PUBLIC_PYSPIDER_USERNAME=admin
NEXT_PUBLIC_PYSPIDER_PASSWORD=PySpider2024!SecurePass#
```

### 2. UIからの動的設定

1. webui-nextのナビゲーションバーの歯車アイコン（⚙️）をクリック
2. 認証設定ダイアログが開きます
3. 以下の情報を入力：
   - **API URL**: `http://localhost:5000`
   - **ユーザー名**: `admin`
   - **パスワード**: `PySpider2024!SecurePass#`
4. 「接続テスト」ボタンで接続を確認
5. 「保存」ボタンで設定を保存

## 認証の仕組み

### Basic認証

webui-nextは以下の方式でAPIサーバーに認証します：

```javascript
// Basic認証ヘッダーの生成
const credentials = btoa(`${username}:${password}`);
const authHeader = `Basic ${credentials}`;

// APIリクエスト
const response = await fetch('http://localhost:5000/api/v2/projects-create', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': authHeader
  },
  body: JSON.stringify(projectData)
});
```

### 認証フロー

1. **設定読み込み**: ローカルストレージまたは環境変数から認証情報を取得
2. **ヘッダー生成**: Basic認証ヘッダーを動的に生成
3. **API呼び出し**: 認証ヘッダー付きでAPIリクエストを送信
4. **レスポンス処理**: 認証エラー（401）の場合は適切なエラーメッセージを表示

## 対応API

現在、以下のAPIが認証付きで利用可能です：

### プロジェクト管理
- `POST /api/v2/projects-create` - プロジェクト作成
- `GET /api/v2/projects` - プロジェクト一覧取得
- `GET /api/v2/projects/{name}` - プロジェクト詳細取得
- `PUT /api/v2/projects/{name}` - プロジェクト更新
- `DELETE /api/v2/projects/{name}` - プロジェクト削除

### テスト用
- `GET /api/v2/test` - 接続テスト

## エラーハンドリング

### 認証エラー（401 Unauthorized）

```json
{
  "error": "Authorization header required",
  "status": "error",
  "code": "AUTH_HEADER_MISSING",
  "timestamp": 1748060628.9324925
}
```

### 認証情報不正（401 Unauthorized）

```json
{
  "error": "Invalid credentials",
  "status": "error", 
  "code": "INVALID_CREDENTIALS",
  "timestamp": 1748060628.9324925
}
```

## トラブルシューティング

### 1. 接続テストが失敗する場合

- APIサーバーが起動しているか確認
- ポート5000番がアクセス可能か確認
- 認証情報が正しいか確認

### 2. CORS エラーが発生する場合

pyspiderサーバーのCORS設定を確認してください：

```python
# pyspider/webui/app.py
from flask_cors import CORS

app = Flask(__name__)
CORS(app, origins=['http://localhost:3000'])  # webui-nextのURL
```

### 3. プロジェクト作成が失敗する場合

- プロジェクト名の形式を確認（英数字、アンダースコア、ハイフンのみ）
- プロジェクト名の長さを確認（50文字以下）
- 重複するプロジェクト名でないか確認

## セキュリティ考慮事項

1. **パスワードの管理**: 本番環境では強力なパスワードを使用
2. **HTTPS使用**: 本番環境ではHTTPSを使用してBasic認証を保護
3. **環境変数**: 認証情報は環境変数で管理し、コードにハードコードしない
4. **ローカルストレージ**: ブラウザのローカルストレージに保存される認証情報に注意

## 開発者向け情報

### 認証設定の拡張

新しい認証方式を追加する場合は、`src/lib/api.js`の`createAuthHeader`関数を修正してください：

```javascript
const createAuthHeader = () => {
  const config = getAuthConfig();
  
  // Bearer Token認証の例
  if (config.token) {
    return `Bearer ${config.token}`;
  }
  
  // Basic認証（デフォルト）
  const credentials = btoa(`${config.username}:${config.password}`);
  return `Basic ${credentials}`;
};
```

### 新しいAPIエンドポイントの追加

認証付きAPIエンドポイントを追加する場合：

```javascript
// 新しいAPI関数の例
const newApiFunction = async (data) => {
  const authenticatedApiClient = createAuthenticatedApiClient();
  const response = await authenticatedApiClient.post('/api/v2/new-endpoint', data);
  return response.data;
};
```
