const express = require('express');
const cors = require('cors');
const bodyParser = require('body-parser');

// モックデータをインポート
const {
  mockProjects,
  mockTaskCounter,
  mockActiveTasks,
  mockMetrics,
  mockComponentsStatus
} = require('./src/lib/mockData');

const app = express();
const port = 5000;

// CORSを有効にする
app.use(cors());

// JSONリクエストを解析する
app.use(bodyParser.json());

// ルートエンドポイント
app.get('/', (req, res) => {
  res.json({ message: 'PySpider Mock API Server' });
});

// プロジェクト一覧を取得
app.get('/index-v2/projects', (req, res) => {
  res.json(mockProjects);
});

// プロジェクトの詳細を取得
app.get('/api/project/:projectName', (req, res) => {
  const { projectName } = req.params;
  const project = mockProjects.find(p => p.name === projectName);

  if (project) {
    res.json(project);
  } else {
    res.status(404).json({ error: 'Project not found' });
  }
});

// プロジェクトを実行
app.post('/api/run', (req, res) => {
  const { project: projectName } = req.body;
  const index = mockProjects.findIndex(p => p.name === projectName);

  if (index !== -1) {
    mockProjects[index].status = 'RUNNING';
    mockProjects[index].updatetime = Math.floor(Date.now() / 1000);
    res.json({ result: 'success' });
  } else {
    res.status(404).json({ error: 'Project not found' });
  }
});

// プロジェクトを一時停止
app.post('/api/pause', (req, res) => {
  const { project: projectName } = req.body;
  const index = mockProjects.findIndex(p => p.name === projectName);

  if (index !== -1) {
    mockProjects[index].status = 'PAUSED';
    mockProjects[index].updatetime = Math.floor(Date.now() / 1000);
    res.json({ result: 'success' });
  } else {
    res.status(404).json({ error: 'Project not found' });
  }
});

// プロジェクトを再開
app.post('/api/resume', (req, res) => {
  const { project: projectName } = req.body;
  const index = mockProjects.findIndex(p => p.name === projectName);

  if (index !== -1) {
    mockProjects[index].status = 'RUNNING';
    mockProjects[index].updatetime = Math.floor(Date.now() / 1000);
    res.json({ result: 'success' });
  } else {
    res.status(404).json({ error: 'Project not found' });
  }
});

// アクティブなタスクを取得
app.get('/dashboard-active-tasks', (req, res) => {
  res.json(mockActiveTasks);
});

// タスクの詳細を取得
app.get('/task/:taskId', (req, res) => {
  const { taskId } = req.params;
  let projectName, id;

  // .jsonで終わる場合は除去
  const cleanTaskId = taskId.endsWith('.json') ? taskId.slice(0, -5) : taskId;

  if (cleanTaskId.includes(':')) {
    [projectName, id] = cleanTaskId.split(':');
  } else {
    id = cleanTaskId;
  }

  console.log(`Fetching task with projectName=${projectName}, id=${id}`);

  // モックタスクデータを生成
  const mockTask = {
    taskid: id,
    project: projectName || 'unknown',
    url: `https://example.com/${projectName || 'unknown'}/page/${id}`,
    status: 'SUCCESS',
    updatetime: Date.now() / 1000 - Math.floor(Math.random() * 86400),
    result: {
      title: `Example Task ${id}`,
      content: `This is example content for task ${id}`
    }
  };

  res.json(mockTask);
});

// APIv2: タスクの詳細を取得
app.get('/api/v2/task/:taskId', (req, res) => {
  const { taskId } = req.params;
  let projectName, id;

  // .jsonで終わる場合は除去
  const cleanTaskId = taskId.endsWith('.json') ? taskId.slice(0, -5) : taskId;

  if (cleanTaskId.includes(':')) {
    [projectName, id] = cleanTaskId.split(':');
  } else {
    id = cleanTaskId;
  }

  console.log(`Fetching task with projectName=${projectName}, id=${id} via APIv2`);

  // モックタスクデータを生成
  const mockTask = {
    taskid: id,
    project: projectName || 'unknown',
    url: `https://example.com/${projectName || 'unknown'}/page/${id}`,
    status: 'SUCCESS',
    updatetime: Date.now() / 1000 - Math.floor(Math.random() * 86400),
    result: {
      title: `Example Task ${id}`,
      content: `This is example content for task ${id}`
    }
  };

  res.json(mockTask);
});

// 標準API: タスクの詳細を取得
app.get('/api/task/:taskId', (req, res) => {
  const { taskId } = req.params;
  let projectName, id;

  // .jsonで終わる場合は除去
  const cleanTaskId = taskId.endsWith('.json') ? taskId.slice(0, -5) : taskId;

  if (cleanTaskId.includes(':')) {
    [projectName, id] = cleanTaskId.split(':');
  } else {
    id = cleanTaskId;
  }

  console.log(`Fetching task with projectName=${projectName}, id=${id} via standard API`);

  // モックタスクデータを生成
  const mockTask = {
    taskid: id,
    project: projectName || 'unknown',
    url: `https://example.com/${projectName || 'unknown'}/page/${id}`,
    status: 'SUCCESS',
    updatetime: Date.now() / 1000 - Math.floor(Math.random() * 86400),
    result: {
      title: `Example Task ${id}`,
      content: `This is example content for task ${id}`
    }
  };

  res.json(mockTask);
});

// タスクのカウンター情報を取得
app.get('/counter', (req, res) => {
  res.json(mockTaskCounter);
});

// キューの状態を取得
app.get('/queues', (req, res) => {
  res.json({
    scheduler: {
      queues: {
        all: mockMetrics.scheduler.queue_size,
        active: mockMetrics.scheduler.processing_tasks,
        pending: mockMetrics.scheduler.pending_tasks
      }
    }
  });
});

// メトリクスデータを取得
app.get('/metrics', (req, res) => {
  res.json(mockMetrics);
});

// コンポーネントの状態を取得
app.get('/api/components/status', (req, res) => {
  res.json(mockComponentsStatus);
});

// 平均処理時間を取得
app.get('/api/avg_time', (req, res) => {
  res.json({
    avg_time: 0.5, // 秒単位
    avg_time_24h: 0.45
  });
});

// ヘルスチェック情報を取得
app.get('/metrics/health', (req, res) => {
  res.json({
    status: 'ok',
    components: {
      scheduler: 'ok',
      fetcher: 'ok',
      processor: 'ok',
      result_worker: 'ok',
      puppeteer_fetcher: 'warning'
    }
  });
});

// APIv2: メトリクスデータを取得
app.get('/api/v2/metrics', (req, res) => {
  res.json(mockMetrics);
});

// APIv2: タスクのカウンター情報を取得
app.get('/api/v2/counter', (req, res) => {
  const { project } = req.query;

  if (project && mockTaskCounter[project]) {
    // 特定のプロジェクトのカウンター情報を返す
    const projectCounter = mockTaskCounter[project];
    res.json({
      success: projectCounter.all.success || 0,
      failed: projectCounter.all.failed || 0,
      pending: projectCounter.all.pending || 0,
      task: projectCounter.all.task || 0,
      paused: projectCounter.paused || false
    });
  } else {
    // すべてのプロジェクトのカウンター情報を返す
    res.json(mockTaskCounter);
  }
});

// APIv2: プロジェクトを実行
app.post('/api/v2/run', (req, res) => {
  const { project: projectName } = req.body;
  const index = mockProjects.findIndex(p => p.name === projectName);

  if (index !== -1) {
    mockProjects[index].status = 'RUNNING';
    mockProjects[index].updatetime = Math.floor(Date.now() / 1000);
    res.json({ result: 'success' });
  } else {
    res.status(404).json({ error: 'Project not found' });
  }
});

// APIv2: プロジェクトを一時停止
app.post('/api/v2/pause', (req, res) => {
  const { project: projectName } = req.body;
  const index = mockProjects.findIndex(p => p.name === projectName);

  if (index !== -1) {
    mockProjects[index].status = 'PAUSED';
    mockProjects[index].updatetime = Math.floor(Date.now() / 1000);
    res.json({ result: 'success' });
  } else {
    res.status(404).json({ error: 'Project not found' });
  }
});

// APIv2: すべてのプロジェクトの結果を取得
app.get('/api/v2/results', (req, res) => {
  const offset = parseInt(req.query.offset || '0', 10);
  const limit = parseInt(req.query.limit || '20', 10);
  const project = req.query.project;

  console.log(`Fetching results with offset=${offset}, limit=${limit}${project ? `, project=${project}` : ''}`);

  // モックデータを生成
  const results = Array.from({ length: Math.min(limit, 100) }, (_, i) => {
    const index = offset + i;
    const projectName = project || `project_${Math.floor(Math.random() * 5)}`;

    return {
      _id: `result_${index}_${Date.now()}`,
      taskid: `task_${index}_${Date.now()}`,
      url: `https://example.com/page/${index}`,
      result: {
        title: `Example Page ${index}`,
        content: `This is example content for page ${index}`
      },
      updatetime: Date.now() / 1000 - Math.floor(Math.random() * 86400),
      project: projectName
    };
  });

  res.json({
    results: results,
    total: 100
  });
});

// APIv2: 特定のプロジェクトの結果を取得
app.get('/api/v2/results/:projectName', (req, res) => {
  const { projectName } = req.params;
  const offset = parseInt(req.query.offset || '0', 10);
  const limit = parseInt(req.query.limit || '20', 10);

  console.log(`Fetching results for project ${projectName} with offset=${offset}, limit=${limit}`);

  // プロジェクトが存在するか確認（2025は特別に許可）
  const project = projectName === '2025' ? { name: '2025' } : mockProjects.find(p => p.name === projectName);

  if (!project) {
    return res.status(404).json({ error: 'Project not found' });
  }

  // モックデータを生成
  const results = Array.from({ length: Math.min(limit, 100) }, (_, i) => {
    const index = offset + i;

    return {
      _id: `result_${index}_${Date.now()}`,
      taskid: `task_${index}_${Date.now()}`,
      url: `https://example.com/${projectName}/page/${index}`,
      result: {
        title: `${projectName} Page ${index}`,
        content: `This is example content for ${projectName} page ${index}`
      },
      updatetime: Date.now() / 1000 - Math.floor(Math.random() * 86400),
      project: projectName
    };
  });

  res.json({
    results: results,
    total: 100
  });
});

// サーバーを起動
app.listen(port, () => {
  console.log(`PySpider Mock API Server is running on http://localhost:${port}`);
});
