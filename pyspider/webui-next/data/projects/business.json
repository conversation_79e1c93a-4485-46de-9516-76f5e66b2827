{"name": "business", "script": "#!/usr/bin/env python\r\n# -*- coding: utf-8 -*-\r\nimport re\r\nfrom pyspider.libs.base_handler import *\r\n\r\nclass Handler(BaseHandler):\r\n    crawl_config = {\r\n    }\r\n\r\n    @every(minutes=24 * 60)\r\n    def on_start(self):\r\n        self.crawl('https://news.yahoo.co.jp/categories/business', callback=self.index_page)\r\n\r\n    @config(age=10 * 24 * 60 * 60)\r\n    def index_page(self, response):\r\n        for each in response.doc('a[href^=\"http\"]').items():\r\n            self.crawl(each.attr.href, callback=self.detail_page)\r\n\r\n    @config(priority=2)\r\n    def detail_page(self, response):\r\n        return {\r\n            \"url\": response.url,\r\n            \"title\": response.doc('title').text(),\r\n        }\r\n", "group": "default", "status": "TODO", "rate": "1", "burst": "10", "updatetime": 1747476969.587}