# PySpider WebUI (Next.js + Tailwind CSS)

PySpiderのモダンなWebUIインターフェース。Next.jsとTailwind CSSを使用して構築されています。

## 機能

- モダンなUI/UXデザイン
- レスポンシブデザイン（モバイル対応）
- ダークモード対応
- リアルタイムメトリクスとチャート
- プロジェクト管理
- タスク監視
- システム状態の可視化

## 技術スタック

- [Next.js](https://nextjs.org/) - Reactフレームワーク
- [Tailwind CSS](https://tailwindcss.com/) - ユーティリティファーストCSSフレームワーク
- [Headless UI](https://headlessui.dev/) - アクセシブルなUIコンポーネント
- [Heroicons](https://heroicons.com/) - アイコンセット
- [Chart.js](https://www.chartjs.org/) - データ可視化
- [SWR](https://swr.vercel.app/) - データフェッチング
- [Axios](https://axios-http.com/) - HTTPクライアント

## 開発環境のセットアップ

### 前提条件

- Node.js 18.x以上
- npm 9.x以上
- PySpider 0.5.0以上（バックエンドAPI）

### インストール

```bash
# 依存関係のインストール
cd pyspider/webui-next
npm install

# 開発サーバーの起動
npm run dev
```

開発サーバーが起動したら、ブラウザで[http://localhost:3000](http://localhost:3000)または[http://localhost:3001](http://localhost:3001)にアクセスしてください。

### PySpider コンポーネントの起動

PySpider の各コンポーネントを個別に起動できます：

```bash
# スケジューラーを起動
npm run pyspider:scheduler

# フェッチャーを起動
npm run pyspider:fetcher

# プロセッサーを起動
npm run pyspider:processor

# WebUI を起動
npm run pyspider:webui

# Result Worker を起動
npm run pyspider:result-worker

# すべてのコンポーネントを一度に起動（PySpider の all コマンド）
npm run pyspider:all
```

### すべてを一度に起動

Next.js の開発サーバーと PySpider のすべてのコンポーネントを一度に起動します：

```bash
npm run start:all
```

### 本番用ビルド

```bash
# 本番用ビルドの作成
npm run build

# 本番サーバーの起動
npm run start
```

## PySpiderバックエンドとの統合

このWebUIは、PySpiderのAPIエンドポイントと通信するように設計されています。デフォルトでは、開発環境では`http://localhost:5000`のPySpiderバックエンドと通信します。

本番環境では、Next.jsアプリケーションとPySpiderバックエンドを同じドメインで提供することをお勧めします。これにより、CORS問題を回避できます。

### 設定

バックエンドのURLを変更するには、`next.config.js`ファイルの`rewrites`セクションを編集します。

### トラブルシューティング

#### フェッチャーが動作しない

フェッチャーが起動されていない可能性があります。以下のコマンドでフェッチャーを起動してください：

```bash
npm run pyspider:fetcher
```

#### タスクが処理されない

プロセッサーが起動されていない可能性があります。以下のコマンドでプロセッサーを起動してください：

```bash
npm run pyspider:processor
```

#### 結果が保存されない

Result Worker が起動されていない可能性があります。以下のコマンドで Result Worker を起動してください：

```bash
npm run pyspider:result-worker
```

## ディレクトリ構造

```
pyspider/webui-next/
├── public/              # 静的ファイル
├── src/
│   ├── app/             # Next.js App Router
│   ├── components/      # Reactコンポーネント
│   ├── lib/             # ユーティリティ関数とAPIクライアント
│   └── styles/          # グローバルスタイル
├── next.config.js       # Next.js設定
├── tailwind.config.js   # Tailwind CSS設定
└── package.json         # 依存関係と scripts
```

## ライセンス

このプロジェクトはPySpiderと同じライセンス（Apache License 2.0）の下で提供されています。
