{"name": "pyspid<PERSON>-<PERSON><PERSON>", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 3000", "build": "next build", "start": "next start -p 3000", "lint": "next lint"}, "dependencies": {"@codemirror/lang-python": "^6.2.0", "@codemirror/theme-one-dark": "^6.1.2", "@headlessui/react": "^2.2.0", "@heroicons/react": "^2.2.0", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@uiw/react-codemirror": "^4.23.12", "axios": "^1.7.9", "chart.js": "^4.4.6", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cors": "^2.8.5", "express": "^4.21.2", "express-rate-limit": "^7.5.0", "helmet": "^8.0.0", "lucide-react": "^0.513.0", "next": "^15.1.3", "next-themes": "^0.4.6", "react": "^19.0.0", "react-chartjs-2": "^5.2.0", "react-dom": "^19.0.0", "react-tooltip": "^5.28.1", "swr": "^2.2.4", "tailwind-merge": "^3.3.0"}, "devDependencies": {"@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "autoprefixer": "^10.4.20", "babel-plugin-react-compiler": "^19.1.0-rc.2", "concurrently": "^9.1.0", "eslint": "^9.17.0", "eslint-config-next": "^15.1.3", "postcss": "^8.5.1", "tailwindcss": "^3.4.17", "typescript": "^5.7.2"}}