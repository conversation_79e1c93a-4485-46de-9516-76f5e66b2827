/** @type {import('next').NextConfig} */
const nextConfig = {
  // React Strict Mode を有効化
  reactStrictMode: true,

  // Next.js 15.x の新機能を有効化
  experimental: {
    // React 19 の新機能をサポート
    reactCompiler: true, // React 19 コンパイラを有効化
  },

  // Turbopack設定（安定版）
  turbopack: {
    rules: {
      '*.svg': {
        loaders: ['@svgr/webpack'],
        as: '*.js',
      },
    },
  },

  // TypeScript設定
  typescript: {
    // 型チェックエラーでもビルドを続行
    ignoreBuildErrors: false,
  },

  // ESLint設定
  eslint: {
    // ESLintエラーでもビルドを続行
    ignoreDuringBuilds: false,
  },

  // 画像最適化
  images: {
    domains: ['localhost'],
    formats: ['image/webp', 'image/avif'],
  },

  // セキュリティヘッダー
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'Referrer-Policy',
            value: 'origin-when-cross-origin',
          },
          {
            key: 'Permissions-Policy',
            value: 'camera=(), microphone=(), geolocation=()',
          },
        ],
      },
    ];
  },

  // API プロキシ設定
  async rewrites() {
    return [
      {
        source: '/api/:path*',
        destination: 'http://localhost:5000/api/:path*',
      },
      {
        source: '/api/v2/:path*',
        destination: 'http://localhost:5000/api/v2/:path*',
      },
      {
        source: '/api/v2/metrics',
        destination: 'http://localhost:5000/api/v2/metrics',
      },
      {
        source: '/api/v2/counter',
        destination: 'http://localhost:5000/api/v2/counter',
      },
      {
        source: '/prometheus/:path*',
        destination: 'http://localhost:5000/prometheus/:path*',
      },
      {
        source: '/metrics',
        destination: 'http://localhost:5000/metrics',
      },
      {
        source: '/counter',
        destination: 'http://localhost:5000/counter',
      },
      {
        source: '/dashboard-active-tasks',
        destination: 'http://localhost:5000/dashboard-active-tasks',
      },
      {
        source: '/index-v2/projects',
        destination: 'http://localhost:5000/index-v2/projects',
      },
      {
        source: '/debug/:path*',
        destination: 'http://localhost:5000/debug/:path*',
      },
      {
        source: '/api/debug/:path*',
        destination: 'http://localhost:5000/api/debug/:path*',
      },
      {
        source: '/update',
        destination: 'http://localhost:5000/update',
      },
      {
        source: '/run',
        destination: 'http://localhost:5000/run',
      },
      {
        source: '/static/:path*',
        destination: 'http://localhost:5000/static/:path*',
      },
    ]
  },

  // パフォーマンス最適化
  compiler: {
    // 本番環境でのconsole.log削除
    removeConsole: process.env.NODE_ENV === 'production',
  },

  // 環境変数
  env: {
    CUSTOM_KEY: 'pyspiderNX2',
    API_BASE_URL: process.env.API_BASE_URL || 'http://localhost:5000',
  },

  // 出力設定
  output: 'standalone',
}

module.exports = nextConfig
