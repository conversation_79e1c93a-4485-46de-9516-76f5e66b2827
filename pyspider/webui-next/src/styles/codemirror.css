.cm-editor {
  height: 100% !important;
  width: 100% !important;
}

.cm-scroller {
  overflow: auto;
  height: 100% !important;
}

.cm-content {
  font-family: monospace;
  font-size: 14px;
  line-height: 1.5;
}

.cm-gutters {
  border-right: 1px solid #ddd;
}

.dark .cm-gutters {
  border-right: 1px solid #444;
  background-color: #1e1e1e;
}

.dark .cm-content {
  color: #d4d4d4;
}

.dark .cm-cursor {
  border-left: 1px solid #d4d4d4;
}

.dark .cm-activeLine {
  background-color: rgba(255, 255, 255, 0.1);
}

.dark .cm-activeLineGutter {
  background-color: rgba(255, 255, 255, 0.1);
}

.dark .cm-selectionMatch {
  background-color: rgba(255, 255, 255, 0.2);
}

.dark .cm-matchingBracket {
  background-color: rgba(255, 255, 255, 0.3);
}

.dark .cm-gutterElement {
  color: #858585;
}
