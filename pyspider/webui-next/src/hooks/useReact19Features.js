/**
 * React 19の新機能を活用するカスタムフック
 */

import { useState, useCallback, useMemo, useTransition, use } from 'react';

/**
 * React 19のuse()フックを活用したデータフェッチ
 */
export function useAsyncData(promise) {
  try {
    // React 19のuse()フックでPromiseを直接処理
    return { data: use(promise), loading: false, error: null };
  } catch (error) {
    if (error instanceof Promise) {
      // まだロード中の場合
      return { data: null, loading: true, error: null };
    }
    // エラーが発生した場合
    return { data: null, loading: false, error };
  }
}

/**
 * React 19の最適化されたstate管理
 */
export function useOptimizedState(initialValue) {
  const [state, setState] = useState(initialValue);
  
  // React 19では自動的に最適化されるが、明示的に最適化
  const optimizedSetState = useCallback((newValue) => {
    setState(prevState => {
      // 値が同じ場合は更新をスキップ
      if (Object.is(prevState, newValue)) {
        return prevState;
      }
      return typeof newValue === 'function' ? newValue(prevState) : newValue;
    });
  }, []);
  
  return [state, optimizedSetState];
}

/**
 * React 19のTransition機能を活用した非同期処理
 */
export function useAsyncTransition() {
  const [isPending, startTransition] = useTransition();
  
  const executeAsync = useCallback(async (asyncFunction) => {
    return new Promise((resolve, reject) => {
      startTransition(async () => {
        try {
          const result = await asyncFunction();
          resolve(result);
        } catch (error) {
          reject(error);
        }
      });
    });
  }, [startTransition]);
  
  return { isPending, executeAsync };
}

/**
 * React 19の自動メモ化を活用した計算処理
 */
export function useAutoMemoizedValue(computeValue, dependencies) {
  // React 19では多くの場合自動的にメモ化されるが、
  // 重い計算の場合は明示的にuseMemoを使用
  return useMemo(() => {
    return computeValue();
  }, dependencies);
}

/**
 * React 19対応のフォーム状態管理
 */
export function useFormState(initialState = {}) {
  const [formState, setFormState] = useOptimizedState(initialState);
  const [errors, setErrors] = useOptimizedState({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  const updateField = useCallback((fieldName, value) => {
    setFormState(prev => ({
      ...prev,
      [fieldName]: value
    }));
    
    // エラーをクリア
    if (errors[fieldName]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[fieldName];
        return newErrors;
      });
    }
  }, [setFormState, setErrors, errors]);
  
  const setFieldError = useCallback((fieldName, error) => {
    setErrors(prev => ({
      ...prev,
      [fieldName]: error
    }));
  }, [setErrors]);
  
  const resetForm = useCallback(() => {
    setFormState(initialState);
    setErrors({});
    setIsSubmitting(false);
  }, [setFormState, setErrors, initialState]);
  
  const submitForm = useCallback(async (submitFunction) => {
    setIsSubmitting(true);
    setErrors({});
    
    try {
      const result = await submitFunction(formState);
      return result;
    } catch (error) {
      if (error.fieldErrors) {
        setErrors(error.fieldErrors);
      } else {
        setErrors({ general: error.message || 'フォームの送信に失敗しました' });
      }
      throw error;
    } finally {
      setIsSubmitting(false);
    }
  }, [formState, setErrors]);
  
  return {
    formState,
    errors,
    isSubmitting,
    updateField,
    setFieldError,
    resetForm,
    submitForm
  };
}

/**
 * React 19のSuspense境界を活用したエラーハンドリング
 */
export function useErrorBoundary() {
  const [error, setError] = useState(null);
  
  const resetError = useCallback(() => {
    setError(null);
  }, []);
  
  const captureError = useCallback((error) => {
    setError(error);
  }, []);
  
  // React 19では自動的にエラー境界が改善される
  if (error) {
    throw error;
  }
  
  return { captureError, resetError };
}

/**
 * React 19の並行機能を活用したデータ管理
 */
export function useConcurrentData(fetchFunction, dependencies = []) {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const { isPending, executeAsync } = useAsyncTransition();
  
  const fetchData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      
      const result = await executeAsync(fetchFunction);
      setData(result);
    } catch (err) {
      setError(err);
    } finally {
      setLoading(false);
    }
  }, [fetchFunction, executeAsync]);
  
  // 依存関係が変更された時にデータを再取得
  useMemo(() => {
    fetchData();
  }, dependencies);
  
  const refetch = useCallback(() => {
    fetchData();
  }, [fetchData]);
  
  return {
    data,
    loading: loading || isPending,
    error,
    refetch
  };
}

/**
 * React 19のパフォーマンス最適化フック
 */
export function usePerformanceOptimization() {
  const [renderCount, setRenderCount] = useState(0);
  const [lastRenderTime, setLastRenderTime] = useState(Date.now());
  
  // レンダリング回数を追跡
  useMemo(() => {
    setRenderCount(prev => prev + 1);
    setLastRenderTime(Date.now());
  }, []);
  
  const getPerformanceMetrics = useCallback(() => {
    return {
      renderCount,
      lastRenderTime,
      averageRenderInterval: renderCount > 1 ? (Date.now() - lastRenderTime) / renderCount : 0
    };
  }, [renderCount, lastRenderTime]);
  
  return { getPerformanceMetrics };
}
