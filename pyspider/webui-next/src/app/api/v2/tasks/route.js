import { NextResponse } from 'next/server';

// 静的エクスポートのためのオプションを設定
export const dynamic = 'force-dynamic';

/**
 * タスク一覧を取得するAPIエンドポイント
 * @param {Request} request - リクエストオブジェクト
 * @returns {Promise<NextResponse>} - レスポンスオブジェクト
 */
export async function GET(request) {
  try {
    // URLからクエリパラメータを取得
    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status') || 'all';
    const limit = parseInt(searchParams.get('limit') || '10', 10);
    const offset = parseInt(searchParams.get('offset') || '0', 10);
    const project = searchParams.get('project');

    console.log(`Fetching tasks with status=${status}, limit=${limit}, offset=${offset}, project=${project}`);

    // pyspiderのAPIサーバーからタスク情報を取得（API v2エンドポイントを使用）
    let apiUrl = `http://localhost:5000/api/v2/tasks?status=${status}&limit=${limit}&offset=${offset}`;
    if (project) {
      apiUrl += `&project=${project}`;
    }

    try {
      console.log(`Fetching tasks from ${apiUrl}`);

      // 認証ヘッダーを追加
      const authConfig = {
        username: process.env.NEXT_PUBLIC_PYSPIDER_USERNAME || 'admin',
        password: process.env.NEXT_PUBLIC_PYSPIDER_PASSWORD || 'PySpider2024!SecurePass#'
      };

      const credentials = Buffer.from(`${authConfig.username}:${authConfig.password}`).toString('base64');
      const authHeader = `Basic ${credentials}`;

      const response = await fetch(apiUrl, {
        headers: {
          'Authorization': authHeader,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`API request failed with status ${response.status}`);
      }

      const data = await response.json();

      // API v2のレスポンス形式を処理
      let tasks = [];
      if (data && data.data && data.data.tasks && Array.isArray(data.data.tasks)) {
        tasks = data.data.tasks;
      } else if (data && Array.isArray(data)) {
        tasks = data;
      }

      console.log(`Received ${tasks.length} tasks from pyspider API v2`);

      // タスク情報を返す
      return NextResponse.json(tasks);
    } catch (error) {
      console.error('Error fetching tasks from pyspider API:', error);

      // pyspiderのAPIサーバーからタスク情報を取得できない場合は、モックデータを返す
      console.log('Returning mock task data');

      // モックデータを生成
      const mockTasks = generateMockTasks(status, limit, project);

      return NextResponse.json(mockTasks);
    }
  } catch (error) {
    console.error('Error in /api/v2/tasks endpoint:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}

/**
 * モックタスクデータを生成する関数
 * @param {string} status - タスクのステータス
 * @param {number} limit - 取得するタスクの数
 * @param {string} project - プロジェクト名
 * @returns {Array} - モックタスクデータ
 */
function generateMockTasks(status, limit, project) {
  const tasks = [];
  const statuses = status === 'all' ? ['pending', 'active', 'success', 'failed'] : [status];
  const projects = project ? [project] : ['test_project', 'example_project', 'demo_project'];

  for (let i = 0; i < limit; i++) {
    const randomStatus = statuses[Math.floor(Math.random() * statuses.length)];
    const randomProject = projects[Math.floor(Math.random() * projects.length)];

    tasks.push({
      taskid: `task_${i}_${Date.now()}`,
      project: randomProject,
      url: `https://example.com/page/${i}`,
      status: randomStatus,
      lastcrawltime: Date.now() / 1000 - Math.floor(Math.random() * 86400),
      updatetime: Date.now() / 1000,
      track: {
        fetch: {
          ok: randomStatus !== 'failed',
          time: Math.random() * 0.5,
          status_code: randomStatus !== 'failed' ? 200 : 404
        },
        process: {
          ok: randomStatus !== 'failed',
          time: Math.random() * 0.2,
          follows: Math.floor(Math.random() * 5)
        }
      }
    });
  }

  return tasks;
}
