import { NextResponse } from 'next/server';

/**
 * タスクを再実行するAPIv2ルート
 * @param {Object} request - リクエストオブジェクト
 * @param {Object} params - URLパラメータ
 * @returns {Promise<NextResponse>} レスポンス
 */
export async function POST(request, { params }) {
  try {
    const { id } = params;
    let projectName, taskId;

    // .jsonで終わる場合は除去
    const cleanId = id.endsWith('.json') ? id.slice(0, -5) : id;

    if (cleanId.includes(':')) {
      [projectName, taskId] = cleanId.split(':', 2);
    } else {
      taskId = cleanId;
    }

    console.log(`[APIv2] Rerunning task with ID: ${id}, projectName: ${projectName}, taskId: ${taskId}`);

    // APIサーバーを使わずにモックレスポンスを返す
    console.log(`[APIv2] APIサーバーを使わずにモックレスポンスを返します: ${id}`);

    // モックレスポンスを生成
    const mockResponse = {
      result: 'success',
      taskid: taskId || id,
      project: projectName || 'unknown',
      status: 'ACTIVE',
      message: 'Task rerun initiated'
    };

    // モックレスポンスを返す
    return NextResponse.json(mockResponse);
  } catch (error) {
    console.error('[APIv2] Error in task rerun API route:', error);
    return NextResponse.json({ error: 'タスクの再実行中にエラーが発生しました。' }, { status: 500 });
  }
}
