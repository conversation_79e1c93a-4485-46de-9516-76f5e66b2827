import { NextResponse } from 'next/server';

/**
 * タスク詳細情報を取得するAPIv2ルート
 * @param {Object} request - リクエストオブジェクト
 * @param {Object} params - URLパラメータ
 * @returns {Promise<NextResponse>} レスポンス
 */
export async function GET(request, { params }) {
  try {
    const { id } = params;
    let projectName, taskId;

    // .jsonで終わる場合は除去
    const cleanId = id.endsWith('.json') ? id.slice(0, -5) : id;

    if (cleanId.includes(':')) {
      [projectName, taskId] = cleanId.split(':', 2);
    } else {
      taskId = cleanId;
    }

    console.log(`[APIv2] Fetching task with ID: ${id}, projectName: ${projectName}, taskId: ${taskId}`);

    // 認証ヘッダーを追加
    const authConfig = {
      username: process.env.NEXT_PUBLIC_PYSPIDER_USERNAME || 'admin',
      password: process.env.NEXT_PUBLIC_PYSPIDER_PASSWORD || 'PySpider2024!SecurePass#'
    };

    const credentials = Buffer.from(`${authConfig.username}:${authConfig.password}`).toString('base64');
    const authHeader = `Basic ${credentials}`;

    try {
      // pyspiderのAPIサーバーからタスク詳細を取得（API v2エンドポイントを使用）
      const apiUrl = `http://localhost:5000/api/v2/task/${cleanId}.json`;
      console.log(`[APIv2] Fetching task from ${apiUrl}`);

      const response = await fetch(apiUrl, {
        headers: {
          'Authorization': authHeader,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`API request failed with status ${response.status}`);
      }

      const data = await response.json();
      console.log(`[APIv2] Received task data from pyspider API v2`);

      // タスク詳細を返す
      return NextResponse.json(data);
    } catch (error) {
      console.error('[APIv2] Error fetching task from pyspider API:', error);

      // pyspiderのAPIサーバーからタスク詳細を取得できない場合は、モックデータを返す
      console.log('[APIv2] Returning mock task data');

      // モックタスクデータを生成
      const mockTask = {
        taskid: taskId || id,
        project: projectName || 'unknown',
        url: `https://example.com/${projectName || 'unknown'}/page/${taskId || id}`,
        status: 'SUCCESS',
        updatetime: Date.now() / 1000 - Math.floor(Math.random() * 86400),
        result: {
          title: `Example Task ${taskId || id}`,
          content: `This is example content for task ${taskId || id}`,
          html: `<html><body><h1>Example Task ${taskId || id}</h1><p>This is example content for task ${taskId || id}</p></body></html>`
        }
      };

      // モックデータを返す
      return NextResponse.json(mockTask);
    }
  } catch (error) {
    console.error('[APIv2] Error in task API route:', error);
    return NextResponse.json({ error: 'タスク情報の取得中にエラーが発生しました。' }, { status: 500 });
  }
}
