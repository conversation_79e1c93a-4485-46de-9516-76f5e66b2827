import { NextResponse } from 'next/server';

/**
 * プロジェクトの結果をエクスポートするAPIエンドポイント
 * @param {Request} request - リクエストオブジェクト
 * @param {Object} params - URLパラメータ
 * @returns {Promise<NextResponse>} - レスポンスオブジェクト
 */
export async function GET(request, { params }) {
  try {
    const projectName = params.name;
    const { searchParams } = new URL(request.url);
    const format = searchParams.get('format') || 'json';
    const limit = parseInt(searchParams.get('limit') || '1000', 10);
    
    console.log(`Exporting results for project ${projectName} in ${format} format with limit ${limit}`);

    // pyspiderのAPIサーバーから結果データをエクスポート
    let apiUrl = `http://localhost:5000/api/results/${projectName}/export?format=${format}&limit=${limit}`;

    try {
      console.log(`Fetching export data from ${apiUrl}`);
      const response = await fetch(apiUrl);
      
      if (!response.ok) {
        throw new Error(`API request failed with status ${response.status}`);
      }
      
      // レスポンスの内容タイプを取得
      const contentType = response.headers.get('content-type');
      
      // レスポンスデータを取得
      const data = await response.text();
      
      // 適切なContent-Typeとファイル名を設定
      let fileName = `${projectName}_results`;
      let responseContentType = 'application/json';
      
      if (format === 'csv') {
        fileName += '.csv';
        responseContentType = 'text/csv';
      } else if (format === 'xml') {
        fileName += '.xml';
        responseContentType = 'application/xml';
      } else {
        fileName += '.json';
        responseContentType = 'application/json';
      }
      
      // レスポンスヘッダーを設定
      const headers = new Headers();
      headers.set('Content-Type', responseContentType);
      headers.set('Content-Disposition', `attachment; filename="${fileName}"`);
      
      // エクスポートデータを返す
      return new NextResponse(data, {
        status: 200,
        headers
      });
    } catch (error) {
      console.error(`Error exporting results for project ${projectName}:`, error);
      
      // エラーが発生した場合は、エラーメッセージを返す
      return NextResponse.json({ error: `Failed to export results: ${error.message}` }, { status: 500 });
    }
  } catch (error) {
    console.error(`Error in /api/v2/results/${params.name}/export endpoint:`, error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}
