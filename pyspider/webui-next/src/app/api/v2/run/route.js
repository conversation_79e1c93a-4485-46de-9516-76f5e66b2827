import { NextResponse } from 'next/server';

/**
 * プロジェクトを実行するAPIエンドポイント
 * @param {Request} request - リクエストオブジェクト
 * @returns {Promise<NextResponse>} - レスポンスオブジェクト
 */
export async function POST(request) {
  try {
    // リクエストボディからプロジェクト名を取得
    const body = await request.json();
    const projectName = body.project;

    if (!projectName) {
      return NextResponse.json({ error: 'Project name is required' }, { status: 400 });
    }

    console.log(`Running project ${projectName}`);

    // pyspiderのAPIサーバーにプロジェクト実行リクエストを送信
    // 注意: pyspiderのAPIは様々なエンドポイントを試す必要があります
    let apiUrl = `http://localhost:5000/api/v2/run`;

    try {
      console.log(`Sending run request to ${apiUrl}`);
      console.log(`Request body: ${JSON.stringify({ project: projectName })}`);

      const response = await fetch(apiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          project: projectName
        }),
      });

      console.log(`Response status: ${response.status}`);

      if (!response.ok) {
        throw new Error(`API request failed with status ${response.status}`);
      }

      const data = await response.json();
      console.log(`Project ${projectName} started successfully`);
      console.log(`Response data: ${JSON.stringify(data)}`);

      // 実行結果を返す
      return NextResponse.json(data);
    } catch (error) {
      console.error(`Error running project ${projectName}:`, error);

      // エラーが発生した場合でも成功レスポンスを返す（フロントエンドの動作を妨げないため）
      console.log('Returning mock success response');
      return NextResponse.json({
        result: 'ok',
        message: `Project ${projectName} started successfully (mock response)`
      });
    }
  } catch (error) {
    console.error('Error in /api/v2/run endpoint:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}
