'use client';

import { useState, useEffect } from 'react';
import { metricsApi } from '@/lib/api';
import Loading from '@/components/common/Loading';
import PerformanceOverview from '@/components/performance/PerformanceOverview';
import FunctionStats from '@/components/performance/FunctionStats';
import RequestStats from '@/components/performance/RequestStats';
import TaskStats from '@/components/performance/TaskStats';
import MemorySnapshots from '@/components/performance/MemorySnapshots';
import PrometheusMetrics from '@/components/performance/PrometheusMetrics';

export default function PerformancePage() {
  const [performanceMetrics, setPerformanceMetrics] = useState(null);
  const [functionStats, setFunctionStats] = useState({});
  const [requestStats, setRequestStats] = useState({});
  const [taskStats, setTaskStats] = useState({});
  const [memorySnapshots, setMemorySnapshots] = useState([]);
  const [prometheusMetrics, setPrometheusMetrics] = useState('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [activeTab, setActiveTab] = useState('overview');

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      setLoading(true);
      setError(null);

      // 並列でデータを取得
      const [
        performanceData,
        functionData,
        requestData,
        taskData,
        snapshotsData,
        prometheusData
      ] = await Promise.allSettled([
        metricsApi.getPerformanceMetrics(),
        metricsApi.getFunctionStats(),
        metricsApi.getRequestStats(),
        metricsApi.getTaskStats(),
        metricsApi.getMemorySnapshots(),
        metricsApi.getPrometheusMetrics()
      ]);

      // 結果を処理
      if (performanceData.status === 'fulfilled') {
        setPerformanceMetrics(performanceData.value);
      } else {
        console.error('Error fetching performance metrics:', performanceData.reason);
      }

      if (functionData.status === 'fulfilled') {
        setFunctionStats(functionData.value);
      } else {
        console.error('Error fetching function stats:', functionData.reason);
      }

      if (requestData.status === 'fulfilled') {
        setRequestStats(requestData.value);
      } else {
        console.error('Error fetching request stats:', requestData.reason);
      }

      if (taskData.status === 'fulfilled') {
        setTaskStats(taskData.value);
      } else {
        console.error('Error fetching task stats:', taskData.reason);
      }

      if (snapshotsData.status === 'fulfilled') {
        setMemorySnapshots(snapshotsData.value);
      } else {
        console.error('Error fetching memory snapshots:', snapshotsData.reason);
      }

      if (prometheusData.status === 'fulfilled') {
        setPrometheusMetrics(prometheusData.value);
      } else {
        console.error('Error fetching Prometheus metrics:', prometheusData.reason);
      }
    } catch (err) {
      console.error('Error fetching performance data:', err);
      setError('パフォーマンスデータの取得中にエラーが発生しました。');
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = () => {
    fetchData();
  };

  const handleTakeSnapshot = async () => {
    try {
      setLoading(true);
      const result = await metricsApi.takeMemorySnapshot();
      if (result.error) {
        setError(`メモリスナップショットの取得に失敗しました: ${result.error}`);
      } else {
        // スナップショットを取得した後、メモリスナップショットを再取得
        const snapshotsData = await metricsApi.getMemorySnapshots();
        setMemorySnapshots(snapshotsData);
      }
    } catch (err) {
      console.error('Error taking memory snapshot:', err);
      setError('メモリスナップショットの取得中にエラーが発生しました。');
    } finally {
      setLoading(false);
    }
  };

  const renderTabContent = () => {
    switch (activeTab) {
      case 'overview':
        return <PerformanceOverview performanceMetrics={performanceMetrics} />;
      case 'functions':
        return <FunctionStats functionStats={functionStats} />;
      case 'requests':
        return <RequestStats requestStats={requestStats} />;
      case 'tasks':
        return <TaskStats taskStats={taskStats} />;
      case 'memory':
        return (
          <MemorySnapshots 
            memorySnapshots={memorySnapshots} 
            onTakeSnapshot={handleTakeSnapshot} 
          />
        );
      case 'prometheus':
        return <PrometheusMetrics metricsText={prometheusMetrics} />;
      default:
        return <PerformanceOverview performanceMetrics={performanceMetrics} />;
    }
  };

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">パフォーマンスメトリクス</h1>
        <button
          onClick={handleRefresh}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
        >
          更新
        </button>
      </div>

      {loading && <Loading />}

      {error && (
        <div className="bg-red-50 border-l-4 border-red-400 p-4 mb-6">
          <div className="flex">
            <div className="ml-3">
              <p className="text-sm text-red-700">{error}</p>
            </div>
          </div>
        </div>
      )}

      {!loading && !error && (
        <div>
          <div className="border-b border-gray-200 dark:border-gray-700 mb-6">
            <nav className="-mb-px flex space-x-8">
              <button
                onClick={() => setActiveTab('overview')}
                className={`${
                  activeTab === 'overview'
                    ? 'border-primary-500 text-primary-600 dark:text-primary-400'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
                } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
              >
                概要
              </button>
              <button
                onClick={() => setActiveTab('functions')}
                className={`${
                  activeTab === 'functions'
                    ? 'border-primary-500 text-primary-600 dark:text-primary-400'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
                } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
              >
                関数統計
              </button>
              <button
                onClick={() => setActiveTab('requests')}
                className={`${
                  activeTab === 'requests'
                    ? 'border-primary-500 text-primary-600 dark:text-primary-400'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
                } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
              >
                リクエスト統計
              </button>
              <button
                onClick={() => setActiveTab('tasks')}
                className={`${
                  activeTab === 'tasks'
                    ? 'border-primary-500 text-primary-600 dark:text-primary-400'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
                } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
              >
                タスク統計
              </button>
              <button
                onClick={() => setActiveTab('memory')}
                className={`${
                  activeTab === 'memory'
                    ? 'border-primary-500 text-primary-600 dark:text-primary-400'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
                } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
              >
                メモリ
              </button>
              <button
                onClick={() => setActiveTab('prometheus')}
                className={`${
                  activeTab === 'prometheus'
                    ? 'border-primary-500 text-primary-600 dark:text-primary-400'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
                } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
              >
                Prometheus
              </button>
            </nav>
          </div>

          {renderTabContent()}
        </div>
      )}
    </div>
  );
}
