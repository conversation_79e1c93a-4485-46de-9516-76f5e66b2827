'use client';

import { useState, useEffect } from 'react';
import { alertsApi } from '@/lib/api';
import Loading from '@/components/common/Loading';
import ActiveAlerts from '@/components/alerts/ActiveAlerts';
import AlertHistory from '@/components/alerts/AlertHistory';
import AlertRules from '@/components/alerts/AlertRules';
import AlertChannels from '@/components/alerts/AlertChannels';

export default function AlertsPage() {
  const [activeAlerts, setActiveAlerts] = useState([]);
  const [alertHistory, setAlertHistory] = useState([]);
  const [alertRules, setAlertRules] = useState([]);
  const [alertChannels, setAlertChannels] = useState({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [activeTab, setActiveTab] = useState('active');

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      setLoading(true);
      setError(null);

      // 並列でデータを取得
      const [
        alertsData,
        historyData,
        rulesData,
        channelsData
      ] = await Promise.allSettled([
        alertsApi.getAlerts(),
        alertsApi.getAlertHistory(),
        alertsApi.getAlertRules(),
        alertsApi.getAlertChannels()
      ]);

      // 結果を処理
      if (alertsData.status === 'fulfilled') {
        setActiveAlerts(alertsData.value);
      } else {
        console.error('Error fetching active alerts:', alertsData.reason);
      }

      if (historyData.status === 'fulfilled') {
        setAlertHistory(historyData.value);
      } else {
        console.error('Error fetching alert history:', historyData.reason);
      }

      if (rulesData.status === 'fulfilled') {
        setAlertRules(rulesData.value);
      } else {
        console.error('Error fetching alert rules:', rulesData.reason);
      }

      if (channelsData.status === 'fulfilled') {
        setAlertChannels(channelsData.value);
      } else {
        console.error('Error fetching alert channels:', channelsData.reason);
      }
    } catch (err) {
      console.error('Error fetching alert data:', err);
      setError('アラートデータの取得中にエラーが発生しました。');
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = () => {
    fetchData();
  };

  const handleAddRule = async (rule) => {
    try {
      setLoading(true);
      const result = await alertsApi.addAlertRule(rule);
      if (result.success) {
        // ルールを再取得
        const rulesData = await alertsApi.getAlertRules();
        setAlertRules(rulesData);
      } else {
        setError(`ルールの追加に失敗しました: ${result.error}`);
      }
    } catch (err) {
      console.error('Error adding alert rule:', err);
      setError('アラートルールの追加中にエラーが発生しました。');
    } finally {
      setLoading(false);
    }
  };

  const handleRemoveRule = async (name) => {
    try {
      setLoading(true);
      const result = await alertsApi.removeAlertRule(name);
      if (result.success) {
        // ルールを再取得
        const rulesData = await alertsApi.getAlertRules();
        setAlertRules(rulesData);
      } else {
        setError(`ルールの削除に失敗しました: ${result.error}`);
      }
    } catch (err) {
      console.error('Error removing alert rule:', err);
      setError('アラートルールの削除中にエラーが発生しました。');
    } finally {
      setLoading(false);
    }
  };

  const handleAddChannel = async (name, channel) => {
    try {
      setLoading(true);
      const result = await alertsApi.addAlertChannel(name, channel);
      if (result.success) {
        // チャンネルを再取得
        const channelsData = await alertsApi.getAlertChannels();
        setAlertChannels(channelsData);
      } else {
        setError(`チャンネルの追加に失敗しました: ${result.error}`);
      }
    } catch (err) {
      console.error('Error adding alert channel:', err);
      setError('アラートチャンネルの追加中にエラーが発生しました。');
    } finally {
      setLoading(false);
    }
  };

  const handleRemoveChannel = async (name) => {
    try {
      setLoading(true);
      const result = await alertsApi.removeAlertChannel(name);
      if (result.success) {
        // チャンネルを再取得
        const channelsData = await alertsApi.getAlertChannels();
        setAlertChannels(channelsData);
      } else {
        setError(`チャンネルの削除に失敗しました: ${result.error}`);
      }
    } catch (err) {
      console.error('Error removing alert channel:', err);
      setError('アラートチャンネルの削除中にエラーが発生しました。');
    } finally {
      setLoading(false);
    }
  };

  const renderTabContent = () => {
    switch (activeTab) {
      case 'active':
        return <ActiveAlerts alerts={activeAlerts} />;
      case 'history':
        return <AlertHistory history={alertHistory} />;
      case 'rules':
        return (
          <AlertRules 
            rules={alertRules} 
            onAddRule={handleAddRule} 
            onRemoveRule={handleRemoveRule} 
          />
        );
      case 'channels':
        return (
          <AlertChannels 
            channels={alertChannels} 
            onAddChannel={handleAddChannel} 
            onRemoveChannel={handleRemoveChannel} 
          />
        );
      default:
        return <ActiveAlerts alerts={activeAlerts} />;
    }
  };

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">アラート管理</h1>
        <button
          onClick={handleRefresh}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
        >
          更新
        </button>
      </div>

      {loading && <Loading />}

      {error && (
        <div className="bg-red-50 border-l-4 border-red-400 p-4 mb-6">
          <div className="flex">
            <div className="ml-3">
              <p className="text-sm text-red-700">{error}</p>
            </div>
          </div>
        </div>
      )}

      {!loading && (
        <div>
          <div className="border-b border-gray-200 dark:border-gray-700 mb-6">
            <nav className="-mb-px flex space-x-8">
              <button
                onClick={() => setActiveTab('active')}
                className={`${
                  activeTab === 'active'
                    ? 'border-primary-500 text-primary-600 dark:text-primary-400'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
                } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
              >
                アクティブなアラート
                {activeAlerts.length > 0 && (
                  <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300">
                    {activeAlerts.length}
                  </span>
                )}
              </button>
              <button
                onClick={() => setActiveTab('history')}
                className={`${
                  activeTab === 'history'
                    ? 'border-primary-500 text-primary-600 dark:text-primary-400'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
                } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
              >
                アラート履歴
              </button>
              <button
                onClick={() => setActiveTab('rules')}
                className={`${
                  activeTab === 'rules'
                    ? 'border-primary-500 text-primary-600 dark:text-primary-400'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
                } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
              >
                アラートルール
              </button>
              <button
                onClick={() => setActiveTab('channels')}
                className={`${
                  activeTab === 'channels'
                    ? 'border-primary-500 text-primary-600 dark:text-primary-400'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
                } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
              >
                通知チャンネル
              </button>
            </nav>
          </div>

          {renderTabContent()}
        </div>
      )}
    </div>
  );
}
