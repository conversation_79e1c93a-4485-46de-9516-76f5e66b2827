'use client';

import { useState, useEffect, Suspense } from 'react';
import Link from 'next/link';
import { useRouter, useSearchParams } from 'next/navigation';
import { tasksApi, projectsApi } from '@/lib/api';
import { ArrowPathIcon, FunnelIcon } from '@heroicons/react/24/outline';
import Loading from '@/components/common/Loading';

// 実際のコンテンツコンポーネント
function TasksContent() {
  const router = useRouter();
  const searchParams = useSearchParams();

  const [tasks, setTasks] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [page, setPage] = useState(0);
  const [limit, setLimit] = useState(20);
  const [total, setTotal] = useState(0);

  // プロジェクトフィルタリング用の状態
  const [projects, setProjects] = useState([]);
  const [projectsLoading, setProjectsLoading] = useState(true);
  const [selectedProject, setSelectedProject] = useState(searchParams.get('project') || '');

  // タスクのステータスに応じた色を返す関数
  const getStatusColor = (status) => {
    switch (status) {
      case 'SUCCESS':
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
      case 'FAILED':
        return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
      case 'ACTIVE':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400';
      case 'PENDING':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
    }
  };

  // タスクを取得する関数
  const fetchTasks = async () => {
    try {
      setLoading(true);

      const params = {
        offset: page * limit,
        limit: limit
      };

      console.log('Fetching tasks with params:', params);

      // API v2エンドポイントからタスクを取得
      try {
        console.log('Trying /api/v2/tasks endpoint');
        const response = await fetch(`http://localhost:5000/api/v2/tasks?offset=${params.offset}&limit=${params.limit}`);

        if (!response.ok) {
          throw new Error(`API request failed with status ${response.status}`);
        }

        const data = await response.json();
        console.log('Received tasks data from /api/v2/tasks:', data);

        // API v2の標準レスポンス形式をチェック
        if (data && data.status === 'success' && data.data && Array.isArray(data.data.tasks)) {
          // プロジェクトでフィルタリング
          let filteredTasks = data.data.tasks;
          if (selectedProject) {
            console.log(`Filtering tasks by project: ${selectedProject}`);
            filteredTasks = data.data.tasks.filter(task => task.project === selectedProject);
          }

          console.log(`Filtered tasks: ${filteredTasks.length} tasks`);

          setTasks(filteredTasks);
          setTotal(data.data.total || filteredTasks.length);
          setError(null);
          return;
        } else if (Array.isArray(data)) {
          // 旧形式の配列レスポンスもサポート
          let filteredTasks = data;
          if (selectedProject) {
            console.log(`Filtering tasks by project: ${selectedProject}`);
            filteredTasks = data.filter(task => task.project === selectedProject);
          }

          console.log(`Filtered tasks: ${filteredTasks.length} tasks`);

          setTasks(filteredTasks);
          setTotal(filteredTasks.length);
          setError(null);
          return;
        } else {
          throw new Error('Invalid response format');
        }
      } catch (apiV2Error) {
        console.error('API v2 request failed:', apiV2Error);

        // APIv2からの取得に失敗した場合、tasksApiを使用
        try {
          console.log('Trying tasksApi.getTasks');
          const data = await tasksApi.getTasks(params);
          console.log('Received tasks data via tasksApi:', data);

          if (data && Array.isArray(data)) {
            // プロジェクトでフィルタリング
            let filteredTasks = data;
            if (selectedProject) {
              console.log(`Filtering tasks by project: ${selectedProject}`);
              filteredTasks = data.filter(task => task.project === selectedProject);
            }

            setTasks(filteredTasks);
            setTotal(filteredTasks.length);
            setError(null);
            return;
          } else if (data && data.tasks && Array.isArray(data.tasks)) {
            // プロジェクトでフィルタリング
            let filteredTasks = data.tasks;
            if (selectedProject) {
              console.log(`Filtering tasks by project: ${selectedProject}`);
              filteredTasks = data.tasks.filter(task => task.project === selectedProject);
            }

            setTasks(filteredTasks);
            setTotal(data.total || filteredTasks.length);
            setError(null);
            return;
          } else {
            throw new Error('No valid tasks data received');
          }
        } catch (tasksApiError) {
          console.error('tasksApi request failed:', tasksApiError);
          throw tasksApiError;
        }
      }
    } catch (err) {
      console.error('Error fetching tasks:', err);
      setError('タスク情報の取得中にエラーが発生しました。');
      setTasks([]);
      setTotal(0);
    } finally {
      setLoading(false);
    }
  };

  // プロジェクト一覧を取得する関数
  const fetchProjects = async () => {
    try {
      setProjectsLoading(true);
      const data = await projectsApi.getProjects();
      setProjects(data);
    } catch (err) {
      console.error('Error fetching projects:', err);
    } finally {
      setProjectsLoading(false);
    }
  };

  // プロジェクトフィルターが変更されたときの処理
  const handleProjectChange = (e) => {
    const projectName = e.target.value;
    setSelectedProject(projectName);

    // URLクエリパラメータを更新
    const params = new URLSearchParams(searchParams);
    if (projectName) {
      params.set('project', projectName);
    } else {
      params.delete('project');
    }

    // ページをリセット
    setPage(0);

    // URLを更新
    router.push(`/tasks?${params.toString()}`);
  };

  // 初回レンダリング時にプロジェクト一覧を取得
  useEffect(() => {
    fetchProjects();
  }, []);

  // 初回レンダリング時とページ、選択プロジェクトが変更されたときにタスクを取得
  useEffect(() => {
    fetchTasks();
  }, [page, limit, selectedProject]);

  // ページ変更ハンドラ
  const handlePageChange = (newPage) => {
    if (newPage >= 0 && newPage * limit < total) {
      setPage(newPage);
    }
  };

  return (
    <Suspense fallback={<Loading size="lg" text="タスク情報を読み込み中..." />}>
      <TasksClient />
    </Suspense>
  );
}

// メインのページコンポーネント
export default function TasksPage() {
  return (
    <Suspense fallback={<Loading size="lg" text="ページを読み込み中..." />}>
      <TasksContent />
    </Suspense>
  );
}
