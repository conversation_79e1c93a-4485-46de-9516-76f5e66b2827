'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { tasksApi } from '@/lib/api';
import { ArrowLeftIcon, ArrowPathIcon } from '@heroicons/react/24/outline';
import Loading from '@/components/common/Loading';

export default function TaskDetailPage({ params }) {
  const [task, setTask] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // タスクのステータスに応じた色を返す関数
  const getStatusColor = (status) => {
    switch (status) {
      case 'SUCCESS':
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
      case 'FAILED':
        return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
      case 'ACTIVE':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400';
      case 'PENDING':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
    }
  };

  // タスクを取得する関数
  const fetchTask = async () => {
    try {
      setLoading(true);

      console.log(`Fetching task with ID: ${params.id}`);

      // タスクIDからプロジェクト名とタスクIDを抽出
      let projectName, taskId;
      if (params.id.includes(':')) {
        [projectName, taskId] = params.id.split(':', 2);
      } else {
        // IDが形式に合わない場合は、タスクIDをそのまま使用
        taskId = params.id;
      }

      // APIv2エンドポイントからタスクを取得を優先

      // APIv2エンドポイントからタスクを取得
      try {
        // Next.jsのAPIルートを使用
        const apiV2Url = projectName
          ? `/api/v2/task/${projectName}:${taskId}.json`
          : `/api/v2/task/${params.id}.json`;

        console.log(`Trying to fetch task from Next.js API route: ${apiV2Url}`);
        const response = await fetch(apiV2Url);

        if (response.ok) {
          const data = await response.json();
          console.log('Received task data from Next.js API route:', data);

          if (data && data.taskid) {
            setTask(data);
            setError(null);
            setLoading(false);
            return;
          }
        }
      } catch (apiV2Error) {
        console.error('Next.js API route request failed:', apiV2Error);
      }

      // 標準APIからタスクを取得（Next.jsのAPIルートを使用）
      try {
        // 標準APIエンドポイントを試す（Next.jsのAPIルートを使用）
        const standardApiUrl = projectName
          ? `/api/task/${projectName}:${taskId}.json`
          : `/api/task/${params.id}.json`;

        console.log(`Trying to fetch task from standard API via Next.js: ${standardApiUrl}`);
        const response = await fetch(standardApiUrl);

        if (response.ok) {
          const data = await response.json();
          console.log('Received task data from standard API via Next.js:', data);

          if (data && (data.taskid || data.task_id)) {
            setTask(data);
            setError(null);
            setLoading(false);
            return;
          }
        }

        throw new Error(`API request failed with status ${response.status}`);
      } catch (directApiError) {
        console.error('Direct API request failed:', directApiError);

        // APIサーバーからの直接取得に失敗した場合、tasksApiを使用
        try {
          console.log('Trying to fetch task via tasksApi');
          const data = await tasksApi.getTask(params.id);
          console.log('Received task data via tasksApi:', data);

          if (data && !data.error) {
            setTask(data);
            setError(null);
            setLoading(false);
            return;
          }

          throw new Error('No valid task data received from tasksApi');
        } catch (tasksApiError) {
          console.error('tasksApi request failed:', tasksApiError);

          // タスク一覧ページから渡されたタスクIDが一意のキーの場合
          // タスク一覧ページに戻るようにエラーメッセージを表示
          setError('タスクが見つかりませんでした。タスク一覧ページに戻って、最新のタスクを確認してください。');
          setTask(null);
        }
      }
    } catch (err) {
      console.error('Error fetching task:', err);
      setError('タスク情報の取得中にエラーが発生しました。');
      setTask(null);
    } finally {
      setLoading(false);
    }
  };

  // タスクを再実行する関数
  const handleRerunTask = async () => {
    try {
      setLoading(true);

      console.log(`Rerunning task with ID: ${params.id}`);

      // タスクIDからプロジェクト名とタスクIDを抽出
      let projectName, taskId;
      if (params.id.includes(':')) {
        [projectName, taskId] = params.id.split(':', 2);
      } else {
        // IDが形式に合わない場合は、そのまま使用
        taskId = params.id;
      }

      // Next.jsのAPIルートを使用してタスクを再実行
      try {
        const url = projectName
          ? `/api/task/${projectName}:${taskId}/rerun`
          : `/api/task/${params.id}/rerun`;

        console.log(`Trying to rerun task via Next.js API route: ${url}`);
        const response = await fetch(url, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          }
        });

        if (!response.ok) {
          throw new Error(`API request failed with status ${response.status}`);
        }

        const data = await response.json();
        console.log('Task rerun response from API server:', data);

        // タスク情報を再取得
        await fetchTask();

        setError(null);
      } catch (directApiError) {
        console.error('Direct API request failed:', directApiError);

        // APIサーバーからの直接実行に失敗した場合、tasksApiを使用
        await tasksApi.rerunTask(params.id);
        console.log('Task rerun via tasksApi completed');

        // タスク情報を再取得
        await fetchTask();

        setError(null);
      }
    } catch (err) {
      console.error('Error rerunning task:', err);
      setError('タスクの再実行中にエラーが発生しました。');
    } finally {
      setLoading(false);
    }
  };

  // 初回レンダリング時にタスクを取得
  useEffect(() => {
    if (params.id) {
      fetchTask();
    }
  }, [params.id]);

  if (loading && !task) {
    return <Loading size="lg" text="タスク情報を読み込み中..." />;
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <Link
            href="/tasks"
            className="mr-4 inline-flex items-center text-sm font-medium text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
          >
            <ArrowLeftIcon className="-ml-1 mr-1 h-5 w-5" aria-hidden="true" />
            戻る
          </Link>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">タスク詳細</h1>
        </div>
        <div className="flex space-x-2">
          <button
            onClick={fetchTask}
            className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white dark:hover:bg-gray-600"
            disabled={loading}
          >
            <ArrowPathIcon className={`-ml-1 mr-2 h-5 w-5 ${loading ? 'animate-spin' : ''}`} />
            更新
          </button>
          <button
            onClick={handleRerunTask}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 dark:bg-primary-700 dark:hover:bg-primary-600"
            disabled={loading || !task}
          >
            再実行
          </button>
        </div>
      </div>

      {error && (
        <div className="bg-red-50 border-l-4 border-red-500 p-4 dark:bg-red-900/20 dark:border-red-500">
          <div className="flex">
            <div className="ml-3">
              <p className="text-sm text-red-700 dark:text-red-400">{error}</p>
            </div>
          </div>
        </div>
      )}

      {task ? (
        <div className="bg-white shadow-sm rounded-lg dark:bg-gray-800">
          <div className="px-4 py-5 sm:p-6">
            <dl className="grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2">
              <div className="sm:col-span-1">
                <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">タスクID</dt>
                <dd className="mt-1 text-sm text-gray-900 dark:text-white">{task.taskid}</dd>
              </div>
              <div className="sm:col-span-1">
                <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">プロジェクト</dt>
                <dd className="mt-1 text-sm text-gray-900 dark:text-white">
                  <Link
                    href={`/projects/${task.project}`}
                    className="text-primary-600 hover:text-primary-900 dark:text-primary-400 dark:hover:text-primary-300"
                  >
                    {task.project}
                  </Link>
                </dd>
              </div>
              <div className="sm:col-span-1">
                <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">ステータス</dt>
                <dd className="mt-1 text-sm">
                  <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusColor(task.status)}`}>
                    {task.status}
                  </span>
                </dd>
              </div>
              <div className="sm:col-span-1">
                <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">更新日時</dt>
                <dd className="mt-1 text-sm text-gray-900 dark:text-white">
                  {new Date(task.updatetime * 1000).toLocaleString()}
                </dd>
              </div>
              <div className="sm:col-span-2">
                <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">URL</dt>
                <dd className="mt-1 text-sm text-gray-900 dark:text-white">
                  <a
                    href={task.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-primary-600 hover:text-primary-900 dark:text-primary-400 dark:hover:text-primary-300"
                  >
                    {task.url}
                  </a>
                </dd>
              </div>
              {task.result && (
                <div className="sm:col-span-2">
                  <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">結果</dt>
                  <dd className="mt-1 text-sm text-gray-900 dark:text-white">
                    <pre className="bg-gray-50 p-4 rounded-md overflow-auto dark:bg-gray-900 dark:text-gray-300">
                      {JSON.stringify(task.result, null, 2)}
                    </pre>
                  </dd>
                </div>
              )}
              {task.lastcrawltime && (
                <div className="sm:col-span-1">
                  <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">最終クロール日時</dt>
                  <dd className="mt-1 text-sm text-gray-900 dark:text-white">
                    {new Date(task.lastcrawltime * 1000).toLocaleString()}
                  </dd>
                </div>
              )}
              {task.track && (
                <div className="sm:col-span-2">
                  <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">トラック情報</dt>
                  <dd className="mt-1 text-sm text-gray-900 dark:text-white">
                    <pre className="bg-gray-50 p-4 rounded-md overflow-auto dark:bg-gray-900 dark:text-gray-300">
                      {JSON.stringify(task.track, null, 2)}
                    </pre>
                  </dd>
                </div>
              )}
            </dl>
          </div>
        </div>
      ) : !loading && (
        <div className="bg-white shadow-sm rounded-lg dark:bg-gray-800">
          <div className="px-4 py-5 sm:p-6">
            <div className="text-center py-10">
              <p className="text-gray-500 dark:text-gray-400">タスクが見つかりませんでした。</p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
