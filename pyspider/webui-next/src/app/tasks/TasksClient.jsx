'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { useRouter, useSearchParams } from 'next/navigation';
import { tasksApi, projectsApi } from '@/lib/api';
import { ArrowPathIcon, FunnelIcon } from '@heroicons/react/24/outline';
import Loading from '@/components/common/Loading';

export default function TasksClient() {
  const router = useRouter();
  const searchParams = useSearchParams();

  const [tasks, setTasks] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [page, setPage] = useState(0);
  const [limit, setLimit] = useState(20);
  const [total, setTotal] = useState(0);

  // プロジェクトフィルタリング用の状態
  const [projects, setProjects] = useState([]);
  const [projectsLoading, setProjectsLoading] = useState(true);
  const [selectedProject, setSelectedProject] = useState(searchParams.get('project') || '');

  // タスクのステータスに応じた色を返す関数
  const getStatusColor = (status) => {
    switch (status) {
      case 'SUCCESS':
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
      case 'FAILED':
        return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
      case 'ACTIVE':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400';
      case 'PENDING':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
    }
  };

  // タスクを取得する関数
  const fetchTasks = async () => {
    try {
      setLoading(true);

      const params = {
        offset: page * limit,
        limit: limit
      };

      console.log('Fetching tasks with params:', params);

      // dashboard-active-tasksエンドポイントからタスクを取得
      try {
        console.log('Fetching active tasks from dashboard-active-tasks endpoint');
        const response = await fetch('http://localhost:5000/dashboard-active-tasks');

        if (!response.ok) {
          throw new Error(`API request failed with status ${response.status}`);
        }

        const data = await response.json();
        console.log('Received active tasks data:', data);

        if (Array.isArray(data) && data.length > 0) {
          // データを整形
          let formattedTasks = data.map(([updatetime, task]) => ({
            ...task,
            updatetime: updatetime,
            status: 'ACTIVE'
          }));

          // プロジェクトでフィルタリング
          if (selectedProject) {
            console.log(`Filtering tasks by project: ${selectedProject}`);
            formattedTasks = formattedTasks.filter(task => task.project === selectedProject);
          }

          console.log(`Filtered tasks: ${formattedTasks.length} tasks`);

          // ページネーション処理
          const startIndex = page * limit;
          const endIndex = startIndex + limit;
          const paginatedTasks = formattedTasks.slice(startIndex, endIndex);

          setTasks(paginatedTasks);
          setTotal(formattedTasks.length);
          setError(null);
          return;
        } else {
          throw new Error('No active tasks found');
        }
      } catch (dashboardApiError) {
        console.error('Dashboard API request failed:', dashboardApiError);

        // dashboard-active-tasksエンドポイントからの取得に失敗した場合、/api/v2/tasksを試す
        try {
          console.log('Trying /api/v2/tasks endpoint');
          const response = await fetch(`/api/v2/tasks?offset=${params.offset}&limit=${params.limit}`);

          if (!response.ok) {
            throw new Error(`API request failed with status ${response.status}`);
          }

          const data = await response.json();
          console.log('Received tasks data from /api/v2/tasks:', data);

          if (Array.isArray(data)) {
            setTasks(data);
            setTotal(data.length);
            setError(null);
            return;
          } else {
            throw new Error('Invalid response format');
          }
        } catch (apiV2Error) {
          console.error('API v2 request failed:', apiV2Error);

          // APIv2からの取得に失敗した場合、tasksApiを使用
          try {
            console.log('Trying tasksApi.getTasks');
            const data = await tasksApi.getTasks(params);
            console.log('Received tasks data via tasksApi:', data);

            if (data && Array.isArray(data)) {
              setTasks(data);
              setTotal(data.length);
              setError(null);
              return;
            } else if (data && data.tasks && Array.isArray(data.tasks)) {
              setTasks(data.tasks);
              setTotal(data.total || data.tasks.length);
              setError(null);
              return;
            } else {
              throw new Error('No valid tasks data received');
            }
          } catch (tasksApiError) {
            console.error('tasksApi request failed:', tasksApiError);
            throw tasksApiError;
          }
        }
      }
    } catch (err) {
      console.error('Error fetching tasks:', err);
      setError('タスク情報の取得中にエラーが発生しました。');
      setTasks([]);
      setTotal(0);
    } finally {
      setLoading(false);
    }
  };

  // プロジェクト一覧を取得する関数
  const fetchProjects = async () => {
    try {
      setProjectsLoading(true);
      const data = await projectsApi.getProjects();
      setProjects(data);
    } catch (err) {
      console.error('Error fetching projects:', err);
    } finally {
      setProjectsLoading(false);
    }
  };

  // プロジェクトフィルターが変更されたときの処理
  const handleProjectChange = (e) => {
    const projectName = e.target.value;
    setSelectedProject(projectName);

    // URLクエリパラメータを更新
    const params = new URLSearchParams(searchParams.toString());
    if (projectName) {
      params.set('project', projectName);
    } else {
      params.delete('project');
    }

    // ページをリセット
    setPage(0);

    // URLを更新
    router.push(`/tasks?${params.toString()}`);
  };

  // 初回レンダリング時にプロジェクト一覧を取得
  useEffect(() => {
    fetchProjects();
  }, []);

  // 初回レンダリング時とページ、選択プロジェクトが変更されたときにタスクを取得
  useEffect(() => {
    fetchTasks();
  }, [page, limit, selectedProject]);

  // ページ変更ハンドラ
  const handlePageChange = (newPage) => {
    if (newPage >= 0 && newPage * limit < total) {
      setPage(newPage);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">タスク一覧</h1>
        <div className="flex items-center space-x-4">
          {/* プロジェクト選択ドロップダウン */}
          <div className="flex items-center">
            <FunnelIcon className="h-5 w-5 text-gray-400 mr-2" />
            <select
              value={selectedProject}
              onChange={handleProjectChange}
              className="block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              disabled={projectsLoading}
            >
              <option value="">すべてのプロジェクト</option>
              {projects.map((project) => (
                <option key={project.name} value={project.name}>
                  {project.name}
                </option>
              ))}
            </select>
          </div>

          {/* 更新ボタン */}
          <button
            onClick={fetchTasks}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 dark:bg-primary-700 dark:hover:bg-primary-600"
            disabled={loading}
          >
            <ArrowPathIcon className={`-ml-1 mr-2 h-5 w-5 ${loading ? 'animate-spin' : ''}`} />
            更新
          </button>
        </div>
      </div>

      {error && (
        <div className="bg-red-50 border-l-4 border-red-500 p-4 dark:bg-red-900/20 dark:border-red-500">
          <div className="flex">
            <div className="ml-3">
              <p className="text-sm text-red-700 dark:text-red-400">{error}</p>
            </div>
          </div>
        </div>
      )}

      {/* タスク一覧テーブル */}
      <div className="bg-white shadow-sm rounded-lg dark:bg-gray-800">
        <div className="px-4 py-5 sm:p-6">
          {loading && tasks.length === 0 ? (
            <Loading size="lg" text="タスク情報を読み込み中..." />
          ) : tasks.length > 0 ? (
            <>
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                  <thead className="bg-gray-50 dark:bg-gray-700">
                    <tr>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-300">
                        プロジェクト
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-300">
                        ステータス
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-300">
                        URL
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-300">
                        更新日時
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-300">
                        アクション
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200 dark:bg-gray-800 dark:divide-gray-700">
                    {tasks.map((task, index) => {
                      // 一意のキーを生成
                      const uniqueKey = task.taskid
                        ? `${task.project}-${task.taskid}-${index}`
                        : `task-${index}-${task.url ? task.url.substring(0, 20) : ''}-${task.updatetime || Date.now()}`;

                      return (
                        <tr key={uniqueKey} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                            <Link
                              href={`/projects/${task.project}`}
                              className="text-primary-600 hover:text-primary-900 dark:text-primary-400 dark:hover:text-primary-300"
                            >
                              {task.project}
                            </Link>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusColor(task.status)}`}>
                              {task.status}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300 max-w-xs truncate">
                            <a
                              href={task.url}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="text-primary-600 hover:text-primary-900 dark:text-primary-400 dark:hover:text-primary-300"
                              title={task.url}
                            >
                              {task.url}
                            </a>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                            {new Date(task.updatetime * 1000).toLocaleString()}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                            <Link
                              href={`/tasks/${task.taskid || uniqueKey}`}
                              className="text-primary-600 hover:text-primary-900 dark:text-primary-400 dark:hover:text-primary-300 mr-4"
                            >
                              詳細
                            </Link>
                            <Link
                              href={`/debug/${task.project}?taskid=${task.taskid || ''}`}
                              className="text-primary-600 hover:text-primary-900 dark:text-primary-400 dark:hover:text-primary-300"
                            >
                              デバッグ
                            </Link>
                          </td>
                        </tr>
                      );
                    })}
                  </tbody>
                </table>
              </div>

              {/* ページネーション */}
              <div className="flex items-center justify-between mt-6">
                <div className="text-sm text-gray-700 dark:text-gray-300">
                  全 <span className="font-medium">{total}</span> 件中
                  <span className="font-medium"> {page * limit + 1}</span> -
                  <span className="font-medium"> {Math.min((page + 1) * limit, total)}</span> 件を表示
                </div>
                <div className="flex space-x-2">
                  <button
                    onClick={() => handlePageChange(page - 1)}
                    disabled={page === 0}
                    className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    前へ
                  </button>
                  <button
                    onClick={() => handlePageChange(page + 1)}
                    disabled={(page + 1) * limit >= total}
                    className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    次へ
                  </button>
                </div>
              </div>
            </>
          ) : (
            <div className="text-center py-10">
              <p className="text-gray-500 dark:text-gray-400">タスクがありません</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
