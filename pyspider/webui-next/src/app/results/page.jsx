'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { projectsApi } from '@/lib/api';
import {
  ArrowPathIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  DocumentArrowDownIcon
} from '@heroicons/react/24/outline';
import Loading from '@/components/common/Loading';

export default function ResultsPage() {
  // 結果データ用の状態
  const [results, setResults] = useState([]);
  const [resultsLoading, setResultsLoading] = useState(false);
  const [resultsError, setResultsError] = useState(null);
  const [resultsPage, setResultsPage] = useState(0);
  const [resultsTotal, setResultsTotal] = useState(0);
  const [resultsLimit, setResultsLimit] = useState(20);
  const [selectedProject, setSelectedProject] = useState('');
  const [projects, setProjects] = useState([]);
  const [projectsLoading, setProjectsLoading] = useState(false);

  // プロジェクト一覧を取得
  useEffect(() => {
    const fetchProjects = async () => {
      setProjectsLoading(true);
      try {
        const data = await projectsApi.getProjects();
        setProjects(data);
        // 最初のプロジェクトを選択
        if (data.length > 0 && !selectedProject) {
          setSelectedProject(data[0].name);
        }
      } catch (err) {
        console.error('Error fetching projects:', err);
      } finally {
        setProjectsLoading(false);
      }
    };

    fetchProjects();
  }, []);

  // 選択されたプロジェクトの結果を取得
  useEffect(() => {
    const fetchResults = async () => {
      console.log(`Fetching results for project ${selectedProject || 'all'} on results page`);
      setResultsLoading(true);
      setResultsError(null);

      try {
        const offset = resultsPage * resultsLimit;
        console.log(`Fetching results with offset=${offset}, limit=${resultsLimit}`);

        let data;
        if (selectedProject) {
          // 特定のプロジェクトの結果を取得
          data = await projectsApi.getProjectResults(selectedProject, {
            offset,
            limit: resultsLimit
          });
        } else {
          // すべてのプロジェクトの結果を取得
          data = await projectsApi.getAllResults({
            offset,
            limit: resultsLimit
          });
        }

        console.log('Received results:', data);

        if (data && data.results && Array.isArray(data.results)) {
          console.log(`Setting ${data.results.length} results with total ${data.total}`);
          setResults(data.results);
          setResultsTotal(data.total || data.results.length);
        } else {
          console.warn('Unexpected results format:', data);
          // 結果が配列の場合の対応
          if (data && Array.isArray(data)) {
            console.log(`Setting ${data.length} results from array`);
            setResults(data);
            setResultsTotal(data.length);
          } else {
            console.error('No valid results data found');
            setResults([]);
            setResultsTotal(0);
          }
        }
      } catch (err) {
        console.error('Error fetching results:', err);
        setResultsError('結果データの取得中にエラーが発生しました。');
        setResults([]);
        setResultsTotal(0);
      } finally {
        setResultsLoading(false);
      }
    };

    fetchResults();
  }, [selectedProject, resultsPage, resultsLimit]);

  // 結果データのページ変更
  const handleResultsPageChange = (newPage) => {
    if (newPage >= 0 && newPage * resultsLimit < resultsTotal) {
      setResultsPage(newPage);
    }
  };

  // 結果データの再読み込み
  const handleRefreshResults = async () => {
    if (resultsLoading) return;

    console.log(`Refreshing results for project ${selectedProject || 'all'}`);
    setResultsLoading(true);
    setResultsError(null);

    try {
      const offset = resultsPage * resultsLimit;
      console.log(`Refreshing results with offset=${offset}, limit=${resultsLimit}`);

      let data;
      if (selectedProject) {
        // 特定のプロジェクトの結果を取得
        data = await projectsApi.getProjectResults(selectedProject, {
          offset,
          limit: resultsLimit
        });
      } else {
        // すべてのプロジェクトの結果を取得
        data = await projectsApi.getAllResults({
          offset,
          limit: resultsLimit
        });
      }

      console.log('Received refreshed results:', data);

      if (data && data.results && Array.isArray(data.results)) {
        console.log(`Setting ${data.results.length} refreshed results with total ${data.total}`);
        setResults(data.results);
        setResultsTotal(data.total || data.results.length);
      } else {
        console.warn('Unexpected refreshed results format:', data);
        // 結果が配列の場合の対応
        if (data && Array.isArray(data)) {
          console.log(`Setting ${data.length} refreshed results from array`);
          setResults(data);
          setResultsTotal(data.length);
        } else {
          console.error('No valid refreshed results data found');
          setResults([]);
          setResultsTotal(0);
        }
      }
    } catch (err) {
      console.error('Error refreshing results:', err);
      setResultsError('結果データの更新中にエラーが発生しました。');
    } finally {
      setResultsLoading(false);
    }
  };

  // 結果データのエクスポート
  const handleExportResults = (format) => {
    if (!selectedProject) {
      setResultsError('エクスポートするプロジェクトを選択してください。');
      return;
    }

    try {
      projectsApi.exportProjectResults(selectedProject, format);
    } catch (err) {
      console.error(`Error exporting results as ${format}:`, err);
      setResultsError(`結果データの${format}形式でのエクスポート中にエラーが発生しました。`);
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-2xl font-bold mb-6 dark:text-white">結果一覧</h1>

      <div className="bg-white dark:bg-gray-800 shadow-md rounded-lg overflow-hidden mb-8">
        <div className="p-4 border-b border-gray-200 dark:border-gray-700">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
            <div className="flex items-center">
              <label htmlFor="project-select" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mr-2">
                プロジェクト:
              </label>
              <select
                id="project-select"
                value={selectedProject}
                onChange={(e) => {
                  setSelectedProject(e.target.value);
                  setResultsPage(0); // ページをリセット
                }}
                className="block w-full md:w-64 rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white text-sm"
                disabled={projectsLoading}
              >
                <option value="">すべてのプロジェクト</option>
                {projectsLoading ? (
                  <option value="" disabled>読み込み中...</option>
                ) : projects.length > 0 ? (
                  projects.map((project) => (
                    <option key={project.name} value={project.name}>
                      {project.name}
                    </option>
                  ))
                ) : (
                  <option value="" disabled>プロジェクトがありません</option>
                )}
              </select>
            </div>

            <div className="flex space-x-2">
              <button
                onClick={handleRefreshResults}
                disabled={resultsLoading}
                className="inline-flex items-center px-3 py-1.5 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <ArrowPathIcon className={`h-4 w-4 mr-1 ${resultsLoading ? 'animate-spin' : ''}`} />
                更新
              </button>

              <div className="relative inline-block text-left">
                <div>
                  <button
                    type="button"
                    className="inline-flex items-center px-3 py-1.5 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"
                    id="export-menu-button"
                    aria-expanded="true"
                    aria-haspopup="true"
                    onClick={() => document.getElementById('export-dropdown').classList.toggle('hidden')}
                    disabled={results.length === 0}
                  >
                    <DocumentArrowDownIcon className="h-4 w-4 mr-1" />
                    エクスポート
                  </button>
                </div>
                <div
                  id="export-dropdown"
                  className="hidden origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 focus:outline-none dark:bg-gray-800 dark:ring-gray-700 z-10"
                  role="menu"
                  aria-orientation="vertical"
                  aria-labelledby="export-menu-button"
                  tabIndex="-1"
                >
                  <div className="py-1" role="none">
                    <button
                      onClick={() => {
                        handleExportResults('json');
                        document.getElementById('export-dropdown').classList.add('hidden');
                      }}
                      className="text-gray-700 block px-4 py-2 text-sm w-full text-left hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700"
                      role="menuitem"
                      tabIndex="-1"
                    >
                      JSON形式
                    </button>
                    <button
                      onClick={() => {
                        handleExportResults('csv');
                        document.getElementById('export-dropdown').classList.add('hidden');
                      }}
                      className="text-gray-700 block px-4 py-2 text-sm w-full text-left hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700"
                      role="menuitem"
                      tabIndex="-1"
                    >
                      CSV形式
                    </button>
                    <button
                      onClick={() => {
                        handleExportResults('xml');
                        document.getElementById('export-dropdown').classList.add('hidden');
                      }}
                      className="text-gray-700 block px-4 py-2 text-sm w-full text-left hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700"
                      role="menuitem"
                      tabIndex="-1"
                    >
                      XML形式
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="p-4">
          {resultsError && (
            <div className="bg-red-50 border-l-4 border-red-500 p-4 mb-4 dark:bg-red-900/20 dark:border-red-500">
              <div className="flex">
                <div className="ml-3">
                  <p className="text-sm text-red-700 dark:text-red-400">{resultsError}</p>
                </div>
              </div>
            </div>
          )}

          {resultsLoading && results.length === 0 ? (
            <div className="text-center py-8">
              <Loading size="md" text="結果データを読み込み中..." />
            </div>
          ) : results.length > 0 ? (
            <>
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                  <thead className="bg-gray-50 dark:bg-gray-800">
                    <tr>
                      {!selectedProject && (
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-400">
                          プロジェクト
                        </th>
                      )}
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-400">
                        URL
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-400">
                        タイトル
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-400">
                        時間
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200 dark:bg-gray-900 dark:divide-gray-700">
                    {results.map((result, index) => (
                      <tr key={result._id || index} className="hover:bg-gray-50 dark:hover:bg-gray-800">
                        {!selectedProject && (
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                            <Link
                              href={`/projects/${result.project}`}
                              className="text-primary-600 hover:text-primary-900 dark:text-primary-400 dark:hover:text-primary-300"
                            >
                              {result.project}
                            </Link>
                          </td>
                        )}
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400 truncate max-w-xs">
                          <a
                            href={result.url}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-primary-600 hover:text-primary-900 dark:text-primary-400 dark:hover:text-primary-300"
                          >
                            {result.url}
                          </a>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400 truncate max-w-xs">
                          {result.result && result.result.title ? result.result.title : ''}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                          {new Date(result.updatetime * 1000).toLocaleString()}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              {/* ページネーション */}
              <div className="flex items-center justify-between border-t border-gray-200 px-4 py-3 sm:px-6 dark:border-gray-700 mt-4">
                <div className="flex flex-1 justify-between sm:hidden">
                  <button
                    onClick={() => handleResultsPageChange(resultsPage - 1)}
                    disabled={resultsPage === 0}
                    className="relative inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    前へ
                  </button>
                  <button
                    onClick={() => handleResultsPageChange(resultsPage + 1)}
                    disabled={(resultsPage + 1) * resultsLimit >= resultsTotal}
                    className="relative ml-3 inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    次へ
                  </button>
                </div>
                <div className="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
                  <div>
                    <p className="text-sm text-gray-700 dark:text-gray-300">
                      <span className="font-medium">{resultsPage * resultsLimit + 1}</span>
                      {' '}から{' '}
                      <span className="font-medium">
                        {Math.min((resultsPage + 1) * resultsLimit, resultsTotal)}
                      </span>
                      {' '}/ 合計{' '}
                      <span className="font-medium">{resultsTotal}</span>
                      {' '}件
                    </p>
                  </div>
                  <div>
                    <nav className="isolate inline-flex -space-x-px rounded-md shadow-sm" aria-label="Pagination">
                      <button
                        onClick={() => handleResultsPageChange(resultsPage - 1)}
                        disabled={resultsPage === 0}
                        className="relative inline-flex items-center rounded-l-md px-2 py-2 text-gray-400 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0 dark:ring-gray-600 dark:hover:bg-gray-700 dark:text-gray-500 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        <span className="sr-only">前へ</span>
                        <ChevronLeftIcon className="h-5 w-5" aria-hidden="true" />
                      </button>
                      <button
                        onClick={() => handleResultsPageChange(resultsPage + 1)}
                        disabled={(resultsPage + 1) * resultsLimit >= resultsTotal}
                        className="relative inline-flex items-center rounded-r-md px-2 py-2 text-gray-400 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0 dark:ring-gray-600 dark:hover:bg-gray-700 dark:text-gray-500 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        <span className="sr-only">次へ</span>
                        <ChevronRightIcon className="h-5 w-5" aria-hidden="true" />
                      </button>
                    </nav>
                  </div>
                </div>
              </div>
            </>
          ) : (
            <div className="text-center py-8">
              <p className="text-gray-500 dark:text-gray-400">結果データがありません。</p>
              {selectedProject && (
                <p className="text-sm text-gray-500 dark:text-gray-400 mt-2">
                  プロジェクトを実行して、タスクを完了させると結果が表示されます。
                </p>
              )}
              {!selectedProject && (
                <p className="text-sm text-gray-500 dark:text-gray-400 mt-2">
                  プロジェクトを選択してください。
                </p>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
