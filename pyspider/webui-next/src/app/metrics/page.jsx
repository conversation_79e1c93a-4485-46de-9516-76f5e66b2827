'use client';

import { useState, useEffect } from 'react';
import { metricsApi } from '@/lib/api';
import Loading from '@/components/common/Loading';
import MetricsOverview from '@/components/metrics/MetricsOverview';
import ComponentStatus from '@/components/metrics/ComponentStatus';
import Charts from '@/components/metrics/Charts';

export default function MetricsPage() {
  const [metrics, setMetrics] = useState(null);
  const [componentsStatus, setComponentsStatus] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [lastUpdated, setLastUpdated] = useState(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);

        // 並列でデータを取得
        const [metricsData, componentsStatusData] = await Promise.all([
          metricsApi.getMetrics(),
          metricsApi.getComponentsStatus()
        ]);

        setMetrics(metricsData);
        setComponentsStatus(componentsStatusData);
        setLastUpdated(new Date());
        setError(null);
      } catch (err) {
        console.error('Error fetching metrics data:', err);
        setError('メトリクスデータの取得中にエラーが発生しました。');
      } finally {
        setLoading(false);
      }
    };

    fetchData();

    // 定期的にデータを更新
    const intervalId = setInterval(fetchData, 30000);

    return () => clearInterval(intervalId);
  }, []);

  if (loading && !metrics) {
    return <Loading size="lg" text="メトリクスデータを読み込み中..." />;
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">メトリクスダッシュボード</h1>
        {lastUpdated && (
          <p className="text-sm text-gray-500 dark:text-gray-400">
            最終更新: {lastUpdated.toLocaleString('ja-JP')}
          </p>
        )}
      </div>

      {error && (
        <div className="bg-red-50 border-l-4 border-red-500 p-4 dark:bg-red-900/20 dark:border-red-500">
          <div className="flex">
            <div className="ml-3">
              <p className="text-sm text-red-700 dark:text-red-400">{error}</p>
            </div>
          </div>
        </div>
      )}

      <ComponentStatus componentsStatus={componentsStatus} />

      <MetricsOverview metrics={metrics} />

      <Charts metrics={metrics} />

      <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200 dark:bg-gray-800 dark:border-gray-700">
        <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">更新情報</h2>
        <p className="text-sm text-gray-500 dark:text-gray-400">
          メトリクスデータは30秒ごとに自動更新されます。
        </p>
        <button
          onClick={() => window.location.reload()}
          className="mt-2 inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 dark:bg-primary-700 dark:hover:bg-primary-600"
        >
          今すぐ更新
        </button>
      </div>
    </div>
  );
}
