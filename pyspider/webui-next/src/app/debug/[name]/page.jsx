'use client';

import { useState, useEffect, useRef } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { projectsApi, debugApi } from '@/lib/api';
import { ArrowLeftIcon, PlayIcon, DocumentDuplicateIcon } from '@heroicons/react/24/outline';
import { DocumentTextIcon } from '@heroicons/react/24/outline';
import CodeMirror from '@uiw/react-codemirror';
import { python } from '@codemirror/lang-python';
import { oneDark } from '@codemirror/theme-one-dark';
import { useTheme } from 'next-themes';
import Loading from '@/components/common/Loading';

export default function DebugPage({ params }) {
  const router = useRouter();
  const { theme } = useTheme();
  const [mounted, setMounted] = useState(false);
  const [project, setProject] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [script, setScript] = useState('');
  const [taskUrl, setTaskUrl] = useState('');
  const [taskCallback, setTaskCallback] = useState('on_start');
  const [isRunning, setIsRunning] = useState(false);
  const [debugResult, setDebugResult] = useState(null);
  const [follows, setFollows] = useState([]);
  const [logs, setLogs] = useState('');
  const iframeRef = useRef(null);

  // マウント状態を管理
  useEffect(() => {
    setMounted(true);
  }, []);

  // プロジェクト情報を取得
  useEffect(() => {
    const fetchProject = async () => {
      try {
        setLoading(true);
        const projectData = await projectsApi.getProject(params.name);
        setProject(projectData);
        setScript(projectData.script || '');
        setError(null);
      } catch (err) {
        console.error('Error fetching project:', err);
        setError('プロジェクト情報の取得中にエラーが発生しました。');
      } finally {
        setLoading(false);
      }
    };

    if (params.name) {
      fetchProject();
    }
  }, [params.name]);

  const handleRunTask = async () => {
    try {
      setIsRunning(true);
      setDebugResult(null);
      setFollows([]);
      setLogs('');

      // iframeをクリア
      if (iframeRef.current) {
        iframeRef.current.innerHTML = '';
      }

      const task = {
        taskid: taskUrl || 'data:,on_start',
        project: params.name,
        url: taskUrl || 'data:,on_start',
        process: {
          callback: taskCallback
        }
      };

      const result = await debugApi.runTask(params.name, task, script);

      setDebugResult(result);

      if (result.follows) {
        setFollows(result.follows);
      }

      if (result.logs) {
        setLogs(Array.isArray(result.logs) ? result.logs.join('\n') : result.logs);
      }

      // fetch_resultがあれば、iframeに表示
      if (result.fetch_result && result.fetch_result.content) {
        try {
          const iframe = document.createElement('iframe');
          iframe.style.width = '100%';
          iframe.style.height = '100%';
          iframe.style.border = 'none';

          if (iframeRef.current) {
            iframeRef.current.innerHTML = '';
            iframeRef.current.appendChild(iframe);

            const doc = iframe.contentDocument || iframe.contentWindow.document;
            doc.open();
            doc.write(result.fetch_result.content);
            doc.close();
          }
        } catch (e) {
          console.error('Error displaying content in iframe:', e);
        }
      }
    } catch (err) {
      console.error('Error running task:', err);
      setError('タスクの実行中にエラーが発生しました。');
    } finally {
      setIsRunning(false);
    }
  };

  const handleSaveScript = async () => {
    try {
      setLoading(true);
      await projectsApi.updateProject(params.name, {
        ...project,
        script: script
      });
      setError(null);
    } catch (err) {
      console.error('Error saving script:', err);
      setError('スクリプトの保存中にエラーが発生しました。');
    } finally {
      setLoading(false);
    }
  };

  if (loading && !project) {
    return <Loading size="lg" text="プロジェクト情報を読み込み中..." />;
  }

  return (
    <div className="flex flex-col h-screen w-full bg-white dark:bg-gray-800">
      {/* ヘッダー */}
      <div className="bg-white dark:bg-gray-800 shadow-sm p-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <Link
              href={`/projects/${params.name}`}
              className="mr-4 inline-flex items-center text-sm font-medium text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
            >
              <ArrowLeftIcon className="-ml-1 mr-1 h-5 w-5" aria-hidden="true" />
              戻る
            </Link>
            <h1 className="text-xl font-bold text-gray-900 dark:text-white">デバッグ: {params.name}</h1>
          </div>
          <div className="flex space-x-2">
            <button
              onClick={handleSaveScript}
              disabled={loading}
              className="inline-flex items-center px-3 py-1.5 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed dark:bg-green-700 dark:hover:bg-green-600"
            >
              <DocumentTextIcon className="-ml-1 mr-1 h-4 w-4" aria-hidden="true" />
              保存
            </button>
          </div>
        </div>
      </div>

      {error && (
        <div className="bg-red-50 border-l-4 border-red-500 p-4 dark:bg-red-900/20 dark:border-red-500">
          <div className="flex">
            <div className="ml-3">
              <p className="text-sm text-red-700 dark:text-red-400">{error}</p>
            </div>
          </div>
        </div>
      )}

      {/* メインコンテンツ */}
      <div className="flex flex-1 overflow-hidden">
        {/* 左側: デバッグパネル */}
        <div className="w-1/2 flex flex-col border-r border-gray-200 dark:border-gray-700">
          <div className="p-4 bg-gray-50 dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700">
            <div className="flex flex-col space-y-4">
              <div>
                <label htmlFor="taskUrl" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  URL
                </label>
                <div className="mt-1">
                  <input
                    type="text"
                    id="taskUrl"
                    value={taskUrl}
                    onChange={(e) => setTaskUrl(e.target.value)}
                    placeholder="https://example.com/ または空白で on_start"
                    className="block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                  />
                </div>
              </div>
              <div>
                <label htmlFor="taskCallback" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  コールバック
                </label>
                <div className="mt-1">
                  <input
                    type="text"
                    id="taskCallback"
                    value={taskCallback}
                    onChange={(e) => setTaskCallback(e.target.value)}
                    className="block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                  />
                </div>
              </div>
              <div>
                <button
                  onClick={handleRunTask}
                  disabled={isRunning}
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed dark:bg-primary-700 dark:hover:bg-primary-600"
                >
                  <PlayIcon className="-ml-1 mr-1 h-5 w-5" aria-hidden="true" />
                  {isRunning ? '実行中...' : '実行'}
                </button>
              </div>
            </div>
          </div>

          <div className="flex-1 flex flex-col overflow-hidden">
            <div className="flex-1 overflow-auto p-4" ref={iframeRef}>
              {/* iframeコンテンツがここに表示されます */}
            </div>

            {/* タブパネル */}
            <div className="border-t border-gray-200 dark:border-gray-700">
              <div className="bg-gray-50 dark:bg-gray-900 px-4 py-2 border-b border-gray-200 dark:border-gray-700">
                <div className="flex space-x-4">
                  <button className="px-3 py-1 text-sm font-medium text-primary-600 border-b-2 border-primary-600 dark:text-primary-400 dark:border-primary-400">
                    ログ
                  </button>
                  <button className="px-3 py-1 text-sm font-medium text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300">
                    フォロー ({follows.length})
                  </button>
                </div>
              </div>
              <div className="p-4 h-48 overflow-auto">
                <pre className="text-xs text-gray-700 dark:text-gray-300 whitespace-pre-wrap">{logs}</pre>
              </div>
            </div>
          </div>
        </div>

        {/* 右側: スクリプトエディタ */}
        <div className="w-1/2 flex flex-col">
          <div className="flex-1 overflow-auto">
            {mounted && (
              <CodeMirror
                value={script}
                height="100%"
                extensions={[python()]}
                theme={theme === 'dark' ? oneDark : undefined}
                onChange={(value) => setScript(value)}
                className="h-full"
                style={{ height: '100%' }}
                basicSetup={{
                  lineNumbers: true,
                  highlightActiveLineGutter: true,
                  highlightSpecialChars: true,
                  foldGutter: true,
                  dropCursor: true,
                  allowMultipleSelections: true,
                  indentOnInput: true,
                  syntaxHighlighting: true,
                  bracketMatching: true,
                  closeBrackets: true,
                  autocompletion: true,
                  rectangularSelection: true,
                  crosshairCursor: true,
                  highlightActiveLine: true,
                  highlightSelectionMatches: true,
                  closeBracketsKeymap: true,
                  searchKeymap: true,
                  foldKeymap: true,
                  completionKeymap: true,
                  lintKeymap: true,
                }}
              />
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
