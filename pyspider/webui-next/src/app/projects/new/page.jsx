'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { projectsApi } from '@/lib/api';
import { ArrowLeftIcon, DocumentTextIcon } from '@heroicons/react/24/outline';
import CodeMirror from '@uiw/react-codemirror';
import { python } from '@codemirror/lang-python';
import { oneDark } from '@codemirror/theme-one-dark';
import { useTheme } from 'next-themes';
import ScriptTemplateModal from '@/components/ScriptTemplateModal';
import { generateScriptTemplate } from '@/lib/scriptTemplates';

export default function NewProjectPage() {
  const router = useRouter();
  const { theme } = useTheme();
  const [mounted, setMounted] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    group: 'default',
    status: 'TODO',
    script: `#!/usr/bin/env python
# -*- coding: utf-8 -*-
import re
from pyspider.libs.base_handler import *

class Handler(BaseHandler):
    crawl_config = {
    }

    @every(minutes=24 * 60)
    def on_start(self):
        self.crawl('https://example.com/', callback=self.index_page)

    @config(age=10 * 24 * 60 * 60)
    def index_page(self, response):
        for each in response.doc('a[href^="http"]').items():
            self.crawl(each.attr.href, callback=self.detail_page)

    @config(priority=2)
    def detail_page(self, response):
        return {
            "url": response.url,
            "title": response.doc('title').text(),
        }
`
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [nameError, setNameError] = useState('');
  const [groupError, setGroupError] = useState('');
  const [isTemplateModalOpen, setIsTemplateModalOpen] = useState(false);

  // 英数字とアンダースコアのみを許可する正規表現
  const alphanumericRegex = /^[a-zA-Z0-9_]+$/;

  // プロジェクト名のバリデーション関数
  const validateProjectName = (name) => {
    return alphanumericRegex.test(name);
  };

  // グループ名のバリデーション関数
  const validateGroupName = (group) => {
    return alphanumericRegex.test(group);
  };

  // マウント状態を管理
  useEffect(() => {
    setMounted(true);
  }, []);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));

    // プロジェクト名のバリデーション
    if (name === 'name') {
      if (!value) {
        setNameError('プロジェクト名は必須です。');
      } else if (!validateProjectName(value)) {
        setNameError('プロジェクト名には英数字とアンダースコアのみ使用可能です。');
      } else {
        setNameError('');
      }
    }

    // グループ名のバリデーション
    if (name === 'group') {
      if (!value) {
        // グループ名が空の場合はデフォルト値を使用するので、エラーにしない
        setGroupError('');
      } else if (!validateGroupName(value)) {
        setGroupError('グループ名には英数字とアンダースコアのみ使用可能です。');
      } else {
        setGroupError('');
      }
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    // すでに処理中の場合は何もしない
    if (loading) {
      return;
    }

    setError(null);

    // プロジェクト名のバリデーション
    if (!formData.name) {
      setNameError('プロジェクト名は必須です。');
      return;
    }

    if (!validateProjectName(formData.name)) {
      setNameError('プロジェクト名には英数字とアンダースコアのみ使用可能です。');
      return;
    }

    // グループ名のバリデーション
    if (formData.group && !validateGroupName(formData.group)) {
      setGroupError('グループ名には英数字とアンダースコアのみ使用可能です。');
      return;
    }

    // 送信中フラグを設定
    setLoading(true);

    try {
      console.log('Submitting form data:', {
        name: formData.name,
        group: formData.group,
        status: formData.status,
        script: formData.script ? `[script content, length: ${formData.script.length}]` : 'empty'
      });

      // プロジェクト作成APIを呼び出す（一度だけ）
      const result = await projectsApi.createProject({
        name: formData.name,
        group: formData.group || 'default',
        status: formData.status || 'TODO',
        script: formData.script || '',
        rate: 1,
        burst: 10
      });

      console.log('Project creation result:', result);

      if (result && result.result === 'ok') {
        // 成功したらプロジェクト一覧ページにリダイレクト
        router.push('/projects');
      } else {
        // エラーメッセージを表示
        setError(result.message || 'プロジェクトの作成中にエラーが発生しました。');
      }
    } catch (err) {
      console.error('Error creating project:', err);
      setError('プロジェクトの作成中にエラーが発生しました。詳細はコンソールを確認してください。');
    } finally {
      setLoading(false);
    }
  };

  const handleTemplateSelect = (templateId) => {
    const templateScript = generateScriptTemplate(templateId, formData.name || 'my_project');
    setFormData(prev => ({ ...prev, script: templateScript }));
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <Link
            href="/projects"
            className="mr-4 inline-flex items-center text-sm font-medium text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
          >
            <ArrowLeftIcon className="-ml-1 mr-1 h-5 w-5" aria-hidden="true" />
            戻る
          </Link>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">新規プロジェクト</h1>
        </div>
      </div>

      {error && (
        <div className="bg-red-50 border-l-4 border-red-500 p-4 dark:bg-red-900/20 dark:border-red-500">
          <div className="flex">
            <div className="ml-3">
              <p className="text-sm text-red-700 dark:text-red-400">{error}</p>
            </div>
          </div>
        </div>
      )}

      <div className="bg-white shadow-sm rounded-lg dark:bg-gray-800">
        <div className="px-4 py-5 sm:p-6">
          <form onSubmit={handleSubmit} className="space-y-6">
            <div>
              <label htmlFor="name" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                プロジェクト名 <span className="text-red-500">*</span>
              </label>
              <div className="mt-1">
                <input
                  type="text"
                  name="name"
                  id="name"
                  required
                  pattern="[a-zA-Z0-9_]+"
                  className={`block w-full rounded-md shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm dark:bg-gray-700 dark:text-white ${
                    nameError ? 'border-red-300 dark:border-red-500' : 'border-gray-300 dark:border-gray-600'
                  }`}
                  value={formData.name}
                  onChange={handleChange}
                  placeholder="project_name"
                />
              </div>
              {nameError ? (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                  {nameError}
                </p>
              ) : (
                <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                  英数字とアンダースコアのみ使用可能です。
                </p>
              )}
            </div>

            <div>
              <label htmlFor="group" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                グループ
              </label>
              <div className="mt-1">
                <input
                  type="text"
                  name="group"
                  id="group"
                  pattern="[a-zA-Z0-9_]*"
                  className={`block w-full rounded-md shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm dark:bg-gray-700 dark:text-white ${
                    groupError ? 'border-red-300 dark:border-red-500' : 'border-gray-300 dark:border-gray-600'
                  }`}
                  value={formData.group}
                  onChange={handleChange}
                  placeholder="default"
                />
              </div>
              {groupError ? (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                  {groupError}
                </p>
              ) : (
                <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                  英数字とアンダースコアのみ使用可能です。空の場合は「default」が使用されます。
                </p>
              )}
            </div>

            <div>
              <label htmlFor="status" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                ステータス
              </label>
              <div className="mt-1">
                <select
                  name="status"
                  id="status"
                  className="block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                  value={formData.status}
                  onChange={handleChange}
                >
                  <option value="TODO">TODO</option>
                  <option value="RUNNING">実行中</option>
                  <option value="PAUSED">一時停止</option>
                  <option value="STOPPED">停止</option>
                  <option value="CHECKING">確認中</option>
                  <option value="DEBUG">デバッグ</option>
                </select>
              </div>
            </div>

            <div>
              <div className="flex items-center justify-between mb-2">
                <label htmlFor="script" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  スクリプト <span className="text-red-500">*</span>
                </label>
                <button
                  type="button"
                  onClick={() => setIsTemplateModalOpen(true)}
                  className="inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white dark:hover:bg-gray-600"
                >
                  <DocumentTextIcon className="h-4 w-4 mr-1" />
                  テンプレート
                </button>
              </div>
              <div className="mt-1">
                <CodeMirror
                  value={formData.script}
                  height="400px"
                  extensions={[python()]}
                  theme={mounted && theme === 'dark' ? oneDark : undefined}
                  onChange={(value) => setFormData(prev => ({ ...prev, script: value }))}
                  className="border rounded-md overflow-hidden"
                />
              </div>
            </div>

            <div className="flex justify-end">
              <Link
                href="/projects"
                className="mr-4 inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white dark:hover:bg-gray-600"
              >
                キャンセル
              </Link>
              <button
                type="submit"
                disabled={loading}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed dark:bg-primary-700 dark:hover:bg-primary-600"
              >
                {loading ? '作成中...' : '作成'}
              </button>
            </div>
          </form>
        </div>
      </div>

      {/* スクリプトテンプレートモーダル */}
      <ScriptTemplateModal
        isOpen={isTemplateModalOpen}
        onClose={() => setIsTemplateModalOpen(false)}
        onSelectTemplate={handleTemplateSelect}
      />
    </div>
  );
}
