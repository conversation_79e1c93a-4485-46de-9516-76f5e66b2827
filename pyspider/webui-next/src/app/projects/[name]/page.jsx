'use client';

import { useState, useEffect, use } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { projectsApi } from '@/lib/api';
import {
  ArrowLeftIcon,
  BugAntIcon,
  PlayIcon,
  DocumentArrowDownIcon,
  ArrowPathIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  DocumentTextIcon
} from '@heroicons/react/24/outline';
import CodeMirror from '@uiw/react-codemirror';
import { python } from '@codemirror/lang-python';
import { oneDark } from '@codemirror/theme-one-dark';
import { useTheme } from 'next-themes';
import Loading from '@/components/common/Loading';
import TaskProgressBar from '@/components/TaskProgressBar';
import ScriptTemplateModal from '@/components/ScriptTemplateModal';
import { generateScriptTemplate } from '@/lib/scriptTemplates';

export default function ProjectDetailPage({ params }) {
  // Next.js 15の新しいAPIに対応
  const resolvedParams = use(params);
  const router = useRouter();
  const { theme } = useTheme();
  const [mounted, setMounted] = useState(false);
  const [project, setProject] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [activeTab, setActiveTab] = useState('script'); // 'script' または 'tasks'
  const [timeRange, setTimeRange] = useState('all'); // 'all', '1d', '1h', '5m'
  const [counterData, setCounterData] = useState({
    pending: 0,
    active: 0,
    success: 0,
    failed: 0
  });
  const [runningProject, setRunningProject] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    group: 'default',
    status: 'RUNNING',
    script: '',
    rate: 1,
    burst: 10
  });

  // 結果データ用の状態
  const [results, setResults] = useState([]);
  const [resultsLoading, setResultsLoading] = useState(false);
  const [resultsError, setResultsError] = useState(null);
  const [resultsPage, setResultsPage] = useState(0);
  const [resultsTotal, setResultsTotal] = useState(0);
  const [resultsLimit, setResultsLimit] = useState(10);

  // テンプレートモーダル用の状態
  const [isTemplateModalOpen, setIsTemplateModalOpen] = useState(false);

  // マウント状態を管理
  useEffect(() => {
    setMounted(true);
  }, []);

  // プロジェクト情報を取得
  useEffect(() => {
    const fetchProject = async () => {
      try {
        setLoading(true);
        console.log(`=== ProjectDetailPage: Fetching project ===`);
        console.log(`resolvedParams:`, resolvedParams);
        console.log(`resolvedParams.name:`, resolvedParams.name);
        console.log(`resolvedParams.name type:`, typeof resolvedParams.name);

        if (!resolvedParams.name) {
          console.error('Project name is undefined or empty');
          setError('プロジェクト名が指定されていません。');
          setLoading(false);
          return;
        }

        console.log(`About to call projectsApi.getProject with: "${resolvedParams.name}"`);
        const projectData = await projectsApi.getProject(resolvedParams.name);
        console.log(`Successfully received project data:`, projectData);
        setProject(projectData);
        setFormData({
          name: projectData.name,
          group: projectData.group || 'default',
          status: projectData.status || 'RUNNING',
          script: projectData.script || '',
          rate: projectData.rate || 1,
          burst: projectData.burst || 10
        });
        setError(null);
      } catch (err) {
        console.error('Error fetching project:', err);
        setError('プロジェクト情報の取得中にエラーが発生しました。');
      } finally {
        setLoading(false);
      }
    };

    if (resolvedParams.name) {
      fetchProject();
    }
  }, [resolvedParams.name]);

  // プロジェクトのカウンター情報を取得
  useEffect(() => {
    const fetchCounter = async () => {
      if (!resolvedParams.name) return;

      try {
        // pyspiderのAPIサーバーから直接カウンターデータを取得
        const response = await fetch(`http://localhost:5000/api/v2/counter?project=${resolvedParams.name}`);
        if (!response.ok) {
          throw new Error(`API request failed with status ${response.status}`);
        }

        const data = await response.json();
        console.log('Counter data from pyspider API:', data);

        // APIレスポンスからカウンターデータを抽出
        // 選択された時間範囲のカウンターデータを使用
        if (data && data[timeRange]) {
          setCounterData({
            pending: data[timeRange].pending || 0,
            active: data[timeRange].task || 0, // 'task' フィールドを 'active' として使用
            success: data[timeRange].success || 0,
            failed: data[timeRange].failed || 0
          });
        } else {
          console.warn(`No counter data found for project ${resolvedParams.name} with time range ${timeRange}`);
          // データが見つからない場合はデフォルト値を設定
          setCounterData({
            pending: 0,
            active: 0,
            success: 0,
            failed: 0
          });
        }
      } catch (err) {
        console.error('Error fetching project counter from pyspider API:', err);

        // エラーが発生した場合はprojectsApiのメソッドを使用してフォールバック
        try {
          const data = await projectsApi.getProjectCounter(resolvedParams.name);
          setCounterData({
            pending: data.pending || 0,
            active: data.active || 0,
            success: data.success || 0,
            failed: data.failed || 0
          });
        } catch (fallbackErr) {
          console.error('Fallback also failed:', fallbackErr);
        }
      }
    };

    fetchCounter();

    // 10秒ごとにカウンター情報を更新
    const intervalId = setInterval(fetchCounter, 10000);

    return () => clearInterval(intervalId);
  }, [resolvedParams.name, timeRange]); // timeRangeが変更されたときにも再取得

  // プロジェクトの結果を取得
  useEffect(() => {
    const fetchResults = async () => {
      if (!resolvedParams.name || activeTab !== 'tasks') return;

      setResultsLoading(true);
      setResultsError(null);

      try {
        const offset = resultsPage * resultsLimit;
        console.log(`Fetching results for project ${resolvedParams.name} with offset=${offset}, limit=${resultsLimit}`);

        // APIv2エンドポイントから直接取得を試みる
        try {
          const apiUrl = `/api/v2/results/${resolvedParams.name}?offset=${offset}&limit=${resultsLimit}`;
          console.log(`Fetching results from APIv2 endpoint: ${apiUrl}`);

          const response = await fetch(apiUrl);
          if (!response.ok) {
            throw new Error(`API request failed with status ${response.status}`);
          }

          const data = await response.json();
          console.log('Received project results from APIv2:', data);

          // APIv2の実際のレスポンス形式に対応
          if (data && data.status === 'success' && data.data && data.data.results && Array.isArray(data.data.results)) {
            console.log(`Setting ${data.data.results.length} results with total ${data.data.total}`);
            setResults(data.data.results);
            setResultsTotal(data.data.total || data.data.results.length);
          } else if (data && data.results && Array.isArray(data.results)) {
            // 従来の形式への対応
            console.log(`Setting ${data.results.length} results with total ${data.total}`);
            setResults(data.results);
            setResultsTotal(data.total || data.results.length);
          } else if (data && Array.isArray(data)) {
            // 結果が配列の場合の対応
            console.log(`Setting ${data.length} results from array`);
            setResults(data);
            setResultsTotal(data.length);
          } else if (data && data.error) {
            // APIエラーレスポンスの場合
            console.warn('API returned error:', data.error);
            throw new Error(`API error: ${data.error}`);
          } else {
            // その他の予期しない形式の場合
            console.warn('Unexpected results format from APIv2:', data);
            // 空の結果として処理（エラーを投げない）
            setResults([]);
            setResultsTotal(0);
          }
        } catch (apiV2Error) {
          console.error('Error with APIv2 endpoint:', apiV2Error);

          // フォールバック: 標準APIメソッドを使用
          console.log('Falling back to standard API method');
          const data = await projectsApi.getProjectResults(resolvedParams.name, {
            offset,
            limit: resultsLimit
          });

          console.log('Received project results from standard API:', data);

          if (data && data.results && Array.isArray(data.results)) {
            console.log(`Setting ${data.results.length} results with total ${data.total}`);
            setResults(data.results);
            setResultsTotal(data.total || data.results.length);
          } else {
            console.warn('Unexpected results format from standard API:', data);
            // 結果が配列の場合の対応
            if (data && Array.isArray(data)) {
              console.log(`Setting ${data.length} results from array`);
              setResults(data);
              setResultsTotal(data.length);
            } else {
              console.error('No valid results data found');
              setResults([]);
              setResultsTotal(0);
            }
          }
        }
      } catch (err) {
        console.error('Error fetching project results:', err);
        setResultsError('結果データの取得中にエラーが発生しました。');
        setResults([]);
        setResultsTotal(0);
      } finally {
        setResultsLoading(false);
      }
    };

    fetchResults();
  }, [resolvedParams.name, activeTab, resultsPage, resultsLimit]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  // 結果データのページ変更
  const handleResultsPageChange = (newPage) => {
    if (newPage >= 0 && newPage * resultsLimit < resultsTotal) {
      setResultsPage(newPage);
    }
  };

  // 結果データの再読み込み
  const handleRefreshResults = async () => {
    if (resultsLoading) return;

    setResultsLoading(true);
    setResultsError(null);

    try {
      const offset = resultsPage * resultsLimit;
      console.log(`Refreshing results for project ${resolvedParams.name} with offset=${offset}, limit=${resultsLimit}`);

      // APIv2エンドポイントから直接取得を試みる
      try {
        const apiUrl = `/api/v2/results/${resolvedParams.name}?offset=${offset}&limit=${resultsLimit}`;
        console.log(`Refreshing results from APIv2 endpoint: ${apiUrl}`);

        const response = await fetch(apiUrl);
        if (!response.ok) {
          throw new Error(`API request failed with status ${response.status}`);
        }

        const data = await response.json();
        console.log('Received refreshed project results from APIv2:', data);

        // APIv2の実際のレスポンス形式に対応
        if (data && data.status === 'success' && data.data && data.data.results && Array.isArray(data.data.results)) {
          console.log(`Setting ${data.data.results.length} refreshed results with total ${data.data.total}`);
          setResults(data.data.results);
          setResultsTotal(data.data.total || data.data.results.length);
        } else if (data && data.results && Array.isArray(data.results)) {
          // 従来の形式への対応
          console.log(`Setting ${data.results.length} refreshed results with total ${data.total}`);
          setResults(data.results);
          setResultsTotal(data.total || data.results.length);
        } else if (data && Array.isArray(data)) {
          // 結果が配列の場合の対応
          console.log(`Setting ${data.length} refreshed results from array`);
          setResults(data);
          setResultsTotal(data.length);
        } else if (data && data.error) {
          // APIエラーレスポンスの場合
          console.warn('API returned error during refresh:', data.error);
          throw new Error(`API error: ${data.error}`);
        } else {
          // その他の予期しない形式の場合
          console.warn('Unexpected refreshed results format from APIv2:', data);
          // 空の結果として処理（エラーを投げない）
          setResults([]);
          setResultsTotal(0);
        }
      } catch (apiV2Error) {
        console.error('Error with APIv2 endpoint during refresh:', apiV2Error);

        // フォールバック: 標準APIメソッドを使用
        console.log('Falling back to standard API method for refresh');
        const data = await projectsApi.getProjectResults(resolvedParams.name, {
          offset,
          limit: resultsLimit
        });

        console.log('Received refreshed project results from standard API:', data);

        if (data && data.results && Array.isArray(data.results)) {
          console.log(`Setting ${data.results.length} refreshed results with total ${data.total}`);
          setResults(data.results);
          setResultsTotal(data.total || data.results.length);
        } else {
          console.warn('Unexpected refreshed results format from standard API:', data);
          // 結果が配列の場合の対応
          if (data && Array.isArray(data)) {
            console.log(`Setting ${data.length} refreshed results from array`);
            setResults(data);
            setResultsTotal(data.length);
          } else {
            console.error('No valid refreshed results data found');
            setResults([]);
            setResultsTotal(0);
          }
        }
      }
    } catch (err) {
      console.error('Error refreshing project results:', err);
      setResultsError('結果データの更新中にエラーが発生しました。');
    } finally {
      setResultsLoading(false);
    }
  };

  // 結果データのエクスポート
  const handleExportResults = (format) => {
    try {
      projectsApi.exportProjectResults(resolvedParams.name, format);
    } catch (err) {
      console.error(`Error exporting results as ${format}:`, err);
      setResultsError(`結果データの${format}形式でのエクスポート中にエラーが発生しました。`);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      // プロジェクト更新APIを呼び出す
      await projectsApi.updateProject(resolvedParams.name, formData);

      // 成功したらプロジェクト一覧ページにリダイレクト
      router.push('/projects');
    } catch (err) {
      console.error('Error updating project:', err);
      setError('プロジェクトの更新中にエラーが発生しました。');
    } finally {
      setLoading(false);
    }
  };

  // プロジェクトを実行
  const handleRunProject = async () => {
    if (runningProject) return;

    setRunningProject(true);
    try {
      // 実行結果を取得
      const result = await projectsApi.runProject(resolvedParams.name);
      console.log('Run project result:', result);

      // 結果に基づいてフィードバックを表示
      if (result.success) {
        // 成功メッセージを表示
        setError(null); // 以前のエラーをクリア

        // 成功メッセージを表示（タスクが追加されたかどうかで異なるメッセージ）
        const successMessage = result.taskAdded
          ? `✅ プロジェクト ${resolvedParams.name} の実行タスクが正常に追加されました。`
          : `ℹ️ プロジェクト ${resolvedParams.name} の実行リクエストは受け付けられましたが、タスクは追加されませんでした。タスクが既に実行中または予定されている可能性があります。`;

        // エラー表示領域を使って成功メッセージを表示
        setError(successMessage);

        // 5秒後にメッセージを消す
        setTimeout(() => {
          setError(null);
        }, 5000);
      } else {
        // エラーメッセージを表示
        setError(`❌ ${result.message}`);
      }

      // 実行後にカウンター情報を更新（pyspiderのAPIサーバーから直接取得）
      try {
        // 少し待ってからカウンターデータを取得（タスクが登録されるまで待つ）
        await new Promise(resolve => setTimeout(resolve, 1000));

        const response = await fetch(`http://localhost:5000/api/v2/counter?project=${resolvedParams.name}`);
        if (!response.ok) {
          throw new Error(`API request failed with status ${response.status}`);
        }

        const data = await response.json();
        console.log('Counter data after run:', data);

        // APIレスポンスからカウンターデータを抽出
        // 選択された時間範囲のカウンターデータを使用
        if (data && data[timeRange]) {
          setCounterData({
            pending: data[timeRange].pending || 0,
            active: data[timeRange].task || 0, // 'task' フィールドを 'active' として使用
            success: data[timeRange].success || 0,
            failed: data[timeRange].failed || 0
          });
        } else {
          console.warn(`No counter data found for project ${resolvedParams.name} with time range ${timeRange} after run`);
        }
      } catch (counterErr) {
        console.error('Error fetching counter after run:', counterErr);
        // フォールバック
        try {
          const data = await projectsApi.getProjectCounter(resolvedParams.name);
          setCounterData({
            pending: data.pending || 0,
            active: data.active || 0,
            success: data.success || 0,
            failed: data.failed || 0
          });
        } catch (fallbackErr) {
          console.error('Fallback also failed:', fallbackErr);
        }
      }
    } catch (err) {
      console.error('Error running project:', err);
      setError(`❌ プロジェクト ${resolvedParams.name} の実行中にエラーが発生しました: ${err.message || '不明なエラー'}`);
    } finally {
      setRunningProject(false);
    }
  };

  const handleTemplateSelect = (templateId) => {
    const templateScript = generateScriptTemplate(templateId, formData.name || 'my_project');
    setFormData(prev => ({ ...prev, script: templateScript }));
  };

  if (loading && !project) {
    return <Loading size="lg" text="プロジェクト情報を読み込み中..." />;
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <Link
            href="/projects"
            className="mr-4 inline-flex items-center text-sm font-medium text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
          >
            <ArrowLeftIcon className="-ml-1 mr-1 h-5 w-5" aria-hidden="true" />
            戻る
          </Link>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">プロジェクト詳細: {resolvedParams.name}</h1>
        </div>
        <div className="flex space-x-2">
          <button
            onClick={handleRunProject}
            disabled={runningProject || !['RUNNING', 'DEBUG'].includes(formData.status)}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 dark:bg-green-700 dark:hover:bg-green-600 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <PlayIcon className="-ml-1 mr-2 h-5 w-5" aria-hidden="true" />
            {runningProject ? '実行中...' : '実行'}
          </button>
          <Link
            href={`/debug/${resolvedParams.name}`}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:bg-blue-700 dark:hover:bg-blue-600"
          >
            <BugAntIcon className="-ml-1 mr-2 h-5 w-5" aria-hidden="true" />
            デバッグ
          </Link>
        </div>
      </div>

      {error && (
        <div className={`p-4 border-l-4 ${
          error.startsWith('✅')
            ? 'bg-green-50 border-green-500 dark:bg-green-900/20 dark:border-green-500'
            : error.startsWith('ℹ️')
              ? 'bg-blue-50 border-blue-500 dark:bg-blue-900/20 dark:border-blue-500'
              : 'bg-red-50 border-red-500 dark:bg-red-900/20 dark:border-red-500'
        }`}>
          <div className="flex">
            <div className="ml-3">
              <p className={`text-sm ${
                error.startsWith('✅')
                  ? 'text-green-700 dark:text-green-400'
                  : error.startsWith('ℹ️')
                    ? 'text-blue-700 dark:text-blue-400'
                    : 'text-red-700 dark:text-red-400'
              }`}>{error}</p>
            </div>
          </div>
        </div>
      )}

      {/* タブナビゲーション */}
      <div className="border-b border-gray-200 dark:border-gray-700">
        <nav className="-mb-px flex space-x-8">
          <button
            onClick={() => setActiveTab('script')}
            className={`${
              activeTab === 'script'
                ? 'border-primary-500 text-primary-600 dark:text-primary-400'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
            } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
          >
            スクリプト
          </button>
          <button
            onClick={() => setActiveTab('tasks')}
            className={`${
              activeTab === 'tasks'
                ? 'border-primary-500 text-primary-600 dark:text-primary-400'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
            } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
          >
            タスク
          </button>
        </nav>
      </div>

      <div className="bg-white shadow-sm rounded-lg dark:bg-gray-800">
        <div className="px-4 py-5 sm:p-6">
          {activeTab === 'script' ? (
            <form onSubmit={handleSubmit} className="space-y-6">
              <div>
                <label htmlFor="name" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  プロジェクト名 <span className="text-red-500">*</span>
                </label>
                <div className="mt-1">
                  <input
                    type="text"
                    name="name"
                    id="name"
                    required
                    disabled
                    className="block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                    value={formData.name}
                    onChange={handleChange}
                  />
                </div>
              </div>

              <div>
                <label htmlFor="group" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  グループ
                </label>
                <div className="mt-1">
                  <input
                    type="text"
                    name="group"
                    id="group"
                    className="block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                    value={formData.group}
                    onChange={handleChange}
                  />
                </div>
              </div>

              <div>
                <label htmlFor="status" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  ステータス
                </label>
                <div className="mt-1">
                  <select
                    name="status"
                    id="status"
                    className="block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                    value={formData.status}
                    onChange={handleChange}
                  >
                    <option value="RUNNING">実行中</option>
                    <option value="PAUSED">一時停止</option>
                    <option value="STOPPED">停止</option>
                    <option value="CHECKING">確認中</option>
                    <option value="TODO">TODO</option>
                    <option value="DEBUG">デバッグ</option>
                  </select>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label htmlFor="rate" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    レート（リクエスト/秒）
                  </label>
                  <div className="mt-1">
                    <input
                      type="number"
                      name="rate"
                      id="rate"
                      min="0.1"
                      step="0.1"
                      className="block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                      value={formData.rate}
                      onChange={handleChange}
                    />
                  </div>
                  <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                    1秒あたりのリクエスト数を指定します。例: 1.0
                  </p>
                </div>

                <div>
                  <label htmlFor="burst" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    バースト（最大同時リクエスト数）
                  </label>
                  <div className="mt-1">
                    <input
                      type="number"
                      name="burst"
                      id="burst"
                      min="1"
                      step="1"
                      className="block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                      value={formData.burst}
                      onChange={handleChange}
                    />
                  </div>
                  <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                    同時に処理できるリクエストの最大数を指定します。例: 10
                  </p>
                </div>
              </div>

              <div>
                <div className="flex items-center justify-between mb-2">
                  <label htmlFor="script" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    スクリプト <span className="text-red-500">*</span>
                  </label>
                  <button
                    type="button"
                    onClick={() => setIsTemplateModalOpen(true)}
                    className="inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white dark:hover:bg-gray-600"
                  >
                    <DocumentTextIcon className="h-4 w-4 mr-1" />
                    テンプレート
                  </button>
                </div>
                <div className="mt-1">
                  {mounted && (
                    <CodeMirror
                      value={formData.script}
                      height="400px"
                      extensions={[python()]}
                      theme={theme === 'dark' ? oneDark : undefined}
                      onChange={(value) => setFormData(prev => ({ ...prev, script: value }))}
                      className="border rounded-md overflow-hidden"
                    />
                  )}
                </div>
              </div>

              <div className="flex justify-end">
                <Link
                  href="/projects"
                  className="mr-4 inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white dark:hover:bg-gray-600"
                >
                  キャンセル
                </Link>
                <button
                  type="submit"
                  disabled={loading}
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed dark:bg-primary-700 dark:hover:bg-primary-600"
                >
                  {loading ? '更新中...' : '更新'}
                </button>
              </div>
            </form>
          ) : (
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-medium leading-6 text-gray-900 dark:text-white">タスク状態</h3>
                <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                  プロジェクト「{resolvedParams.name}」のタスク実行状況です。
                </p>
              </div>

              <div className="bg-gray-50 p-4 rounded-lg dark:bg-gray-700">
                <div className="flex justify-between items-center mb-4">
                  <h4 className="text-md font-medium text-gray-900 dark:text-white">タスク進捗</h4>

                  {/* 時間範囲切り替えタブ */}
                  <div className="flex text-sm border border-gray-300 rounded-md overflow-hidden dark:border-gray-600">
                    <button
                      onClick={() => setTimeRange('all')}
                      className={`px-3 py-1 ${
                        timeRange === 'all'
                          ? 'bg-primary-500 text-white dark:bg-primary-600'
                          : 'bg-white text-gray-700 hover:bg-gray-100 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600'
                      }`}
                    >
                      全期間
                    </button>
                    <button
                      onClick={() => setTimeRange('1d')}
                      className={`px-3 py-1 border-l border-gray-300 dark:border-gray-600 ${
                        timeRange === '1d'
                          ? 'bg-primary-500 text-white dark:bg-primary-600'
                          : 'bg-white text-gray-700 hover:bg-gray-100 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600'
                      }`}
                    >
                      1日
                    </button>
                    <button
                      onClick={() => setTimeRange('1h')}
                      className={`px-3 py-1 border-l border-gray-300 dark:border-gray-600 ${
                        timeRange === '1h'
                          ? 'bg-primary-500 text-white dark:bg-primary-600'
                          : 'bg-white text-gray-700 hover:bg-gray-100 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600'
                      }`}
                    >
                      1時間
                    </button>
                    <button
                      onClick={() => setTimeRange('5m')}
                      className={`px-3 py-1 border-l border-gray-300 dark:border-gray-600 ${
                        timeRange === '5m'
                          ? 'bg-primary-500 text-white dark:bg-primary-600'
                          : 'bg-white text-gray-700 hover:bg-gray-100 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600'
                      }`}
                    >
                      5分
                    </button>
                  </div>
                </div>

                <TaskProgressBar
                  pending={counterData.pending}
                  active={counterData.active}
                  success={counterData.success}
                  failed={counterData.failed}
                  id={`${resolvedParams.name}-${timeRange}`}
                />

                <div className="mt-2 text-xs text-gray-500 dark:text-gray-400">
                  {timeRange === 'all' && '全期間のタスク状態を表示しています'}
                  {timeRange === '1d' && '過去24時間のタスク状態を表示しています'}
                  {timeRange === '1h' && '過去1時間のタスク状態を表示しています'}
                  {timeRange === '5m' && '過去5分間のタスク状態を表示しています'}
                </div>
              </div>

              <div className="bg-gray-50 p-4 rounded-lg dark:bg-gray-700">
                <h4 className="text-md font-medium text-gray-900 dark:text-white mb-2">タスク実行</h4>
                <p className="text-sm text-gray-500 dark:text-gray-400 mb-4">
                  「実行」ボタンをクリックすると、プロジェクトのクローラーが起動します。
                  プロジェクトのステータスが「実行中」または「デバッグ」の場合のみ実行できます。
                </p>

                <button
                  onClick={handleRunProject}
                  disabled={runningProject || !['RUNNING', 'DEBUG'].includes(formData.status)}
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 dark:bg-green-700 dark:hover:bg-green-600 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <PlayIcon className="-ml-1 mr-2 h-5 w-5" aria-hidden="true" />
                  {runningProject ? '実行中...' : '実行'}
                </button>

                {!['RUNNING', 'DEBUG'].includes(formData.status) && (
                  <p className="mt-2 text-sm text-yellow-600 dark:text-yellow-400">
                    プロジェクトのステータスが「実行中」または「デバッグ」ではないため、実行できません。
                  </p>
                )}
              </div>

              {/* 結果セクション */}
              <div className="bg-gray-50 p-4 rounded-lg dark:bg-gray-700">
                <div className="flex justify-between items-center mb-4">
                  <h4 className="text-md font-medium text-gray-900 dark:text-white">結果一覧</h4>

                  <div className="flex space-x-2">
                    <button
                      onClick={handleRefreshResults}
                      disabled={resultsLoading}
                      className="inline-flex items-center px-3 py-1.5 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      <ArrowPathIcon className={`h-4 w-4 mr-1 ${resultsLoading ? 'animate-spin' : ''}`} />
                      更新
                    </button>

                    <div className="relative inline-block text-left">
                      <div>
                        <button
                          type="button"
                          className="inline-flex items-center px-3 py-1.5 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white dark:hover:bg-gray-600"
                          id="export-menu-button"
                          aria-expanded="true"
                          aria-haspopup="true"
                          onClick={() => document.getElementById('export-dropdown').classList.toggle('hidden')}
                        >
                          <DocumentArrowDownIcon className="h-4 w-4 mr-1" />
                          エクスポート
                        </button>
                      </div>
                      <div
                        id="export-dropdown"
                        className="hidden origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 focus:outline-none dark:bg-gray-800 dark:ring-gray-700 z-10"
                        role="menu"
                        aria-orientation="vertical"
                        aria-labelledby="export-menu-button"
                        tabIndex="-1"
                      >
                        <div className="py-1" role="none">
                          <button
                            onClick={() => {
                              handleExportResults('json');
                              document.getElementById('export-dropdown').classList.add('hidden');
                            }}
                            className="text-gray-700 block px-4 py-2 text-sm w-full text-left hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700"
                            role="menuitem"
                            tabIndex="-1"
                          >
                            JSON形式
                          </button>
                          <button
                            onClick={() => {
                              handleExportResults('csv');
                              document.getElementById('export-dropdown').classList.add('hidden');
                            }}
                            className="text-gray-700 block px-4 py-2 text-sm w-full text-left hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700"
                            role="menuitem"
                            tabIndex="-1"
                          >
                            CSV形式
                          </button>
                          <button
                            onClick={() => {
                              handleExportResults('xml');
                              document.getElementById('export-dropdown').classList.add('hidden');
                            }}
                            className="text-gray-700 block px-4 py-2 text-sm w-full text-left hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700"
                            role="menuitem"
                            tabIndex="-1"
                          >
                            XML形式
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {resultsError && (
                  <div className="bg-red-50 border-l-4 border-red-500 p-4 mb-4 dark:bg-red-900/20 dark:border-red-500">
                    <div className="flex">
                      <div className="ml-3">
                        <p className="text-sm text-red-700 dark:text-red-400">{resultsError}</p>
                      </div>
                    </div>
                  </div>
                )}

                {resultsLoading && results.length === 0 ? (
                  <div className="text-center py-8">
                    <Loading size="md" text="結果データを読み込み中..." />
                  </div>
                ) : results.length > 0 ? (
                  <>
                    <div className="overflow-x-auto">
                      <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                        <thead className="bg-gray-50 dark:bg-gray-800">
                          <tr>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-400">
                              URL
                            </th>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-400">
                              ステータス
                            </th>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-400">
                              時間
                            </th>
                          </tr>
                        </thead>
                        <tbody className="bg-white divide-y divide-gray-200 dark:bg-gray-900 dark:divide-gray-700">
                          {results.map((result, index) => (
                            <tr key={result._id || index} className="hover:bg-gray-50 dark:hover:bg-gray-800">
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400 truncate max-w-xs">
                                <a
                                  href={result.url}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  className="text-primary-600 hover:text-primary-900 dark:text-primary-400 dark:hover:text-primary-300"
                                >
                                  {result.url}
                                </a>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm">
                                <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300`}>
                                  成功
                                </span>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                {new Date(result.updatetime * 1000).toLocaleString()}
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>

                    {/* ページネーション */}
                    <div className="flex items-center justify-between border-t border-gray-200 px-4 py-3 sm:px-6 dark:border-gray-700 mt-4">
                      <div className="flex flex-1 justify-between sm:hidden">
                        <button
                          onClick={() => handleResultsPageChange(resultsPage - 1)}
                          disabled={resultsPage === 0}
                          className="relative inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                          前へ
                        </button>
                        <button
                          onClick={() => handleResultsPageChange(resultsPage + 1)}
                          disabled={(resultsPage + 1) * resultsLimit >= resultsTotal}
                          className="relative ml-3 inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                          次へ
                        </button>
                      </div>
                      <div className="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
                        <div>
                          <p className="text-sm text-gray-700 dark:text-gray-300">
                            <span className="font-medium">{resultsPage * resultsLimit + 1}</span>
                            {' '}から{' '}
                            <span className="font-medium">
                              {Math.min((resultsPage + 1) * resultsLimit, resultsTotal)}
                            </span>
                            {' '}/ 合計{' '}
                            <span className="font-medium">{resultsTotal}</span>
                            {' '}件
                          </p>
                        </div>
                        <div>
                          <nav className="isolate inline-flex -space-x-px rounded-md shadow-sm" aria-label="Pagination">
                            <button
                              onClick={() => handleResultsPageChange(resultsPage - 1)}
                              disabled={resultsPage === 0}
                              className="relative inline-flex items-center rounded-l-md px-2 py-2 text-gray-400 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0 dark:ring-gray-600 dark:hover:bg-gray-700 dark:text-gray-500 disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                              <span className="sr-only">前へ</span>
                              <ChevronLeftIcon className="h-5 w-5" aria-hidden="true" />
                            </button>
                            <button
                              onClick={() => handleResultsPageChange(resultsPage + 1)}
                              disabled={(resultsPage + 1) * resultsLimit >= resultsTotal}
                              className="relative inline-flex items-center rounded-r-md px-2 py-2 text-gray-400 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0 dark:ring-gray-600 dark:hover:bg-gray-700 dark:text-gray-500 disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                              <span className="sr-only">次へ</span>
                              <ChevronRightIcon className="h-5 w-5" aria-hidden="true" />
                            </button>
                          </nav>
                        </div>
                      </div>
                    </div>
                  </>
                ) : (
                  <div className="text-center py-8">
                    <p className="text-gray-500 dark:text-gray-400">結果データがありません。</p>
                    <p className="text-sm text-gray-500 dark:text-gray-400 mt-2">
                      プロジェクトを実行して、タスクを完了させると結果が表示されます。
                    </p>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* スクリプトテンプレートモーダル */}
      <ScriptTemplateModal
        isOpen={isTemplateModalOpen}
        onClose={() => setIsTemplateModalOpen(false)}
        onSelectTemplate={handleTemplateSelect}
      />
    </div>
  );
}
