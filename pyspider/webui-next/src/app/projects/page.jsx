'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { projectsApi, tasksApi } from '@/lib/api';
import Loading from '@/components/common/Loading';
import { getProjectStatusBadgeClass, formatDate } from '@/lib/utils';
import { PlusIcon, MagnifyingGlassIcon, PlayIcon, PauseIcon } from '@heroicons/react/24/outline';
import TaskProgressBar from '@/components/TaskProgressBar';

export default function ProjectsPage() {
  const [projects, setProjects] = useState([]);
  const [counters, setCounters] = useState({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('ALL');

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);

        console.log('Fetching projects data...');

        // プロジェクト一覧を取得（エラーハンドリングを強化）
        try {
          const projectsData = await projectsApi.getProjects();
          console.log('Projects data:', projectsData);
          setProjects(Array.isArray(projectsData) ? projectsData : []);
        } catch (err) {
          console.error('Error fetching projects:', err);
          setProjects([]);
        }

        // タスクカウンターを取得（エラーハンドリングを強化）
        try {
          const counterData = await tasksApi.getTaskCounter();
          console.log('Counter data:', counterData);
          setCounters(counterData || {});
        } catch (err) {
          console.error('Error fetching counters:', err);
          setCounters({});
        }

        setError(null);
      } catch (err) {
        console.error('Error fetching projects data:', err);
        setError('プロジェクトデータの取得中にエラーが発生しました。');
      } finally {
        setLoading(false);
      }
    };

    fetchData();

    // 定期的にデータを更新
    const intervalId = setInterval(fetchData, 30000);

    return () => clearInterval(intervalId);
  }, []);

  const handleRunProject = async (projectName) => {
    try {
      // 実行結果を取得
      const result = await projectsApi.runProject(projectName);
      console.log('Run project result:', result);

      // 結果に基づいてフィードバックを表示
      if (result.success) {
        // 成功メッセージを表示
        setError(null); // 以前のエラーをクリア

        // 成功メッセージを表示（タスクが追加されたかどうかで異なるメッセージ）
        const successMessage = result.taskAdded
          ? `✅ プロジェクト ${projectName} の実行タスクが正常に追加されました。`
          : `ℹ️ プロジェクト ${projectName} の実行リクエストは受け付けられましたが、タスクは追加されませんでした。タスクが既に実行中または予定されている可能性があります。`;

        // エラー表示領域を使って成功メッセージを表示（一時的に）
        setError(successMessage);

        // 3秒後にメッセージを消す
        setTimeout(() => {
          setError(null);
        }, 5000);
      } else {
        // エラーメッセージを表示
        setError(`❌ ${result.message}`);
      }

      // プロジェクト一覧を更新
      const updatedProjects = await projectsApi.getProjects();
      setProjects(updatedProjects);
    } catch (err) {
      console.error(`Error running project ${projectName}:`, err);
      setError(`❌ プロジェクト ${projectName} の実行中にエラーが発生しました: ${err.message || '不明なエラー'}`);
    }
  };

  const handlePauseProject = async (projectName) => {
    try {
      await projectsApi.pauseProject(projectName);
      // プロジェクト一覧を更新
      const updatedProjects = await projectsApi.getProjects();
      setProjects(updatedProjects);
    } catch (err) {
      console.error(`Error pausing project ${projectName}:`, err);
      setError(`プロジェクト ${projectName} の一時停止中にエラーが発生しました。`);
    }
  };

  // 検索とフィルタリングを適用したプロジェクト一覧を取得
  const filteredProjects = projects.filter(project => {
    const matchesSearch = project.name.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'ALL' || project.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  if (loading && projects.length === 0) {
    return <Loading size="lg" text="プロジェクトデータを読み込み中..." />;
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">プロジェクト一覧</h1>
        <Link
          href="/projects/new"
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 dark:bg-primary-700 dark:hover:bg-primary-600"
        >
          <PlusIcon className="-ml-1 mr-2 h-5 w-5" aria-hidden="true" />
          新規プロジェクト
        </Link>
      </div>

      {error && (
        <div className={`p-4 border-l-4 ${
          error.startsWith('✅')
            ? 'bg-green-50 border-green-500 dark:bg-green-900/20 dark:border-green-500'
            : error.startsWith('ℹ️')
              ? 'bg-blue-50 border-blue-500 dark:bg-blue-900/20 dark:border-blue-500'
              : 'bg-red-50 border-red-500 dark:bg-red-900/20 dark:border-red-500'
        }`}>
          <div className="flex">
            <div className="ml-3">
              <p className={`text-sm ${
                error.startsWith('✅')
                  ? 'text-green-700 dark:text-green-400'
                  : error.startsWith('ℹ️')
                    ? 'text-blue-700 dark:text-blue-400'
                    : 'text-red-700 dark:text-red-400'
              }`}>{error}</p>
            </div>
          </div>
        </div>
      )}

      <div className="bg-white shadow-sm rounded-lg dark:bg-gray-800">
        <div className="px-4 py-5 sm:p-6">
          <div className="flex flex-col sm:flex-row gap-4 mb-6">
            <div className="relative flex-1">
              <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
              </div>
              <input
                type="text"
                className="block w-full rounded-md border-0 py-1.5 pl-10 text-gray-900 ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-primary-600 sm:text-sm sm:leading-6 dark:bg-gray-700 dark:text-white dark:ring-gray-600 dark:placeholder:text-gray-400 dark:focus:ring-primary-500"
                placeholder="プロジェクト名で検索..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <div>
              <select
                className="block w-full rounded-md border-0 py-1.5 pl-3 pr-10 text-gray-900 ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-primary-600 sm:text-sm sm:leading-6 dark:bg-gray-700 dark:text-white dark:ring-gray-600 dark:focus:ring-primary-500"
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
              >
                <option value="ALL">すべてのステータス</option>
                <option value="RUNNING">実行中</option>
                <option value="PAUSED">一時停止</option>
                <option value="STOPPED">停止</option>
              </select>
            </div>
          </div>

          {filteredProjects.length === 0 ? (
            <div className="text-center py-12">
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {searchTerm || statusFilter !== 'ALL'
                  ? '検索条件に一致するプロジェクトがありません。'
                  : 'プロジェクトがありません。新規プロジェクトを作成してください。'}
              </p>
            </div>
          ) : (
            <div className="overflow-hidden shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg">
              <table className="min-w-full divide-y divide-gray-300 dark:divide-gray-700">
                <thead className="bg-gray-50 dark:bg-gray-700">
                  <tr>
                    <th scope="col" className="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6 dark:text-white">
                      プロジェクト名
                    </th>
                    <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 dark:text-white">
                      グループ
                    </th>
                    <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 dark:text-white">
                      ステータス
                    </th>
                    <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 dark:text-white">
                      最終更新
                    </th>
                    <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 dark:text-white">
                      rate/burst
                    </th>
                    <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 dark:text-white">
                      タスク
                    </th>
                    <th scope="col" className="relative py-3.5 pl-3 pr-4 sm:pr-6">
                      <span className="sr-only">アクション</span>
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200 bg-white dark:divide-gray-700 dark:bg-gray-800">
                  {filteredProjects.map((project) => {
                    // カウンターデータの安全な取得
                    const counter = counters[project.name] || {};
                    const allStats = counter.all || {};

                    return (
                      <tr key={project.name}>
                        <td className="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-primary-600 sm:pl-6 dark:text-primary-400">
                          <Link href={`/projects/${project.name}`} className="hover:underline">
                            {project.name}
                          </Link>
                        </td>
                        <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500 dark:text-gray-400">
                          {project.group || 'default'}
                        </td>
                        <td className="whitespace-nowrap px-3 py-4 text-sm">
                          <span className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${getProjectStatusBadgeClass(project.status)}`}>
                            {project.status}
                          </span>
                        </td>
                        <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500 dark:text-gray-400">
                          {formatDate(project.updatetime)}
                        </td>
                        <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500 dark:text-gray-400">
                          {project.rate || 1}/{project.burst || 10}
                        </td>
                        <td className="px-3 py-4 text-sm text-gray-500 dark:text-gray-400">
                          <div className="w-48">
                            <TaskProgressBar
                              pending={allStats.pending || 0}
                              active={allStats.retry || 0}
                              success={allStats.success || 0}
                              failed={allStats.failed || 0}
                              id={project.name}
                            />
                          </div>
                          {/* retry をアクティブとして扱う */}
                        </td>
                        <td className="relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-6">
                          {project.status === 'RUNNING' ? (
                            <button
                              onClick={() => handlePauseProject(project.name)}
                              className="inline-flex items-center text-yellow-600 hover:text-yellow-900 dark:text-yellow-400 dark:hover:text-yellow-300"
                            >
                              <PauseIcon className="mr-1.5 h-4 w-4" />
                              一時停止
                            </button>
                          ) : (
                            <button
                              onClick={() => handleRunProject(project.name)}
                              disabled={project.status !== 'DEBUG' && project.status !== 'RUNNING'}
                              className={`inline-flex items-center ${
                                project.status === 'DEBUG' || project.status === 'RUNNING'
                                  ? 'text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300'
                                  : 'text-gray-400 cursor-not-allowed dark:text-gray-600'
                              }`}
                              title={
                                project.status === 'DEBUG' || project.status === 'RUNNING'
                                  ? '実行'
                                  : 'ステータスが DEBUG または RUNNING の場合のみ実行できます'
                              }
                            >
                              <PlayIcon className="mr-1.5 h-4 w-4" />
                              実行
                            </button>
                          )}
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
