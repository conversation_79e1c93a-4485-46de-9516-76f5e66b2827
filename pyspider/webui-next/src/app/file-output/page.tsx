'use client';

import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  Download, 
  FileText, 
  HardDrive, 
  RefreshCw, 
  Settings,
  AlertCircle,
  CheckCircle,
  Clock,
  Database
} from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';

interface FileInfo {
  name: string;
  size: number;
  size_human: string;
  modified: string;
  lines: number;
  project: string;
  is_rotated?: boolean;
}

interface FileOutputStats {
  enabled: boolean;
  output_directory: string;
  max_file_size: number;
  rotation_enabled: boolean;
  rotation_count: number;
  files: FileInfo[];
  total_files: number;
  total_size: number;
  total_size_human: string;
  total_lines: number;
  message?: string;
}

export default function FileOutputPage() {
  const [stats, setStats] = useState<FileOutputStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchStats = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await fetch('/api/v2/file-output/stats');
      const data = await response.json();
      
      if (data.success) {
        setStats(data.data);
      } else {
        setError(data.message || 'Failed to fetch file output stats');
      }
    } catch (err) {
      setError('Network error occurred');
      console.error('Error fetching file output stats:', err);
    } finally {
      setLoading(false);
    }
  };

  const downloadFile = async (filename: string) => {
    try {
      const response = await fetch(`/api/v2/file-output/download/${filename}`);
      
      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      } else {
        const errorData = await response.json();
        alert(`Download failed: ${errorData.message || 'Unknown error'}`);
      }
    } catch (err) {
      alert('Download failed: Network error');
      console.error('Download error:', err);
    }
  };

  const formatDate = (isoString: string) => {
    return new Date(isoString).toLocaleString();
  };

  const getProjectBadgeColor = (project: string) => {
    const colors = ['bg-blue-100 text-blue-800', 'bg-green-100 text-green-800', 'bg-purple-100 text-purple-800', 'bg-orange-100 text-orange-800'];
    const hash = project.split('').reduce((a, b) => a + b.charCodeAt(0), 0);
    return colors[hash % colors.length];
  };

  useEffect(() => {
    fetchStats();
  }, []);

  if (loading) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-center h-64">
          <RefreshCw className="h-8 w-8 animate-spin" />
          <span className="ml-2">Loading file output stats...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto p-6">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      </div>
    );
  }

  if (!stats?.enabled) {
    return (
      <div className="container mx-auto p-6">
        <Alert>
          <Settings className="h-4 w-4" />
          <AlertDescription>
            File output is disabled. Enable it in the configuration to start saving results to JSONL files.
            {stats?.message && ` (${stats.message})`}
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">File Output Management</h1>
        <Button onClick={fetchStats} variant="outline">
          <RefreshCw className="h-4 w-4 mr-2" />
          Refresh
        </Button>
      </div>

      {/* 統計情報カード */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <FileText className="h-5 w-5 text-blue-500" />
              <div>
                <p className="text-sm font-medium text-gray-600">Total Files</p>
                <p className="text-2xl font-bold">{stats.total_files}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <HardDrive className="h-5 w-5 text-green-500" />
              <div>
                <p className="text-sm font-medium text-gray-600">Total Size</p>
                <p className="text-2xl font-bold">{stats.total_size_human}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <Database className="h-5 w-5 text-purple-500" />
              <div>
                <p className="text-sm font-medium text-gray-600">Total Records</p>
                <p className="text-2xl font-bold">{stats.total_lines.toLocaleString()}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <CheckCircle className="h-5 w-5 text-green-500" />
              <div>
                <p className="text-sm font-medium text-gray-600">Status</p>
                <p className="text-lg font-semibold text-green-600">Active</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 設定情報 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Settings className="h-5 w-5 mr-2" />
            Configuration
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div>
              <p className="text-sm font-medium text-gray-600">Output Directory</p>
              <p className="text-sm font-mono bg-gray-100 p-1 rounded">{stats.output_directory}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-600">Max File Size</p>
              <p className="text-sm">{(stats.max_file_size / 1024 / 1024).toFixed(1)} MB</p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-600">Rotation</p>
              <p className="text-sm">{stats.rotation_enabled ? 'Enabled' : 'Disabled'}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-600">Rotation Count</p>
              <p className="text-sm">{stats.rotation_count}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* ファイル一覧 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <FileText className="h-5 w-5 mr-2" />
            Output Files ({stats.files.length})
          </CardTitle>
        </CardHeader>
        <CardContent>
          {stats.files.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>No output files found</p>
            </div>
          ) : (
            <div className="space-y-4">
              {stats.files.map((file, index) => (
                <div key={index} className="border rounded-lg p-4 hover:bg-gray-50">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-2">
                        <h3 className="font-medium">{file.name}</h3>
                        <Badge className={getProjectBadgeColor(file.project)}>
                          {file.project}
                        </Badge>
                        {file.is_rotated && (
                          <Badge variant="secondary">Rotated</Badge>
                        )}
                      </div>
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-gray-600">
                        <div>
                          <span className="font-medium">Size:</span> {file.size_human}
                        </div>
                        <div>
                          <span className="font-medium">Records:</span> {file.lines.toLocaleString()}
                        </div>
                        <div className="flex items-center">
                          <Clock className="h-3 w-3 mr-1" />
                          <span>{formatDate(file.modified)}</span>
                        </div>
                        <div>
                          <Progress 
                            value={(file.size / stats.max_file_size) * 100} 
                            className="w-full h-2"
                          />
                          <span className="text-xs">
                            {((file.size / stats.max_file_size) * 100).toFixed(1)}% of max
                          </span>
                        </div>
                      </div>
                    </div>
                    <Button
                      onClick={() => downloadFile(file.name)}
                      variant="outline"
                      size="sm"
                      className="ml-4"
                    >
                      <Download className="h-4 w-4 mr-1" />
                      Download
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
