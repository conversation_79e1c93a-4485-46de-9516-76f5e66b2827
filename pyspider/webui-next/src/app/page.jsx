'use client';

import { useState, useEffect } from 'react';
import { projectsApi, tasksApi, metricsApi } from '@/lib/api';
import Loading from '@/components/common/Loading';
import ProjectsOverview from '@/components/dashboard/ProjectsOverview';
import SystemStatus from '@/components/dashboard/SystemStatus';
import RecentTasks from '@/components/dashboard/RecentTasks';

export default function Dashboard() {
  const [projects, setProjects] = useState([]);
  const [counters, setCounters] = useState({});
  const [activeTasks, setActiveTasks] = useState([]);
  const [metrics, setMetrics] = useState(null);
  const [componentsStatus, setComponentsStatus] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    // クライアントサイドでのみ実行
    if (typeof window === 'undefined') return;

    const fetchData = async () => {
      try {
        setLoading(true);

        // 並列でデータを取得（エラーハンドリングを強化）
        try {
          const projectsData = await projectsApi.getProjects();
          setProjects(Array.isArray(projectsData) ? projectsData : []);
        } catch (err) {
          console.error('Error fetching projects:', err);
          setProjects([]);
        }

        try {
          const counterData = await tasksApi.getTaskCounter();
          setCounters(counterData || {});
        } catch (err) {
          console.error('Error fetching counters:', err);
          setCounters({});
        }

        // アクティブタスクの取得をAPI v2エンドポイントに変更
        try {
          console.log('Fetching active tasks from API v2 endpoint');
          const activeTasksData = await tasksApi.getActiveTasks(20);

          if (Array.isArray(activeTasksData) && activeTasksData.length > 0) {
            // 更新日時でソート（降順）
            const sortedTasks = [...activeTasksData].sort((a, b) => {
              const aTime = a.updatetime || 0;
              const bTime = b.updatetime || 0;
              return bTime - aTime;
            });

            // 最新の5件を表示
            setActiveTasks(sortedTasks.slice(0, 5));
          } else {
            setActiveTasks([]);
          }
        } catch (err) {
          console.error('Error fetching active tasks:', err);
          setActiveTasks([]);
        }

        try {
          const metricsData = await metricsApi.getMetrics();
          setMetrics(metricsData || null);
        } catch (err) {
          console.error('Error fetching metrics:', err);
          setMetrics(null);
        }

        try {
          const componentsStatusData = await metricsApi.getComponentsStatus();
          setComponentsStatus(componentsStatusData || null);
        } catch (err) {
          console.error('Error fetching components status:', err);
          setComponentsStatus(null);
        }
        setError(null);
      } catch (err) {
        console.error('Error fetching dashboard data:', err);
        setError('データの取得中にエラーが発生しました。');
      } finally {
        setLoading(false);
      }
    };

    fetchData();

    // 定期的にデータを更新
    const intervalId = setInterval(fetchData, 30000);

    return () => clearInterval(intervalId);
  }, []);

  const handleRunProject = async (projectName) => {
    try {
      await projectsApi.runProject(projectName);
      // プロジェクト一覧を更新
      const updatedProjects = await projectsApi.getProjects();
      setProjects(updatedProjects);
    } catch (err) {
      console.error(`Error running project ${projectName}:`, err);
      setError(`プロジェクト ${projectName} の実行中にエラーが発生しました。`);
    }
  };

  const handlePauseProject = async (projectName) => {
    try {
      await projectsApi.pauseProject(projectName);
      // プロジェクト一覧を更新
      const updatedProjects = await projectsApi.getProjects();
      setProjects(updatedProjects);
    } catch (err) {
      console.error(`Error pausing project ${projectName}:`, err);
      setError(`プロジェクト ${projectName} の一時停止中にエラーが発生しました。`);
    }
  };

  if (loading) {
    return <Loading size="lg" text="ダッシュボードデータを読み込み中..." />;
  }

  if (error) {
    return (
      <div className="bg-red-50 border-l-4 border-red-500 p-4 dark:bg-red-900/20 dark:border-red-500">
        <div className="flex">
          <div className="ml-3">
            <p className="text-sm text-red-700 dark:text-red-400">{error}</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <h1 className="text-2xl font-bold text-gray-900 dark:text-white">ダッシュボード</h1>

      <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
        <ProjectsOverview
          projects={projects}
          counters={counters}
          onRunProject={handleRunProject}
          onPauseProject={handlePauseProject}
        />
        <SystemStatus
          metrics={metrics}
          componentsStatus={componentsStatus}
        />
      </div>

      <RecentTasks activeTasks={activeTasks} />
    </div>
  );
}
