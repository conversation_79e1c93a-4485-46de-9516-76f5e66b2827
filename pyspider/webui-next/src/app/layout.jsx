import { Inter } from 'next/font/google';
import '../styles/globals.css';
import Navbar from '@/components/common/Navbar';
import Footer from '@/components/common/Footer';
import ThemeProviderWrapper from '../components/ThemeProviderWrapper';

const inter = Inter({ subsets: ['latin'] });

export default function RootLayout({ children }) {
  return (
    <html lang="ja" suppressHydrationWarning>
      <body className={`${inter.className} min-h-screen flex flex-col`}>
        <ThemeProviderWrapper>
          <Navbar />
          <main className="flex-grow">
            <div className="mx-auto max-w-7xl px-4 py-6 sm:px-6 lg:px-8">
              {children}
            </div>
          </main>
          <Footer />
        </ThemeProviderWrapper>
      </body>
    </html>
  );
}
