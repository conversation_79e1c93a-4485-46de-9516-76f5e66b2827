'use client';

import Link from 'next/link';
import { ArrowRightIcon } from '@heroicons/react/24/outline';
import { getComponentStatusBadgeClass, getComponentStatusText, getComponentName } from '@/lib/utils';

const SystemStatus = ({ metrics, componentsStatus }) => {
  // システムメトリクスを取得
  const system = metrics?.system || {};
  
  return (
    <div className="card">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-lg font-semibold text-gray-900 dark:text-white">システム状態</h2>
        <Link
          href="/metrics"
          className="text-sm font-medium text-primary-600 hover:text-primary-700 dark:text-primary-400 dark:hover:text-primary-300 flex items-center"
        >
          詳細を表示
          <ArrowRightIcon className="ml-1 h-4 w-4" />
        </Link>
      </div>

      <div className="grid grid-cols-1 gap-4 sm:grid-cols-3 mb-6">
        <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200 dark:bg-gray-800 dark:border-gray-700">
          <div className="text-sm font-medium text-gray-500 dark:text-gray-400">CPU使用率</div>
          <div className="mt-1 text-3xl font-semibold text-gray-900 dark:text-white">
            {system.cpu_percent?.toFixed(1) || 0}%
          </div>
        </div>
        <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200 dark:bg-gray-800 dark:border-gray-700">
          <div className="text-sm font-medium text-gray-500 dark:text-gray-400">メモリ使用率</div>
          <div className="mt-1 text-3xl font-semibold text-gray-900 dark:text-white">
            {system.memory_percent?.toFixed(1) || 0}%
          </div>
        </div>
        <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200 dark:bg-gray-800 dark:border-gray-700">
          <div className="text-sm font-medium text-gray-500 dark:text-gray-400">ディスク使用率</div>
          <div className="mt-1 text-3xl font-semibold text-gray-900 dark:text-white">
            {system.disk_percent?.toFixed(1) || 0}%
          </div>
        </div>
      </div>

      <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">コンポーネント状態</h3>
      <div className="overflow-hidden rounded-md border border-gray-200 dark:border-gray-700">
        <ul className="divide-y divide-gray-200 dark:divide-gray-700">
          {componentsStatus && Object.entries(componentsStatus).map(([key, value]) => {
            if (key === 'timestamp') return null;
            
            return (
              <li key={key} className="bg-white dark:bg-gray-800">
                <div className="flex items-center justify-between px-4 py-3 sm:px-6">
                  <div className="flex items-center">
                    <span className="text-sm font-medium text-gray-900 dark:text-white">
                      {getComponentName(key)}
                    </span>
                    <span
                      className={`ml-2 inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${getComponentStatusBadgeClass(
                        value.status
                      )}`}
                    >
                      {getComponentStatusText(value.status)}
                    </span>
                  </div>
                  {value.host && value.port && (
                    <div className="text-xs text-gray-500 dark:text-gray-400">
                      {value.host}:{value.port}
                    </div>
                  )}
                </div>
              </li>
            );
          })}
        </ul>
      </div>
    </div>
  );
};

export default SystemStatus;
