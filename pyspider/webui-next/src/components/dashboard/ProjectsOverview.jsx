'use client';

import Link from 'next/link';
import { ArrowRightIcon, PlayIcon, PauseIcon } from '@heroicons/react/24/outline';
import { getProjectStatusBadgeClass } from '@/lib/utils';

const ProjectsOverview = ({ projects, counters, onRunProject, onPauseProject }) => {
  // 最近更新されたプロジェクトを取得（最大5件）
  const recentProjects = [...projects]
    .sort((a, b) => b.updatetime - a.updatetime)
    .slice(0, 5);

  return (
    <div className="card">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-lg font-semibold text-gray-900 dark:text-white">プロジェクト概要</h2>
        <Link
          href="/projects"
          className="text-sm font-medium text-primary-600 hover:text-primary-700 dark:text-primary-400 dark:hover:text-primary-300 flex items-center"
        >
          すべて表示
          <ArrowRightIcon className="ml-1 h-4 w-4" />
        </Link>
      </div>

      <div className="grid grid-cols-1 gap-4 sm:grid-cols-3 mb-6">
        <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200 dark:bg-gray-800 dark:border-gray-700">
          <div className="text-sm font-medium text-gray-500 dark:text-gray-400">総プロジェクト数</div>
          <div className="mt-1 text-3xl font-semibold text-gray-900 dark:text-white">{projects.length}</div>
        </div>
        <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200 dark:bg-gray-800 dark:border-gray-700">
          <div className="text-sm font-medium text-gray-500 dark:text-gray-400">実行中</div>
          <div className="mt-1 text-3xl font-semibold text-green-600 dark:text-green-400">
            {projects.filter(p => p.status === 'RUNNING').length}
          </div>
        </div>
        <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200 dark:bg-gray-800 dark:border-gray-700">
          <div className="text-sm font-medium text-gray-500 dark:text-gray-400">一時停止</div>
          <div className="mt-1 text-3xl font-semibold text-yellow-600 dark:text-yellow-400">
            {projects.filter(p => p.status === 'PAUSED').length}
          </div>
        </div>
      </div>

      <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">最近のプロジェクト</h3>
      <div className="overflow-hidden rounded-md border border-gray-200 dark:border-gray-700">
        <ul className="divide-y divide-gray-200 dark:divide-gray-700">
          {recentProjects.map((project) => {
            // カウンターデータの安全な取得
            const counter = counters[project.name] || {};
            const allStats = counter.all || {};

            return (
              <li key={project.name} className="bg-white dark:bg-gray-800">
                <div className="flex items-center justify-between px-4 py-4 sm:px-6">
                  <div className="min-w-0 flex-1">
                    <div className="flex items-center">
                      <p className="truncate text-sm font-medium text-primary-600 dark:text-primary-400">
                        <Link href={`/projects/${project.name}`} className="hover:underline">
                          {project.name}
                        </Link>
                      </p>
                      <span
                        className={`ml-2 inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${getProjectStatusBadgeClass(
                          project.status
                        )}`}
                      >
                        {project.status}
                      </span>
                    </div>
                    <div className="mt-1 flex text-xs text-gray-500 dark:text-gray-400">
                      <span className="mr-3">
                        成功: {allStats.success || 0}
                      </span>
                      <span className="mr-3">
                        失敗: {allStats.failed || 0}
                      </span>
                      <span>
                        保留: {allStats.pending || 0}
                      </span>
                    </div>
                  </div>
                  <div className="ml-4 flex flex-shrink-0">
                    {project.status === 'RUNNING' ? (
                      <button
                        onClick={() => onPauseProject(project.name)}
                        className="inline-flex items-center rounded-md bg-yellow-50 px-2 py-1 text-xs font-medium text-yellow-700 hover:bg-yellow-100 dark:bg-yellow-900/20 dark:text-yellow-400 dark:hover:bg-yellow-900/30"
                      >
                        <PauseIcon className="mr-1 h-4 w-4" />
                        一時停止
                      </button>
                    ) : (
                      <button
                        onClick={() => onRunProject(project.name)}
                        disabled={project.status !== 'DEBUG' && project.status !== 'RUNNING'}
                        className={`inline-flex items-center rounded-md px-2 py-1 text-xs font-medium ${
                          project.status === 'DEBUG' || project.status === 'RUNNING'
                            ? 'bg-green-50 text-green-700 hover:bg-green-100 dark:bg-green-900/20 dark:text-green-400 dark:hover:bg-green-900/30'
                            : 'bg-gray-50 text-gray-400 cursor-not-allowed dark:bg-gray-800 dark:text-gray-600'
                        }`}
                        title={
                          project.status === 'DEBUG' || project.status === 'RUNNING'
                            ? '実行'
                            : 'ステータスが DEBUG または RUNNING の場合のみ実行できます'
                        }
                      >
                        <PlayIcon className="mr-1 h-4 w-4" />
                        実行
                      </button>
                    )}
                  </div>
                </div>
              </li>
            );
          })}
        </ul>
      </div>
    </div>
  );
};

export default ProjectsOverview;
