'use client';

import React, { useTransition, useMemo } from 'react';
import { ClockIcon, CheckCircleIcon, XCircleIcon, ExclamationTriangleIcon } from '@heroicons/react/24/outline';
import Link from 'next/link';
import { useOptimizedState } from '../../hooks/useReact19Features';

const RecentTasks = ({ activeTasks = [] }) => {
  // React 19の最適化されたstate管理
  const [filter, setFilter] = useOptimizedState('all');
  const [isPending, startTransition] = useTransition();

  // React 19の自動メモ化を活用したスタイル関数
  const getStatusBadgeClass = useMemo(() => (status) => {
    switch (status?.toLowerCase()) {
      case 'success':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      case 'error':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
      case 'active':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
    }
  }, []);

  // React 19の並行機能を活用したフィルタリング
  const filteredTasks = useMemo(() => {
    if (filter === 'all') return activeTasks;
    return activeTasks.filter(task => task.status?.toLowerCase() === filter);
  }, [activeTasks, filter]);

  const handleFilterChange = (newFilter) => {
    startTransition(() => {
      setFilter(newFilter);
    });
  };

  const formatDate = (timestamp) => {
    if (!timestamp) return 'N/A';
    const date = new Date(timestamp * 1000);
    return date.toLocaleString('ja-JP');
  };

  const shortenUrl = (url) => {
    if (!url) return 'N/A';
    return url.length > 50 ? url.substring(0, 50) + '...' : url;
  };

  return (
    <div className="bg-white dark:bg-gray-800 shadow rounded-lg">
      <div className="px-4 py-5 sm:p-6">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium leading-6 text-gray-900 dark:text-white">
            最近のタスク
          </h3>
          {/* React 19の並行機能を活用したフィルター */}
          <div className="flex space-x-2">
            {['all', 'active', 'success', 'error', 'pending'].map((status) => (
              <button
                key={status}
                onClick={() => handleFilterChange(status)}
                className={`px-3 py-1 text-xs rounded-full transition-colors ${
                  filter === status
                    ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300'
                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-400 dark:hover:bg-gray-600'
                } ${isPending ? 'opacity-50' : ''}`}
                disabled={isPending}
              >
                {status === 'all' ? 'すべて' : status}
              </button>
            ))}
          </div>
        </div>
      </div>
      {filteredTasks.length === 0 ? (
        <div className="px-4 py-5 sm:p-6 text-center">
          <h3 className="mt-2 text-sm font-medium">アクティブなタスクはありません</h3>
          <p className="mt-1 text-sm">現在実行中のタスクはありません。プロジェクトを実行すると、ここにタスクが表示されます。</p>
        </div>
      ) : (
        <div className="overflow-hidden rounded-md border border-gray-200 dark:border-gray-700">
          <ul className="divide-y divide-gray-200 dark:divide-gray-700">
            {filteredTasks.slice(0, 5).map((task) => {
              // タスクIDの形式を決定（プロジェクト名が含まれているかチェック）
              const taskId = task.taskid || task.task_id;
              const taskLink = taskId.includes(':') ? `/tasks/${taskId}` : `/tasks/${task.project}:${taskId}`;

              return (
                <li key={`${task.project}:${taskId}`} className="bg-white dark:bg-gray-800">
                  <div className="px-4 py-4 sm:px-6">
                    <div className="flex items-center justify-between">
                      <div className="truncate text-sm font-medium text-primary-600 dark:text-primary-400">
                        <Link
                          href={taskLink}
                          className="hover:underline"
                          title={`タスクID: ${taskId}`}
                        >
                          {task.project}
                        </Link>
                      </div>
                      <div className="ml-2 flex-shrink-0">
                        <span
                          className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${getStatusBadgeClass(
                            task.status || 'active'
                          )}`}
                        >
                          {task.status || 'active'}
                        </span>
                      </div>
                    </div>
                    <div className="mt-2 sm:flex sm:justify-between">
                      <div className="sm:flex">
                        <p className="flex items-center text-sm text-gray-500 dark:text-gray-400">
                          {shortenUrl(task.url)}
                        </p>
                      </div>
                      <div className="mt-2 flex items-center text-sm text-gray-500 dark:text-gray-400 sm:mt-0">
                        <ClockIcon
                          className="mr-1.5 h-4 w-4 flex-shrink-0 text-gray-400 dark:text-gray-500"
                          aria-hidden="true"
                        />
                        <p>{formatDate(task.updatetime || task.create_time)}</p>
                      </div>
                    </div>
                  </div>
                </li>
              );
            })}
          </ul>
        </div>
      )}
    </div>
  );
};

export default RecentTasks;
