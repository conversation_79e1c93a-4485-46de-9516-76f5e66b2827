import React, { useState } from 'react';

const AlertChannels = ({ channels, onAddChannel, onRemoveChannel }) => {
  const [showAddForm, setShowAddForm] = useState(false);
  const [channelType, setChannelType] = useState('email');
  const [newChannel, setNewChannel] = useState({
    name: '',
    type: 'email',
    enabled: true,
    send_resolved: true,
    severities: ['warning', 'critical'],
    // Email specific
    smtp_server: '',
    smtp_port: 587,
    smtp_username: '',
    smtp_password: '',
    from_addr: '',
    to_addrs: [],
    // Webhook specific
    url: '',
    // Slack specific
    webhook_url: ''
  });
  const [errors, setErrors] = useState({});
  const [toEmail, setToEmail] = useState('');

  // チャンネルタイプに基づいて色を取得
  const getChannelTypeColor = (type) => {
    switch (type.toLowerCase()) {
      case 'email':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
      case 'webhook':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300';
      case 'slack':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
    }
  };

  // 入力ハンドラ
  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    
    if (name === 'type') {
      setChannelType(value);
    }
    
    setNewChannel(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : 
              name === 'smtp_port' ? parseInt(value, 10) : 
              value
    }));
    
    // エラーをクリア
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: null }));
    }
  };

  // 重要度の選択ハンドラ
  const handleSeverityChange = (e) => {
    const { value, checked } = e.target;
    setNewChannel(prev => {
      const severities = [...prev.severities];
      if (checked) {
        if (!severities.includes(value)) {
          severities.push(value);
        }
      } else {
        const index = severities.indexOf(value);
        if (index !== -1) {
          severities.splice(index, 1);
        }
      }
      return { ...prev, severities };
    });
  };

  // メールアドレス追加ハンドラ
  const handleAddEmail = () => {
    if (!toEmail.trim()) return;
    
    // 簡易的なメールアドレス検証
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(toEmail)) {
      setErrors(prev => ({ ...prev, to_addrs: '有効なメールアドレスを入力してください' }));
      return;
    }
    
    setNewChannel(prev => ({
      ...prev,
      to_addrs: [...prev.to_addrs, toEmail]
    }));
    setToEmail('');
    
    // エラーをクリア
    if (errors.to_addrs) {
      setErrors(prev => ({ ...prev, to_addrs: null }));
    }
  };

  // メールアドレス削除ハンドラ
  const handleRemoveEmail = (email) => {
    setNewChannel(prev => ({
      ...prev,
      to_addrs: prev.to_addrs.filter(e => e !== email)
    }));
  };

  // フォーム送信ハンドラ
  const handleSubmit = (e) => {
    e.preventDefault();
    
    // バリデーション
    const validationErrors = {};
    if (!newChannel.name.trim()) {
      validationErrors.name = 'チャンネル名は必須です';
    }
    
    // タイプ別のバリデーション
    if (newChannel.type === 'email') {
      if (!newChannel.smtp_server.trim()) {
        validationErrors.smtp_server = 'SMTPサーバーは必須です';
      }
      if (!newChannel.from_addr.trim()) {
        validationErrors.from_addr = '送信元アドレスは必須です';
      }
      if (newChannel.to_addrs.length === 0) {
        validationErrors.to_addrs = '少なくとも1つの送信先アドレスが必要です';
      }
    } else if (newChannel.type === 'webhook') {
      if (!newChannel.url.trim()) {
        validationErrors.url = 'WebhookのURLは必須です';
      }
    } else if (newChannel.type === 'slack') {
      if (!newChannel.webhook_url.trim()) {
        validationErrors.webhook_url = 'SlackのWebhook URLは必須です';
      }
    }
    
    if (Object.keys(validationErrors).length > 0) {
      setErrors(validationErrors);
      return;
    }
    
    // チャンネルを追加
    onAddChannel(newChannel.name, newChannel);
    
    // フォームをリセット
    setNewChannel({
      name: '',
      type: 'email',
      enabled: true,
      send_resolved: true,
      severities: ['warning', 'critical'],
      smtp_server: '',
      smtp_port: 587,
      smtp_username: '',
      smtp_password: '',
      from_addr: '',
      to_addrs: [],
      url: '',
      webhook_url: ''
    });
    setChannelType('email');
    setShowAddForm(false);
  };

  // チャンネル削除ハンドラ
  const handleRemoveChannel = (name) => {
    if (window.confirm(`チャンネル「${name}」を削除してもよろしいですか？`)) {
      onRemoveChannel(name);
    }
  };

  return (
    <div>
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-lg font-semibold text-gray-900 dark:text-white">通知チャンネル</h2>
        <button
          onClick={() => setShowAddForm(!showAddForm)}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
        >
          {showAddForm ? 'キャンセル' : '新しいチャンネルを追加'}
        </button>
      </div>

      {showAddForm && (
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200 mb-6 dark:bg-gray-800 dark:border-gray-700">
          <h3 className="text-lg font-medium text-gray-900 mb-4 dark:text-white">新しい通知チャンネル</h3>
          <form onSubmit={handleSubmit}>
            <div className="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
              <div className="sm:col-span-3">
                <label htmlFor="name" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  チャンネル名 *
                </label>
                <div className="mt-1">
                  <input
                    type="text"
                    name="name"
                    id="name"
                    value={newChannel.name}
                    onChange={handleInputChange}
                    className={`shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 rounded-md dark:bg-gray-700 dark:border-gray-600 dark:text-white ${
                      errors.name ? 'border-red-300 dark:border-red-700' : ''
                    }`}
                  />
                  {errors.name && (
                    <p className="mt-2 text-sm text-red-600 dark:text-red-500">{errors.name}</p>
                  )}
                </div>
              </div>

              <div className="sm:col-span-3">
                <label htmlFor="type" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  チャンネルタイプ
                </label>
                <div className="mt-1">
                  <select
                    id="type"
                    name="type"
                    value={newChannel.type}
                    onChange={handleInputChange}
                    className="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 rounded-md dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                  >
                    <option value="email">メール</option>
                    <option value="webhook">Webhook</option>
                    <option value="slack">Slack</option>
                  </select>
                </div>
              </div>

              <div className="sm:col-span-6">
                <fieldset>
                  <legend className="text-sm font-medium text-gray-700 dark:text-gray-300">重要度</legend>
                  <div className="mt-2 space-y-2">
                    <div className="flex items-center">
                      <input
                        id="severity-info"
                        name="severity-info"
                        type="checkbox"
                        checked={newChannel.severities.includes('info')}
                        value="info"
                        onChange={handleSeverityChange}
                        className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded dark:border-gray-600 dark:bg-gray-700"
                      />
                      <label htmlFor="severity-info" className="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                        情報
                      </label>
                    </div>
                    <div className="flex items-center">
                      <input
                        id="severity-warning"
                        name="severity-warning"
                        type="checkbox"
                        checked={newChannel.severities.includes('warning')}
                        value="warning"
                        onChange={handleSeverityChange}
                        className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded dark:border-gray-600 dark:bg-gray-700"
                      />
                      <label htmlFor="severity-warning" className="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                        警告
                      </label>
                    </div>
                    <div className="flex items-center">
                      <input
                        id="severity-critical"
                        name="severity-critical"
                        type="checkbox"
                        checked={newChannel.severities.includes('critical')}
                        value="critical"
                        onChange={handleSeverityChange}
                        className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded dark:border-gray-600 dark:bg-gray-700"
                      />
                      <label htmlFor="severity-critical" className="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                        重大
                      </label>
                    </div>
                  </div>
                </fieldset>
              </div>

              <div className="sm:col-span-3">
                <div className="flex items-center">
                  <input
                    id="enabled"
                    name="enabled"
                    type="checkbox"
                    checked={newChannel.enabled}
                    onChange={handleInputChange}
                    className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded dark:border-gray-600 dark:bg-gray-700"
                  />
                  <label htmlFor="enabled" className="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                    有効
                  </label>
                </div>
              </div>

              <div className="sm:col-span-3">
                <div className="flex items-center">
                  <input
                    id="send_resolved"
                    name="send_resolved"
                    type="checkbox"
                    checked={newChannel.send_resolved}
                    onChange={handleInputChange}
                    className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded dark:border-gray-600 dark:bg-gray-700"
                  />
                  <label htmlFor="send_resolved" className="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                    解決通知を送信
                  </label>
                </div>
              </div>

              {/* メール設定 */}
              {channelType === 'email' && (
                <>
                  <div className="sm:col-span-4">
                    <label htmlFor="smtp_server" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                      SMTPサーバー *
                    </label>
                    <div className="mt-1">
                      <input
                        type="text"
                        name="smtp_server"
                        id="smtp_server"
                        value={newChannel.smtp_server}
                        onChange={handleInputChange}
                        className={`shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 rounded-md dark:bg-gray-700 dark:border-gray-600 dark:text-white ${
                          errors.smtp_server ? 'border-red-300 dark:border-red-700' : ''
                        }`}
                      />
                      {errors.smtp_server && (
                        <p className="mt-2 text-sm text-red-600 dark:text-red-500">{errors.smtp_server}</p>
                      )}
                    </div>
                  </div>

                  <div className="sm:col-span-2">
                    <label htmlFor="smtp_port" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                      SMTPポート
                    </label>
                    <div className="mt-1">
                      <input
                        type="number"
                        name="smtp_port"
                        id="smtp_port"
                        value={newChannel.smtp_port}
                        onChange={handleInputChange}
                        className="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 rounded-md dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                      />
                    </div>
                  </div>

                  <div className="sm:col-span-3">
                    <label htmlFor="smtp_username" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                      SMTPユーザー名
                    </label>
                    <div className="mt-1">
                      <input
                        type="text"
                        name="smtp_username"
                        id="smtp_username"
                        value={newChannel.smtp_username}
                        onChange={handleInputChange}
                        className="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 rounded-md dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                      />
                    </div>
                  </div>

                  <div className="sm:col-span-3">
                    <label htmlFor="smtp_password" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                      SMTPパスワード
                    </label>
                    <div className="mt-1">
                      <input
                        type="password"
                        name="smtp_password"
                        id="smtp_password"
                        value={newChannel.smtp_password}
                        onChange={handleInputChange}
                        className="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 rounded-md dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                      />
                    </div>
                  </div>

                  <div className="sm:col-span-6">
                    <label htmlFor="from_addr" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                      送信元アドレス *
                    </label>
                    <div className="mt-1">
                      <input
                        type="email"
                        name="from_addr"
                        id="from_addr"
                        value={newChannel.from_addr}
                        onChange={handleInputChange}
                        className={`shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 rounded-md dark:bg-gray-700 dark:border-gray-600 dark:text-white ${
                          errors.from_addr ? 'border-red-300 dark:border-red-700' : ''
                        }`}
                      />
                      {errors.from_addr && (
                        <p className="mt-2 text-sm text-red-600 dark:text-red-500">{errors.from_addr}</p>
                      )}
                    </div>
                  </div>

                  <div className="sm:col-span-6">
                    <label htmlFor="to_addrs" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                      送信先アドレス *
                    </label>
                    <div className="mt-1 flex">
                      <input
                        type="email"
                        name="to_addrs"
                        id="to_addrs"
                        value={toEmail}
                        onChange={(e) => setToEmail(e.target.value)}
                        className={`shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 rounded-md dark:bg-gray-700 dark:border-gray-600 dark:text-white ${
                          errors.to_addrs ? 'border-red-300 dark:border-red-700' : ''
                        }`}
                      />
                      <button
                        type="button"
                        onClick={handleAddEmail}
                        className="ml-2 inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                      >
                        追加
                      </button>
                    </div>
                    {errors.to_addrs && (
                      <p className="mt-2 text-sm text-red-600 dark:text-red-500">{errors.to_addrs}</p>
                    )}
                    {newChannel.to_addrs.length > 0 && (
                      <div className="mt-2 flex flex-wrap gap-2">
                        {newChannel.to_addrs.map((email) => (
                          <span
                            key={email}
                            className="inline-flex items-center px-2.5 py-0.5 rounded-md text-sm font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300"
                          >
                            {email}
                            <button
                              type="button"
                              onClick={() => handleRemoveEmail(email)}
                              className="ml-1.5 inline-flex text-blue-400 hover:text-blue-600 dark:text-blue-300 dark:hover:text-blue-100"
                            >
                              <span className="sr-only">削除</span>
                              <svg className="h-4 w-4" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                              </svg>
                            </button>
                          </span>
                        ))}
                      </div>
                    )}
                  </div>
                </>
              )}

              {/* Webhook設定 */}
              {channelType === 'webhook' && (
                <div className="sm:col-span-6">
                  <label htmlFor="url" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Webhook URL *
                  </label>
                  <div className="mt-1">
                    <input
                      type="url"
                      name="url"
                      id="url"
                      value={newChannel.url}
                      onChange={handleInputChange}
                      className={`shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 rounded-md dark:bg-gray-700 dark:border-gray-600 dark:text-white ${
                        errors.url ? 'border-red-300 dark:border-red-700' : ''
                      }`}
                    />
                    {errors.url && (
                      <p className="mt-2 text-sm text-red-600 dark:text-red-500">{errors.url}</p>
                    )}
                  </div>
                </div>
              )}

              {/* Slack設定 */}
              {channelType === 'slack' && (
                <div className="sm:col-span-6">
                  <label htmlFor="webhook_url" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Slack Webhook URL *
                  </label>
                  <div className="mt-1">
                    <input
                      type="url"
                      name="webhook_url"
                      id="webhook_url"
                      value={newChannel.webhook_url}
                      onChange={handleInputChange}
                      className={`shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 rounded-md dark:bg-gray-700 dark:border-gray-600 dark:text-white ${
                        errors.webhook_url ? 'border-red-300 dark:border-red-700' : ''
                      }`}
                    />
                    {errors.webhook_url && (
                      <p className="mt-2 text-sm text-red-600 dark:text-red-500">{errors.webhook_url}</p>
                    )}
                  </div>
                </div>
              )}
            </div>

            <div className="mt-6 flex justify-end">
              <button
                type="button"
                onClick={() => setShowAddForm(false)}
                className="mr-3 inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-600"
              >
                キャンセル
              </button>
              <button
                type="submit"
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              >
                保存
              </button>
            </div>
          </form>
        </div>
      )}

      {(!channels || Object.keys(channels).length === 0) ? (
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200 dark:bg-gray-800 dark:border-gray-700">
          <p className="text-gray-500 dark:text-gray-400">通知チャンネルがありません。</p>
        </div>
      ) : (
        <div className="bg-white shadow-sm overflow-hidden sm:rounded-md dark:bg-gray-800">
          <ul className="divide-y divide-gray-200 dark:divide-gray-700">
            {Object.entries(channels).map(([name, channel]) => (
              <li key={name}>
                <div className="px-4 py-4 sm:px-6">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <p className="text-sm font-medium text-primary-600 truncate dark:text-primary-400">
                        {name}
                      </p>
                      <div className={`ml-2 flex-shrink-0 flex`}>
                        <p className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getChannelTypeColor(channel.type)}`}>
                          {channel.type}
                        </p>
                      </div>
                      {channel.enabled === false && (
                        <div className="ml-2 flex-shrink-0 flex">
                          <p className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300">
                            無効
                          </p>
                        </div>
                      )}
                    </div>
                    <div className="ml-2 flex-shrink-0 flex">
                      <button
                        onClick={() => handleRemoveChannel(name)}
                        className="inline-flex items-center px-2.5 py-1.5 border border-gray-300 text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-600"
                      >
                        削除
                      </button>
                    </div>
                  </div>
                  <div className="mt-2 sm:flex sm:justify-between">
                    <div className="sm:flex">
                      <p className="flex items-center text-sm text-gray-500 dark:text-gray-400">
                        {channel.type === 'email' && `${channel.smtp_server}:${channel.smtp_port}`}
                        {channel.type === 'webhook' && channel.url}
                        {channel.type === 'slack' && 'Slack Webhook'}
                      </p>
                    </div>
                    <div className="mt-2 flex items-center text-sm text-gray-500 sm:mt-0 dark:text-gray-400">
                      <p>
                        重要度: {channel.severities.join(', ')}
                      </p>
                    </div>
                  </div>
                  {channel.type === 'email' && channel.to_addrs && channel.to_addrs.length > 0 && (
                    <div className="mt-2">
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        送信先: {channel.to_addrs.join(', ')}
                      </p>
                    </div>
                  )}
                </div>
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
};

export default AlertChannels;
