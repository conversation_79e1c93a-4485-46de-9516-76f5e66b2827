import React from 'react';

const ActiveAlerts = ({ alerts }) => {
  // アラートの重要度に基づいて色を取得
  const getSeverityColor = (severity) => {
    switch (severity.toLowerCase()) {
      case 'critical':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
      case 'warning':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
      case 'info':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
    }
  };

  // タイムスタンプをフォーマット
  const formatTime = (timestamp) => {
    if (!timestamp) return 'N/A';
    return new Date(timestamp * 1000).toLocaleString();
  };

  // 経過時間を計算
  const getElapsedTime = (timestamp) => {
    if (!timestamp) return '';
    
    const now = Date.now();
    const alertTime = timestamp * 1000;
    const elapsed = now - alertTime;
    
    const seconds = Math.floor(elapsed / 1000);
    if (seconds < 60) {
      return `${seconds}秒前`;
    }
    
    const minutes = Math.floor(seconds / 60);
    if (minutes < 60) {
      return `${minutes}分前`;
    }
    
    const hours = Math.floor(minutes / 60);
    if (hours < 24) {
      return `${hours}時間前`;
    }
    
    const days = Math.floor(hours / 24);
    return `${days}日前`;
  };

  if (!alerts || alerts.length === 0) {
    return (
      <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200 dark:bg-gray-800 dark:border-gray-700">
        <p className="text-gray-500 dark:text-gray-400">アクティブなアラートはありません。</p>
      </div>
    );
  }

  return (
    <div>
      <div className="bg-white shadow-sm overflow-hidden sm:rounded-md dark:bg-gray-800">
        <ul className="divide-y divide-gray-200 dark:divide-gray-700">
          {alerts.map((alert, index) => (
            <li key={`${alert.rule}-${alert.timestamp}`}>
              <div className="px-4 py-4 sm:px-6">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <p className="text-sm font-medium text-primary-600 truncate dark:text-primary-400">
                      {alert.rule}
                    </p>
                    <div className={`ml-2 flex-shrink-0 flex`}>
                      <p className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getSeverityColor(alert.severity)}`}>
                        {alert.severity}
                      </p>
                    </div>
                  </div>
                  <div className="ml-2 flex-shrink-0 flex">
                    <p className="px-2 text-xs text-gray-500 dark:text-gray-400">
                      {getElapsedTime(alert.timestamp)}
                    </p>
                  </div>
                </div>
                <div className="mt-2 sm:flex sm:justify-between">
                  <div className="sm:flex">
                    <p className="flex items-center text-sm text-gray-500 dark:text-gray-400">
                      {alert.description}
                    </p>
                  </div>
                  <div className="mt-2 flex items-center text-sm text-gray-500 sm:mt-0 dark:text-gray-400">
                    <p>
                      {formatTime(alert.timestamp)}
                    </p>
                  </div>
                </div>
                
                {/* メトリクス情報 */}
                {alert.metrics && (
                  <div className="mt-2 border-t border-gray-100 pt-2 dark:border-gray-700">
                    <details className="text-sm">
                      <summary className="text-gray-500 cursor-pointer dark:text-gray-400">メトリクス詳細</summary>
                      <div className="mt-2 overflow-x-auto">
                        <pre className="text-xs text-gray-600 dark:text-gray-300 bg-gray-50 p-2 rounded dark:bg-gray-900">
                          {JSON.stringify(alert.metrics, null, 2)}
                        </pre>
                      </div>
                    </details>
                  </div>
                )}
              </div>
            </li>
          ))}
        </ul>
      </div>
    </div>
  );
};

export default ActiveAlerts;
