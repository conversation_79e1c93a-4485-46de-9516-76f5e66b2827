import React, { useState } from 'react';

const AlertRules = ({ rules, onAddRule, onRemoveRule }) => {
  const [showAddForm, setShowAddForm] = useState(false);
  const [newRule, setNewRule] = useState({
    name: '',
    description: '',
    condition: '',
    duration: 300,
    severity: 'warning',
    enabled: true
  });
  const [errors, setErrors] = useState({});

  // アラートの重要度に基づいて色を取得
  const getSeverityColor = (severity) => {
    switch (severity.toLowerCase()) {
      case 'critical':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
      case 'warning':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
      case 'info':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
    }
  };

  // 入力ハンドラ
  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setNewRule(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
    
    // エラーをクリア
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: null }));
    }
  };

  // フォーム送信ハンドラ
  const handleSubmit = (e) => {
    e.preventDefault();
    
    // バリデーション
    const validationErrors = {};
    if (!newRule.name.trim()) {
      validationErrors.name = 'ルール名は必須です';
    }
    if (!newRule.condition.trim()) {
      validationErrors.condition = '条件は必須です';
    }
    if (!newRule.description.trim()) {
      validationErrors.description = '説明は必須です';
    }
    if (newRule.duration <= 0) {
      validationErrors.duration = '持続時間は正の値である必要があります';
    }
    
    if (Object.keys(validationErrors).length > 0) {
      setErrors(validationErrors);
      return;
    }
    
    // ルールを追加
    onAddRule(newRule);
    
    // フォームをリセット
    setNewRule({
      name: '',
      description: '',
      condition: '',
      duration: 300,
      severity: 'warning',
      enabled: true
    });
    setShowAddForm(false);
  };

  // ルール削除ハンドラ
  const handleRemoveRule = (name) => {
    if (window.confirm(`ルール「${name}」を削除してもよろしいですか？`)) {
      onRemoveRule(name);
    }
  };

  return (
    <div>
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-lg font-semibold text-gray-900 dark:text-white">アラートルール</h2>
        <button
          onClick={() => setShowAddForm(!showAddForm)}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
        >
          {showAddForm ? 'キャンセル' : '新しいルールを追加'}
        </button>
      </div>

      {showAddForm && (
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200 mb-6 dark:bg-gray-800 dark:border-gray-700">
          <h3 className="text-lg font-medium text-gray-900 mb-4 dark:text-white">新しいアラートルール</h3>
          <form onSubmit={handleSubmit}>
            <div className="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
              <div className="sm:col-span-3">
                <label htmlFor="name" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  ルール名 *
                </label>
                <div className="mt-1">
                  <input
                    type="text"
                    name="name"
                    id="name"
                    value={newRule.name}
                    onChange={handleInputChange}
                    className={`shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 rounded-md dark:bg-gray-700 dark:border-gray-600 dark:text-white ${
                      errors.name ? 'border-red-300 dark:border-red-700' : ''
                    }`}
                  />
                  {errors.name && (
                    <p className="mt-2 text-sm text-red-600 dark:text-red-500">{errors.name}</p>
                  )}
                </div>
              </div>

              <div className="sm:col-span-3">
                <label htmlFor="severity" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  重要度
                </label>
                <div className="mt-1">
                  <select
                    id="severity"
                    name="severity"
                    value={newRule.severity}
                    onChange={handleInputChange}
                    className="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 rounded-md dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                  >
                    <option value="info">情報</option>
                    <option value="warning">警告</option>
                    <option value="critical">重大</option>
                  </select>
                </div>
              </div>

              <div className="sm:col-span-6">
                <label htmlFor="description" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  説明 *
                </label>
                <div className="mt-1">
                  <input
                    type="text"
                    name="description"
                    id="description"
                    value={newRule.description}
                    onChange={handleInputChange}
                    className={`shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 rounded-md dark:bg-gray-700 dark:border-gray-600 dark:text-white ${
                      errors.description ? 'border-red-300 dark:border-red-700' : ''
                    }`}
                  />
                  {errors.description && (
                    <p className="mt-2 text-sm text-red-600 dark:text-red-500">{errors.description}</p>
                  )}
                </div>
              </div>

              <div className="sm:col-span-6">
                <label htmlFor="condition" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  条件 *
                </label>
                <div className="mt-1">
                  <input
                    type="text"
                    name="condition"
                    id="condition"
                    value={newRule.condition}
                    onChange={handleInputChange}
                    placeholder="例: system.cpu_percent > 90"
                    className={`shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 rounded-md dark:bg-gray-700 dark:border-gray-600 dark:text-white ${
                      errors.condition ? 'border-red-300 dark:border-red-700' : ''
                    }`}
                  />
                  {errors.condition && (
                    <p className="mt-2 text-sm text-red-600 dark:text-red-500">{errors.condition}</p>
                  )}
                  <p className="mt-2 text-xs text-gray-500 dark:text-gray-400">
                    利用可能な変数: system.cpu_percent, system.memory_percent, system.disk_percent, scheduler.queue_size, scheduler.processing_tasks, scheduler.total_tasks_24h, scheduler.failed_tasks_24h, scheduler.success_tasks_24h, scheduler.pending_tasks
                  </p>
                </div>
              </div>

              <div className="sm:col-span-3">
                <label htmlFor="duration" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  持続時間（秒）
                </label>
                <div className="mt-1">
                  <input
                    type="number"
                    name="duration"
                    id="duration"
                    value={newRule.duration}
                    onChange={handleInputChange}
                    min="0"
                    className={`shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 rounded-md dark:bg-gray-700 dark:border-gray-600 dark:text-white ${
                      errors.duration ? 'border-red-300 dark:border-red-700' : ''
                    }`}
                  />
                  {errors.duration && (
                    <p className="mt-2 text-sm text-red-600 dark:text-red-500">{errors.duration}</p>
                  )}
                </div>
              </div>

              <div className="sm:col-span-3">
                <div className="flex items-center h-full mt-6">
                  <input
                    id="enabled"
                    name="enabled"
                    type="checkbox"
                    checked={newRule.enabled}
                    onChange={handleInputChange}
                    className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded dark:border-gray-600 dark:bg-gray-700"
                  />
                  <label htmlFor="enabled" className="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                    有効
                  </label>
                </div>
              </div>
            </div>

            <div className="mt-6 flex justify-end">
              <button
                type="button"
                onClick={() => setShowAddForm(false)}
                className="mr-3 inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-600"
              >
                キャンセル
              </button>
              <button
                type="submit"
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              >
                保存
              </button>
            </div>
          </form>
        </div>
      )}

      {(!rules || rules.length === 0) ? (
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200 dark:bg-gray-800 dark:border-gray-700">
          <p className="text-gray-500 dark:text-gray-400">アラートルールがありません。</p>
        </div>
      ) : (
        <div className="bg-white shadow-sm overflow-hidden sm:rounded-md dark:bg-gray-800">
          <ul className="divide-y divide-gray-200 dark:divide-gray-700">
            {rules.map((rule) => (
              <li key={rule.name}>
                <div className="px-4 py-4 sm:px-6">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <p className="text-sm font-medium text-primary-600 truncate dark:text-primary-400">
                        {rule.name}
                      </p>
                      <div className={`ml-2 flex-shrink-0 flex`}>
                        <p className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getSeverityColor(rule.severity)}`}>
                          {rule.severity}
                        </p>
                      </div>
                      {rule.enabled === false && (
                        <div className="ml-2 flex-shrink-0 flex">
                          <p className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300">
                            無効
                          </p>
                        </div>
                      )}
                    </div>
                    <div className="ml-2 flex-shrink-0 flex">
                      <button
                        onClick={() => handleRemoveRule(rule.name)}
                        className="inline-flex items-center px-2.5 py-1.5 border border-gray-300 text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-600"
                      >
                        削除
                      </button>
                    </div>
                  </div>
                  <div className="mt-2 sm:flex sm:justify-between">
                    <div className="sm:flex">
                      <p className="flex items-center text-sm text-gray-500 dark:text-gray-400">
                        {rule.description}
                      </p>
                    </div>
                    <div className="mt-2 flex items-center text-sm text-gray-500 sm:mt-0 dark:text-gray-400">
                      <p>
                        持続時間: {rule.duration}秒
                      </p>
                    </div>
                  </div>
                  <div className="mt-2">
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      <span className="font-medium">条件:</span> {rule.condition}
                    </p>
                  </div>
                </div>
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
};

export default AlertRules;
