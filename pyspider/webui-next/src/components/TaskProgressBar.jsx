'use client';

import React from 'react';
import { Tooltip } from 'react-tooltip';

/**
 * タスクの進捗状況を表示するプログレスバーコンポーネント
 * 
 * @param {Object} props
 * @param {number} props.pending - 保留中のタスク数
 * @param {number} props.active - 実行中のタスク数
 * @param {number} props.success - 成功したタスク数
 * @param {number} props.failed - 失敗したタスク数
 * @param {string} props.id - ツールチップのID（一意である必要があります）
 */
export default function TaskProgressBar({ pending = 0, active = 0, success = 0, failed = 0, id }) {
  // 合計タスク数を計算
  const total = pending + active + success + failed;
  
  // 各タスクの割合を計算（%）
  const pendingPercent = total > 0 ? (pending / total) * 100 : 0;
  const activePercent = total > 0 ? (active / total) * 100 : 0;
  const successPercent = total > 0 ? (success / total) * 100 : 0;
  const failedPercent = total > 0 ? (failed / total) * 100 : 0;
  
  // ツールチップのID
  const tooltipId = `task-progress-${id}`;
  
  return (
    <div className="w-full">
      {/* タスク数の表示 */}
      <div className="flex justify-between text-xs mb-1">
        <div className="flex space-x-2">
          <span className="text-yellow-600 dark:text-yellow-400">Pending: {pending}</span>
          <span className="text-blue-600 dark:text-blue-400">Active: {active}</span>
          <span className="text-green-600 dark:text-green-400">Success: {success}</span>
          <span className="text-red-600 dark:text-red-400">Error: {failed}</span>
        </div>
        <span className="text-gray-500 dark:text-gray-400">Total: {total}</span>
      </div>
      
      {/* プログレスバー */}
      <div 
        className="h-2 w-full bg-gray-200 rounded-full overflow-hidden dark:bg-gray-700 flex"
        data-tooltip-id={tooltipId}
        data-tooltip-content={`Pending: ${pending} (${pendingPercent.toFixed(1)}%), Active: ${active} (${activePercent.toFixed(1)}%), Success: ${success} (${successPercent.toFixed(1)}%), Error: ${failed} (${failedPercent.toFixed(1)}%)`}
      >
        {/* 保留中 */}
        {pendingPercent > 0 && (
          <div 
            className="h-full bg-yellow-500" 
            style={{ width: `${pendingPercent}%` }}
          />
        )}
        
        {/* 実行中 */}
        {activePercent > 0 && (
          <div 
            className="h-full bg-blue-500" 
            style={{ width: `${activePercent}%` }}
          />
        )}
        
        {/* 成功 */}
        {successPercent > 0 && (
          <div 
            className="h-full bg-green-500" 
            style={{ width: `${successPercent}%` }}
          />
        )}
        
        {/* 失敗 */}
        {failedPercent > 0 && (
          <div 
            className="h-full bg-red-500" 
            style={{ width: `${failedPercent}%` }}
          />
        )}
      </div>
      
      {/* ツールチップ */}
      <Tooltip id={tooltipId} />
    </div>
  );
}
