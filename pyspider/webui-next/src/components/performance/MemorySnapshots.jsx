import React, { useState, useMemo } from 'react';

const MemorySnapshots = ({ memorySnapshots, onTakeSnapshot }) => {
  const [selectedSnapshot, setSelectedSnapshot] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [sortField, setSortField] = useState('size');
  const [sortDirection, setSortDirection] = useState('desc');

  // 最新のスナップショットを選択
  useMemo(() => {
    if (memorySnapshots && memorySnapshots.length > 0 && !selectedSnapshot) {
      setSelectedSnapshot(memorySnapshots[memorySnapshots.length - 1]);
    }
  }, [memorySnapshots, selectedSnapshot]);

  // 選択されたスナップショットの統計を取得
  const selectedStats = useMemo(() => {
    if (!selectedSnapshot) return [];
    
    let stats = [...(selectedSnapshot.stats || [])];
    
    // 検索フィルタリング
    if (searchTerm) {
      const lowerSearchTerm = searchTerm.toLowerCase();
      stats = stats.filter(item => 
        (item.file && item.file.toLowerCase().includes(lowerSearchTerm))
      );
    }
    
    // ソート
    stats.sort((a, b) => {
      const aValue = a[sortField] || 0;
      const bValue = b[sortField] || 0;
      
      if (sortDirection === 'asc') {
        return aValue - bValue;
      } else {
        return bValue - aValue;
      }
    });
    
    return stats;
  }, [selectedSnapshot, searchTerm, sortField, sortDirection]);

  // ソートハンドラ
  const handleSort = (field) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('desc');
    }
  };

  // 検索ハンドラ
  const handleSearch = (e) => {
    setSearchTerm(e.target.value);
  };

  // 値のフォーマット関数
  const formatBytes = (bytes) => {
    if (bytes === undefined || bytes === null) return 'N/A';
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    if (bytes === 0) return '0 B';
    const i = parseInt(Math.floor(Math.log(bytes) / Math.log(1024)), 10);
    if (i === 0) return `${bytes} ${sizes[i]}`;
    return `${(bytes / (1024 ** i)).toFixed(2)} ${sizes[i]}`;
  };

  const formatTime = (timestamp) => {
    if (!timestamp) return 'N/A';
    return new Date(timestamp * 1000).toLocaleString();
  };

  if (!memorySnapshots || memorySnapshots.length === 0) {
    return (
      <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200 dark:bg-gray-800 dark:border-gray-700">
        <div className="flex justify-between items-center mb-4">
          <p className="text-gray-500 dark:text-gray-400">メモリスナップショットがありません。</p>
          <button
            onClick={onTakeSnapshot}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            スナップショットを取得
          </button>
        </div>
      </div>
    );
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-lg font-semibold text-gray-900 dark:text-white">メモリスナップショット</h2>
        <button
          onClick={onTakeSnapshot}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
        >
          新しいスナップショットを取得
        </button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        {memorySnapshots.slice(-4).reverse().map((snapshot, index) => (
          <div
            key={snapshot.timestamp}
            className={`p-4 rounded-lg border ${
              selectedSnapshot && selectedSnapshot.timestamp === snapshot.timestamp
                ? 'border-primary-500 bg-primary-50 dark:bg-primary-900 dark:border-primary-500'
                : 'border-gray-200 bg-white dark:bg-gray-800 dark:border-gray-700'
            } cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors`}
            onClick={() => setSelectedSnapshot(snapshot)}
          >
            <p className="text-sm font-medium text-gray-900 dark:text-white">
              スナップショット {memorySnapshots.length - index}
            </p>
            <p className="text-xs text-gray-500 dark:text-gray-400">
              {formatTime(snapshot.timestamp)}
            </p>
            <p className="mt-2 text-sm font-semibold text-gray-900 dark:text-white">
              合計: {formatBytes(snapshot.total_size)}
            </p>
            <p className="text-xs text-gray-500 dark:text-gray-400">
              オブジェクト数: {snapshot.total_count.toLocaleString()}
            </p>
          </div>
        ))}
      </div>

      {selectedSnapshot && (
        <div>
          <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200 dark:bg-gray-800 dark:border-gray-700 mb-4">
            <h3 className="text-sm font-medium text-gray-900 dark:text-white mb-2">スナップショット情報</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <p className="text-xs text-gray-500 dark:text-gray-400">取得時間</p>
                <p className="text-sm font-medium text-gray-900 dark:text-white">{formatTime(selectedSnapshot.timestamp)}</p>
              </div>
              <div>
                <p className="text-xs text-gray-500 dark:text-gray-400">合計サイズ</p>
                <p className="text-sm font-medium text-gray-900 dark:text-white">{formatBytes(selectedSnapshot.total_size)}</p>
              </div>
              <div>
                <p className="text-xs text-gray-500 dark:text-gray-400">オブジェクト数</p>
                <p className="text-sm font-medium text-gray-900 dark:text-white">{selectedSnapshot.total_count.toLocaleString()}</p>
              </div>
            </div>
          </div>

          <div className="mb-4">
            <label htmlFor="search" className="sr-only">検索</label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg className="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                  <path fillRule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clipRule="evenodd" />
                </svg>
              </div>
              <input
                id="search"
                name="search"
                className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white dark:bg-gray-700 dark:border-gray-600 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                placeholder="ファイルパスで検索..."
                type="search"
                value={searchTerm}
                onChange={handleSearch}
              />
            </div>
          </div>

          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead className="bg-gray-50 dark:bg-gray-800">
                <tr>
                  <th
                    scope="col"
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer"
                    onClick={() => handleSort('file')}
                  >
                    ファイル
                    {sortField === 'file' && (
                      <span className="ml-1">
                        {sortDirection === 'asc' ? '↑' : '↓'}
                      </span>
                    )}
                  </th>
                  <th
                    scope="col"
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer"
                    onClick={() => handleSort('line')}
                  >
                    行番号
                    {sortField === 'line' && (
                      <span className="ml-1">
                        {sortDirection === 'asc' ? '↑' : '↓'}
                      </span>
                    )}
                  </th>
                  <th
                    scope="col"
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer"
                    onClick={() => handleSort('size')}
                  >
                    サイズ
                    {sortField === 'size' && (
                      <span className="ml-1">
                        {sortDirection === 'asc' ? '↑' : '↓'}
                      </span>
                    )}
                  </th>
                  <th
                    scope="col"
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer"
                    onClick={() => handleSort('count')}
                  >
                    オブジェクト数
                    {sortField === 'count' && (
                      <span className="ml-1">
                        {sortDirection === 'asc' ? '↑' : '↓'}
                      </span>
                    )}
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200 dark:bg-gray-900 dark:divide-gray-700">
                {selectedStats.map((stat, index) => (
                  <tr key={`${stat.file}-${stat.line}`} className={index % 2 === 0 ? 'bg-white dark:bg-gray-900' : 'bg-gray-50 dark:bg-gray-800'}>
                    <td className="px-6 py-4 text-sm font-medium text-gray-900 dark:text-white">
                      <div className="truncate max-w-xs">{stat.file}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                      {stat.line}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                      {formatBytes(stat.size)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                      {stat.count.toLocaleString()}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}
    </div>
  );
};

export default MemorySnapshots;
