import React, { useState } from 'react';

const PrometheusMetrics = ({ metricsText }) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [showHelp, setShowHelp] = useState(false);

  // 検索ハンドラ
  const handleSearch = (e) => {
    setSearchTerm(e.target.value);
  };

  // メトリクスをフィルタリング
  const filteredMetrics = metricsText
    ? metricsText
        .split('\n')
        .filter(line => {
          // コメント行と空行をスキップ
          if (line.startsWith('#') || line.trim() === '') {
            return searchTerm ? false : true;
          }
          
          // 検索語句でフィルタリング
          if (searchTerm) {
            return line.toLowerCase().includes(searchTerm.toLowerCase());
          }
          
          return true;
        })
        .join('\n')
    : '';

  if (!metricsText) {
    return (
      <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200 dark:bg-gray-800 dark:border-gray-700">
        <p className="text-gray-500 dark:text-gray-400">Prometheusメトリクスデータがありません。</p>
      </div>
    );
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-lg font-semibold text-gray-900 dark:text-white">Prometheusメトリクス</h2>
        <button
          onClick={() => setShowHelp(!showHelp)}
          className="inline-flex items-center px-3 py-1.5 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-700"
        >
          {showHelp ? 'ヘルプを隠す' : 'ヘルプを表示'}
        </button>
      </div>

      {showHelp && (
        <div className="bg-blue-50 border-l-4 border-blue-400 p-4 mb-4 dark:bg-blue-900 dark:border-blue-600">
          <div className="flex">
            <div className="ml-3">
              <h3 className="text-sm font-medium text-blue-700 dark:text-blue-300">Prometheusメトリクスについて</h3>
              <div className="mt-2 text-sm text-blue-600 dark:text-blue-400">
                <p>Prometheusメトリクスは、アプリケーションのパフォーマンスと状態を監視するための標準形式です。</p>
                <p className="mt-1">メトリクスの形式:</p>
                <ul className="list-disc list-inside mt-1 ml-2">
                  <li><strong>メトリクス名{'{ラベル}'}:</strong> 値</li>
                  <li><strong>#HELP:</strong> メトリクスの説明</li>
                  <li><strong>#TYPE:</strong> メトリクスのタイプ (counter, gauge, histogram, summary)</li>
                </ul>
                <p className="mt-2">これらのメトリクスはPrometheusサーバーで収集し、Grafanaなどのツールで可視化できます。</p>
              </div>
            </div>
          </div>
        </div>
      )}

      <div className="mb-4">
        <label htmlFor="search" className="sr-only">検索</label>
        <div className="relative">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <svg className="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
              <path fillRule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clipRule="evenodd" />
            </svg>
          </div>
          <input
            id="search"
            name="search"
            className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white dark:bg-gray-700 dark:border-gray-600 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
            placeholder="メトリクスを検索..."
            type="search"
            value={searchTerm}
            onChange={handleSearch}
          />
        </div>
      </div>

      <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200 dark:bg-gray-800 dark:border-gray-700 overflow-x-auto">
        <pre className="text-xs text-gray-800 dark:text-gray-300 whitespace-pre-wrap font-mono">
          {filteredMetrics || 'メトリクスデータがありません。'}
        </pre>
      </div>

      <div className="mt-4 flex justify-end">
        <a
          href="/prometheus/metrics"
          target="_blank"
          rel="noopener noreferrer"
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
        >
          生のメトリクスを表示
        </a>
      </div>
    </div>
  );
};

export default PrometheusMetrics;
