import React from 'react';

const MetricCard = ({ title, value, subtitle, className = '' }) => {
  return (
    <div className={`bg-white p-4 rounded-lg shadow-sm border border-gray-200 dark:bg-gray-800 dark:border-gray-700 ${className}`}>
      <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">{title}</h3>
      <p className="mt-1 text-2xl font-semibold text-gray-900 dark:text-white">{value}</p>
      {subtitle && <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">{subtitle}</p>}
    </div>
  );
};

const PerformanceOverview = ({ performanceMetrics }) => {
  if (!performanceMetrics) {
    return (
      <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200 dark:bg-gray-800 dark:border-gray-700">
        <p className="text-gray-500 dark:text-gray-400">パフォーマンスメトリクスデータがありません。</p>
      </div>
    );
  }

  // 最新のプロセス統計を取得
  const latestProcessStats = performanceMetrics.process_stats && performanceMetrics.process_stats.length > 0
    ? performanceMetrics.process_stats[performanceMetrics.process_stats.length - 1]
    : null;

  // 最新のGC統計を取得
  const latestGcStats = performanceMetrics.gc_stats && performanceMetrics.gc_stats.length > 0
    ? performanceMetrics.gc_stats[performanceMetrics.gc_stats.length - 1]
    : null;

  // 最新のメモリスナップショットを取得
  const latestMemorySnapshot = performanceMetrics.memory_snapshots && performanceMetrics.memory_snapshots.length > 0
    ? performanceMetrics.memory_snapshots[performanceMetrics.memory_snapshots.length - 1]
    : null;

  // 関数統計の概要を計算
  const functionStatsCount = Object.keys(performanceMetrics.function_stats || {}).length;
  const totalFunctionCalls = Object.values(performanceMetrics.function_stats || {}).reduce(
    (sum, stat) => sum + (stat.calls || 0), 0
  );
  const totalFunctionErrors = Object.values(performanceMetrics.function_stats || {}).reduce(
    (sum, stat) => sum + (stat.errors || 0), 0
  );

  // リクエスト統計の概要を計算
  const requestStatsCount = Object.keys(performanceMetrics.request_stats || {}).length;
  const totalRequests = Object.values(performanceMetrics.request_stats || {}).reduce(
    (sum, stat) => sum + (stat.count || 0), 0
  );
  const totalRequestErrors = Object.values(performanceMetrics.request_stats || {}).reduce(
    (sum, stat) => sum + (stat.errors || 0), 0
  );

  // タスク統計の概要を計算
  const taskStatsCount = Object.keys(performanceMetrics.task_stats || {}).length;
  const totalTasks = Object.values(performanceMetrics.task_stats || {}).reduce(
    (sum, stat) => sum + (stat.count || 0), 0
  );
  const totalTaskErrors = Object.values(performanceMetrics.task_stats || {}).reduce(
    (sum, stat) => sum + (stat.errors || 0), 0
  );

  // 値のフォーマット関数
  const formatNumber = (num) => {
    if (num === undefined || num === null) return 'N/A';
    return num.toLocaleString();
  };

  const formatPercent = (num) => {
    if (num === undefined || num === null) return 'N/A';
    return `${num.toFixed(1)}%`;
  };

  const formatBytes = (bytes) => {
    if (bytes === undefined || bytes === null) return 'N/A';
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    if (bytes === 0) return '0 B';
    const i = parseInt(Math.floor(Math.log(bytes) / Math.log(1024)), 10);
    if (i === 0) return `${bytes} ${sizes[i]}`;
    return `${(bytes / (1024 ** i)).toFixed(2)} ${sizes[i]}`;
  };

  const formatTime = (timestamp) => {
    if (!timestamp) return 'N/A';
    return new Date(timestamp * 1000).toLocaleString();
  };

  return (
    <div>
      <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">パフォーマンス概要</h2>
      
      <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4 mb-6">
        <MetricCard
          title="プロセスCPU使用率"
          value={latestProcessStats ? formatPercent(latestProcessStats.cpu_percent) : 'N/A'}
          subtitle="最新の測定値"
        />
        
        <MetricCard
          title="プロセスメモリ使用量"
          value={latestProcessStats ? formatBytes(latestProcessStats.memory_rss) : 'N/A'}
          subtitle="RSS (常駐セットサイズ)"
        />
        
        <MetricCard
          title="スレッド数"
          value={latestProcessStats ? formatNumber(latestProcessStats.num_threads) : 'N/A'}
          subtitle="プロセス内のスレッド"
        />
        
        <MetricCard
          title="接続数"
          value={latestProcessStats ? formatNumber(latestProcessStats.connections) : 'N/A'}
          subtitle="ネットワーク接続"
        />
      </div>
      
      <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">関数とリクエスト</h2>
      
      <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4 mb-6">
        <MetricCard
          title="追跡関数数"
          value={formatNumber(functionStatsCount)}
          subtitle="ユニークな関数"
        />
        
        <MetricCard
          title="関数呼び出し数"
          value={formatNumber(totalFunctionCalls)}
          subtitle="合計呼び出し"
        />
        
        <MetricCard
          title="追跡ドメイン数"
          value={formatNumber(requestStatsCount)}
          subtitle="ユニークなドメイン"
        />
        
        <MetricCard
          title="リクエスト数"
          value={formatNumber(totalRequests)}
          subtitle="合計リクエスト"
        />
      </div>
      
      <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">タスクとメモリ</h2>
      
      <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4 mb-6">
        <MetricCard
          title="プロジェクト数"
          value={formatNumber(taskStatsCount)}
          subtitle="追跡プロジェクト"
        />
        
        <MetricCard
          title="タスク数"
          value={formatNumber(totalTasks)}
          subtitle="合計タスク"
        />
        
        <MetricCard
          title="GCオブジェクト数"
          value={latestGcStats ? formatNumber(latestGcStats.objects) : 'N/A'}
          subtitle="ガベージコレクション"
        />
        
        <MetricCard
          title="追跡メモリサイズ"
          value={latestMemorySnapshot ? formatBytes(latestMemorySnapshot.total_size) : 'N/A'}
          subtitle="tracemalloc"
        />
      </div>
      
      <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">エラー統計</h2>
      
      <div className="grid grid-cols-1 gap-4 sm:grid-cols-3 mb-6">
        <MetricCard
          title="関数エラー"
          value={formatNumber(totalFunctionErrors)}
          subtitle={`エラー率: ${totalFunctionCalls > 0 ? ((totalFunctionErrors / totalFunctionCalls) * 100).toFixed(2) : 0}%`}
          className={totalFunctionErrors > 0 ? 'border-yellow-300 dark:border-yellow-600' : ''}
        />
        
        <MetricCard
          title="リクエストエラー"
          value={formatNumber(totalRequestErrors)}
          subtitle={`エラー率: ${totalRequests > 0 ? ((totalRequestErrors / totalRequests) * 100).toFixed(2) : 0}%`}
          className={totalRequestErrors > 0 ? 'border-yellow-300 dark:border-yellow-600' : ''}
        />
        
        <MetricCard
          title="タスクエラー"
          value={formatNumber(totalTaskErrors)}
          subtitle={`エラー率: ${totalTasks > 0 ? ((totalTaskErrors / totalTasks) * 100).toFixed(2) : 0}%`}
          className={totalTaskErrors > 0 ? 'border-yellow-300 dark:border-yellow-600' : ''}
        />
      </div>
      
      <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200 dark:bg-gray-800 dark:border-gray-700">
        <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">最終更新時間</h3>
        <p className="text-sm text-gray-700 dark:text-gray-300">
          {performanceMetrics.timestamp ? formatTime(performanceMetrics.timestamp) : 'N/A'}
        </p>
      </div>
    </div>
  );
};

export default PerformanceOverview;
