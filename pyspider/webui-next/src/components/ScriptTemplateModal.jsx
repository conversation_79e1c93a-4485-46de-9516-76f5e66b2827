'use client';

import { useState } from 'react';
import { XMarkIcon } from '@heroicons/react/24/outline';

const ScriptTemplateModal = ({ isOpen, onClose, onSelectTemplate }) => {
  const [selectedCategory, setSelectedCategory] = useState('basic');

  const templateCategories = {
    basic: {
      name: '基本',
      description: '基本的なWebスクレイピングテンプレート',
      templates: [
        {
          id: 'basic_crawler',
          name: '基本クローラー',
          description: 'シンプルなWebページクローラー',
          icon: '🕷️'
        },
        {
          id: 'pagination_crawler',
          name: 'ページネーション対応',
          description: '複数ページにわたるデータ収集',
          icon: '📄'
        },
        {
          id: 'form_crawler',
          name: 'フォーム送信',
          description: 'フォームを使った検索・データ取得',
          icon: '📝'
        }
      ]
    },
    ecommerce: {
      name: 'ECサイト',
      description: 'ECサイト専用のスクレイピングテンプレート',
      templates: [
        {
          id: 'amazon_products',
          name: 'Amazon商品',
          description: 'Amazon商品情報の収集',
          icon: '📦'
        },
        {
          id: 'rakuten_products',
          name: '楽天商品',
          description: '楽天市場の商品情報収集',
          icon: '🛒'
        },
        {
          id: 'yahoo_shopping',
          name: 'Yahoo!ショッピング',
          description: 'Yahoo!ショッピングの商品情報',
          icon: '🏪'
        },
        {
          id: 'mercari_products',
          name: 'メルカリ',
          description: 'メルカリの商品情報収集',
          icon: '💰'
        }
      ]
    },
    news: {
      name: 'ニュース',
      description: 'ニュースサイト専用テンプレート',
      templates: [
        {
          id: 'yahoo_news',
          name: 'Yahoo!ニュース',
          description: 'Yahoo!ニュースの記事収集',
          icon: '📰'
        },
        {
          id: 'nikkei_news',
          name: '日経新聞',
          description: '日経新聞の記事収集',
          icon: '📊'
        },
        {
          id: 'asahi_news',
          name: '朝日新聞',
          description: '朝日新聞の記事収集',
          icon: '🗞️'
        }
      ]
    },
    social: {
      name: 'ソーシャル',
      description: 'ソーシャルメディア関連テンプレート',
      templates: [
        {
          id: 'twitter_posts',
          name: 'Twitter投稿',
          description: 'Twitter投稿の収集',
          icon: '🐦'
        },
        {
          id: 'instagram_posts',
          name: 'Instagram投稿',
          description: 'Instagram投稿の収集',
          icon: '📸'
        },
        {
          id: 'youtube_videos',
          name: 'YouTube動画',
          description: 'YouTube動画情報の収集',
          icon: '🎥'
        }
      ]
    },
    business: {
      name: 'ビジネス',
      description: 'ビジネス関連サイトのテンプレート',
      templates: [
        {
          id: 'company_info',
          name: '企業情報',
          description: '企業情報の収集',
          icon: '🏢'
        },
        {
          id: 'job_listings',
          name: '求人情報',
          description: '求人サイトの情報収集',
          icon: '💼'
        },
        {
          id: 'stock_prices',
          name: '株価情報',
          description: '株価・金融情報の収集',
          icon: '📈'
        }
      ]
    },
    restaurant: {
      name: 'グルメ',
      description: 'グルメ・レストラン関連テンプレート',
      templates: [
        {
          id: 'gurunavi',
          name: 'ぐるなび',
          description: 'ぐるなびのレストラン情報',
          icon: '🍽️'
        },
        {
          id: 'tabelog',
          name: '食べログ',
          description: '食べログのレストラン情報',
          icon: '⭐'
        },
        {
          id: 'hotpepper',
          name: 'ホットペッパー',
          description: 'ホットペッパーグルメの情報',
          icon: '🌶️'
        }
      ]
    },
    advanced: {
      name: '高度な技術',
      description: '高度なスクレイピング技術とパターン',
      templates: [
        {
          id: 'async_crawler',
          name: '非同期処理',
          description: '非同期処理を活用した高速クローラー',
          icon: '⚡'
        },
        {
          id: 'rate_limiting',
          name: 'レート制限',
          description: '高度なレート制限とリトライ機能',
          icon: '🎛️'
        },
        {
          id: 'session_management',
          name: 'セッション管理',
          description: 'ログインとセッション維持',
          icon: '🔐'
        },
        {
          id: 'data_validation',
          name: 'データ検証',
          description: '高度なデータ検証とクリーニング',
          icon: '✅'
        },
        {
          id: 'error_handling',
          name: 'エラーハンドリング',
          description: '包括的なエラー処理とリカバリ',
          icon: '🛡️'
        },
        {
          id: 'distributed_crawling',
          name: '分散クローリング',
          description: '分散処理とロードバランシング',
          icon: '🌐'
        }
      ]
    },
    decorators: {
      name: 'デコレータ活用',
      description: 'pyspiderデコレータの高度な活用事例',
      templates: [
        {
          id: 'config_advanced',
          name: '@config高度活用',
          description: '@configデコレータの全機能活用',
          icon: '⚙️'
        },
        {
          id: 'every_patterns',
          name: '@everyパターン集',
          description: '@everyデコレータの様々なパターン',
          icon: '⏰'
        },
        {
          id: 'catch_decorator',
          name: '@catch活用',
          description: '@catchデコレータでエラー処理',
          icon: '🎣'
        },
        {
          id: 'custom_decorators',
          name: 'カスタムデコレータ',
          description: '独自デコレータの作成と活用',
          icon: '🎨'
        },
        {
          id: 'priority_management',
          name: '優先度管理',
          description: 'priorityを使った高度な制御',
          icon: '📊'
        },
        {
          id: 'age_cache_control',
          name: 'キャッシュ制御',
          description: 'ageとvalidateを使ったキャッシュ戦略',
          icon: '💾'
        }
      ]
    },
    learning: {
      name: '学習・研究',
      description: '学習目的の高度なサンプル',
      templates: [
        {
          id: 'machine_learning_data',
          name: 'ML用データ収集',
          description: '機械学習用データセット構築',
          icon: '🤖'
        },
        {
          id: 'academic_research',
          name: '学術研究',
          description: '学術論文・研究データ収集',
          icon: '📚'
        },
        {
          id: 'sentiment_analysis',
          name: '感情分析データ',
          description: '感情分析用テキストデータ収集',
          icon: '😊'
        },
        {
          id: 'time_series_data',
          name: '時系列データ',
          description: '時系列分析用データ収集',
          icon: '📈'
        },
        {
          id: 'network_analysis',
          name: 'ネットワーク分析',
          description: 'ソーシャルネットワーク分析用データ',
          icon: '🕸️'
        },
        {
          id: 'content_analysis',
          name: 'コンテンツ分析',
          description: 'テキスト・画像コンテンツ分析',
          icon: '🔍'
        }
      ]
    },
    parsing: {
      name: 'パース技術',
      description: '高度なデータ抽出・解析技術',
      templates: [
        {
          id: 'regex_patterns',
          name: '正規表現活用',
          description: '正規表現を使った高度なデータ抽出',
          icon: '🔍'
        },
        {
          id: 'css_selectors',
          name: 'CSS3/4セレクタ',
          description: 'CSS3/4セレクタの高度な活用',
          icon: '🎨'
        },
        {
          id: 'xpath_advanced',
          name: 'XPath高度活用',
          description: 'XPathを使った複雑な要素選択',
          icon: '🗂️'
        },
        {
          id: 'beautifulsoup_advanced',
          name: 'BeautifulSoup4',
          description: 'BS4の高度な機能活用',
          icon: '🍲'
        }
      ]
    },
    automation: {
      name: 'ブラウザ自動化',
      description: 'モダンブラウザ自動化技術',
      templates: [
        {
          id: 'playwright_advanced',
          name: 'Playwright活用',
          description: 'Playwrightを使った高度な自動化',
          icon: '🎭'
        },
        {
          id: 'puppeteer_advanced',
          name: 'Puppeteer活用',
          description: 'Puppeteerを使ったブラウザ制御',
          icon: '🎪'
        },
        {
          id: 'selenium_integration',
          name: 'Selenium統合',
          description: 'Seleniumとpyspiderの統合',
          icon: '🤖'
        },
        {
          id: 'headless_browsers',
          name: 'ヘッドレスブラウザ',
          description: 'ヘッドレスブラウザの活用パターン',
          icon: '👻'
        }
      ]
    },
    spa_modern: {
      name: 'モダンWeb対応',
      description: 'SPA・モダンWebアプリ対応',
      templates: [
        {
          id: 'spa_crawling',
          name: 'SPA対応クローリング',
          description: 'Single Page Applicationの効率的クローリング',
          icon: '⚡'
        },
        {
          id: 'react_vue_crawling',
          name: 'React/Vue対応',
          description: 'React/Vueアプリケーションのクローリング',
          icon: '⚛️'
        },
        {
          id: 'ajax_handling',
          name: 'AJAX処理',
          description: '動的コンテンツとAJAX処理',
          icon: '🔄'
        },
        {
          id: 'websocket_crawling',
          name: 'WebSocket対応',
          description: 'リアルタイム通信のクローリング',
          icon: '🔌'
        }
      ]
    },
    hybrid: {
      name: '複合技術',
      description: '複数の技術を組み合わせた高度な事例',
      templates: [
        {
          id: 'xpath_css_combo',
          name: 'XPath×CSS3/4',
          description: 'XPathとCSS3/4セレクタの組み合わせ',
          icon: '🔗'
        },
        {
          id: 'bs4_pyquery_combo',
          name: 'BS4×pyQuery',
          description: 'BeautifulSoup4とpyQueryの連携',
          icon: '🤝'
        },
        {
          id: 'all_parsing_integrated',
          name: '全技術統合',
          description: 'XPath・CSS・BS4・pyQueryの完全統合',
          icon: '🎯'
        },
        {
          id: 'ecommerce_hybrid_parsing',
          name: 'ECサイト複合解析',
          description: 'ECサイト特化の複合技術活用',
          icon: '🛍️'
        },
        {
          id: 'news_hybrid_parsing',
          name: 'ニュース複合解析',
          description: 'ニュースサイト特化の複合技術',
          icon: '📰'
        }
      ]
    }
  };

  const handleTemplateSelect = (templateId) => {
    onSelectTemplate(templateId);
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex min-h-screen items-center justify-center p-4">
        <div className="fixed inset-0 bg-black bg-opacity-50 transition-opacity" onClick={onClose}></div>

        <div className="relative bg-white rounded-lg shadow-xl max-w-6xl w-full max-h-[90vh] overflow-hidden dark:bg-gray-800">
          {/* ヘッダー */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
              スクリプトテンプレートを選択
            </h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
            >
              <XMarkIcon className="h-6 w-6" />
            </button>
          </div>

          <div className="flex h-[70vh]">
            {/* サイドバー - カテゴリ選択 */}
            <div className="w-1/4 bg-gray-50 border-r border-gray-200 dark:bg-gray-900 dark:border-gray-700 flex flex-col">
              <div className="p-4 flex-shrink-0">
                <h3 className="text-sm font-medium text-gray-900 dark:text-white mb-3">カテゴリ</h3>
              </div>
              <div className="flex-1 overflow-y-auto px-4 pb-4 custom-scrollbar min-h-0">
                <nav className="space-y-1">
                  {Object.entries(templateCategories).map(([key, category]) => (
                    <button
                      key={key}
                      onClick={() => setSelectedCategory(key)}
                      className={`w-full text-left px-3 py-2 rounded-md text-sm font-medium transition-all duration-200 ${
                        selectedCategory === key
                          ? 'bg-primary-100 text-primary-700 dark:bg-primary-900 dark:text-primary-300 shadow-sm border-l-2 border-primary-500'
                          : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900 dark:text-gray-400 dark:hover:bg-gray-800 dark:hover:text-gray-200 hover:shadow-sm'
                      }`}
                    >
                      {category.name}
                    </button>
                  ))}
                </nav>
              </div>
            </div>

            {/* メインコンテンツ - テンプレート一覧 */}
            <div className="flex-1 p-6 overflow-y-auto custom-scrollbar">
              <div className="mb-4">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                  {templateCategories[selectedCategory].name}
                </h3>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  {templateCategories[selectedCategory].description}
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {templateCategories[selectedCategory].templates.map((template) => (
                  <div
                    key={template.id}
                    onClick={() => handleTemplateSelect(template.id)}
                    className="bg-white border border-gray-200 rounded-lg p-4 cursor-pointer hover:border-primary-500 hover:shadow-md transition-all dark:bg-gray-700 dark:border-gray-600 dark:hover:border-primary-400"
                  >
                    <div className="flex items-center mb-3">
                      <span className="text-2xl mr-3">{template.icon}</span>
                      <h4 className="text-md font-medium text-gray-900 dark:text-white">
                        {template.name}
                      </h4>
                    </div>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      {template.description}
                    </p>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ScriptTemplateModal;
