'use client';

import { useState, useEffect } from 'react';
import { ThemeProvider } from 'next-themes';

export default function ThemeProviderWrapper({ children }) {
  const [mounted, setMounted] = useState(false);

  // マウント後にのみレンダリングするようにする
  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return <>{children}</>;
  }

  return (
    <ThemeProvider attribute="class" defaultTheme="system" enableSystem={true}>
      {children}
    </ThemeProvider>
  );
}
