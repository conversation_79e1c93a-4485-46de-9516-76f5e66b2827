'use client';

import { ArrowUpIcon, ArrowDownIcon } from '@heroicons/react/24/solid';
import { formatNumber, formatTime } from '@/lib/utils';

const MetricsOverview = ({ metrics }) => {
  const system = metrics?.system || {};
  const scheduler = metrics?.scheduler || {};
  
  // メトリクスカードを生成
  const MetricCard = ({ title, value, subtitle, trend, trendValue }) => (
    <div className="bg-white rounded-lg border border-gray-200 p-4 dark:bg-gray-800 dark:border-gray-700">
      <div className="flex items-center justify-between">
        <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">{title}</h3>
        {trend && (
          <div className={`flex items-center ${trend === 'up' ? 'text-green-500' : 'text-red-500'}`}>
            {trend === 'up' ? (
              <ArrowUpIcon className="h-4 w-4" />
            ) : (
              <ArrowDownIcon className="h-4 w-4" />
            )}
            <span className="text-xs ml-1">{trendValue}</span>
          </div>
        )}
      </div>
      <div className="mt-2 text-3xl font-semibold text-gray-900 dark:text-white">{value}</div>
      {subtitle && <div className="mt-1 text-xs text-gray-500 dark:text-gray-400">{subtitle}</div>}
    </div>
  );
  
  return (
    <div className="card">
      <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">システムメトリクス</h2>
      
      <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
        <MetricCard
          title="CPU使用率"
          value={`${system.cpu_percent?.toFixed(1) || 0}%`}
          subtitle="システム全体"
        />
        
        <MetricCard
          title="メモリ使用率"
          value={`${system.memory_percent?.toFixed(1) || 0}%`}
          subtitle="システム全体"
        />
        
        <MetricCard
          title="ディスク使用率"
          value={`${system.disk_percent?.toFixed(1) || 0}%`}
          subtitle="システム全体"
        />
        
        <MetricCard
          title="稼働時間"
          value={formatTime(system.uptime)}
          subtitle="サーバー起動からの時間"
        />
      </div>
      
      <h2 className="text-lg font-semibold text-gray-900 dark:text-white mt-8 mb-4">プロセスメトリクス</h2>
      
      <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
        <MetricCard
          title="プロセスCPU"
          value={`${system.process_cpu_percent?.toFixed(1) || 0}%`}
          subtitle="PySpiderプロセス"
        />
        
        <MetricCard
          title="プロセスメモリ"
          value={`${system.process_memory_mb?.toFixed(2) || 0} MB`}
          subtitle="PySpiderプロセス"
        />
        
        <MetricCard
          title="接続数"
          value={formatNumber(system.process_connections)}
          subtitle="ネットワーク接続"
        />
        
        <MetricCard
          title="スレッド数"
          value={formatNumber(system.process_threads)}
          subtitle="プロセススレッド"
        />
      </div>
      
      <h2 className="text-lg font-semibold text-gray-900 dark:text-white mt-8 mb-4">スケジューラメトリクス</h2>
      
      <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
        <MetricCard
          title="キューサイズ"
          value={formatNumber(scheduler.queue_size)}
          subtitle="スケジューラキュー"
        />
        
        <MetricCard
          title="処理中タスク"
          value={formatNumber(scheduler.processing_tasks)}
          subtitle="現在処理中"
        />
        
        <MetricCard
          title="24時間タスク数"
          value={formatNumber(scheduler.total_tasks_24h)}
          subtitle="過去24時間"
        />
        
        <MetricCard
          title="保留中タスク"
          value={formatNumber(scheduler.pending_tasks)}
          subtitle="実行待ち"
        />
      </div>
    </div>
  );
};

export default MetricsOverview;
