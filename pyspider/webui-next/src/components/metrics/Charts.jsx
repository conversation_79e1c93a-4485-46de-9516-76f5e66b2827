'use client';

import { useEffect, useRef } from 'react';
import { Chart, registerables } from 'chart.js';

// Chart.jsの登録
Chart.register(...registerables);

const Charts = ({ metrics }) => {
  const systemResourcesChartRef = useRef(null);
  const processResourcesChartRef = useRef(null);
  const schedulerStatsChartRef = useRef(null);
  const taskStatsChartRef = useRef(null);
  
  const systemResourcesChart = useRef(null);
  const processResourcesChart = useRef(null);
  const schedulerStatsChart = useRef(null);
  const taskStatsChart = useRef(null);
  
  const system = metrics?.system || {};
  const scheduler = metrics?.scheduler || {};
  
  useEffect(() => {
    // チャートの初期化
    initCharts();
    
    // クリーンアップ
    return () => {
      if (systemResourcesChart.current) {
        systemResourcesChart.current.destroy();
      }
      if (processResourcesChart.current) {
        processResourcesChart.current.destroy();
      }
      if (schedulerStatsChart.current) {
        schedulerStatsChart.current.destroy();
      }
      if (taskStatsChart.current) {
        taskStatsChart.current.destroy();
      }
    };
  }, []);
  
  useEffect(() => {
    // メトリクスが更新されたらチャートを更新
    updateCharts();
  }, [metrics]);
  
  const initCharts = () => {
    // システムリソースチャート
    if (systemResourcesChartRef.current) {
      const ctx = systemResourcesChartRef.current.getContext('2d');
      systemResourcesChart.current = new Chart(ctx, {
        type: 'bar',
        data: {
          labels: ['CPU使用率 (%)', 'メモリ使用率 (%)', 'ディスク使用率 (%)'],
          datasets: [{
            label: 'システムリソース',
            data: [0, 0, 0],
            backgroundColor: [
              'rgba(255, 99, 132, 0.5)',
              'rgba(54, 162, 235, 0.5)',
              'rgba(255, 206, 86, 0.5)'
            ],
            borderColor: [
              'rgba(255, 99, 132, 1)',
              'rgba(54, 162, 235, 1)',
              'rgba(255, 206, 86, 1)'
            ],
            borderWidth: 1
          }]
        },
        options: {
          responsive: true,
          plugins: {
            legend: {
              display: false
            }
          },
          scales: {
            y: {
              beginAtZero: true,
              max: 100,
              title: {
                display: true,
                text: '使用率 (%)'
              }
            }
          }
        }
      });
    }
    
    // プロセスリソースチャート
    if (processResourcesChartRef.current) {
      const ctx = processResourcesChartRef.current.getContext('2d');
      processResourcesChart.current = new Chart(ctx, {
        type: 'bar',
        data: {
          labels: ['CPU使用率 (%)', 'メモリ使用率 (%)'],
          datasets: [{
            label: 'プロセスリソース',
            data: [0, 0],
            backgroundColor: [
              'rgba(75, 192, 192, 0.5)',
              'rgba(153, 102, 255, 0.5)'
            ],
            borderColor: [
              'rgba(75, 192, 192, 1)',
              'rgba(153, 102, 255, 1)'
            ],
            borderWidth: 1
          }]
        },
        options: {
          responsive: true,
          plugins: {
            legend: {
              display: false
            }
          },
          scales: {
            y: {
              beginAtZero: true,
              max: 100,
              title: {
                display: true,
                text: '使用率 (%)'
              }
            }
          }
        }
      });
    }
    
    // スケジューラ統計チャート
    if (schedulerStatsChartRef.current) {
      const ctx = schedulerStatsChartRef.current.getContext('2d');
      schedulerStatsChart.current = new Chart(ctx, {
        type: 'bar',
        data: {
          labels: ['キューサイズ', '処理中タスク', '保留中タスク'],
          datasets: [{
            label: 'スケジューラ統計',
            data: [0, 0, 0],
            backgroundColor: [
              'rgba(255, 159, 64, 0.5)',
              'rgba(255, 99, 132, 0.5)',
              'rgba(54, 162, 235, 0.5)'
            ],
            borderColor: [
              'rgba(255, 159, 64, 1)',
              'rgba(255, 99, 132, 1)',
              'rgba(54, 162, 235, 1)'
            ],
            borderWidth: 1
          }]
        },
        options: {
          responsive: true,
          plugins: {
            legend: {
              display: false
            }
          },
          scales: {
            y: {
              beginAtZero: true,
              title: {
                display: true,
                text: '数'
              }
            }
          }
        }
      });
    }
    
    // タスク統計チャート
    if (taskStatsChartRef.current) {
      const ctx = taskStatsChartRef.current.getContext('2d');
      taskStatsChart.current = new Chart(ctx, {
        type: 'pie',
        data: {
          labels: ['成功', '失敗', 'その他'],
          datasets: [{
            label: 'タスク統計（24時間）',
            data: [0, 0, 0],
            backgroundColor: [
              'rgba(75, 192, 192, 0.5)',
              'rgba(255, 99, 132, 0.5)',
              'rgba(201, 203, 207, 0.5)'
            ],
            borderColor: [
              'rgba(75, 192, 192, 1)',
              'rgba(255, 99, 132, 1)',
              'rgba(201, 203, 207, 1)'
            ],
            borderWidth: 1
          }]
        },
        options: {
          responsive: true
        }
      });
    }
  };
  
  const updateCharts = () => {
    // システムリソースチャートを更新
    if (systemResourcesChart.current) {
      systemResourcesChart.current.data.datasets[0].data = [
        system.cpu_percent || 0,
        system.memory_percent || 0,
        system.disk_percent || 0
      ];
      systemResourcesChart.current.update();
    }
    
    // プロセスリソースチャートを更新
    if (processResourcesChart.current) {
      // CPU使用率が100%を超える場合は100%に制限
      const cpuPercent = Math.min(system.process_cpu_percent || 0, 100);
      processResourcesChart.current.data.datasets[0].data = [
        cpuPercent,
        (system.process_memory_percent || 0) * 100
      ];
      processResourcesChart.current.update();
    }
    
    // スケジューラ統計チャートを更新
    if (schedulerStatsChart.current) {
      schedulerStatsChart.current.data.datasets[0].data = [
        scheduler.queue_size || 0,
        scheduler.processing_tasks || 0,
        scheduler.pending_tasks || 0
      ];
      schedulerStatsChart.current.update();
    }
    
    // タスク統計チャートを更新
    if (taskStatsChart.current) {
      const successTasks = scheduler.success_tasks_24h || 0;
      const failedTasks = scheduler.failed_tasks_24h || 0;
      const totalTasks = scheduler.total_tasks_24h || 0;
      const otherTasks = Math.max(0, totalTasks - successTasks - failedTasks);
      
      taskStatsChart.current.data.datasets[0].data = [
        successTasks,
        failedTasks,
        otherTasks
      ];
      taskStatsChart.current.update();
    }
  };
  
  return (
    <div className="card">
      <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">メトリクスチャート</h2>
      
      <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
        <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200 dark:bg-gray-800 dark:border-gray-700">
          <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">システムリソース使用率</h3>
          <div className="h-64">
            <canvas ref={systemResourcesChartRef}></canvas>
          </div>
        </div>
        
        <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200 dark:bg-gray-800 dark:border-gray-700">
          <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">プロセスリソース使用率</h3>
          <div className="h-64">
            <canvas ref={processResourcesChartRef}></canvas>
          </div>
        </div>
        
        <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200 dark:bg-gray-800 dark:border-gray-700">
          <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">スケジューラ統計</h3>
          <div className="h-64">
            <canvas ref={schedulerStatsChartRef}></canvas>
          </div>
        </div>
        
        <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200 dark:bg-gray-800 dark:border-gray-700">
          <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">タスク統計（24時間）</h3>
          <div className="h-64">
            <canvas ref={taskStatsChartRef}></canvas>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Charts;
