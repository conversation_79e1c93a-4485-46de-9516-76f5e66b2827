'use client';

import { CheckCircleIcon, XCircleIcon, ExclamationTriangleIcon } from '@heroicons/react/24/outline';
import { getComponentName, getComponentStatusText } from '@/lib/utils';

const ComponentStatus = ({ componentsStatus }) => {
  // コンポーネントの状態に応じたアイコンを取得
  const getStatusIcon = (status) => {
    switch (status) {
      case 'running':
        return <CheckCircleIcon className="h-6 w-6 text-green-500" />;
      case 'stopped':
        return <XCircleIcon className="h-6 w-6 text-red-500" />;
      case 'unknown':
        return <ExclamationTriangleIcon className="h-6 w-6 text-yellow-500" />;
      default:
        return <ExclamationTriangleIcon className="h-6 w-6 text-gray-400" />;
    }
  };

  // コンポーネントの状態に応じたカードのスタイルを取得
  const getCardClass = (status) => {
    switch (status) {
      case 'running':
        return 'border-green-200 bg-green-50 dark:border-green-900 dark:bg-green-900/20';
      case 'stopped':
        return 'border-red-200 bg-red-50 dark:border-red-900 dark:bg-red-900/20';
      case 'unknown':
        return 'border-yellow-200 bg-yellow-50 dark:border-yellow-900 dark:bg-yellow-900/20';
      default:
        return 'border-gray-200 bg-gray-50 dark:border-gray-700 dark:bg-gray-800';
    }
  };

  return (
    <div className="card">
      <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">コンポーネント状態</h2>

      <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
        {componentsStatus && Object.entries(componentsStatus).map(([key, value]) => {
          if (key === 'timestamp') return null;

          return (
            <div
              key={key}
              className={`rounded-lg border p-4 ${getCardClass(value.status)}`}
            >
              <div className="flex items-center">
                {getStatusIcon(value.status)}
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-gray-900 dark:text-white">
                    {getComponentName(key)}
                  </h3>
                  <div className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                    <span className="font-medium">{getComponentStatusText(value.status)}</span>
                    {value.host && value.port && typeof value.host === 'string' && typeof value.port === 'number' && (
                      <span className="ml-2">
                        {value.host}:{value.port}
                      </span>
                    )}
                    {value.host && value.port && (typeof value.host !== 'string' || typeof value.port !== 'number') && (
                      <span className="ml-2">接続情報あり</span>
                    )}
                  </div>
                </div>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default ComponentStatus;
