'use client';

import { useState, useEffect } from 'react';
import { Cog6ToothIcon, EyeIcon, EyeSlashIcon } from '@heroicons/react/24/outline';

export default function AuthConfig() {
  const [isOpen, setIsOpen] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [config, setConfig] = useState({
    apiUrl: '',
    username: '',
    password: ''
  });
  const [testResult, setTestResult] = useState(null);
  const [isTestingConnection, setIsTestingConnection] = useState(false);

  // 初期設定を読み込み
  useEffect(() => {
    const savedConfig = localStorage.getItem('pyspider_auth_config');
    if (savedConfig) {
      try {
        const parsed = JSON.parse(savedConfig);
        setConfig(parsed);
      } catch (error) {
        console.error('Failed to parse saved auth config:', error);
      }
    } else {
      // デフォルト設定
      setConfig({
        apiUrl: process.env.NEXT_PUBLIC_PYSPIDER_API_URL || 'http://localhost:5000',
        username: process.env.NEXT_PUBLIC_PYSPIDER_USERNAME || 'admin',
        password: process.env.NEXT_PUBLIC_PYSPIDER_PASSWORD || 'PySpider2024!SecurePass#'
      });
    }
  }, []);

  // 設定を保存
  const saveConfig = () => {
    localStorage.setItem('pyspider_auth_config', JSON.stringify(config));
    
    // 動的にAPIクライアントの設定を更新
    if (typeof window !== 'undefined') {
      window.pyspiderAuthConfig = config;
    }
    
    setIsOpen(false);
    alert('認証設定が保存されました。');
  };

  // 接続テスト
  const testConnection = async () => {
    setIsTestingConnection(true);
    setTestResult(null);

    try {
      const credentials = btoa(`${config.username}:${config.password}`);
      const response = await fetch(`${config.apiUrl}/api/v2/test`, {
        method: 'GET',
        headers: {
          'Authorization': `Basic ${credentials}`,
          'Content-Type': 'application/json'
        },
        timeout: 5000
      });

      if (response.ok) {
        const data = await response.json();
        setTestResult({
          success: true,
          message: 'API接続に成功しました！',
          data: data
        });
      } else {
        setTestResult({
          success: false,
          message: `API接続に失敗しました。ステータス: ${response.status}`,
          error: response.statusText
        });
      }
    } catch (error) {
      setTestResult({
        success: false,
        message: 'API接続中にエラーが発生しました。',
        error: error.message
      });
    } finally {
      setIsTestingConnection(false);
    }
  };

  return (
    <>
      {/* 設定ボタン */}
      <button
        onClick={() => setIsOpen(true)}
        className="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
        title="API認証設定"
      >
        <Cog6ToothIcon className="h-5 w-5" />
      </button>

      {/* 設定モーダル */}
      {isOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-md mx-4">
            <h2 className="text-lg font-semibold mb-4 text-gray-900 dark:text-white">
              API認証設定
            </h2>

            <div className="space-y-4">
              {/* API URL */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  API URL
                </label>
                <input
                  type="url"
                  value={config.apiUrl}
                  onChange={(e) => setConfig({ ...config, apiUrl: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md 
                           bg-white dark:bg-gray-700 text-gray-900 dark:text-white
                           focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="http://localhost:5000"
                />
              </div>

              {/* ユーザー名 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  ユーザー名
                </label>
                <input
                  type="text"
                  value={config.username}
                  onChange={(e) => setConfig({ ...config, username: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md 
                           bg-white dark:bg-gray-700 text-gray-900 dark:text-white
                           focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="admin"
                />
              </div>

              {/* パスワード */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  パスワード
                </label>
                <div className="relative">
                  <input
                    type={showPassword ? 'text' : 'password'}
                    value={config.password}
                    onChange={(e) => setConfig({ ...config, password: e.target.value })}
                    className="w-full px-3 py-2 pr-10 border border-gray-300 dark:border-gray-600 rounded-md 
                             bg-white dark:bg-gray-700 text-gray-900 dark:text-white
                             focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="パスワード"
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute inset-y-0 right-0 pr-3 flex items-center"
                  >
                    {showPassword ? (
                      <EyeSlashIcon className="h-4 w-4 text-gray-400" />
                    ) : (
                      <EyeIcon className="h-4 w-4 text-gray-400" />
                    )}
                  </button>
                </div>
              </div>

              {/* 接続テスト結果 */}
              {testResult && (
                <div className={`p-3 rounded-md ${
                  testResult.success 
                    ? 'bg-green-50 dark:bg-green-900/20 text-green-800 dark:text-green-200' 
                    : 'bg-red-50 dark:bg-red-900/20 text-red-800 dark:text-red-200'
                }`}>
                  <p className="text-sm font-medium">{testResult.message}</p>
                  {testResult.error && (
                    <p className="text-xs mt-1 opacity-75">{testResult.error}</p>
                  )}
                </div>
              )}
            </div>

            {/* ボタン */}
            <div className="flex justify-between mt-6">
              <button
                onClick={testConnection}
                disabled={isTestingConnection}
                className="px-4 py-2 text-sm font-medium text-blue-600 dark:text-blue-400 
                         hover:text-blue-800 dark:hover:text-blue-200 disabled:opacity-50"
              >
                {isTestingConnection ? '接続テスト中...' : '接続テスト'}
              </button>

              <div className="space-x-2">
                <button
                  onClick={() => setIsOpen(false)}
                  className="px-4 py-2 text-sm font-medium text-gray-600 dark:text-gray-400 
                           hover:text-gray-800 dark:hover:text-gray-200"
                >
                  キャンセル
                </button>
                <button
                  onClick={saveConfig}
                  className="px-4 py-2 text-sm font-medium text-white bg-blue-600 
                           hover:bg-blue-700 rounded-md"
                >
                  保存
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
}
