'use client';

const Footer = () => {
  return (
    <footer className="bg-white shadow-sm dark:bg-gray-800 mt-auto">
      <div className="mx-auto max-w-7xl px-4 py-4 sm:px-6 lg:px-8">
        <div className="flex flex-col items-center justify-between md:flex-row">
          <div className="flex items-center">
            <span className="text-sm text-gray-500 dark:text-gray-400">
              &copy; {new Date().getFullYear()} PySpider. All rights reserved.
            </span>
          </div>
          <div className="mt-4 flex space-x-6 md:mt-0">
            <a
              href="http://docs.pyspider.org/"
              target="_blank"
              rel="noopener noreferrer"
              className="text-gray-400 hover:text-gray-500 dark:hover:text-gray-300"
            >
              ドキュメント
            </a>
            <a
              href="https://github.com/binux/pyspider"
              target="_blank"
              rel="noopener noreferrer"
              className="text-gray-400 hover:text-gray-500 dark:hover:text-gray-300"
            >
              GitHub
            </a>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
