import { NextResponse } from 'next/server';

export function middleware(request) {
  // 認証が必要かどうかを環境変数で制御
  const authRequired = process.env.NEXT_PUBLIC_AUTH_REQUIRED === 'true';

  if (!authRequired) {
    return NextResponse.next();
  }

  // 認証情報を取得
  const username = 'admin';
  const password = 'PySpider2024!SecurePass#';

  // Authorization ヘッダーを確認
  const authHeader = request.headers.get('authorization');

  if (!authHeader || !authHeader.startsWith('Basic ')) {
    // 認証が必要な場合、Basic認証を要求
    return new NextResponse('Authentication required', {
      status: 401,
      headers: {
        'WWW-Authenticate': 'Basic realm="PySpider WebUI"',
      },
    });
  }

  try {
    // Basic認証の検証
    const base64Credentials = authHeader.split(' ')[1];
    const credentials = Buffer.from(base64Credentials, 'base64').toString('ascii');
    const [inputUsername, inputPassword] = credentials.split(':');

    if (inputUsername === username && inputPassword === password) {
      // 認証成功
      return NextResponse.next();
    } else {
      // 認証失敗
      return new NextResponse('Invalid credentials', {
        status: 401,
        headers: {
          'WWW-Authenticate': 'Basic realm="PySpider WebUI"',
        },
      });
    }
  } catch (error) {
    // 認証エラー
    return new NextResponse('Authentication error', {
      status: 401,
      headers: {
        'WWW-Authenticate': 'Basic realm="PySpider WebUI"',
      },
    });
  }
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!api|_next/static|_next/image|favicon.ico).*)',
  ],
};
