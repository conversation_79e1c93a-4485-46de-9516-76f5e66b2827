#!/usr/bin/env python
# -*- coding: utf-8 -*-
import re
import json
import time
import subprocess
import sys
from pyspider.libs.base_handler import *

# Playwrightのインストールを確認
try:
    from playwright.sync_api import sync_playwright
except ImportError:
    print("Playwrightがインストールされていません。インストールしています...")
    subprocess.check_call([sys.executable, "-m", "pip", "install", "playwright"])
    subprocess.check_call([sys.executable, "-m", "playwright", "install"])
    from playwright.sync_api import sync_playwright

class PlaywrightHandler(BaseHandler):
    """
    Playwrightを使用したカスタムハンドラー
    JavaScriptで動的に生成されるコンテンツをスクレイピングするために使用
    """
    
    crawl_config = {
        'headers': {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        },
        'timeout': 120,  # Playwrightの処理に時間がかかる場合があるため、タイムアウトを長めに設定
    }
    
    def __init__(self):
        self.browser = None
        self.page = None
        super(<PERSON><PERSON><PERSON><PERSON><PERSON>, self).__init__()
    
    def _init_playwright(self):
        """Playwrightを初期化"""
        if self.browser is None:
            self.playwright = sync_playwright().start()
            self.browser = self.playwright.chromium.launch(headless=True)
    
    def _close_playwright(self):
        """Playwrightを終了"""
        if self.browser:
            self.browser.close()
            self.playwright.stop()
            self.browser = None
    
    def crawl_with_playwright(self, url, callback=None, **kwargs):
        """
        Playwrightを使用してURLをクロール
        """
        self._init_playwright()
        
        try:
            # 新しいページを開く
            page = self.browser.new_page()
            
            # ユーザーエージェントを設定
            page.set_extra_http_headers({
                'User-Agent': self.crawl_config['headers']['User-Agent']
            })
            
            # URLに移動
            self.logger.info(f"Navigating to {url} with Playwright")
            page.goto(url, wait_until="networkidle", timeout=self.crawl_config['timeout'] * 1000)
            
            # ページのHTMLを取得
            html = page.content()
            
            # ページのスクリーンショットを取得（デバッグ用）
            screenshot = page.screenshot()
            
            # ページを閉じる
            page.close()
            
            # 結果を処理
            result = {
                'url': url,
                'html': html,
                'screenshot': screenshot,
                'cookies': page.context.cookies(),
                'status_code': 200,  # Playwrightでは正確なステータスコードを取得するのが難しいため、成功した場合は200とする
            }
            
            # コールバック関数を呼び出す
            if callback:
                return callback(result, **kwargs)
            
            return result
        
        except Exception as e:
            self.logger.error(f"Error crawling {url} with Playwright: {e}")
            # エラーが発生した場合は、ブラウザを再起動
            self._close_playwright()
            self._init_playwright()
            raise
    
    def on_exit(self):
        """終了時にPlaywrightを終了"""
        self._close_playwright()


class Handler(PlaywrightHandler):
    """
    Playwrightを使用したスクレイピングの例
    動的に生成されるコンテンツを含むWebサイトからデータを抽出
    """
    
    @every(minutes=24 * 60)
    def on_start(self):
        # 動的コンテンツを含むWebサイトをクロール
        # 例として、GitHubのトレンドリポジトリページを使用
        self.crawl_with_playwright('https://github.com/trending', callback=self.index_page)
    
    def index_page(self, response):
        # HTMLを解析
        html = response['html']
        
        # トレンドリポジトリのリストを抽出
        # Playwrightで取得したHTMLを使用して、BeautifulSoupやpyqueryで解析することもできます
        repo_pattern = r'<article class="Box-row">(.*?)</article>'
        repo_blocks = re.findall(repo_pattern, html, re.DOTALL)
        
        results = []
        
        for repo_block in repo_blocks:
            # リポジトリ名を抽出
            name_pattern = r'<h2 class="h3 lh-condensed">\s*<a href="([^"]+)"[^>]*>(.*?)</a>'
            name_match = re.search(name_pattern, repo_block, re.DOTALL)
            
            if name_match:
                repo_url = f"https://github.com{name_match.group(1)}"
                repo_name = re.sub(r'<[^>]+>', '', name_match.group(2)).strip()
                
                # 説明を抽出
                description_pattern = r'<p class="col-9 color-fg-muted my-1 pr-4">(.*?)</p>'
                description_match = re.search(description_pattern, repo_block, re.DOTALL)
                description = ''
                if description_match:
                    description = re.sub(r'<[^>]+>', '', description_match.group(1)).strip()
                
                # 言語を抽出
                language_pattern = r'<span class="d-inline-block ml-0 mr-3">\s*<span[^>]*>(.*?)</span>\s*<span[^>]*>(.*?)</span>'
                language_match = re.search(language_pattern, repo_block, re.DOTALL)
                language = ''
                if language_match:
                    language = re.sub(r'<[^>]+>', '', language_match.group(2)).strip()
                
                # スター数を抽出
                stars_pattern = r'<a[^>]*href="[^"]+/stargazers"[^>]*>\s*<svg[^>]*>.*?</svg>\s*(.*?)\s*</a>'
                stars_match = re.search(stars_pattern, repo_block, re.DOTALL)
                stars = ''
                if stars_match:
                    stars = re.sub(r'<[^>]+>', '', stars_match.group(1)).strip()
                
                # フォーク数を抽出
                forks_pattern = r'<a[^>]*href="[^"]+/forks"[^>]*>\s*<svg[^>]*>.*?</svg>\s*(.*?)\s*</a>'
                forks_match = re.search(forks_pattern, repo_block, re.DOTALL)
                forks = ''
                if forks_match:
                    forks = re.sub(r'<[^>]+>', '', forks_match.group(1)).strip()
                
                # 今日のスター数を抽出
                today_stars_pattern = r'<span class="d-inline-block float-sm-right">\s*<svg[^>]*>.*?</svg>\s*(.*?)\s*stars today\s*</span>'
                today_stars_match = re.search(today_stars_pattern, repo_block, re.DOTALL)
                today_stars = ''
                if today_stars_match:
                    today_stars = re.sub(r'<[^>]+>', '', today_stars_match.group(1)).strip()
                
                # リポジトリの詳細ページをクロール
                self.crawl_with_playwright(repo_url, callback=self.detail_page, save={
                    'name': repo_name,
                    'description': description,
                    'language': language,
                    'stars': stars,
                    'forks': forks,
                    'today_stars': today_stars
                })
                
                results.append({
                    'name': repo_name,
                    'url': repo_url,
                    'description': description,
                    'language': language,
                    'stars': stars,
                    'forks': forks,
                    'today_stars': today_stars
                })
        
        # スクリーンショットを保存（デバッグ用）
        with open('github_trending.png', 'wb') as f:
            f.write(response['screenshot'])
        
        # 結果をJSONとして保存（デバッグ用）
        with open('github_trending.json', 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        return results
    
    def detail_page(self, response, save):
        html = response['html']
        
        # リポジトリの詳細情報を抽出
        # README内容を抽出
        readme_pattern = r'<article class="markdown-body entry-content container-lg"[^>]*>(.*?)</article>'
        readme_match = re.search(readme_pattern, html, re.DOTALL)
        readme = ''
        if readme_match:
            readme = re.sub(r'<[^>]+>', ' ', readme_match.group(1))
            readme = re.sub(r'\s+', ' ', readme).strip()
            # 長すぎる場合は切り詰める
            if len(readme) > 1000:
                readme = readme[:1000] + '...'
        
        # コントリビューター数を抽出
        contributors_pattern = r'<a[^>]*href="[^"]+/graphs/contributors"[^>]*>\s*<span[^>]*>(.*?)</span>\s*Contributors\s*</a>'
        contributors_match = re.search(contributors_pattern, html, re.DOTALL)
        contributors = ''
        if contributors_match:
            contributors = re.sub(r'<[^>]+>', '', contributors_match.group(1)).strip()
        
        # 最新のリリースを抽出
        release_pattern = r'<a[^>]*href="[^"]+/releases/tag/([^"]+)"[^>]*>'
        release_match = re.search(release_pattern, html)
        latest_release = release_match.group(1) if release_match else ''
        
        # 結果を返す
        return {
            'url': response['url'],
            'name': save['name'],
            'description': save['description'],
            'language': save['language'],
            'stars': save['stars'],
            'forks': save['forks'],
            'today_stars': save['today_stars'],
            'readme_excerpt': readme,
            'contributors': contributors,
            'latest_release': latest_release
        }
