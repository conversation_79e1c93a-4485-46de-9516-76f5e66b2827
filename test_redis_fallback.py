#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Redis必須化とXMLRPCフォールバック機能のテストスクリプト
"""

import os
import sys
import time
import redis
import requests
import subprocess
from datetime import datetime

def log_info(message):
    print(f"\033[32m[INFO]\033[0m {message}")

def log_warn(message):
    print(f"\033[33m[WARN]\033[0m {message}")

def log_error(message):
    print(f"\033[31m[ERROR]\033[0m {message}")

def test_redis_connection():
    """Redis接続テスト"""
    log_info("=== Redis接続テスト ===")
    
    try:
        # Redis接続を試行
        redis_client = redis.from_url("redis://localhost:6379/0", socket_timeout=5)
        redis_client.ping()
        log_info("✅ Redis接続成功")
        
        # 基本操作テスト
        test_key = "pyspider_test_key"
        test_value = f"test_value_{int(time.time())}"
        
        redis_client.set(test_key, test_value, ex=60)
        retrieved_value = redis_client.get(test_key)
        
        if retrieved_value and retrieved_value.decode('utf-8') == test_value:
            log_info("✅ Redis読み書きテスト成功")
            redis_client.delete(test_key)
            return True
        else:
            log_error("❌ Redis読み書きテスト失敗")
            return False
            
    except Exception as e:
        log_error(f"❌ Redis接続失敗: {e}")
        return False

def test_redis_fallback_library():
    """Redis フォールバックライブラリのテスト"""
    log_info("=== Redis フォールバックライブラリテスト ===")
    
    try:
        # フォールバック機能をインポート
        # 相対パス対応
        script_dir = os.path.dirname(os.path.abspath(__file__))
        project_root = os.path.dirname(script_dir)
        sys.path.insert(0, project_root)
        from pyspider.libs.redis_fallback import initialize_redis_fallback, get_redis_manager, get_message_queue
        
        # Redis管理機能を初期化
        redis_manager = initialize_redis_fallback(
            redis_url="redis://localhost:6379/0",
            check_interval=10,
            auto_fallback=True
        )
        
        log_info("✅ Redis フォールバック機能初期化成功")
        
        # 状態確認
        status = redis_manager.get_status()
        log_info(f"Redis状態: {status}")
        
        # メッセージキューテスト
        message_queue = get_message_queue()
        if message_queue:
            # テストメッセージを送信
            test_queue = "test_queue"
            test_message = f"test_message_{int(time.time())}"
            
            message_queue.put(test_queue, test_message)
            log_info("✅ メッセージキュー送信成功")
            
            # メッセージを受信
            received_message = message_queue.get(test_queue, timeout=5)
            if received_message == test_message:
                log_info("✅ メッセージキュー受信成功")
                return True
            else:
                log_error(f"❌ メッセージキュー受信失敗: {received_message}")
                return False
        else:
            log_error("❌ メッセージキュー取得失敗")
            return False
            
    except Exception as e:
        log_error(f"❌ Redis フォールバックライブラリテスト失敗: {e}")
        return False

def test_scheduler_redis_integration():
    """スケジューラのRedis統合テスト"""
    log_info("=== スケジューラRedis統合テスト ===")
    
    try:
        # スケジューラが動作しているかチェック
        import xmlrpc.client
        rpc = xmlrpc.client.ServerProxy('http://localhost:23333/')
        
        # カウンター情報を取得
        counter_result = rpc.counter('5m', 'sum')
        log_info(f"✅ スケジューラ接続成功: {len(counter_result)}プロジェクト")
        
        return True
        
    except Exception as e:
        log_error(f"❌ スケジューラ接続失敗: {e}")
        return False

def test_webui_redis_api():
    """WebUI Redis APIテスト"""
    log_info("=== WebUI Redis APIテスト ===")
    
    base_url = "http://localhost:5000"
    
    try:
        # Redis状態確認API
        response = requests.get(f"{base_url}/api/v2/redis/status", timeout=10)
        if response.status_code == 200:
            data = response.json()
            log_info(f"✅ Redis状態確認API: {data.get('status')}")
            
            redis_status = data.get('redis_status', {})
            if redis_status.get('enabled'):
                log_info(f"  Redis有効: {redis_status.get('status')}")
            else:
                log_warn("  Redis無効")
            
            return True
        else:
            log_error(f"❌ Redis状態確認API失敗: {response.status_code}")
            return False
            
    except Exception as e:
        log_error(f"❌ WebUI Redis APIテスト失敗: {e}")
        return False

def test_redis_service_control():
    """Redisサービス制御テスト"""
    log_info("=== Redisサービス制御テスト ===")
    
    try:
        # Redisサービスの状態確認
        result = subprocess.run(['systemctl', 'is-active', 'redis'], 
                              capture_output=True, text=True)
        
        if result.returncode == 0 and result.stdout.strip() == 'active':
            log_info("✅ Redisサービス動作中")
            
            # Redis停止テスト（注意: 実際のサービスを停止します）
            log_warn("⚠️ Redisサービス停止テスト（5秒後に再開）")
            
            # 停止
            subprocess.run(['sudo', 'systemctl', 'stop', 'redis'], check=True)
            time.sleep(2)
            
            # 接続テスト（失敗するはず）
            redis_available = test_redis_connection()
            if not redis_available:
                log_info("✅ Redis停止確認")
            
            # 再開
            subprocess.run(['sudo', 'systemctl', 'start', 'redis'], check=True)
            time.sleep(3)
            
            # 接続テスト（成功するはず）
            redis_available = test_redis_connection()
            if redis_available:
                log_info("✅ Redis再開確認")
                return True
            else:
                log_error("❌ Redis再開失敗")
                return False
                
        else:
            log_warn("⚠️ Redisサービスが動作していません")
            return False
            
    except Exception as e:
        log_error(f"❌ Redisサービス制御テスト失敗: {e}")
        return False

def test_fallback_mode():
    """フォールバックモードテスト"""
    log_info("=== フォールバックモードテスト ===")
    
    try:
        # WebUI経由でフォールバックモードを強制実行
        base_url = "http://localhost:5000"
        
        payload = {"action": "force_fallback"}
        response = requests.post(
            f"{base_url}/api/v2/redis/control",
            json=payload,
            headers={'Content-Type': 'application/json'},
            timeout=10
        )
        
        if response.status_code == 200:
            data = response.json()
            log_info(f"✅ フォールバック強制実行: {data.get('status')}")
            
            # 状態確認
            time.sleep(2)
            status_response = requests.get(f"{base_url}/api/v2/redis/status", timeout=10)
            if status_response.status_code == 200:
                status_data = status_response.json()
                redis_status = status_data.get('redis_status', {})
                
                if redis_status.get('status') == 'fallback':
                    log_info("✅ フォールバックモード確認")
                    return True
                else:
                    log_warn(f"⚠️ フォールバックモード未確認: {redis_status.get('status')}")
                    return False
            else:
                log_error("❌ フォールバック状態確認失敗")
                return False
        else:
            log_error(f"❌ フォールバック強制実行失敗: {response.status_code}")
            return False
            
    except Exception as e:
        log_error(f"❌ フォールバックモードテスト失敗: {e}")
        return False

def main():
    """メインテスト処理"""
    log_info("=== Redis必須化とXMLRPCフォールバック機能テスト開始 ===")
    
    tests = [
        ("Redis接続", test_redis_connection),
        ("Redis フォールバックライブラリ", test_redis_fallback_library),
        ("スケジューラRedis統合", test_scheduler_redis_integration),
        ("WebUI Redis API", test_webui_redis_api),
        ("フォールバックモード", test_fallback_mode),
        # ("Redisサービス制御", test_redis_service_control),  # 危険なテストなのでコメントアウト
    ]
    
    results = []
    
    for test_name, test_func in tests:
        log_info(f"\n--- {test_name}テスト実行中 ---")
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                log_info(f"✅ {test_name}テスト: 成功")
            else:
                log_warn(f"⚠️ {test_name}テスト: 失敗")
        except Exception as e:
            log_error(f"❌ {test_name}テスト: 例外 - {e}")
            results.append((test_name, False))
    
    # 結果サマリー
    log_info("\n=== テスト結果サマリー ===")
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 成功" if result else "❌ 失敗"
        log_info(f"  {test_name}: {status}")
    
    log_info(f"\n📊 テスト結果: {passed}/{total} 成功")
    
    if passed == total:
        log_info("🎉 全テストが成功しました！")
        log_info("Redis必須化とXMLRPCフォールバック機能が正常に動作しています。")
        return 0
    else:
        log_warn("⚠️ 一部のテストが失敗しました。")
        log_warn("設定や実装を確認してください。")
        return 1

if __name__ == "__main__":
    sys.exit(main())
