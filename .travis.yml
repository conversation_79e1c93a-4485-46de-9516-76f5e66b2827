language: python
cache: pip
python:
  - 3.8
  - 3.9
  - 3.10
  - 3.11
  - 3.12
  - 3.13
services:
    - docker
    - mongodb
    - rabbitmq
    - redis
    - mysql
    # - elasticsearch
    - postgresql
addons:
  postgresql: "9.4"
  apt:
    packages:
    - rabbitmq-server
env:
    - IGNORE_COUCHDB=1

before_install:
    - sudo apt-get update -qq
    - curl -O https://download.elastic.co/elasticsearch/release/org/elasticsearch/distribution/deb/elasticsearch/2.4.0/elasticsearch-2.4.0.deb && sudo dpkg -i --force-confnew elasticsearch-2.4.0.deb && sudo service elasticsearch restart
    - npm install express puppeteer
    - sudo docker pull scrapinghub/splash
    - sudo docker run -d --net=host scrapinghub/splash
before_script:
    - psql -c "CREATE DATABASE pyspider_test_taskdb ENCODING 'UTF8' TEMPLATE=template0;" -U postgres
    - psql -c "CREATE DATABASE pyspider_test_projectdb ENCODING 'UTF8' TEMPLATE=template0;" -U postgres
    - psql -c "CREATE DATABASE pyspider_test_resultdb ENCODING 'UTF8' TEMPLATE=template0;" -U postgres
    - sleep 10
install:
    - pip install https://github.com/marcus67/easywebdav/archive/master.zip
    - sudo apt-get install libgnutls28-dev
    - pip install -e .[all,test]
    - pip install coveralls
script:
    - coverage run setup.py test
after_success:
    - coverage combine
    - coveralls
