{"timestamp": "2025-06-08T09:41:05.886990", "level": "INFO", "logger": "root", "message": "Enhanced logging configured - Level: info, Dir: logs", "module": "enhanced_logging", "function": "setup_enhanced_logging", "line": 203}
[I 250608 09:41:06 run:31] Created PID file for fetcher: /tmp/pyspider_fetcher.pid
[I 250608 09:41:06 tornado_fetcher:1107] Fetcher starting...
[I 250608 09:41:06 tornado_fetcher:1164] Starting Tornado IOLoop
[I 250608 09:41:06 tornado_fetcher:1268] XML-RPC server listening on 0.0.0.0:24444
[I 250608 09:41:06 tornado_fetcher:1277] on fetch data:{'taskid': '_on_get_info', 'project': 'douyo', 'url': 'data:,_on_get_info', 'fetch': {'method': None, 'fetch_type': None}}
[I 250608 09:41:06 tornado_fetcher:480] [200] douyo:_on_get_info data:,_on_get_info 0s
[I 250608 09:41:06 tornado_fetcher:1277] on fetch data:{'taskid': '_on_get_info', 'project': 'bunbunbun', 'url': 'data:,_on_get_info', 'fetch': {'method': None, 'fetch_type': None}}
[I 250608 09:41:06 tornado_fetcher:480] [200] bunbunbun:_on_get_info data:,_on_get_info 0s
[I 250608 09:41:06 tornado_fetcher:1277] on fetch data:{'taskid': '_on_get_info', 'project': 'iuiu', 'url': 'data:,_on_get_info', 'fetch': {'method': None, 'fetch_type': None}}
[I 250608 09:41:06 tornado_fetcher:480] [200] iuiu:_on_get_info data:,_on_get_info 0s
[I 250608 09:41:06 tornado_fetcher:1277] on fetch data:{'taskid': '_on_get_info', 'project': 'mysql_detailed_test_2', 'url': 'data:,_on_get_info', 'fetch': {'method': None, 'fetch_type': None}}
[I 250608 09:41:06 tornado_fetcher:480] [200] mysql_detailed_test_2:_on_get_info data:,_on_get_info 0s
[I 250608 09:41:06 tornado_fetcher:1277] on fetch data:{'taskid': '_on_get_info', 'project': 'kokoko', 'url': 'data:,_on_get_info', 'fetch': {'method': None, 'fetch_type': None}}
[I 250608 09:41:06 tornado_fetcher:480] [200] kokoko:_on_get_info data:,_on_get_info 0s
[I 250608 09:41:06 tornado_fetcher:1277] on fetch data:{'taskid': '_on_get_info', 'project': 'mysql_detailed_test', 'url': 'data:,_on_get_info', 'fetch': {'method': None, 'fetch_type': None}}
[I 250608 09:41:06 tornado_fetcher:480] [200] mysql_detailed_test:_on_get_info data:,_on_get_info 0s
[I 250608 09:41:06 tornado_fetcher:1277] on fetch data:{'taskid': '_on_get_info', 'project': 'jijijiji', 'url': 'data:,_on_get_info', 'fetch': {'method': None, 'fetch_type': None}}
[I 250608 09:41:06 tornado_fetcher:480] [200] jijijiji:_on_get_info data:,_on_get_info 0s
[I 250608 09:41:06 tornado_fetcher:1277] on fetch data:{'taskid': '_on_get_info', 'project': 'tinti', 'url': 'data:,_on_get_info', 'fetch': {'method': None, 'fetch_type': None}}
[I 250608 09:41:06 tornado_fetcher:480] [200] tinti:_on_get_info data:,_on_get_info 0s
[2025-06-08 09:41:07] フェッチャー起動成功 (ポート: 24444)
